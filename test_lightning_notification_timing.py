#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار توقيت الإشعار التلقائي عند اختيار طريقة البرق في User Manager
تم إنشاؤه: 2025-07-24
الهدف: اختبار أن الإشعار يظهر بعد اختيار طريقة "البرق" وليس عند اختيار القالب
"""

import unittest
import sys
import os
from unittest.mock import Mock, patch, MagicMock

# إضافة مسار المشروع
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

class TestLightningNotificationTiming(unittest.TestCase):
    """اختبار توقيت الإشعار التلقائي عند اختيار طريقة البرق"""
    
    def setUp(self):
        """إعداد الاختبار"""
        # إنشاء mock للتطبيق
        self.mock_app = Mock()
        self.mock_app.logger = Mock()
        
        # إعداد mock للدوال المطلوبة
        self.mock_app.send_telegram_message_direct = Mock(return_value=True)
        self.mock_app.send_template_selection_notification = Mock(return_value=True)
        self.mock_app.show_independent_cards_count_menu = Mock()
        
        # إعداد بيانات الاختبار
        self.bot_token = "test_bot_token"
        self.chat_id = "test_chat_id"
        self.template_name = "قالب_اختبار"

    def test_template_selection_no_notification(self):
        """اختبار أن اختيار القالب لا يرسل إشعار"""
        
        # محاكاة دالة اختيار القالب
        def mock_process_independent_template_selection(callback_data):
            # استخراج نوع النظام واسم القالب
            parts = callback_data.replace("independent_template_", "").split("_", 1)
            if len(parts) < 2:
                return False
            
            template_type = parts[0]  # um أو hs
            template_name = parts[1]
            
            # تحديد النظام
            if template_type == "um":
                system_name = "User Manager"
            elif template_type == "hs":
                system_name = "Hotspot"
            else:
                return False
            
            # في التوقيت الجديد، لا يتم إرسال إشعار هنا
            notification_sent = False
            
            return {
                "template_selected": True,
                "notification_sent": notification_sent,
                "template_type": template_type,
                "template_name": template_name,
                "system_name": system_name
            }
        
        # تنفيذ الاختبار
        result = mock_process_independent_template_selection("independent_template_um_قالب_اختبار")
        
        # التحقق من النتائج
        self.assertTrue(result["template_selected"])
        self.assertFalse(result["notification_sent"])  # لا يجب إرسال إشعار عند اختيار القالب
        self.assertEqual(result["template_type"], "um")
        self.assertEqual(result["system_name"], "User Manager")
        
        print("✅ اختبار عدم إرسال إشعار عند اختيار القالب نجح")

    def test_lightning_method_selection_with_notification(self):
        """اختبار إرسال الإشعار عند اختيار طريقة البرق في User Manager"""
        
        # محاكاة دالة اختيار طريقة الإنشاء
        def mock_process_independent_template_creation(callback_data):
            # استخراج البيانات من callback_data
            parts = callback_data.replace("independent_create_", "").split("_")
            if len(parts) < 3:
                return False
            
            template_type = parts[0]  # um أو hs
            method = parts[-1]  # normal أو lightning
            template_name = "_".join(parts[1:-1])  # اسم القالب
            
            # إرسال إشعار تلقائي عند اختيار البرق في User Manager
            notification_sent = False
            if method == "lightning" and template_type == "um":
                notification_sent = True
            
            return {
                "method_selected": True,
                "notification_sent": notification_sent,
                "template_type": template_type,
                "method": method,
                "template_name": template_name
            }
        
        # تنفيذ الاختبار - اختيار البرق في User Manager
        result_um_lightning = mock_process_independent_template_creation("independent_create_um_قالب_اختبار_lightning")
        
        # التحقق من النتائج
        self.assertTrue(result_um_lightning["method_selected"])
        self.assertTrue(result_um_lightning["notification_sent"])  # يجب إرسال إشعار عند اختيار البرق في UM
        self.assertEqual(result_um_lightning["template_type"], "um")
        self.assertEqual(result_um_lightning["method"], "lightning")
        
        print("✅ اختبار إرسال إشعار عند اختيار البرق في User Manager نجح")

    def test_normal_method_selection_no_notification(self):
        """اختبار عدم إرسال الإشعار عند اختيار الطريقة العادية في User Manager"""
        
        # محاكاة دالة اختيار طريقة الإنشاء
        def mock_process_independent_template_creation(callback_data):
            # استخراج البيانات من callback_data
            parts = callback_data.replace("independent_create_", "").split("_")
            if len(parts) < 3:
                return False
            
            template_type = parts[0]  # um أو hs
            method = parts[-1]  # normal أو lightning
            template_name = "_".join(parts[1:-1])  # اسم القالب
            
            # إرسال إشعار تلقائي عند اختيار البرق في User Manager فقط
            notification_sent = False
            if method == "lightning" and template_type == "um":
                notification_sent = True
            
            return {
                "method_selected": True,
                "notification_sent": notification_sent,
                "template_type": template_type,
                "method": method,
                "template_name": template_name
            }
        
        # تنفيذ الاختبار - اختيار الطريقة العادية في User Manager
        result_um_normal = mock_process_independent_template_creation("independent_create_um_قالب_اختبار_normal")
        
        # التحقق من النتائج
        self.assertTrue(result_um_normal["method_selected"])
        self.assertFalse(result_um_normal["notification_sent"])  # لا يجب إرسال إشعار عند اختيار الطريقة العادية
        self.assertEqual(result_um_normal["template_type"], "um")
        self.assertEqual(result_um_normal["method"], "normal")
        
        print("✅ اختبار عدم إرسال إشعار عند اختيار الطريقة العادية نجح")

    def test_hotspot_lightning_no_notification(self):
        """اختبار عدم إرسال الإشعار عند اختيار البرق في Hotspot"""
        
        # محاكاة دالة اختيار طريقة الإنشاء
        def mock_process_independent_template_creation(callback_data):
            # استخراج البيانات من callback_data
            parts = callback_data.replace("independent_create_", "").split("_")
            if len(parts) < 3:
                return False
            
            template_type = parts[0]  # um أو hs
            method = parts[-1]  # normal أو lightning
            template_name = "_".join(parts[1:-1])  # اسم القالب
            
            # إرسال إشعار تلقائي عند اختيار البرق في User Manager فقط
            notification_sent = False
            if method == "lightning" and template_type == "um":
                notification_sent = True
            
            return {
                "method_selected": True,
                "notification_sent": notification_sent,
                "template_type": template_type,
                "method": method,
                "template_name": template_name
            }
        
        # تنفيذ الاختبار - اختيار البرق في Hotspot
        result_hs_lightning = mock_process_independent_template_creation("independent_create_hs_قالب_هوت_سبوت_lightning")
        
        # التحقق من النتائج
        self.assertTrue(result_hs_lightning["method_selected"])
        self.assertFalse(result_hs_lightning["notification_sent"])  # لا يجب إرسال إشعار في Hotspot
        self.assertEqual(result_hs_lightning["template_type"], "hs")
        self.assertEqual(result_hs_lightning["method"], "lightning")
        
        print("✅ اختبار عدم إرسال إشعار عند اختيار البرق في Hotspot نجح")

    def test_notification_content_for_lightning(self):
        """اختبار محتوى الإشعار عند اختيار البرق"""
        
        # محاكاة دالة إنشاء رسالة الإشعار
        def mock_create_lightning_notification(template_name, system_name, current_count):
            notification_message = f"""📊 **إشعار اختيار البرق**

🎯 **القالب المختار:** {template_name}
🔧 **النظام:** {system_name}
⚡ **الطريقة:** البرق (Lightning)
📈 **العدد الحالي للكروت:** {current_count}

⏰ **الوقت:** 14:30:25
📅 **التاريخ:** 2025-07-24

✅ **الحالة:** جاهز لبدء عملية البرق"""
            return notification_message
        
        # تنفيذ الاختبار
        message = mock_create_lightning_notification("قالب_اختبار", "User Manager", 150)
        
        # التحقق من وجود العناصر المطلوبة
        self.assertIn("📊", message)  # أيقونة الإحصائيات
        self.assertIn("🎯", message)  # أيقونة القالب
        self.assertIn("🔧", message)  # أيقونة النظام
        self.assertIn("⚡", message)  # أيقونة البرق
        self.assertIn("📈", message)  # أيقونة العدد
        self.assertIn("⏰", message)  # أيقونة الوقت
        self.assertIn("📅", message)  # أيقونة التاريخ
        self.assertIn("✅", message)  # أيقونة الحالة
        self.assertIn("قالب_اختبار", message)  # اسم القالب
        self.assertIn("User Manager", message)  # نوع النظام
        self.assertIn("البرق (Lightning)", message)  # طريقة البرق
        self.assertIn("150", message)  # العدد الحالي
        
        print("✅ اختبار محتوى إشعار البرق نجح")

    def test_workflow_sequence(self):
        """اختبار تسلسل العمليات الكامل"""
        
        workflow_steps = []
        
        # الخطوة 1: اختيار القالب
        def step1_select_template():
            workflow_steps.append("template_selected")
            return {"notification_sent": False}  # لا إشعار عند اختيار القالب
        
        # الخطوة 2: عرض خيارات الطرق
        def step2_show_methods():
            workflow_steps.append("methods_shown")
            return {"methods": ["normal", "lightning"]}
        
        # الخطوة 3: اختيار طريقة البرق
        def step3_select_lightning():
            workflow_steps.append("lightning_selected")
            return {"notification_sent": True}  # إشعار عند اختيار البرق
        
        # الخطوة 4: عرض خيارات العدد
        def step4_show_count_options():
            workflow_steps.append("count_options_shown")
            return {"counts": [5, 10, 20, 50, 100, 200]}
        
        # تنفيذ التسلسل
        result1 = step1_select_template()
        result2 = step2_show_methods()
        result3 = step3_select_lightning()
        result4 = step4_show_count_options()
        
        # التحقق من التسلسل الصحيح
        expected_sequence = ["template_selected", "methods_shown", "lightning_selected", "count_options_shown"]
        self.assertEqual(workflow_steps, expected_sequence)
        
        # التحقق من أن الإشعار يرسل في الخطوة الصحيحة فقط
        self.assertFalse(result1["notification_sent"])  # لا إشعار في الخطوة 1
        self.assertTrue(result3["notification_sent"])   # إشعار في الخطوة 3 (اختيار البرق)
        
        print("✅ اختبار تسلسل العمليات الكامل نجح")

    def test_callback_data_parsing(self):
        """اختبار تحليل callback_data للطرق المختلفة"""
        
        # محاكاة دالة تحليل callback_data
        def mock_parse_callback_data(callback_data):
            if callback_data.startswith("independent_template_"):
                # اختيار قالب
                parts = callback_data.replace("independent_template_", "").split("_", 1)
                return {
                    "action": "template_selection",
                    "template_type": parts[0],
                    "template_name": parts[1],
                    "notification_required": False
                }
            elif callback_data.startswith("independent_create_"):
                # اختيار طريقة إنشاء
                parts = callback_data.replace("independent_create_", "").split("_")
                template_type = parts[0]
                method = parts[-1]
                template_name = "_".join(parts[1:-1])
                
                return {
                    "action": "method_selection",
                    "template_type": template_type,
                    "template_name": template_name,
                    "method": method,
                    "notification_required": (method == "lightning" and template_type == "um")
                }
            
            return {"action": "unknown"}
        
        # اختبار تحليل callback_data مختلفة
        test_cases = [
            ("independent_template_um_قالب_10", False),
            ("independent_template_hs_قالب_هوت_سبوت", False),
            ("independent_create_um_قالب_10_normal", False),
            ("independent_create_um_قالب_10_lightning", True),
            ("independent_create_hs_قالب_هوت_سبوت_lightning", False),
        ]
        
        for callback_data, expected_notification in test_cases:
            result = mock_parse_callback_data(callback_data)
            self.assertEqual(result["notification_required"], expected_notification,
                           f"فشل في {callback_data}: متوقع {expected_notification}, حصل على {result['notification_required']}")
        
        print("✅ اختبار تحليل callback_data نجح")

def run_tests():
    """تشغيل جميع الاختبارات"""
    print("🧪 بدء اختبار توقيت الإشعار التلقائي عند اختيار طريقة البرق")
    print("=" * 80)
    
    # إنشاء مجموعة الاختبارات
    test_suite = unittest.TestLoader().loadTestsFromTestCase(TestLightningNotificationTiming)
    
    # تشغيل الاختبارات
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # عرض النتائج
    print("\n" + "=" * 80)
    if result.wasSuccessful():
        print("🎉 جميع الاختبارات نجحت!")
        print(f"✅ تم تشغيل {result.testsRun} اختبار بنجاح")
        print("\n📋 ملخص التوقيت الجديد:")
        print("• ❌ لا إشعار عند اختيار القالب")
        print("• ✅ إشعار عند اختيار طريقة البرق في User Manager")
        print("• ❌ لا إشعار عند اختيار الطريقة العادية")
        print("• ❌ لا إشعار عند اختيار البرق في Hotspot")
    else:
        print("❌ بعض الاختبارات فشلت!")
        print(f"❌ فشل {len(result.failures)} اختبار")
        print(f"⚠️ خطأ في {len(result.errors)} اختبار")
    
    return result.wasSuccessful()

if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1)
