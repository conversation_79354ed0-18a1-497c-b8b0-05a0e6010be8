# MikroTik Script - نسخة احتياطية للكروت
# تم الإنشاء: 2025-07-26 01:34:24
# القالب: 10
# النظام: user_manager
# عدد الكروت: 50
# تم إنشاؤه بواسطة: مولد كروت MikroTik

:put "🚀 بدء تنفيذ النسخة الاحتياطية للكروت";
:put "📋 القالب: 10";
:put "🎯 النظام: user_manager";
:put "📊 عدد الكروت: 50";

:local success 0;
:local errors 0;
:local total 50;

:put "⏳ بدء إضافة الكروت...";

# إضافة مستخدمي User Manager
:put "👤 إضافة 50 مستخدم User Manager...";

# المستخدم 1: 0197314060
:do {
    /tool user-manager user add customer="admin" username="0197314060" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0197314060";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0197314060";
};

# المستخدم 2: 0122474693
:do {
    /tool user-manager user add customer="admin" username="0122474693" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0122474693";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0122474693";
};

# المستخدم 3: 0126720259
:do {
    /tool user-manager user add customer="admin" username="0126720259" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0126720259";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0126720259";
};

# المستخدم 4: 0126359281
:do {
    /tool user-manager user add customer="admin" username="0126359281" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0126359281";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0126359281";
};

# المستخدم 5: 0136237488
:do {
    /tool user-manager user add customer="admin" username="0136237488" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0136237488";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0136237488";
};

# المستخدم 6: 0195312742
:do {
    /tool user-manager user add customer="admin" username="0195312742" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0195312742";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0195312742";
};

# المستخدم 7: 0148226505
:do {
    /tool user-manager user add customer="admin" username="0148226505" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0148226505";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0148226505";
};

# المستخدم 8: 0143331043
:do {
    /tool user-manager user add customer="admin" username="0143331043" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0143331043";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0143331043";
};

# المستخدم 9: 0144077934
:do {
    /tool user-manager user add customer="admin" username="0144077934" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0144077934";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0144077934";
};

# المستخدم 10: 0161130752
:do {
    /tool user-manager user add customer="admin" username="0161130752" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0161130752";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0161130752";
};

# المستخدم 11: 0117088550
:do {
    /tool user-manager user add customer="admin" username="0117088550" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0117088550";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0117088550";
};

# المستخدم 12: 0152393830
:do {
    /tool user-manager user add customer="admin" username="0152393830" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0152393830";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0152393830";
};

# المستخدم 13: 0108471506
:do {
    /tool user-manager user add customer="admin" username="0108471506" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0108471506";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0108471506";
};

# المستخدم 14: 0176636870
:do {
    /tool user-manager user add customer="admin" username="0176636870" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0176636870";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0176636870";
};

# المستخدم 15: 0179504933
:do {
    /tool user-manager user add customer="admin" username="0179504933" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0179504933";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0179504933";
};

# المستخدم 16: 0177719499
:do {
    /tool user-manager user add customer="admin" username="0177719499" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0177719499";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0177719499";
};

# المستخدم 17: 0160700037
:do {
    /tool user-manager user add customer="admin" username="0160700037" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0160700037";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0160700037";
};

# المستخدم 18: 0115135877
:do {
    /tool user-manager user add customer="admin" username="0115135877" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0115135877";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0115135877";
};

# المستخدم 19: 0104370310
:do {
    /tool user-manager user add customer="admin" username="0104370310" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0104370310";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0104370310";
};

# المستخدم 20: 0154223036
:do {
    /tool user-manager user add customer="admin" username="0154223036" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0154223036";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0154223036";
};

# المستخدم 21: 0117175421
:do {
    /tool user-manager user add customer="admin" username="0117175421" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0117175421";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0117175421";
};

# المستخدم 22: 0186389557
:do {
    /tool user-manager user add customer="admin" username="0186389557" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0186389557";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0186389557";
};

# المستخدم 23: 0158724420
:do {
    /tool user-manager user add customer="admin" username="0158724420" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0158724420";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0158724420";
};

# المستخدم 24: 0167698821
:do {
    /tool user-manager user add customer="admin" username="0167698821" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0167698821";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0167698821";
};

# المستخدم 25: 0132175203
:do {
    /tool user-manager user add customer="admin" username="0132175203" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0132175203";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0132175203";
};

# المستخدم 26: 0150642360
:do {
    /tool user-manager user add customer="admin" username="0150642360" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0150642360";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0150642360";
};

# المستخدم 27: 0121271590
:do {
    /tool user-manager user add customer="admin" username="0121271590" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0121271590";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0121271590";
};

# المستخدم 28: 0158722768
:do {
    /tool user-manager user add customer="admin" username="0158722768" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0158722768";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0158722768";
};

# المستخدم 29: 0149841077
:do {
    /tool user-manager user add customer="admin" username="0149841077" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0149841077";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0149841077";
};

# المستخدم 30: 0131466753
:do {
    /tool user-manager user add customer="admin" username="0131466753" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0131466753";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0131466753";
};

# المستخدم 31: 0146201221
:do {
    /tool user-manager user add customer="admin" username="0146201221" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0146201221";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0146201221";
};

# المستخدم 32: 0107959096
:do {
    /tool user-manager user add customer="admin" username="0107959096" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0107959096";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0107959096";
};

# المستخدم 33: 0116614502
:do {
    /tool user-manager user add customer="admin" username="0116614502" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0116614502";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0116614502";
};

# المستخدم 34: 0176332959
:do {
    /tool user-manager user add customer="admin" username="0176332959" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0176332959";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0176332959";
};

# المستخدم 35: 0117763816
:do {
    /tool user-manager user add customer="admin" username="0117763816" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0117763816";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0117763816";
};

# المستخدم 36: 0163349158
:do {
    /tool user-manager user add customer="admin" username="0163349158" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0163349158";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0163349158";
};

# المستخدم 37: 0180136259
:do {
    /tool user-manager user add customer="admin" username="0180136259" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0180136259";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0180136259";
};

# المستخدم 38: 0126384183
:do {
    /tool user-manager user add customer="admin" username="0126384183" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0126384183";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0126384183";
};

# المستخدم 39: 0100694127
:do {
    /tool user-manager user add customer="admin" username="0100694127" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0100694127";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0100694127";
};

# المستخدم 40: 0183827644
:do {
    /tool user-manager user add customer="admin" username="0183827644" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0183827644";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0183827644";
};

# المستخدم 41: 0141390549
:do {
    /tool user-manager user add customer="admin" username="0141390549" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0141390549";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0141390549";
};

# المستخدم 42: 0179989211
:do {
    /tool user-manager user add customer="admin" username="0179989211" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0179989211";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0179989211";
};

# المستخدم 43: 0160039583
:do {
    /tool user-manager user add customer="admin" username="0160039583" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0160039583";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0160039583";
};

# المستخدم 44: 0138617057
:do {
    /tool user-manager user add customer="admin" username="0138617057" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0138617057";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0138617057";
};

# المستخدم 45: 0192485912
:do {
    /tool user-manager user add customer="admin" username="0192485912" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0192485912";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0192485912";
};

# المستخدم 46: 0174942603
:do {
    /tool user-manager user add customer="admin" username="0174942603" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0174942603";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0174942603";
};

# المستخدم 47: 0108680941
:do {
    /tool user-manager user add customer="admin" username="0108680941" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0108680941";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0108680941";
};

# المستخدم 48: 0163543035
:do {
    /tool user-manager user add customer="admin" username="0163543035" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0163543035";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0163543035";
};

# المستخدم 49: 0117324870
:do {
    /tool user-manager user add customer="admin" username="0117324870" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0117324870";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0117324870";
};

# المستخدم 50: 0165477686
:do {
    /tool user-manager user add customer="admin" username="0165477686" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0165477686";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0165477686";
};


:put "📊 تم الانتهاء من النسخة الاحتياطية";
:put "✅ نجح: $success كرت";
:put "❌ فشل: $errors كرت";
:put "📈 الإجمالي: $total كرت";

:put "🎉 تم استعادة جميع كروت القالب: 10";
:put "💡 النسخة الاحتياطية مكتملة بنجاح!";
