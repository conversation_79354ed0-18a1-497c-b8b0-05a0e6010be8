# MikroTik Script - نسخة احتياطية للكروت
# تم الإنشاء: 2025-07-25 04:35:49
# القالب: 10
# النظام: user_manager
# عدد الكروت: 200
# تم إنشاؤه بواسطة: مولد كروت MikroTik

:put "🚀 بدء تنفيذ النسخة الاحتياطية للكروت";
:put "📋 القالب: 10";
:put "🎯 النظام: user_manager";
:put "📊 عدد الكروت: 200";

:local success 0;
:local errors 0;
:local total 200;

:put "⏳ بدء إضافة الكروت...";

# إضافة مستخدمي User Manager
:put "👤 إضافة 200 مستخدم User Manager...";

# المستخدم 1: 0156787975
:do {
    /tool user-manager user add customer="admin" username="0156787975" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0156787975";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0156787975";
};

# المستخدم 2: 0184972813
:do {
    /tool user-manager user add customer="admin" username="0184972813" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0184972813";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0184972813";
};

# المستخدم 3: 0172268897
:do {
    /tool user-manager user add customer="admin" username="0172268897" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0172268897";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0172268897";
};

# المستخدم 4: 0116032884
:do {
    /tool user-manager user add customer="admin" username="0116032884" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0116032884";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0116032884";
};

# المستخدم 5: 0194609187
:do {
    /tool user-manager user add customer="admin" username="0194609187" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0194609187";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0194609187";
};

# المستخدم 6: 0119824474
:do {
    /tool user-manager user add customer="admin" username="0119824474" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0119824474";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0119824474";
};

# المستخدم 7: 0122700253
:do {
    /tool user-manager user add customer="admin" username="0122700253" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0122700253";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0122700253";
};

# المستخدم 8: 0128524093
:do {
    /tool user-manager user add customer="admin" username="0128524093" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0128524093";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0128524093";
};

# المستخدم 9: 0134132165
:do {
    /tool user-manager user add customer="admin" username="0134132165" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0134132165";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0134132165";
};

# المستخدم 10: 0166217870
:do {
    /tool user-manager user add customer="admin" username="0166217870" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0166217870";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0166217870";
};

# المستخدم 11: 0183704882
:do {
    /tool user-manager user add customer="admin" username="0183704882" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0183704882";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0183704882";
};

# المستخدم 12: 0153090548
:do {
    /tool user-manager user add customer="admin" username="0153090548" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0153090548";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0153090548";
};

# المستخدم 13: 0179088632
:do {
    /tool user-manager user add customer="admin" username="0179088632" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0179088632";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0179088632";
};

# المستخدم 14: 0196176861
:do {
    /tool user-manager user add customer="admin" username="0196176861" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0196176861";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0196176861";
};

# المستخدم 15: 0165349609
:do {
    /tool user-manager user add customer="admin" username="0165349609" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0165349609";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0165349609";
};

# المستخدم 16: 0149956739
:do {
    /tool user-manager user add customer="admin" username="0149956739" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0149956739";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0149956739";
};

# المستخدم 17: 0163508464
:do {
    /tool user-manager user add customer="admin" username="0163508464" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0163508464";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0163508464";
};

# المستخدم 18: 0161509817
:do {
    /tool user-manager user add customer="admin" username="0161509817" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0161509817";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0161509817";
};

# المستخدم 19: 0164715807
:do {
    /tool user-manager user add customer="admin" username="0164715807" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0164715807";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0164715807";
};

# المستخدم 20: 0127310091
:do {
    /tool user-manager user add customer="admin" username="0127310091" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0127310091";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0127310091";
};

# المستخدم 21: 0105668346
:do {
    /tool user-manager user add customer="admin" username="0105668346" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0105668346";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0105668346";
};

# المستخدم 22: 0131524532
:do {
    /tool user-manager user add customer="admin" username="0131524532" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0131524532";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0131524532";
};

# المستخدم 23: 0101078593
:do {
    /tool user-manager user add customer="admin" username="0101078593" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0101078593";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0101078593";
};

# المستخدم 24: 0137743276
:do {
    /tool user-manager user add customer="admin" username="0137743276" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0137743276";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0137743276";
};

# المستخدم 25: 0109297963
:do {
    /tool user-manager user add customer="admin" username="0109297963" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0109297963";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0109297963";
};

# المستخدم 26: 0147707635
:do {
    /tool user-manager user add customer="admin" username="0147707635" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0147707635";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0147707635";
};

# المستخدم 27: 0111613959
:do {
    /tool user-manager user add customer="admin" username="0111613959" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0111613959";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0111613959";
};

# المستخدم 28: 0155576835
:do {
    /tool user-manager user add customer="admin" username="0155576835" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0155576835";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0155576835";
};

# المستخدم 29: 0114940691
:do {
    /tool user-manager user add customer="admin" username="0114940691" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0114940691";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0114940691";
};

# المستخدم 30: 0181529439
:do {
    /tool user-manager user add customer="admin" username="0181529439" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0181529439";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0181529439";
};

# المستخدم 31: 0166594178
:do {
    /tool user-manager user add customer="admin" username="0166594178" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0166594178";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0166594178";
};

# المستخدم 32: 0130035156
:do {
    /tool user-manager user add customer="admin" username="0130035156" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0130035156";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0130035156";
};

# المستخدم 33: 0193284745
:do {
    /tool user-manager user add customer="admin" username="0193284745" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0193284745";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0193284745";
};

# المستخدم 34: 0106407251
:do {
    /tool user-manager user add customer="admin" username="0106407251" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0106407251";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0106407251";
};

# المستخدم 35: 0173699680
:do {
    /tool user-manager user add customer="admin" username="0173699680" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0173699680";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0173699680";
};

# المستخدم 36: 0114095062
:do {
    /tool user-manager user add customer="admin" username="0114095062" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0114095062";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0114095062";
};

# المستخدم 37: 0111290334
:do {
    /tool user-manager user add customer="admin" username="0111290334" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0111290334";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0111290334";
};

# المستخدم 38: 0160879268
:do {
    /tool user-manager user add customer="admin" username="0160879268" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0160879268";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0160879268";
};

# المستخدم 39: 0123386343
:do {
    /tool user-manager user add customer="admin" username="0123386343" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0123386343";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0123386343";
};

# المستخدم 40: 0116927990
:do {
    /tool user-manager user add customer="admin" username="0116927990" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0116927990";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0116927990";
};

# المستخدم 41: 0154926380
:do {
    /tool user-manager user add customer="admin" username="0154926380" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0154926380";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0154926380";
};

# المستخدم 42: 0189464800
:do {
    /tool user-manager user add customer="admin" username="0189464800" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0189464800";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0189464800";
};

# المستخدم 43: 0197237195
:do {
    /tool user-manager user add customer="admin" username="0197237195" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0197237195";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0197237195";
};

# المستخدم 44: 0166842946
:do {
    /tool user-manager user add customer="admin" username="0166842946" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0166842946";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0166842946";
};

# المستخدم 45: 0163850230
:do {
    /tool user-manager user add customer="admin" username="0163850230" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0163850230";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0163850230";
};

# المستخدم 46: 0127514857
:do {
    /tool user-manager user add customer="admin" username="0127514857" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0127514857";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0127514857";
};

# المستخدم 47: 0155462013
:do {
    /tool user-manager user add customer="admin" username="0155462013" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0155462013";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0155462013";
};

# المستخدم 48: 0113099752
:do {
    /tool user-manager user add customer="admin" username="0113099752" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0113099752";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0113099752";
};

# المستخدم 49: 0118091860
:do {
    /tool user-manager user add customer="admin" username="0118091860" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0118091860";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0118091860";
};

# المستخدم 50: 0177784197
:do {
    /tool user-manager user add customer="admin" username="0177784197" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0177784197";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0177784197";
};

# المستخدم 51: 0111062420
:do {
    /tool user-manager user add customer="admin" username="0111062420" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0111062420";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0111062420";
};

# المستخدم 52: 0150635953
:do {
    /tool user-manager user add customer="admin" username="0150635953" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0150635953";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0150635953";
};

# المستخدم 53: 0186887994
:do {
    /tool user-manager user add customer="admin" username="0186887994" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0186887994";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0186887994";
};

# المستخدم 54: 0179940655
:do {
    /tool user-manager user add customer="admin" username="0179940655" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0179940655";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0179940655";
};

# المستخدم 55: 0177367996
:do {
    /tool user-manager user add customer="admin" username="0177367996" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0177367996";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0177367996";
};

# المستخدم 56: 0181186025
:do {
    /tool user-manager user add customer="admin" username="0181186025" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0181186025";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0181186025";
};

# المستخدم 57: 0150837199
:do {
    /tool user-manager user add customer="admin" username="0150837199" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0150837199";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0150837199";
};

# المستخدم 58: 0114383528
:do {
    /tool user-manager user add customer="admin" username="0114383528" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0114383528";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0114383528";
};

# المستخدم 59: 0148282128
:do {
    /tool user-manager user add customer="admin" username="0148282128" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0148282128";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0148282128";
};

# المستخدم 60: 0118195151
:do {
    /tool user-manager user add customer="admin" username="0118195151" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0118195151";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0118195151";
};

# المستخدم 61: 0127930671
:do {
    /tool user-manager user add customer="admin" username="0127930671" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0127930671";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0127930671";
};

# المستخدم 62: 0131652316
:do {
    /tool user-manager user add customer="admin" username="0131652316" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0131652316";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0131652316";
};

# المستخدم 63: 0150504195
:do {
    /tool user-manager user add customer="admin" username="0150504195" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0150504195";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0150504195";
};

# المستخدم 64: 0137313168
:do {
    /tool user-manager user add customer="admin" username="0137313168" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0137313168";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0137313168";
};

# المستخدم 65: 0176859231
:do {
    /tool user-manager user add customer="admin" username="0176859231" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0176859231";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0176859231";
};

# المستخدم 66: 0181181783
:do {
    /tool user-manager user add customer="admin" username="0181181783" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0181181783";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0181181783";
};

# المستخدم 67: 0186119345
:do {
    /tool user-manager user add customer="admin" username="0186119345" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0186119345";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0186119345";
};

# المستخدم 68: 0124990679
:do {
    /tool user-manager user add customer="admin" username="0124990679" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0124990679";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0124990679";
};

# المستخدم 69: 0104426611
:do {
    /tool user-manager user add customer="admin" username="0104426611" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0104426611";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0104426611";
};

# المستخدم 70: 0179014892
:do {
    /tool user-manager user add customer="admin" username="0179014892" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0179014892";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0179014892";
};

# المستخدم 71: 0136035839
:do {
    /tool user-manager user add customer="admin" username="0136035839" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0136035839";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0136035839";
};

# المستخدم 72: 0128566507
:do {
    /tool user-manager user add customer="admin" username="0128566507" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0128566507";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0128566507";
};

# المستخدم 73: 0169099585
:do {
    /tool user-manager user add customer="admin" username="0169099585" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0169099585";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0169099585";
};

# المستخدم 74: 0128872382
:do {
    /tool user-manager user add customer="admin" username="0128872382" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0128872382";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0128872382";
};

# المستخدم 75: 0141989238
:do {
    /tool user-manager user add customer="admin" username="0141989238" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0141989238";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0141989238";
};

# المستخدم 76: 0157920194
:do {
    /tool user-manager user add customer="admin" username="0157920194" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0157920194";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0157920194";
};

# المستخدم 77: 0111814355
:do {
    /tool user-manager user add customer="admin" username="0111814355" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0111814355";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0111814355";
};

# المستخدم 78: 0144337220
:do {
    /tool user-manager user add customer="admin" username="0144337220" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0144337220";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0144337220";
};

# المستخدم 79: 0170255579
:do {
    /tool user-manager user add customer="admin" username="0170255579" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0170255579";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0170255579";
};

# المستخدم 80: 0153805216
:do {
    /tool user-manager user add customer="admin" username="0153805216" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0153805216";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0153805216";
};

# المستخدم 81: 0150512596
:do {
    /tool user-manager user add customer="admin" username="0150512596" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0150512596";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0150512596";
};

# المستخدم 82: 0127573794
:do {
    /tool user-manager user add customer="admin" username="0127573794" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0127573794";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0127573794";
};

# المستخدم 83: 0149488065
:do {
    /tool user-manager user add customer="admin" username="0149488065" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0149488065";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0149488065";
};

# المستخدم 84: 0187510433
:do {
    /tool user-manager user add customer="admin" username="0187510433" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0187510433";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0187510433";
};

# المستخدم 85: 0184055378
:do {
    /tool user-manager user add customer="admin" username="0184055378" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0184055378";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0184055378";
};

# المستخدم 86: 0185630987
:do {
    /tool user-manager user add customer="admin" username="0185630987" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0185630987";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0185630987";
};

# المستخدم 87: 0135631831
:do {
    /tool user-manager user add customer="admin" username="0135631831" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0135631831";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0135631831";
};

# المستخدم 88: 0186880468
:do {
    /tool user-manager user add customer="admin" username="0186880468" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0186880468";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0186880468";
};

# المستخدم 89: 0103250258
:do {
    /tool user-manager user add customer="admin" username="0103250258" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0103250258";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0103250258";
};

# المستخدم 90: 0188828265
:do {
    /tool user-manager user add customer="admin" username="0188828265" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0188828265";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0188828265";
};

# المستخدم 91: 0166458539
:do {
    /tool user-manager user add customer="admin" username="0166458539" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0166458539";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0166458539";
};

# المستخدم 92: 0103056241
:do {
    /tool user-manager user add customer="admin" username="0103056241" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0103056241";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0103056241";
};

# المستخدم 93: 0131561984
:do {
    /tool user-manager user add customer="admin" username="0131561984" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0131561984";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0131561984";
};

# المستخدم 94: 0112678908
:do {
    /tool user-manager user add customer="admin" username="0112678908" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0112678908";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0112678908";
};

# المستخدم 95: 0112325048
:do {
    /tool user-manager user add customer="admin" username="0112325048" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0112325048";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0112325048";
};

# المستخدم 96: 0171629622
:do {
    /tool user-manager user add customer="admin" username="0171629622" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0171629622";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0171629622";
};

# المستخدم 97: 0160883591
:do {
    /tool user-manager user add customer="admin" username="0160883591" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0160883591";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0160883591";
};

# المستخدم 98: 0145747420
:do {
    /tool user-manager user add customer="admin" username="0145747420" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0145747420";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0145747420";
};

# المستخدم 99: 0107005715
:do {
    /tool user-manager user add customer="admin" username="0107005715" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0107005715";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0107005715";
};

# المستخدم 100: 0102884822
:do {
    /tool user-manager user add customer="admin" username="0102884822" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0102884822";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0102884822";
};

# المستخدم 101: 0148130830
:do {
    /tool user-manager user add customer="admin" username="0148130830" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0148130830";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0148130830";
};

# المستخدم 102: 0121653690
:do {
    /tool user-manager user add customer="admin" username="0121653690" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0121653690";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0121653690";
};

# المستخدم 103: 0141130303
:do {
    /tool user-manager user add customer="admin" username="0141130303" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0141130303";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0141130303";
};

# المستخدم 104: 0138401142
:do {
    /tool user-manager user add customer="admin" username="0138401142" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0138401142";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0138401142";
};

# المستخدم 105: 0169465839
:do {
    /tool user-manager user add customer="admin" username="0169465839" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0169465839";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0169465839";
};

# المستخدم 106: 0197504993
:do {
    /tool user-manager user add customer="admin" username="0197504993" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0197504993";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0197504993";
};

# المستخدم 107: 0193951818
:do {
    /tool user-manager user add customer="admin" username="0193951818" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0193951818";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0193951818";
};

# المستخدم 108: 0191365703
:do {
    /tool user-manager user add customer="admin" username="0191365703" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0191365703";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0191365703";
};

# المستخدم 109: 0164241784
:do {
    /tool user-manager user add customer="admin" username="0164241784" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0164241784";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0164241784";
};

# المستخدم 110: 0162650900
:do {
    /tool user-manager user add customer="admin" username="0162650900" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0162650900";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0162650900";
};

# المستخدم 111: 0183229916
:do {
    /tool user-manager user add customer="admin" username="0183229916" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0183229916";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0183229916";
};

# المستخدم 112: 0167605697
:do {
    /tool user-manager user add customer="admin" username="0167605697" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0167605697";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0167605697";
};

# المستخدم 113: 0107164733
:do {
    /tool user-manager user add customer="admin" username="0107164733" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0107164733";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0107164733";
};

# المستخدم 114: 0172858262
:do {
    /tool user-manager user add customer="admin" username="0172858262" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0172858262";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0172858262";
};

# المستخدم 115: 0128748308
:do {
    /tool user-manager user add customer="admin" username="0128748308" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0128748308";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0128748308";
};

# المستخدم 116: 0172686662
:do {
    /tool user-manager user add customer="admin" username="0172686662" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0172686662";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0172686662";
};

# المستخدم 117: 0140650157
:do {
    /tool user-manager user add customer="admin" username="0140650157" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0140650157";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0140650157";
};

# المستخدم 118: 0139121593
:do {
    /tool user-manager user add customer="admin" username="0139121593" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0139121593";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0139121593";
};

# المستخدم 119: 0175481768
:do {
    /tool user-manager user add customer="admin" username="0175481768" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0175481768";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0175481768";
};

# المستخدم 120: 0139468439
:do {
    /tool user-manager user add customer="admin" username="0139468439" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0139468439";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0139468439";
};

# المستخدم 121: 0164823215
:do {
    /tool user-manager user add customer="admin" username="0164823215" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0164823215";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0164823215";
};

# المستخدم 122: 0175754590
:do {
    /tool user-manager user add customer="admin" username="0175754590" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0175754590";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0175754590";
};

# المستخدم 123: 0182530601
:do {
    /tool user-manager user add customer="admin" username="0182530601" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0182530601";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0182530601";
};

# المستخدم 124: 0122963068
:do {
    /tool user-manager user add customer="admin" username="0122963068" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0122963068";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0122963068";
};

# المستخدم 125: 0192148259
:do {
    /tool user-manager user add customer="admin" username="0192148259" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0192148259";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0192148259";
};

# المستخدم 126: 0133925862
:do {
    /tool user-manager user add customer="admin" username="0133925862" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0133925862";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0133925862";
};

# المستخدم 127: 0100368213
:do {
    /tool user-manager user add customer="admin" username="0100368213" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0100368213";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0100368213";
};

# المستخدم 128: 0183674455
:do {
    /tool user-manager user add customer="admin" username="0183674455" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0183674455";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0183674455";
};

# المستخدم 129: 0102790446
:do {
    /tool user-manager user add customer="admin" username="0102790446" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0102790446";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0102790446";
};

# المستخدم 130: 0106517704
:do {
    /tool user-manager user add customer="admin" username="0106517704" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0106517704";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0106517704";
};

# المستخدم 131: 0106733452
:do {
    /tool user-manager user add customer="admin" username="0106733452" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0106733452";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0106733452";
};

# المستخدم 132: 0144661207
:do {
    /tool user-manager user add customer="admin" username="0144661207" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0144661207";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0144661207";
};

# المستخدم 133: 0185438314
:do {
    /tool user-manager user add customer="admin" username="0185438314" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0185438314";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0185438314";
};

# المستخدم 134: 0131231753
:do {
    /tool user-manager user add customer="admin" username="0131231753" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0131231753";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0131231753";
};

# المستخدم 135: 0189101169
:do {
    /tool user-manager user add customer="admin" username="0189101169" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0189101169";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0189101169";
};

# المستخدم 136: 0136034243
:do {
    /tool user-manager user add customer="admin" username="0136034243" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0136034243";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0136034243";
};

# المستخدم 137: 0118138755
:do {
    /tool user-manager user add customer="admin" username="0118138755" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0118138755";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0118138755";
};

# المستخدم 138: 0101219609
:do {
    /tool user-manager user add customer="admin" username="0101219609" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0101219609";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0101219609";
};

# المستخدم 139: 0164549909
:do {
    /tool user-manager user add customer="admin" username="0164549909" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0164549909";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0164549909";
};

# المستخدم 140: 0165455919
:do {
    /tool user-manager user add customer="admin" username="0165455919" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0165455919";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0165455919";
};

# المستخدم 141: 0194845306
:do {
    /tool user-manager user add customer="admin" username="0194845306" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0194845306";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0194845306";
};

# المستخدم 142: 0154291170
:do {
    /tool user-manager user add customer="admin" username="0154291170" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0154291170";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0154291170";
};

# المستخدم 143: 0163380828
:do {
    /tool user-manager user add customer="admin" username="0163380828" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0163380828";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0163380828";
};

# المستخدم 144: 0109754594
:do {
    /tool user-manager user add customer="admin" username="0109754594" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0109754594";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0109754594";
};

# المستخدم 145: 0122299287
:do {
    /tool user-manager user add customer="admin" username="0122299287" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0122299287";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0122299287";
};

# المستخدم 146: 0127791151
:do {
    /tool user-manager user add customer="admin" username="0127791151" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0127791151";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0127791151";
};

# المستخدم 147: 0166413939
:do {
    /tool user-manager user add customer="admin" username="0166413939" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0166413939";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0166413939";
};

# المستخدم 148: 0117685216
:do {
    /tool user-manager user add customer="admin" username="0117685216" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0117685216";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0117685216";
};

# المستخدم 149: 0146898479
:do {
    /tool user-manager user add customer="admin" username="0146898479" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0146898479";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0146898479";
};

# المستخدم 150: 0187859277
:do {
    /tool user-manager user add customer="admin" username="0187859277" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0187859277";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0187859277";
};

# المستخدم 151: 0169933836
:do {
    /tool user-manager user add customer="admin" username="0169933836" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0169933836";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0169933836";
};

# المستخدم 152: 0170121833
:do {
    /tool user-manager user add customer="admin" username="0170121833" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0170121833";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0170121833";
};

# المستخدم 153: 0187313270
:do {
    /tool user-manager user add customer="admin" username="0187313270" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0187313270";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0187313270";
};

# المستخدم 154: 0105323512
:do {
    /tool user-manager user add customer="admin" username="0105323512" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0105323512";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0105323512";
};

# المستخدم 155: 0162958805
:do {
    /tool user-manager user add customer="admin" username="0162958805" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0162958805";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0162958805";
};

# المستخدم 156: 0122218114
:do {
    /tool user-manager user add customer="admin" username="0122218114" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0122218114";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0122218114";
};

# المستخدم 157: 0132682931
:do {
    /tool user-manager user add customer="admin" username="0132682931" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0132682931";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0132682931";
};

# المستخدم 158: 0111846383
:do {
    /tool user-manager user add customer="admin" username="0111846383" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0111846383";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0111846383";
};

# المستخدم 159: 0196516352
:do {
    /tool user-manager user add customer="admin" username="0196516352" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0196516352";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0196516352";
};

# المستخدم 160: 0132799585
:do {
    /tool user-manager user add customer="admin" username="0132799585" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0132799585";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0132799585";
};

# المستخدم 161: 0180309345
:do {
    /tool user-manager user add customer="admin" username="0180309345" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0180309345";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0180309345";
};

# المستخدم 162: 0113358643
:do {
    /tool user-manager user add customer="admin" username="0113358643" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0113358643";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0113358643";
};

# المستخدم 163: 0181717865
:do {
    /tool user-manager user add customer="admin" username="0181717865" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0181717865";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0181717865";
};

# المستخدم 164: 0132772253
:do {
    /tool user-manager user add customer="admin" username="0132772253" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0132772253";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0132772253";
};

# المستخدم 165: 0140492659
:do {
    /tool user-manager user add customer="admin" username="0140492659" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0140492659";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0140492659";
};

# المستخدم 166: 0194506993
:do {
    /tool user-manager user add customer="admin" username="0194506993" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0194506993";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0194506993";
};

# المستخدم 167: 0198638114
:do {
    /tool user-manager user add customer="admin" username="0198638114" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0198638114";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0198638114";
};

# المستخدم 168: 0184383390
:do {
    /tool user-manager user add customer="admin" username="0184383390" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0184383390";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0184383390";
};

# المستخدم 169: 0170080626
:do {
    /tool user-manager user add customer="admin" username="0170080626" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0170080626";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0170080626";
};

# المستخدم 170: 0101325521
:do {
    /tool user-manager user add customer="admin" username="0101325521" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0101325521";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0101325521";
};

# المستخدم 171: 0194899617
:do {
    /tool user-manager user add customer="admin" username="0194899617" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0194899617";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0194899617";
};

# المستخدم 172: 0177943818
:do {
    /tool user-manager user add customer="admin" username="0177943818" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0177943818";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0177943818";
};

# المستخدم 173: 0122493020
:do {
    /tool user-manager user add customer="admin" username="0122493020" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0122493020";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0122493020";
};

# المستخدم 174: 0129150937
:do {
    /tool user-manager user add customer="admin" username="0129150937" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0129150937";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0129150937";
};

# المستخدم 175: 0171647078
:do {
    /tool user-manager user add customer="admin" username="0171647078" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0171647078";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0171647078";
};

# المستخدم 176: 0129595839
:do {
    /tool user-manager user add customer="admin" username="0129595839" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0129595839";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0129595839";
};

# المستخدم 177: 0150198755
:do {
    /tool user-manager user add customer="admin" username="0150198755" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0150198755";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0150198755";
};

# المستخدم 178: 0152826561
:do {
    /tool user-manager user add customer="admin" username="0152826561" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0152826561";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0152826561";
};

# المستخدم 179: 0148655591
:do {
    /tool user-manager user add customer="admin" username="0148655591" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0148655591";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0148655591";
};

# المستخدم 180: 0135683843
:do {
    /tool user-manager user add customer="admin" username="0135683843" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0135683843";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0135683843";
};

# المستخدم 181: 0194703082
:do {
    /tool user-manager user add customer="admin" username="0194703082" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0194703082";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0194703082";
};

# المستخدم 182: 0162947414
:do {
    /tool user-manager user add customer="admin" username="0162947414" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0162947414";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0162947414";
};

# المستخدم 183: 0144259245
:do {
    /tool user-manager user add customer="admin" username="0144259245" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0144259245";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0144259245";
};

# المستخدم 184: 0157619780
:do {
    /tool user-manager user add customer="admin" username="0157619780" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0157619780";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0157619780";
};

# المستخدم 185: 0109991573
:do {
    /tool user-manager user add customer="admin" username="0109991573" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0109991573";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0109991573";
};

# المستخدم 186: 0186009145
:do {
    /tool user-manager user add customer="admin" username="0186009145" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0186009145";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0186009145";
};

# المستخدم 187: 0115526898
:do {
    /tool user-manager user add customer="admin" username="0115526898" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0115526898";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0115526898";
};

# المستخدم 188: 0157296094
:do {
    /tool user-manager user add customer="admin" username="0157296094" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0157296094";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0157296094";
};

# المستخدم 189: 0164391018
:do {
    /tool user-manager user add customer="admin" username="0164391018" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0164391018";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0164391018";
};

# المستخدم 190: 0147947228
:do {
    /tool user-manager user add customer="admin" username="0147947228" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0147947228";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0147947228";
};

# المستخدم 191: 0159650787
:do {
    /tool user-manager user add customer="admin" username="0159650787" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0159650787";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0159650787";
};

# المستخدم 192: 0175515381
:do {
    /tool user-manager user add customer="admin" username="0175515381" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0175515381";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0175515381";
};

# المستخدم 193: 0135018221
:do {
    /tool user-manager user add customer="admin" username="0135018221" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0135018221";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0135018221";
};

# المستخدم 194: 0126155336
:do {
    /tool user-manager user add customer="admin" username="0126155336" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0126155336";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0126155336";
};

# المستخدم 195: 0170032947
:do {
    /tool user-manager user add customer="admin" username="0170032947" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0170032947";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0170032947";
};

# المستخدم 196: 0131102208
:do {
    /tool user-manager user add customer="admin" username="0131102208" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0131102208";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0131102208";
};

# المستخدم 197: 0125060136
:do {
    /tool user-manager user add customer="admin" username="0125060136" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0125060136";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0125060136";
};

# المستخدم 198: 0168557634
:do {
    /tool user-manager user add customer="admin" username="0168557634" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0168557634";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0168557634";
};

# المستخدم 199: 0128481710
:do {
    /tool user-manager user add customer="admin" username="0128481710" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0128481710";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0128481710";
};

# المستخدم 200: 0146565404
:do {
    /tool user-manager user add customer="admin" username="0146565404" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0146565404";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0146565404";
};


:put "📊 تم الانتهاء من النسخة الاحتياطية";
:put "✅ نجح: $success كرت";
:put "❌ فشل: $errors كرت";
:put "📈 الإجمالي: $total كرت";

:put "🎉 تم استعادة جميع كروت القالب: 10";
:put "💡 النسخة الاحتياطية مكتملة بنجاح!";
