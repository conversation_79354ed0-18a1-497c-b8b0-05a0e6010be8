2025-07-24 00:03:52,513 - INFO - تم بدء تشغيل التطبيق
2025-07-24 00:03:52,514 - INFO - تم إنشاء المجلدات الأساسية
2025-07-24 00:03:52,683 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-24 00:03:52,683 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-24 00:03:54,003 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-24 00:03:56,058 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-24 00:03:56,058 - INFO - تم <PERSON>عداد التطبيق بنجاح
2025-07-24 00:03:58,101 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-24 00:03:58,614 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-24 00:03:58,622 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-24 00:04:01,635 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-24 00:04:01,665 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-24 00:04:01,985 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-24 00:04:01,986 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-24 00:04:43,314 - INFO - معالجة callback: single_card
2025-07-24 00:04:43,557 - INFO - 🎴 بدء معالجة طلب إنشاء كروت
2025-07-24 00:04:43,558 - INFO - 🔢 عرض خيارات عدد الكروت
2025-07-24 00:04:43,843 - INFO - ✅ تم عرض خيارات عدد الكروت
2025-07-24 00:04:51,712 - INFO - معالجة callback: card_count_10
2025-07-24 00:04:51,923 - INFO - 📋 عرض قوالب Hotspot لإنشاء 10 كرت
2025-07-24 00:04:52,185 - INFO - ✅ تم إرسال قائمة قوالب لإنشاء 10 كرت - 5 قالب
2025-07-24 00:04:53,322 - INFO - معالجة callback: cards_template_10_10
2025-07-24 00:04:53,535 - INFO - 🎴 بدء إنشاء 10 كرت باستخدام القالب: 10
2025-07-24 00:04:53,807 - INFO - 🔄 تفعيل نظام Hotspot مع تحديث الواجهة...
2025-07-24 00:04:53,808 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-24 00:04:53,924 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-24 00:04:54,024 - WARNING - تحذير: فشل في حفظ الإعدادات: 'MikroTikCardGenerator' object has no attribute 'version_combo'
2025-07-24 00:04:54,025 - INFO - تم تعيين النظام الجديد: hotspot
2025-07-24 00:04:54,026 - INFO - 🔄 إعادة بناء الواجهة للنظام: hotspot
2025-07-24 00:04:54,026 - INFO - ✅ تم تبديل النظام من None إلى hotspot
2025-07-24 00:04:54,026 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-24 00:04:54,029 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-24 00:04:54,774 - INFO - تم إعداد تبويب عرض الكروت لـ Hotspot بنجاح
2025-07-24 00:04:54,975 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-24 00:04:54,976 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-24 00:04:55,158 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام hotspot
2025-07-24 00:04:55,158 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-24 00:04:55,160 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot
2025-07-24 00:04:55,508 - INFO - 📋 تطبيق القالب مع مزامنة شاملة: 10
2025-07-24 00:04:55,509 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-24 00:04:55,549 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-24 00:04:55,635 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-24 00:04:55,636 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-24 00:04:55,692 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-24 00:04:55,693 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-24 00:04:55,695 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-24 00:04:55,699 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-24 00:04:55,699 - INFO - ✅ تم تطبيق القالب '10' بنجاح
2025-07-24 00:04:55,701 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-24 00:04:55,705 - INFO - ✅ تم تحديث معاينة الكارت
2025-07-24 00:04:55,764 - INFO - ✅ تم تحديث حالة عناصر الواجهة
2025-07-24 00:04:55,767 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-24 00:04:55,767 - INFO - 🔢 تعيين عدد الكروت إلى 10...
2025-07-24 00:04:55,770 - INFO - ✅ تم تعيين عدد الكروت إلى 10
2025-07-24 00:04:55,772 - INFO - ✅ تم تحديث جميع المتغيرات الداخلية
2025-07-24 00:04:55,772 - INFO - 🏭 بدء توليد الكرت...
2025-07-24 00:04:55,826 - INFO - تم توليد 10 حساب
2025-07-24 00:04:55,831 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 320 (من generate_all)
2025-07-24 00:04:55,914 - INFO - 🚀 إرسال الكرت إلى MikroTik...
2025-07-24 00:04:55,915 - INFO - محاولة الاتصال بـ *********:8728 (عادي)
2025-07-24 00:04:55,983 - INFO - نجح الاتصال مع *********
2025-07-24 00:04:56,536 - INFO - ✅ تم إرسال المستخدم: 0169638030
2025-07-24 00:04:56,833 - INFO - ✅ تم إرسال المستخدم: 0187318847
2025-07-24 00:04:57,137 - INFO - ✅ تم إرسال المستخدم: 0193823921
2025-07-24 00:04:57,539 - INFO - ✅ تم إرسال المستخدم: 0176062250
2025-07-24 00:04:57,782 - INFO - ✅ تم إرسال المستخدم: 0188760322
2025-07-24 00:05:13,057 - ERROR - ❌ فشل في إرسال المستخدم 0147333849: timed out
2025-07-24 00:05:13,360 - ERROR - ❌ فشل في إرسال المستخدم 0146531211: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 00:05:13,598 - ERROR - ❌ فشل في إرسال المستخدم 0171032003: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 00:05:13,849 - ERROR - ❌ فشل في إرسال المستخدم 0121585621: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 00:05:14,311 - ERROR - ❌ فشل في إرسال المستخدم 0120809705: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 00:05:14,552 - INFO - 📊 بدء إرسال التقرير النهائي: card_type=single, success=5, failed=5
2025-07-24 00:05:14,814 - INFO - ✅ تم إرسال التقرير النهائي: 5 ناجح، 5 فاشل
2025-07-24 00:05:14,814 - INFO - 💾 حفظ معلومات الكروت الفاشلة: card_type=single, count=5
2025-07-24 00:05:14,814 - ERROR - خطأ في إرسال التقرير النهائي للكروت: name 'time' is not defined
2025-07-24 00:05:14,814 - INFO - 🔍 تشخيص حفظ الكروت للكرت الواحد: failed_count=5, success_count=5, system_type=hotspot
2025-07-24 00:05:14,815 - INFO - 💾 حفظ 5 كرت ناجح لعرض خيار حذف الكروت المرسلة بنجاح للكرت الواحد
2025-07-24 00:05:14,815 - INFO - ✅ تم حفظ معلومات الكروت الناجحة للكرت الواحد: 5 كرت
2025-07-24 00:05:14,815 - INFO - 💾 حفظ 5 كرت فاشل لعرض خيار إعادة المحاولة للكرت الواحد
2025-07-24 00:05:14,815 - INFO - ✅ تم حفظ معلومات الكروت الفاشلة للكرت الواحد: 5 كرت
2025-07-24 00:05:14,815 - INFO - 🔍 تشخيص حفظ الكروت الناجحة للكرت الواحد:
2025-07-24 00:05:14,816 - INFO -    - failed_count: 5
2025-07-24 00:05:14,816 - INFO -    - success_count: 5
2025-07-24 00:05:14,816 - INFO -    - system_type: hotspot
2025-07-24 00:05:14,816 - INFO -    - successful_cards count: 5
2025-07-24 00:05:14,816 - INFO - 💾 حفظ 5 كرت ناجح لعرض خيار حذف الكروت المرسلة بنجاح للكرت الواحد
2025-07-24 00:05:14,816 - INFO - 📝 قائمة الكروت الناجحة المحفوظة: ['0169638030', '0187318847', '0193823921', '0176062250', '0188760322']
2025-07-24 00:05:14,817 - INFO - ✅ تم حفظ معلومات الكروت الناجحة للكرت الواحد: 5 كرت
2025-07-24 00:05:14,817 - INFO - ✅ كرت واحد: تم إرسال 5/10 مستخدم بنجاح
2025-07-24 00:05:15,154 - INFO - ✅ تم إرسال تفاصيل 10 كرت عبر التلجرام بنجاح
2025-07-24 03:16:22,967 - INFO - تم بدء تشغيل التطبيق
2025-07-24 03:16:22,968 - INFO - تم إنشاء المجلدات الأساسية
2025-07-24 03:16:23,105 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-24 03:16:23,105 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-24 03:16:23,327 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-24 03:16:26,628 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-24 03:16:26,628 - INFO - تم إعداد التطبيق بنجاح
2025-07-24 03:16:28,676 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-24 03:16:28,900 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-24 03:16:28,908 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-24 03:16:31,913 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-24 03:16:31,977 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-24 03:16:32,221 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-24 03:16:32,221 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-24 03:16:42,473 - INFO - معالجة callback: single_card
2025-07-24 03:16:42,683 - INFO - 🎴 بدء معالجة طلب إنشاء كروت
2025-07-24 03:16:42,684 - INFO - 🔢 عرض خيارات عدد الكروت
2025-07-24 03:16:42,944 - INFO - ✅ تم عرض خيارات عدد الكروت
2025-07-24 03:16:44,628 - INFO - معالجة callback: card_count_10
2025-07-24 03:16:44,844 - INFO - 📋 عرض قوالب Hotspot لإنشاء 10 كرت
2025-07-24 03:16:45,096 - INFO - ✅ تم إرسال قائمة قوالب لإنشاء 10 كرت - 5 قالب
2025-07-24 03:16:46,135 - INFO - معالجة callback: cards_template_10_10
2025-07-24 03:16:46,400 - INFO - 🎴 بدء إنشاء 10 كرت باستخدام القالب: 10
2025-07-24 03:16:46,678 - INFO - 🔄 تفعيل نظام Hotspot مع تحديث الواجهة...
2025-07-24 03:16:46,679 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-24 03:16:46,777 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-24 03:16:46,877 - WARNING - تحذير: فشل في حفظ الإعدادات: 'MikroTikCardGenerator' object has no attribute 'version_combo'
2025-07-24 03:16:46,878 - INFO - تم تعيين النظام الجديد: hotspot
2025-07-24 03:16:46,878 - INFO - ✅ تم تبديل النظام من None إلى hotspot
2025-07-24 03:16:46,878 - INFO - 🔄 إعادة بناء الواجهة للنظام: hotspot
2025-07-24 03:16:46,879 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-24 03:16:46,882 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-24 03:16:47,372 - INFO - تم إعداد تبويب عرض الكروت لـ Hotspot بنجاح
2025-07-24 03:16:47,518 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-24 03:16:47,518 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-24 03:16:47,690 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام hotspot
2025-07-24 03:16:47,690 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-24 03:16:47,693 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot
2025-07-24 03:16:48,101 - INFO - 📋 تطبيق القالب مع مزامنة شاملة: 10
2025-07-24 03:16:48,103 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-24 03:16:48,103 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-24 03:16:48,135 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-24 03:16:48,137 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-24 03:16:48,172 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-24 03:16:48,173 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-24 03:16:48,178 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-24 03:16:48,184 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-24 03:16:48,186 - INFO - ✅ تم تطبيق القالب '10' بنجاح
2025-07-24 03:16:48,188 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-24 03:16:48,192 - INFO - ✅ تم تحديث معاينة الكارت
2025-07-24 03:16:48,206 - INFO - ✅ تم تحديث حالة عناصر الواجهة
2025-07-24 03:16:48,224 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-24 03:16:48,226 - INFO - 🔢 تعيين عدد الكروت إلى 10...
2025-07-24 03:16:48,227 - INFO - ✅ تم تعيين عدد الكروت إلى 10
2025-07-24 03:16:48,228 - INFO - ✅ تم تحديث جميع المتغيرات الداخلية
2025-07-24 03:16:48,228 - INFO - 🏭 بدء توليد الكرت...
2025-07-24 03:16:48,236 - INFO - تم توليد 10 حساب
2025-07-24 03:16:48,243 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 330 (من generate_all)
2025-07-24 03:16:48,260 - INFO - 🚀 إرسال الكرت إلى MikroTik...
2025-07-24 03:16:48,261 - INFO - محاولة الاتصال بـ *********:8728 (عادي)
2025-07-24 03:16:48,330 - INFO - نجح الاتصال مع *********
2025-07-24 03:16:48,824 - INFO - ✅ تم إرسال المستخدم: 0142898127
2025-07-24 03:16:49,090 - INFO - ✅ تم إرسال المستخدم: 0100342077
2025-07-24 03:16:49,372 - INFO - ✅ تم إرسال المستخدم: 0104248187
2025-07-24 03:16:49,640 - INFO - ✅ تم إرسال المستخدم: 0182981583
2025-07-24 03:16:49,976 - INFO - ✅ تم إرسال المستخدم: 0197089674
2025-07-24 03:16:50,206 - INFO - ✅ تم إرسال المستخدم: 0115171356
2025-07-24 03:16:50,898 - INFO - ✅ تم إرسال المستخدم: 0130870104
2025-07-24 03:17:06,252 - ERROR - ❌ فشل في إرسال المستخدم 0138927070: timed out
2025-07-24 03:17:06,498 - ERROR - ❌ فشل في إرسال المستخدم 0130720623: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 03:17:06,725 - ERROR - ❌ فشل في إرسال المستخدم 0129835979: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 03:17:06,956 - INFO - 📊 بدء إرسال التقرير النهائي: card_type=single, success=7, failed=3
2025-07-24 03:17:07,188 - INFO - ✅ تم إرسال التقرير النهائي: 7 ناجح، 3 فاشل
2025-07-24 03:17:07,189 - INFO - 💾 حفظ معلومات الكروت الفاشلة: card_type=single, count=3
2025-07-24 03:17:07,189 - ERROR - خطأ في إرسال التقرير النهائي للكروت: name 'time' is not defined
2025-07-24 03:17:07,190 - INFO - 🔍 تشخيص حفظ الكروت للكرت الواحد: failed_count=3, success_count=7, system_type=hotspot
2025-07-24 03:17:07,190 - INFO - 💾 حفظ 7 كرت ناجح لعرض خيار حذف الكروت المرسلة بنجاح للكرت الواحد
2025-07-24 03:17:07,190 - INFO - ✅ تم حفظ معلومات الكروت الناجحة للكرت الواحد: 7 كرت
2025-07-24 03:17:07,190 - INFO - 💾 حفظ 3 كرت فاشل لعرض خيار إعادة المحاولة للكرت الواحد
2025-07-24 03:17:07,191 - INFO - ✅ تم حفظ معلومات الكروت الفاشلة للكرت الواحد: 3 كرت
2025-07-24 03:17:07,191 - INFO - 🔍 تشخيص حفظ الكروت الناجحة للكرت الواحد:
2025-07-24 03:17:07,191 - INFO -    - failed_count: 3
2025-07-24 03:17:07,192 - INFO -    - success_count: 7
2025-07-24 03:17:07,192 - INFO -    - system_type: hotspot
2025-07-24 03:17:07,193 - INFO -    - successful_cards count: 7
2025-07-24 03:17:07,194 - INFO - 💾 حفظ 7 كرت ناجح لعرض خيار حذف الكروت المرسلة بنجاح للكرت الواحد
2025-07-24 03:17:07,195 - INFO - 📝 قائمة الكروت الناجحة المحفوظة: ['0142898127', '0100342077', '0104248187', '0182981583', '0197089674', '0115171356', '0130870104']
2025-07-24 03:17:07,197 - INFO - ✅ تم حفظ معلومات الكروت الناجحة للكرت الواحد: 7 كرت
2025-07-24 03:17:07,198 - INFO - ✅ كرت واحد: تم إرسال 7/10 مستخدم بنجاح
2025-07-24 03:17:07,489 - INFO - ✅ تم إرسال تفاصيل 10 كرت عبر التلجرام بنجاح
2025-07-24 03:38:15,537 - INFO - تم بدء تشغيل التطبيق
2025-07-24 03:38:15,638 - INFO - تم إنشاء المجلدات الأساسية
2025-07-24 03:38:16,239 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-24 03:38:16,239 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-24 03:38:17,452 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-24 03:38:23,000 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-24 03:38:23,000 - INFO - تم إعداد التطبيق بنجاح
2025-07-24 03:38:25,046 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-24 03:38:25,535 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-24 03:38:25,535 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-24 03:38:28,549 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-24 03:38:28,645 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-24 03:38:28,907 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-24 03:38:28,908 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-24 03:38:52,564 - INFO - معالجة callback: single_card
2025-07-24 03:38:52,771 - INFO - 🎴 بدء معالجة طلب إنشاء كروت
2025-07-24 03:38:52,772 - INFO - 🔢 عرض خيارات عدد الكروت
2025-07-24 03:38:53,016 - INFO - ✅ تم عرض خيارات عدد الكروت
2025-07-24 03:38:54,990 - INFO - معالجة callback: card_count_10
2025-07-24 03:38:55,219 - INFO - 📋 عرض قوالب Hotspot لإنشاء 10 كرت
2025-07-24 03:38:55,491 - INFO - ✅ تم إرسال قائمة قوالب لإنشاء 10 كرت - 5 قالب
2025-07-24 03:38:56,744 - INFO - معالجة callback: cards_template_10_10
2025-07-24 03:38:56,955 - INFO - 🎴 بدء إنشاء 10 كرت باستخدام القالب: 10
2025-07-24 03:38:57,197 - INFO - 🔄 تفعيل نظام Hotspot مع تحديث الواجهة...
2025-07-24 03:38:57,198 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-24 03:38:57,315 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-24 03:38:57,416 - WARNING - تحذير: فشل في حفظ الإعدادات: 'MikroTikCardGenerator' object has no attribute 'version_combo'
2025-07-24 03:38:57,416 - INFO - تم تعيين النظام الجديد: hotspot
2025-07-24 03:38:57,417 - INFO - ✅ تم تبديل النظام من None إلى hotspot
2025-07-24 03:38:57,417 - INFO - 🔄 إعادة بناء الواجهة للنظام: hotspot
2025-07-24 03:38:57,417 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-24 03:38:57,420 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-24 03:38:58,174 - INFO - تم إعداد تبويب عرض الكروت لـ Hotspot بنجاح
2025-07-24 03:38:58,334 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-24 03:38:58,335 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-24 03:38:58,547 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام hotspot
2025-07-24 03:38:58,547 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-24 03:38:58,548 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot
2025-07-24 03:38:59,046 - INFO - 📋 تطبيق القالب مع مزامنة شاملة: 10
2025-07-24 03:38:59,048 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-24 03:38:59,048 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-24 03:38:59,115 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-24 03:38:59,117 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-24 03:38:59,155 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-24 03:38:59,156 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-24 03:38:59,159 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-24 03:38:59,164 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-24 03:38:59,164 - INFO - ✅ تم تطبيق القالب '10' بنجاح
2025-07-24 03:38:59,165 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-24 03:38:59,169 - INFO - ✅ تم تحديث معاينة الكارت
2025-07-24 03:38:59,195 - INFO - ✅ تم تحديث حالة عناصر الواجهة
2025-07-24 03:38:59,196 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-24 03:38:59,196 - INFO - 🔢 تعيين عدد الكروت إلى 10...
2025-07-24 03:38:59,198 - INFO - ✅ تم تعيين عدد الكروت إلى 10
2025-07-24 03:38:59,199 - INFO - ✅ تم تحديث جميع المتغيرات الداخلية
2025-07-24 03:38:59,199 - INFO - 🏭 بدء توليد الكرت...
2025-07-24 03:38:59,210 - INFO - تم توليد 10 حساب
2025-07-24 03:38:59,226 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 340 (من generate_all)
2025-07-24 03:38:59,256 - INFO - 🚀 إرسال الكرت إلى MikroTik...
2025-07-24 03:38:59,257 - INFO - محاولة الاتصال بـ *********:8728 (عادي)
2025-07-24 03:38:59,323 - INFO - نجح الاتصال مع *********
2025-07-24 03:38:59,951 - INFO - ✅ تم إرسال المستخدم: 0171230677
2025-07-24 03:39:00,254 - INFO - ✅ تم إرسال المستخدم: 0117844053
2025-07-24 03:39:00,526 - INFO - ✅ تم إرسال المستخدم: 0197513163
2025-07-24 03:39:00,794 - INFO - ✅ تم إرسال المستخدم: 0137237708
2025-07-24 03:39:01,055 - INFO - ✅ تم إرسال المستخدم: 0123671902
2025-07-24 03:39:01,314 - INFO - ✅ تم إرسال المستخدم: 0100892985
2025-07-24 03:39:01,575 - INFO - ✅ تم إرسال المستخدم: 0133006472
2025-07-24 03:39:01,854 - INFO - ✅ تم إرسال المستخدم: 0178450772
2025-07-24 03:39:17,080 - ERROR - ❌ فشل في إرسال المستخدم 0147367800: timed out
2025-07-24 03:39:17,347 - ERROR - ❌ فشل في إرسال المستخدم 0132097261: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 03:39:17,592 - INFO - 📊 بدء إرسال التقرير النهائي: card_type=single, success=8, failed=2
2025-07-24 03:39:17,828 - INFO - ✅ تم إرسال التقرير النهائي: 8 ناجح، 2 فاشل
2025-07-24 03:39:17,829 - INFO - 💾 حفظ معلومات الكروت الفاشلة: card_type=single, count=2
2025-07-24 03:39:17,829 - ERROR - خطأ في إرسال التقرير النهائي للكروت: name 'time' is not defined
2025-07-24 03:39:17,829 - INFO - 🔍 تشخيص حفظ الكروت للكرت الواحد: failed_count=2, success_count=8, system_type=hotspot
2025-07-24 03:39:17,830 - INFO - 💾 حفظ 8 كرت ناجح لعرض خيار حذف الكروت المرسلة بنجاح للكرت الواحد
2025-07-24 03:39:17,830 - INFO - ✅ تم حفظ معلومات الكروت الناجحة للكرت الواحد: 8 كرت
2025-07-24 03:39:17,830 - INFO - 💾 حفظ 2 كرت فاشل لعرض خيار إعادة المحاولة للكرت الواحد
2025-07-24 03:39:17,831 - INFO - ✅ تم حفظ معلومات الكروت الفاشلة للكرت الواحد: 2 كرت
2025-07-24 03:39:17,831 - INFO - 🔍 تشخيص حفظ الكروت الناجحة للكرت الواحد:
2025-07-24 03:39:17,831 - INFO -    - failed_count: 2
2025-07-24 03:39:17,831 - INFO -    - success_count: 8
2025-07-24 03:39:17,831 - INFO -    - system_type: hotspot
2025-07-24 03:39:17,832 - INFO -    - successful_cards count: 8
2025-07-24 03:39:17,832 - INFO - 💾 حفظ 8 كرت ناجح لعرض خيار حذف الكروت المرسلة بنجاح للكرت الواحد
2025-07-24 03:39:17,832 - INFO - 📝 قائمة الكروت الناجحة المحفوظة: ['0171230677', '0117844053', '0197513163', '0137237708', '0123671902', '0100892985', '0133006472', '0178450772']
2025-07-24 03:39:17,833 - INFO - ✅ تم حفظ معلومات الكروت الناجحة للكرت الواحد: 8 كرت
2025-07-24 03:39:17,833 - INFO - ✅ كرت واحد: تم إرسال 8/10 مستخدم بنجاح
2025-07-24 03:39:18,123 - INFO - ✅ تم إرسال تفاصيل 10 كرت عبر التلجرام بنجاح
2025-07-24 03:46:53,551 - INFO - تم بدء تشغيل التطبيق
2025-07-24 03:46:53,581 - INFO - تم إنشاء المجلدات الأساسية
2025-07-24 03:46:53,689 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-24 03:46:53,689 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-24 03:46:54,461 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-24 03:46:59,786 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-24 03:46:59,786 - INFO - تم إعداد التطبيق بنجاح
2025-07-24 03:47:02,056 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-24 03:47:03,202 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-24 03:47:03,211 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-24 03:47:06,220 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-24 03:47:06,245 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-24 03:47:06,516 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-24 03:47:06,517 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-24 03:47:17,801 - INFO - معالجة callback: single_card
2025-07-24 03:47:18,011 - INFO - 🎴 بدء معالجة طلب إنشاء كروت
2025-07-24 03:47:18,012 - INFO - 🔢 عرض خيارات عدد الكروت
2025-07-24 03:47:18,253 - INFO - ✅ تم عرض خيارات عدد الكروت
2025-07-24 03:47:22,883 - INFO - معالجة callback: card_count_10
2025-07-24 03:47:23,095 - INFO - 📋 عرض قوالب Hotspot لإنشاء 10 كرت
2025-07-24 03:47:23,338 - INFO - ✅ تم إرسال قائمة قوالب لإنشاء 10 كرت - 5 قالب
2025-07-24 03:47:24,268 - INFO - معالجة callback: cards_template_10_10
2025-07-24 03:47:24,502 - INFO - 🎴 بدء إنشاء 10 كرت باستخدام القالب: 10
2025-07-24 03:47:24,807 - INFO - 🔄 تفعيل نظام Hotspot مع تحديث الواجهة...
2025-07-24 03:47:24,808 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-24 03:47:25,000 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-24 03:47:25,100 - WARNING - تحذير: فشل في حفظ الإعدادات: 'MikroTikCardGenerator' object has no attribute 'version_combo'
2025-07-24 03:47:25,101 - INFO - تم تعيين النظام الجديد: hotspot
2025-07-24 03:47:25,101 - INFO - ✅ تم تبديل النظام من None إلى hotspot
2025-07-24 03:47:25,101 - INFO - 🔄 إعادة بناء الواجهة للنظام: hotspot
2025-07-24 03:47:25,102 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-24 03:47:25,104 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-24 03:47:25,883 - INFO - تم إعداد تبويب عرض الكروت لـ Hotspot بنجاح
2025-07-24 03:47:26,047 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-24 03:47:26,048 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-24 03:47:26,246 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام hotspot
2025-07-24 03:47:26,247 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-24 03:47:26,250 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot
2025-07-24 03:47:26,689 - INFO - 📋 تطبيق القالب مع مزامنة شاملة: 10
2025-07-24 03:47:26,690 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-24 03:47:26,692 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-24 03:47:26,807 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-24 03:47:26,808 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-24 03:47:26,835 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-24 03:47:26,846 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-24 03:47:26,885 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-24 03:47:26,890 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-24 03:47:26,890 - INFO - ✅ تم تطبيق القالب '10' بنجاح
2025-07-24 03:47:26,892 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-24 03:47:26,904 - INFO - ✅ تم تحديث معاينة الكارت
2025-07-24 03:47:26,923 - INFO - ✅ تم تحديث حالة عناصر الواجهة
2025-07-24 03:47:26,930 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-24 03:47:26,942 - INFO - 🔢 تعيين عدد الكروت إلى 10...
2025-07-24 03:47:26,944 - INFO - ✅ تم تعيين عدد الكروت إلى 10
2025-07-24 03:47:26,944 - INFO - ✅ تم تحديث جميع المتغيرات الداخلية
2025-07-24 03:47:26,944 - INFO - 🏭 بدء توليد الكرت...
2025-07-24 03:47:26,977 - INFO - تم توليد 10 حساب
2025-07-24 03:47:26,978 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 350 (من generate_all)
2025-07-24 03:47:27,002 - INFO - 🚀 إرسال الكرت إلى MikroTik...
2025-07-24 03:47:27,002 - INFO - 🧹 تم مسح بيانات العمليات السابقة
2025-07-24 03:47:27,003 - INFO - محاولة الاتصال بـ *********:8728 (عادي)
2025-07-24 03:47:27,072 - INFO - نجح الاتصال مع *********
2025-07-24 03:47:27,584 - INFO - ✅ تم إرسال المستخدم: 0155645191
2025-07-24 03:47:27,862 - INFO - ✅ تم إرسال المستخدم: 0179072834
2025-07-24 03:47:28,133 - INFO - ✅ تم إرسال المستخدم: 0162161887
2025-07-24 03:47:28,412 - INFO - ✅ تم إرسال المستخدم: 0113469144
2025-07-24 03:47:28,713 - INFO - ✅ تم إرسال المستخدم: 0112083555
2025-07-24 03:47:28,992 - INFO - ✅ تم إرسال المستخدم: 0129505789
2025-07-24 03:47:29,263 - INFO - ✅ تم إرسال المستخدم: 0127524659
2025-07-24 03:47:44,510 - ERROR - ❌ فشل في إرسال المستخدم 0186526919: timed out
2025-07-24 03:47:44,829 - ERROR - ❌ فشل في إرسال المستخدم 0116452536: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 03:47:45,068 - ERROR - ❌ فشل في إرسال المستخدم 0116490326: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 03:47:45,335 - INFO - 📊 بدء إرسال التقرير النهائي: card_type=single, success=7, failed=3
2025-07-24 03:47:45,593 - INFO - ✅ تم إرسال التقرير النهائي: 7 ناجح، 3 فاشل
2025-07-24 03:47:45,594 - INFO - 💾 حفظ معلومات الكروت الفاشلة: card_type=single, count=3
2025-07-24 03:47:45,594 - ERROR - خطأ في إرسال التقرير النهائي للكروت: name 'time' is not defined
2025-07-24 03:47:45,594 - INFO - 🔍 تشخيص حفظ الكروت للكرت الواحد: failed_count=3, success_count=7, system_type=hotspot
2025-07-24 03:47:45,595 - INFO - 💾 حفظ 7 كرت ناجح لعرض خيار حذف الكروت المرسلة بنجاح للكرت الواحد
2025-07-24 03:47:45,595 - INFO - ✅ تم حفظ معلومات الكروت الناجحة للكرت الواحد: 7 كرت
2025-07-24 03:47:45,595 - INFO - 💾 حفظ 3 كرت فاشل لعرض خيار إعادة المحاولة للكرت الواحد
2025-07-24 03:47:45,596 - INFO - ✅ تم حفظ معلومات الكروت الفاشلة للكرت الواحد: 3 كرت
2025-07-24 03:47:45,596 - INFO - 🔍 تشخيص حفظ الكروت الناجحة للكرت الواحد:
2025-07-24 03:47:45,596 - INFO -    - failed_count: 3
2025-07-24 03:47:45,597 - INFO -    - success_count: 7
2025-07-24 03:47:45,597 - INFO -    - system_type: hotspot
2025-07-24 03:47:45,597 - INFO -    - successful_cards count: 7
2025-07-24 03:47:45,597 - INFO - 💾 حفظ 7 كرت ناجح لعرض خيار حذف الكروت المرسلة بنجاح للكرت الواحد
2025-07-24 03:47:45,598 - INFO - 📝 قائمة الكروت الناجحة المحفوظة: ['0155645191', '0179072834', '0162161887', '0113469144', '0112083555', '0129505789', '0127524659']
2025-07-24 03:47:45,598 - INFO - ✅ تم حفظ معلومات الكروت الناجحة للكرت الواحد: 7 كرت
2025-07-24 03:47:45,598 - INFO - ✅ كرت واحد: تم إرسال 7/10 مستخدم بنجاح
2025-07-24 03:47:45,862 - INFO - ✅ تم إرسال تفاصيل 10 كرت عبر التلجرام بنجاح
2025-07-24 03:48:04,843 - INFO - معالجة callback: retry_failed_cards_single_3
2025-07-24 03:48:05,194 - INFO - 🔄 بدء معالجة إعادة المحاولة للكروت الفاشلة: retry_failed_cards_single_3
2025-07-24 03:48:05,195 - INFO - 🔍 تشخيص إعادة المحاولة: card_type=single, failed_count=3
2025-07-24 03:48:05,195 - INFO - 🔍 تشخيص failed_cards_info: hasattr=True, exists=True
2025-07-24 03:48:05,196 - INFO - 🔍 محتوى failed_cards_info: ['card_type', 'failed_cards', 'template_name', 'timestamp', 'system_type', 'operation_type']
2025-07-24 03:48:05,196 - INFO - 🔍 card_type في failed_cards_info: single
2025-07-24 03:48:05,196 - INFO - 🔍 عدد failed_cards: 3
2025-07-24 03:48:09,659 - INFO - معالجة callback: confirm_retry_single_3
2025-07-24 03:48:09,875 - INFO - 🔄 بدء تنفيذ إعادة المحاولة للكروت الفاشلة: confirm_retry_single_3
2025-07-24 03:48:10,118 - INFO - 🔄 بدء إعادة محاولة الكرت الواحد للكروت الفاشلة: 3 كرت
2025-07-24 03:48:10,119 - INFO - 🧹 تم مسح بيانات العمليات السابقة
2025-07-24 03:48:10,119 - WARNING - الاتصال الحالي غير صالح: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 03:48:10,119 - INFO - تم قطع الاتصال مع MikroTik
2025-07-24 03:48:10,122 - INFO - محاولة الاتصال بـ *********:8728 (عادي)
2025-07-24 03:48:10,181 - INFO - نجح الاتصال مع *********
2025-07-24 03:48:10,703 - INFO - ✅ تم إرسال المستخدم: 0186526919
2025-07-24 03:48:10,974 - INFO - ✅ تم إرسال المستخدم: 0116452536
2025-07-24 03:48:11,245 - INFO - ✅ تم إرسال المستخدم: 0116490326
2025-07-24 03:48:11,512 - INFO - 📊 بدء إرسال التقرير النهائي: card_type=single, success=3, failed=0
2025-07-24 03:48:11,774 - INFO - ✅ تم إرسال التقرير النهائي: 3 ناجح، 0 فاشل
2025-07-24 03:48:11,774 - INFO - ℹ️ لا توجد كروت فاشلة لحفظها: failed_cards=0
2025-07-24 03:48:11,774 - INFO - 🔍 تشخيص حفظ الكروت للكرت الواحد: failed_count=0, success_count=3, system_type=hotspot
2025-07-24 03:48:11,775 - INFO - ❌ لم يتم حفظ الكروت الناجحة للكرت الواحد - الشروط غير مستوفاة
2025-07-24 03:48:11,775 - INFO - ❌ لم يتم حفظ الكروت الفاشلة للكرت الواحد - الشروط غير مستوفاة
2025-07-24 03:48:11,775 - INFO - 🔍 تشخيص حفظ الكروت الناجحة للكرت الواحد:
2025-07-24 03:48:11,776 - INFO -    - failed_count: 0
2025-07-24 03:48:11,776 - INFO -    - success_count: 3
2025-07-24 03:48:11,776 - INFO -    - system_type: hotspot
2025-07-24 03:48:11,776 - INFO -    - successful_cards count: 3
2025-07-24 03:48:11,777 - INFO - ❌ لم يتم حفظ الكروت الناجحة للكرت الواحد - الشروط غير مستوفاة
2025-07-24 03:48:11,777 - INFO - ✅ كرت واحد: تم إرسال 3/3 مستخدم بنجاح
2025-07-24 03:48:11,777 - INFO - ✅ نجحت إعادة المحاولة للكرت الواحد: 3 كرت
2025-07-24 03:49:22,214 - INFO - تم بدء تشغيل التطبيق
2025-07-24 03:49:22,215 - INFO - تم إنشاء المجلدات الأساسية
2025-07-24 03:49:22,218 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-24 03:49:22,218 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-24 03:49:22,321 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-24 03:49:29,780 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-24 03:49:29,780 - INFO - تم إعداد التطبيق بنجاح
2025-07-24 03:49:31,814 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-24 03:49:32,097 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-24 03:49:32,098 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-24 03:49:35,103 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-24 03:49:35,154 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-24 03:49:35,395 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-24 03:49:35,395 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-24 03:49:53,322 - INFO - معالجة callback: single_card
2025-07-24 03:49:53,556 - INFO - 🎴 بدء معالجة طلب إنشاء كروت
2025-07-24 03:49:53,557 - INFO - 🔢 عرض خيارات عدد الكروت
2025-07-24 03:49:53,793 - INFO - ✅ تم عرض خيارات عدد الكروت
2025-07-24 03:49:55,436 - INFO - معالجة callback: card_count_10
2025-07-24 03:49:55,649 - INFO - 📋 عرض قوالب Hotspot لإنشاء 10 كرت
2025-07-24 03:49:55,886 - INFO - ✅ تم إرسال قائمة قوالب لإنشاء 10 كرت - 5 قالب
2025-07-24 03:50:15,201 - INFO - معالجة callback: cards_template_10_10
2025-07-24 03:50:15,438 - INFO - 🎴 بدء إنشاء 10 كرت باستخدام القالب: 10
2025-07-24 03:50:15,679 - INFO - 🔄 تفعيل نظام Hotspot مع تحديث الواجهة...
2025-07-24 03:50:15,680 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-24 03:50:15,767 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-24 03:50:15,868 - WARNING - تحذير: فشل في حفظ الإعدادات: 'MikroTikCardGenerator' object has no attribute 'version_combo'
2025-07-24 03:50:15,868 - INFO - تم تعيين النظام الجديد: hotspot
2025-07-24 03:50:15,869 - INFO - ✅ تم تبديل النظام من None إلى hotspot
2025-07-24 03:50:15,869 - INFO - 🔄 إعادة بناء الواجهة للنظام: hotspot
2025-07-24 03:50:15,869 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-24 03:50:15,872 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-24 03:50:16,440 - INFO - تم إعداد تبويب عرض الكروت لـ Hotspot بنجاح
2025-07-24 03:50:16,599 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-24 03:50:16,600 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-24 03:50:16,788 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام hotspot
2025-07-24 03:50:16,789 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-24 03:50:16,793 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot
2025-07-24 03:50:17,389 - INFO - 📋 تطبيق القالب مع مزامنة شاملة: 10
2025-07-24 03:50:17,390 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-24 03:50:17,391 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-24 03:50:17,475 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-24 03:50:17,476 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-24 03:50:17,506 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-24 03:50:17,507 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-24 03:50:17,510 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-24 03:50:17,515 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-24 03:50:17,515 - INFO - ✅ تم تطبيق القالب '10' بنجاح
2025-07-24 03:50:17,517 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-24 03:50:17,541 - INFO - ✅ تم تحديث معاينة الكارت
2025-07-24 03:50:17,564 - INFO - ✅ تم تحديث حالة عناصر الواجهة
2025-07-24 03:50:17,569 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-24 03:50:17,569 - INFO - 🔢 تعيين عدد الكروت إلى 10...
2025-07-24 03:50:17,571 - INFO - ✅ تم تعيين عدد الكروت إلى 10
2025-07-24 03:50:17,572 - INFO - ✅ تم تحديث جميع المتغيرات الداخلية
2025-07-24 03:50:17,572 - INFO - 🏭 بدء توليد الكرت...
2025-07-24 03:50:17,580 - INFO - تم توليد 10 حساب
2025-07-24 03:50:17,586 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 360 (من generate_all)
2025-07-24 03:50:17,604 - INFO - 🚀 إرسال الكرت إلى MikroTik...
2025-07-24 03:50:17,605 - INFO - محاولة الاتصال بـ *********:8728 (عادي)
2025-07-24 03:50:17,667 - INFO - نجح الاتصال مع *********
2025-07-24 03:50:18,196 - INFO - ✅ تم إرسال المستخدم: 0103499294
2025-07-24 03:50:18,495 - INFO - ✅ تم إرسال المستخدم: 0188265364
2025-07-24 03:50:18,827 - INFO - ✅ تم إرسال المستخدم: 0163931352
2025-07-24 03:50:19,086 - INFO - ✅ تم إرسال المستخدم: 0132879356
2025-07-24 03:50:19,377 - INFO - ✅ تم إرسال المستخدم: 0152680178
2025-07-24 03:50:19,646 - INFO - ✅ تم إرسال المستخدم: 0101188863
2025-07-24 03:50:19,937 - INFO - ✅ تم إرسال المستخدم: 0184880978
2025-07-24 03:50:20,206 - INFO - ✅ تم إرسال المستخدم: 0198978174
2025-07-24 03:50:20,653 - INFO - ✅ تم إرسال المستخدم: 0168282540
2025-07-24 03:50:35,900 - ERROR - ❌ فشل في إرسال المستخدم 0195066705: timed out
2025-07-24 03:50:36,131 - INFO - 📊 بدء إرسال التقرير النهائي: card_type=single, success=9, failed=1
2025-07-24 03:50:36,366 - INFO - ✅ تم إرسال التقرير النهائي: 9 ناجح، 1 فاشل
2025-07-24 03:50:36,366 - INFO - 💾 حفظ معلومات الكروت الفاشلة: card_type=single, count=1
2025-07-24 03:50:36,367 - ERROR - خطأ في إرسال التقرير النهائي للكروت: name 'time' is not defined
2025-07-24 03:50:36,367 - INFO - 🔍 تشخيص حفظ الكروت للكرت الواحد: failed_count=1, success_count=9, system_type=hotspot
2025-07-24 03:50:36,367 - INFO - 💾 حفظ 9 كرت ناجح لعرض خيار حذف الكروت المرسلة بنجاح للكرت الواحد
2025-07-24 03:50:36,367 - INFO - ✅ تم حفظ معلومات الكروت الناجحة للكرت الواحد: 9 كرت
2025-07-24 03:50:36,367 - INFO - 💾 حفظ 1 كرت فاشل لعرض خيار إعادة المحاولة للكرت الواحد
2025-07-24 03:50:36,368 - INFO - ✅ تم حفظ معلومات الكروت الفاشلة للكرت الواحد: 1 كرت
2025-07-24 03:50:36,368 - INFO - ✅ كرت واحد: تم إرسال 9/10 مستخدم بنجاح
2025-07-24 03:50:36,633 - INFO - ✅ تم إرسال تفاصيل 10 كرت عبر التلجرام بنجاح
2025-07-24 03:54:31,172 - INFO - تم بدء تشغيل التطبيق
2025-07-24 03:54:31,263 - INFO - تم إنشاء المجلدات الأساسية
2025-07-24 03:54:31,265 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-24 03:54:31,283 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-24 03:54:31,387 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-24 03:54:31,953 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-24 03:54:31,954 - INFO - تم إعداد التطبيق بنجاح
2025-07-24 03:54:33,967 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-24 03:54:34,238 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-24 03:54:34,239 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-24 03:54:37,246 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-24 03:54:37,248 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-24 03:54:37,487 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-24 03:54:37,487 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-24 03:55:27,801 - INFO - معالجة callback: single_card
2025-07-24 03:55:28,011 - INFO - 🎴 بدء معالجة طلب إنشاء كروت
2025-07-24 03:55:28,012 - INFO - 🔢 عرض خيارات عدد الكروت
2025-07-24 03:55:28,255 - INFO - ✅ تم عرض خيارات عدد الكروت
2025-07-24 03:55:32,920 - INFO - معالجة callback: card_count_10
2025-07-24 03:55:33,143 - INFO - 📋 عرض قوالب Hotspot لإنشاء 10 كرت
2025-07-24 03:55:33,384 - INFO - ✅ تم إرسال قائمة قوالب لإنشاء 10 كرت - 5 قالب
2025-07-24 03:55:34,309 - INFO - معالجة callback: cards_template_10_10
2025-07-24 03:55:34,517 - INFO - 🎴 بدء إنشاء 10 كرت باستخدام القالب: 10
2025-07-24 03:55:34,762 - INFO - 🔄 تفعيل نظام Hotspot مع تحديث الواجهة...
2025-07-24 03:55:34,763 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-24 03:55:34,792 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-24 03:55:34,893 - WARNING - تحذير: فشل في حفظ الإعدادات: 'MikroTikCardGenerator' object has no attribute 'version_combo'
2025-07-24 03:55:34,894 - INFO - تم تعيين النظام الجديد: hotspot
2025-07-24 03:55:34,894 - INFO - ✅ تم تبديل النظام من None إلى hotspot
2025-07-24 03:55:34,894 - INFO - 🔄 إعادة بناء الواجهة للنظام: hotspot
2025-07-24 03:55:34,895 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-24 03:55:34,902 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-24 03:55:35,398 - INFO - تم إعداد تبويب عرض الكروت لـ Hotspot بنجاح
2025-07-24 03:55:35,567 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-24 03:55:35,567 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-24 03:55:35,690 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام hotspot
2025-07-24 03:55:35,691 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-24 03:55:35,693 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot
2025-07-24 03:55:36,024 - INFO - 📋 تطبيق القالب مع مزامنة شاملة: 10
2025-07-24 03:55:36,026 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-24 03:55:36,026 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-24 03:55:36,113 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-24 03:55:36,114 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-24 03:55:36,144 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-24 03:55:36,144 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-24 03:55:36,157 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-24 03:55:36,162 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-24 03:55:36,162 - INFO - ✅ تم تطبيق القالب '10' بنجاح
2025-07-24 03:55:36,164 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-24 03:55:36,168 - INFO - ✅ تم تحديث معاينة الكارت
2025-07-24 03:55:36,194 - INFO - ✅ تم تحديث حالة عناصر الواجهة
2025-07-24 03:55:36,197 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-24 03:55:36,198 - INFO - 🔢 تعيين عدد الكروت إلى 10...
2025-07-24 03:55:36,199 - INFO - ✅ تم تعيين عدد الكروت إلى 10
2025-07-24 03:55:36,200 - INFO - ✅ تم تحديث جميع المتغيرات الداخلية
2025-07-24 03:55:36,201 - INFO - 🏭 بدء توليد الكرت...
2025-07-24 03:55:36,225 - INFO - تم توليد 10 حساب
2025-07-24 03:55:36,225 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 370 (من generate_all)
2025-07-24 03:55:36,231 - INFO - 🚀 إرسال الكرت إلى MikroTik...
2025-07-24 03:55:36,233 - INFO - محاولة الاتصال بـ *********:8728 (عادي)
2025-07-24 03:55:36,300 - INFO - نجح الاتصال مع *********
2025-07-24 03:55:36,827 - INFO - ✅ تم إرسال المستخدم: 0119670435
2025-07-24 03:55:37,141 - INFO - ✅ تم إرسال المستخدم: 0197786109
2025-07-24 03:55:37,422 - INFO - ✅ تم إرسال المستخدم: 0158176192
2025-07-24 03:55:37,691 - INFO - ✅ تم إرسال المستخدم: 0180300798
2025-07-24 03:55:37,962 - INFO - ✅ تم إرسال المستخدم: 0116577554
2025-07-24 03:55:38,241 - INFO - ✅ تم إرسال المستخدم: 0184386212
2025-07-24 03:55:38,582 - INFO - ✅ تم إرسال المستخدم: 0158408298
2025-07-24 03:55:38,951 - INFO - ✅ تم إرسال المستخدم: 0100854352
2025-07-24 03:55:39,282 - INFO - ✅ تم إرسال المستخدم: 0100352827
2025-07-24 03:55:39,621 - INFO - ✅ تم إرسال المستخدم: 0167693174
2025-07-24 03:55:39,897 - INFO - 📊 بدء إرسال التقرير النهائي: card_type=single, success=10, failed=0
2025-07-24 03:55:40,271 - INFO - ✅ تم إرسال التقرير النهائي: 10 ناجح، 0 فاشل
2025-07-24 03:55:40,272 - INFO - ℹ️ لا توجد كروت فاشلة لحفظها: failed_cards=0
2025-07-24 03:55:40,272 - INFO - 🔍 تشخيص حفظ الكروت للكرت الواحد: failed_count=0, success_count=10, system_type=hotspot
2025-07-24 03:55:40,272 - INFO - ❌ لم يتم حفظ الكروت الناجحة للكرت الواحد - الشروط غير مستوفاة
2025-07-24 03:55:40,273 - INFO - ❌ لم يتم حفظ الكروت الفاشلة للكرت الواحد - الشروط غير مستوفاة
2025-07-24 03:55:40,273 - INFO - ✅ كرت واحد: تم إرسال 10/10 مستخدم بنجاح
2025-07-24 03:55:40,607 - INFO - ✅ تم إرسال تفاصيل 10 كرت عبر التلجرام بنجاح
2025-07-24 03:56:47,988 - INFO - معالجة callback: cards_template_10_10
2025-07-24 03:56:48,210 - INFO - 🎴 بدء إنشاء 10 كرت باستخدام القالب: 10
2025-07-24 03:56:48,486 - INFO - 🔄 تفعيل نظام Hotspot مع تحديث الواجهة...
2025-07-24 03:56:48,487 - INFO - النظام hotspot مفعل بالفعل
2025-07-24 03:56:48,488 - INFO - ✅ تم تبديل النظام من hotspot إلى hotspot
2025-07-24 03:56:48,488 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot
2025-07-24 03:56:48,568 - INFO - 📋 تطبيق القالب مع مزامنة شاملة: 10
2025-07-24 03:56:48,570 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-24 03:56:48,571 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-24 03:56:48,589 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-24 03:56:48,590 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-24 03:56:48,637 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-24 03:56:48,637 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-24 03:56:48,641 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-24 03:56:48,649 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-24 03:56:48,649 - INFO - ✅ تم تطبيق القالب '10' بنجاح
2025-07-24 03:56:48,650 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-24 03:56:48,656 - INFO - ✅ تم تحديث معاينة الكارت
2025-07-24 03:56:48,669 - INFO - ✅ تم تحديث حالة عناصر الواجهة
2025-07-24 03:56:48,672 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-24 03:56:48,673 - INFO - 🔢 تعيين عدد الكروت إلى 10...
2025-07-24 03:56:48,674 - INFO - ✅ تم تعيين عدد الكروت إلى 10
2025-07-24 03:56:48,674 - INFO - ✅ تم تحديث جميع المتغيرات الداخلية
2025-07-24 03:56:48,675 - INFO - 🏭 بدء توليد الكرت...
2025-07-24 03:56:48,696 - INFO - تم توليد 10 حساب
2025-07-24 03:56:48,697 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 380 (من generate_all)
2025-07-24 03:56:48,704 - INFO - 🚀 إرسال الكرت إلى MikroTik...
2025-07-24 03:56:48,748 - WARNING - الاتصال الحالي غير صالح: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 03:56:48,748 - INFO - تم قطع الاتصال مع MikroTik
2025-07-24 03:56:48,752 - INFO - محاولة الاتصال بـ *********:8728 (عادي)
2025-07-24 03:56:48,816 - INFO - نجح الاتصال مع *********
2025-07-24 03:56:49,280 - INFO - ✅ تم إرسال المستخدم: 0151313482
2025-07-24 03:56:49,546 - INFO - ✅ تم إرسال المستخدم: 0148086374
2025-07-24 03:56:49,817 - INFO - ✅ تم إرسال المستخدم: 0128234252
2025-07-24 03:56:50,111 - INFO - ✅ تم إرسال المستخدم: 0199986536
2025-07-24 03:56:50,388 - INFO - ✅ تم إرسال المستخدم: 0175903760
2025-07-24 03:56:50,686 - INFO - ✅ تم إرسال المستخدم: 0119681272
2025-07-24 03:56:50,967 - INFO - ✅ تم إرسال المستخدم: 0176156878
2025-07-24 03:56:51,236 - INFO - ✅ تم إرسال المستخدم: 0106838229
2025-07-24 03:56:51,577 - INFO - ✅ تم إرسال المستخدم: 0174832432
2025-07-24 03:56:51,837 - INFO - ✅ تم إرسال المستخدم: 0175569115
2025-07-24 03:56:52,084 - INFO - 📊 بدء إرسال التقرير النهائي: card_type=single, success=10, failed=0
2025-07-24 03:56:52,311 - INFO - ✅ تم إرسال التقرير النهائي: 10 ناجح، 0 فاشل
2025-07-24 03:56:52,311 - INFO - ℹ️ لا توجد كروت فاشلة لحفظها: failed_cards=0
2025-07-24 03:56:52,311 - INFO - 🔍 تشخيص حفظ الكروت للكرت الواحد: failed_count=0, success_count=10, system_type=hotspot
2025-07-24 03:56:52,312 - INFO - ❌ لم يتم حفظ الكروت الناجحة للكرت الواحد - الشروط غير مستوفاة
2025-07-24 03:56:52,312 - INFO - ❌ لم يتم حفظ الكروت الفاشلة للكرت الواحد - الشروط غير مستوفاة
2025-07-24 03:56:52,313 - INFO - ✅ كرت واحد: تم إرسال 10/10 مستخدم بنجاح
2025-07-24 03:56:52,574 - INFO - ✅ تم إرسال تفاصيل 10 كرت عبر التلجرام بنجاح
2025-07-24 03:57:10,914 - INFO - معالجة callback: cards_template_10_10
2025-07-24 03:57:11,125 - INFO - 🎴 بدء إنشاء 10 كرت باستخدام القالب: 10
2025-07-24 03:57:11,371 - INFO - 🔄 تفعيل نظام Hotspot مع تحديث الواجهة...
2025-07-24 03:57:11,372 - INFO - النظام hotspot مفعل بالفعل
2025-07-24 03:57:11,373 - INFO - ✅ تم تبديل النظام من hotspot إلى hotspot
2025-07-24 03:57:11,373 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot
2025-07-24 03:57:11,442 - INFO - 📋 تطبيق القالب مع مزامنة شاملة: 10
2025-07-24 03:57:11,443 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-24 03:57:11,443 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-24 03:57:11,477 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-24 03:57:11,484 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-24 03:57:11,534 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-24 03:57:11,534 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-24 03:57:11,538 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-24 03:57:11,542 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-24 03:57:11,542 - INFO - ✅ تم تطبيق القالب '10' بنجاح
2025-07-24 03:57:11,545 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-24 03:57:11,549 - INFO - ✅ تم تحديث معاينة الكارت
2025-07-24 03:57:11,571 - INFO - ✅ تم تحديث حالة عناصر الواجهة
2025-07-24 03:57:11,574 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-24 03:57:11,575 - INFO - 🔢 تعيين عدد الكروت إلى 10...
2025-07-24 03:57:11,576 - INFO - ✅ تم تعيين عدد الكروت إلى 10
2025-07-24 03:57:11,577 - INFO - ✅ تم تحديث جميع المتغيرات الداخلية
2025-07-24 03:57:11,577 - INFO - 🏭 بدء توليد الكرت...
2025-07-24 03:57:11,600 - INFO - تم توليد 10 حساب
2025-07-24 03:57:11,600 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 390 (من generate_all)
2025-07-24 03:57:11,607 - INFO - 🚀 إرسال الكرت إلى MikroTik...
2025-07-24 03:57:11,607 - WARNING - الاتصال الحالي غير صالح: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 03:57:11,608 - INFO - تم قطع الاتصال مع MikroTik
2025-07-24 03:57:11,610 - INFO - محاولة الاتصال بـ *********:8728 (عادي)
2025-07-24 03:57:11,676 - INFO - نجح الاتصال مع *********
2025-07-24 03:57:12,139 - INFO - ✅ تم إرسال المستخدم: 0170537061
2025-07-24 03:57:12,547 - INFO - ✅ تم إرسال المستخدم: 0137125977
2025-07-24 03:57:12,818 - INFO - ✅ تم إرسال المستخدم: 0109001990
2025-07-24 03:57:13,107 - INFO - ✅ تم إرسال المستخدم: 0130432493
2025-07-24 03:57:13,370 - INFO - ✅ تم إرسال المستخدم: 0116276021
2025-07-24 03:57:13,637 - INFO - ✅ تم إرسال المستخدم: 0175048819
2025-07-24 03:57:13,909 - INFO - ✅ تم إرسال المستخدم: 0180397506
2025-07-24 03:57:14,177 - INFO - ✅ تم إرسال المستخدم: 0136911142
2025-07-24 03:57:14,469 - INFO - ✅ تم إرسال المستخدم: 0122048390
2025-07-24 03:57:14,737 - INFO - ✅ تم إرسال المستخدم: 0186251955
2025-07-24 03:57:14,965 - INFO - 📊 بدء إرسال التقرير النهائي: card_type=single, success=10, failed=0
2025-07-24 03:57:15,193 - INFO - ✅ تم إرسال التقرير النهائي: 10 ناجح، 0 فاشل
2025-07-24 03:57:15,193 - INFO - ℹ️ لا توجد كروت فاشلة لحفظها: failed_cards=0
2025-07-24 03:57:15,194 - INFO - 🔍 تشخيص حفظ الكروت للكرت الواحد: failed_count=0, success_count=10, system_type=hotspot
2025-07-24 03:57:15,194 - INFO - ❌ لم يتم حفظ الكروت الناجحة للكرت الواحد - الشروط غير مستوفاة
2025-07-24 03:57:15,194 - INFO - ❌ لم يتم حفظ الكروت الفاشلة للكرت الواحد - الشروط غير مستوفاة
2025-07-24 03:57:15,195 - INFO - ✅ كرت واحد: تم إرسال 10/10 مستخدم بنجاح
2025-07-24 03:57:15,466 - INFO - ✅ تم إرسال تفاصيل 10 كرت عبر التلجرام بنجاح
2025-07-24 03:58:21,717 - INFO - معالجة callback: card_count_20
2025-07-24 03:58:21,935 - INFO - 📋 عرض قوالب Hotspot لإنشاء 20 كرت
2025-07-24 03:58:22,177 - INFO - ✅ تم إرسال قائمة قوالب لإنشاء 20 كرت - 5 قالب
2025-07-24 03:58:24,173 - INFO - معالجة callback: cards_template_20_10
2025-07-24 03:58:24,429 - INFO - 🎴 بدء إنشاء 20 كرت باستخدام القالب: 10
2025-07-24 03:58:24,692 - INFO - 🔄 تفعيل نظام Hotspot مع تحديث الواجهة...
2025-07-24 03:58:24,693 - INFO - النظام hotspot مفعل بالفعل
2025-07-24 03:58:24,694 - INFO - ✅ تم تبديل النظام من hotspot إلى hotspot
2025-07-24 03:58:24,695 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot
2025-07-24 03:58:24,753 - INFO - 📋 تطبيق القالب مع مزامنة شاملة: 10
2025-07-24 03:58:24,754 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-24 03:58:24,754 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-24 03:58:24,783 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-24 03:58:24,789 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-24 03:58:24,821 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-24 03:58:24,822 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-24 03:58:24,829 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-24 03:58:24,847 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-24 03:58:24,848 - INFO - ✅ تم تطبيق القالب '10' بنجاح
2025-07-24 03:58:24,848 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-24 03:58:24,853 - INFO - ✅ تم تحديث معاينة الكارت
2025-07-24 03:58:24,865 - INFO - ✅ تم تحديث حالة عناصر الواجهة
2025-07-24 03:58:24,878 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-24 03:58:24,879 - INFO - 🔢 تعيين عدد الكروت إلى 20...
2025-07-24 03:58:24,880 - INFO - ✅ تم تعيين عدد الكروت إلى 20
2025-07-24 03:58:24,880 - INFO - ✅ تم تحديث جميع المتغيرات الداخلية
2025-07-24 03:58:24,881 - INFO - 🏭 بدء توليد الكرت...
2025-07-24 03:58:24,891 - INFO - تم توليد 20 حساب
2025-07-24 03:58:24,891 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 410 (من generate_all)
2025-07-24 03:58:24,910 - INFO - 🚀 إرسال الكرت إلى MikroTik...
2025-07-24 03:58:24,911 - WARNING - الاتصال الحالي غير صالح: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 03:58:24,911 - INFO - تم قطع الاتصال مع MikroTik
2025-07-24 03:58:24,913 - INFO - محاولة الاتصال بـ *********:8728 (عادي)
2025-07-24 03:58:24,977 - INFO - نجح الاتصال مع *********
2025-07-24 03:58:25,476 - INFO - ✅ تم إرسال المستخدم: 0130793295
2025-07-24 03:58:25,747 - INFO - ✅ تم إرسال المستخدم: 0164390953
2025-07-24 03:58:26,038 - INFO - ✅ تم إرسال المستخدم: 0109942895
2025-07-24 03:58:26,297 - INFO - ✅ تم إرسال المستخدم: 0104319518
2025-07-24 03:58:26,568 - INFO - ✅ تم إرسال المستخدم: 0137811454
2025-07-24 03:58:26,847 - INFO - ✅ تم إرسال المستخدم: 0180500709
2025-07-24 03:58:27,148 - INFO - ✅ تم إرسال المستخدم: 0101800723
2025-07-24 03:58:27,437 - INFO - ✅ تم إرسال المستخدم: 0138073342
2025-07-24 03:58:27,728 - INFO - ✅ تم إرسال المستخدم: 0131161868
2025-07-24 03:58:28,057 - INFO - ✅ تم إرسال المستخدم: 0167667395
2025-07-24 03:58:43,313 - ERROR - ❌ فشل في إرسال المستخدم 0113315007: timed out
2025-07-24 03:58:43,596 - ERROR - ❌ فشل في إرسال المستخدم 0138129365: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 03:58:43,826 - ERROR - ❌ فشل في إرسال المستخدم 0141007016: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 03:58:44,141 - ERROR - ❌ فشل في إرسال المستخدم 0197651277: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 03:58:44,365 - ERROR - ❌ فشل في إرسال المستخدم 0106119600: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 03:58:44,636 - ERROR - ❌ فشل في إرسال المستخدم 0123892180: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 03:58:44,861 - ERROR - ❌ فشل في إرسال المستخدم 0198680145: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 03:58:45,086 - ERROR - ❌ فشل في إرسال المستخدم 0121115533: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 03:58:45,312 - ERROR - ❌ فشل في إرسال المستخدم 0152425507: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 03:58:45,535 - ERROR - ❌ فشل في إرسال المستخدم 0191586660: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 03:58:45,771 - INFO - 📊 بدء إرسال التقرير النهائي: card_type=single, success=10, failed=10
2025-07-24 03:58:46,005 - INFO - ✅ تم إرسال التقرير النهائي: 10 ناجح، 10 فاشل
2025-07-24 03:58:46,006 - INFO - 💾 حفظ معلومات الكروت الفاشلة: card_type=single, count=10
2025-07-24 03:58:46,006 - ERROR - خطأ في إرسال التقرير النهائي للكروت: name 'time' is not defined
2025-07-24 03:58:46,006 - INFO - 🔍 تشخيص حفظ الكروت للكرت الواحد: failed_count=10, success_count=10, system_type=hotspot
2025-07-24 03:58:46,007 - INFO - 💾 حفظ 10 كرت ناجح لعرض خيار حذف الكروت المرسلة بنجاح للكرت الواحد
2025-07-24 03:58:46,007 - INFO - ✅ تم حفظ معلومات الكروت الناجحة للكرت الواحد: 10 كرت
2025-07-24 03:58:46,007 - INFO - 💾 حفظ 10 كرت فاشل لعرض خيار إعادة المحاولة للكرت الواحد
2025-07-24 03:58:46,007 - INFO - ✅ تم حفظ معلومات الكروت الفاشلة للكرت الواحد: 10 كرت
2025-07-24 03:58:46,008 - INFO - ✅ كرت واحد: تم إرسال 10/20 مستخدم بنجاح
2025-07-24 03:58:46,273 - INFO - ✅ تم إرسال تفاصيل 20 كرت عبر التلجرام بنجاح
2025-07-24 04:00:09,787 - INFO - تم بدء تشغيل التطبيق
2025-07-24 04:00:09,843 - INFO - تم إنشاء المجلدات الأساسية
2025-07-24 04:00:09,891 - INFO - تم بدء تشغيل التطبيق
2025-07-24 04:00:09,937 - INFO - تم إنشاء المجلدات الأساسية
2025-07-24 04:00:09,939 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-24 04:00:09,943 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-24 04:00:09,945 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-24 04:00:09,950 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-24 04:00:10,048 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-24 04:00:10,076 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-24 04:00:10,727 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-24 04:00:10,727 - INFO - تم إعداد التطبيق بنجاح
2025-07-24 04:00:12,734 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-24 04:00:13,026 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-24 04:00:13,027 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-24 04:00:16,040 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-24 04:00:16,041 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-24 04:00:16,301 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-24 04:00:16,302 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-24 04:00:37,979 - INFO - معالجة callback: single_card
2025-07-24 04:00:38,194 - INFO - 🎴 بدء معالجة طلب إنشاء كروت
2025-07-24 04:00:38,195 - INFO - 🔢 عرض خيارات عدد الكروت
2025-07-24 04:00:38,449 - INFO - ✅ تم عرض خيارات عدد الكروت
2025-07-24 04:00:40,687 - INFO - معالجة callback: card_count_20
2025-07-24 04:00:40,896 - INFO - 📋 عرض قوالب Hotspot لإنشاء 20 كرت
2025-07-24 04:00:41,150 - INFO - ✅ تم إرسال قائمة قوالب لإنشاء 20 كرت - 5 قالب
2025-07-24 04:01:14,836 - INFO - معالجة callback: cards_template_20_10
2025-07-24 04:01:15,081 - INFO - 🎴 بدء إنشاء 20 كرت باستخدام القالب: 10
2025-07-24 04:01:15,422 - INFO - 🔄 تفعيل نظام Hotspot مع تحديث الواجهة...
2025-07-24 04:01:15,423 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-24 04:01:15,439 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-24 04:01:15,540 - WARNING - تحذير: فشل في حفظ الإعدادات: 'MikroTikCardGenerator' object has no attribute 'version_combo'
2025-07-24 04:01:15,540 - INFO - تم تعيين النظام الجديد: hotspot
2025-07-24 04:01:15,540 - INFO - 🔄 إعادة بناء الواجهة للنظام: hotspot
2025-07-24 04:01:15,541 - INFO - ✅ تم تبديل النظام من None إلى hotspot
2025-07-24 04:01:15,541 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-24 04:01:15,543 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-24 04:01:16,046 - INFO - تم إعداد تبويب عرض الكروت لـ Hotspot بنجاح
2025-07-24 04:01:16,217 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-24 04:01:16,217 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-24 04:01:16,340 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام hotspot
2025-07-24 04:01:16,340 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-24 04:01:16,343 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot
2025-07-24 04:01:16,605 - INFO - 📋 تطبيق القالب مع مزامنة شاملة: 10
2025-07-24 04:01:16,606 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-24 04:01:16,606 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-24 04:01:16,617 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-24 04:01:16,625 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-24 04:01:16,654 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-24 04:01:16,654 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-24 04:01:16,658 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-24 04:01:16,663 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-24 04:01:16,664 - INFO - ✅ تم تطبيق القالب '10' بنجاح
2025-07-24 04:01:16,664 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-24 04:01:16,667 - INFO - ✅ تم تحديث معاينة الكارت
2025-07-24 04:01:16,681 - INFO - ✅ تم تحديث حالة عناصر الواجهة
2025-07-24 04:01:16,686 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-24 04:01:16,687 - INFO - 🔢 تعيين عدد الكروت إلى 20...
2025-07-24 04:01:16,688 - INFO - ✅ تم تعيين عدد الكروت إلى 20
2025-07-24 04:01:16,689 - INFO - ✅ تم تحديث جميع المتغيرات الداخلية
2025-07-24 04:01:16,689 - INFO - 🏭 بدء توليد الكرت...
2025-07-24 04:01:16,700 - INFO - تم توليد 20 حساب
2025-07-24 04:01:16,704 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 430 (من generate_all)
2025-07-24 04:01:16,712 - INFO - 🚀 إرسال الكرت إلى MikroTik...
2025-07-24 04:01:16,713 - INFO - محاولة الاتصال بـ *********:8728 (عادي)
2025-07-24 04:01:16,777 - INFO - نجح الاتصال مع *********
2025-07-24 04:01:17,436 - INFO - ✅ تم إرسال المستخدم: 0152756263
2025-07-24 04:01:17,682 - INFO - ✅ تم إرسال المستخدم: 0100663264
2025-07-24 04:01:17,960 - INFO - ✅ تم إرسال المستخدم: 0178545869
2025-07-24 04:01:18,218 - INFO - ✅ تم إرسال المستخدم: 0110917418
2025-07-24 04:01:18,494 - INFO - ✅ تم إرسال المستخدم: 0167939557
2025-07-24 04:01:18,822 - INFO - ✅ تم إرسال المستخدم: 0118695737
2025-07-24 04:01:34,063 - ERROR - ❌ فشل في إرسال المستخدم 0158164558: timed out
2025-07-24 04:01:34,344 - ERROR - ❌ فشل في إرسال المستخدم 0117887072: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:01:34,569 - ERROR - ❌ فشل في إرسال المستخدم 0180729503: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:01:34,824 - ERROR - ❌ فشل في إرسال المستخدم 0147193469: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:01:35,078 - ERROR - ❌ فشل في إرسال المستخدم 0179481030: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:01:35,306 - ERROR - ❌ فشل في إرسال المستخدم 0181063810: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:01:35,531 - ERROR - ❌ فشل في إرسال المستخدم 0152535995: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:01:35,758 - ERROR - ❌ فشل في إرسال المستخدم 0142559648: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:01:35,992 - ERROR - ❌ فشل في إرسال المستخدم 0125523582: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:01:36,231 - ERROR - ❌ فشل في إرسال المستخدم 0180338322: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:01:36,459 - ERROR - ❌ فشل في إرسال المستخدم 0167820763: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:01:36,718 - ERROR - ❌ فشل في إرسال المستخدم 0123075318: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:01:36,945 - ERROR - ❌ فشل في إرسال المستخدم 0119781619: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:01:37,175 - ERROR - ❌ فشل في إرسال المستخدم 0105826060: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:01:37,403 - INFO - 📊 بدء إرسال التقرير النهائي: card_type=single, success=6, failed=14
2025-07-24 04:01:37,645 - INFO - ✅ تم إرسال التقرير النهائي: 6 ناجح، 14 فاشل
2025-07-24 04:01:37,646 - INFO - 💾 حفظ معلومات الكروت الفاشلة: card_type=single, count=14
2025-07-24 04:01:37,646 - ERROR - خطأ في إرسال التقرير النهائي للكروت: name 'time' is not defined
2025-07-24 04:01:37,646 - INFO - 🔍 تشخيص حفظ الكروت للكرت الواحد: failed_count=14, success_count=6, system_type=hotspot
2025-07-24 04:01:37,646 - INFO - 💾 حفظ 6 كرت ناجح لعرض خيار حذف الكروت المرسلة بنجاح للكرت الواحد
2025-07-24 04:01:37,646 - INFO - ✅ تم حفظ معلومات الكروت الناجحة للكرت الواحد: 6 كرت
2025-07-24 04:01:37,647 - INFO - 💾 حفظ 14 كرت فاشل لعرض خيار إعادة المحاولة للكرت الواحد
2025-07-24 04:01:37,647 - INFO - ✅ تم حفظ معلومات الكروت الفاشلة للكرت الواحد: 14 كرت
2025-07-24 04:01:37,647 - INFO - ✅ كرت واحد: تم إرسال 6/20 مستخدم بنجاح
2025-07-24 04:01:37,951 - INFO - ✅ تم إرسال تفاصيل 20 كرت عبر التلجرام بنجاح
2025-07-24 04:25:51,232 - INFO - تم بدء تشغيل التطبيق
2025-07-24 04:25:51,391 - INFO - تم إنشاء المجلدات الأساسية
2025-07-24 04:25:52,275 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-24 04:25:52,276 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-24 04:25:52,379 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-24 04:26:12,406 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-24 04:26:12,407 - INFO - تم إعداد التطبيق بنجاح
2025-07-24 04:26:14,909 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-24 04:26:18,043 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-24 04:26:18,056 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-24 04:26:21,057 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-24 04:26:21,098 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-24 04:26:21,333 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-24 04:26:21,334 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-24 04:26:31,920 - INFO - معالجة callback: single_card
2025-07-24 04:26:32,128 - INFO - 🎴 بدء معالجة طلب إنشاء كروت
2025-07-24 04:26:32,129 - INFO - 🔢 عرض خيارات عدد الكروت
2025-07-24 04:26:32,370 - INFO - ✅ تم عرض خيارات عدد الكروت
2025-07-24 04:26:35,531 - INFO - معالجة callback: card_count_10
2025-07-24 04:26:35,740 - INFO - 📋 عرض قوالب Hotspot لإنشاء 10 كرت
2025-07-24 04:26:35,991 - INFO - ✅ تم إرسال قائمة قوالب لإنشاء 10 كرت - 5 قالب
2025-07-24 04:26:37,360 - INFO - معالجة callback: cards_template_10_10
2025-07-24 04:26:37,570 - INFO - 🎴 بدء إنشاء 10 كرت باستخدام القالب: 10
2025-07-24 04:26:37,829 - INFO - 🔄 تفعيل نظام Hotspot مع تحديث الواجهة...
2025-07-24 04:26:37,830 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-24 04:26:37,948 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-24 04:26:38,052 - WARNING - تحذير: فشل في حفظ الإعدادات: 'MikroTikCardGenerator' object has no attribute 'version_combo'
2025-07-24 04:26:38,052 - INFO - تم تعيين النظام الجديد: hotspot
2025-07-24 04:26:38,053 - INFO - ✅ تم تبديل النظام من None إلى hotspot
2025-07-24 04:26:38,053 - INFO - 🔄 إعادة بناء الواجهة للنظام: hotspot
2025-07-24 04:26:38,053 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-24 04:26:38,055 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-24 04:26:38,750 - INFO - تم إعداد تبويب عرض الكروت لـ Hotspot بنجاح
2025-07-24 04:26:38,937 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-24 04:26:38,937 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-24 04:26:39,191 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام hotspot
2025-07-24 04:26:39,192 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-24 04:26:39,194 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot
2025-07-24 04:26:39,760 - INFO - 📋 تطبيق القالب مع مزامنة شاملة: 10
2025-07-24 04:26:39,762 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-24 04:26:39,763 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-24 04:26:39,858 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-24 04:26:39,858 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-24 04:26:39,887 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-24 04:26:39,888 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-24 04:26:39,892 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-24 04:26:39,908 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-24 04:26:39,909 - INFO - ✅ تم تطبيق القالب '10' بنجاح
2025-07-24 04:26:39,910 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-24 04:26:39,914 - INFO - ✅ تم تحديث معاينة الكارت
2025-07-24 04:26:39,927 - INFO - ✅ تم تحديث حالة عناصر الواجهة
2025-07-24 04:26:39,940 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-24 04:26:39,941 - INFO - 🔢 تعيين عدد الكروت إلى 10...
2025-07-24 04:26:39,943 - INFO - ✅ تم تعيين عدد الكروت إلى 10
2025-07-24 04:26:39,944 - INFO - ✅ تم تحديث جميع المتغيرات الداخلية
2025-07-24 04:26:39,944 - INFO - 🏭 بدء توليد الكرت...
2025-07-24 04:26:40,011 - INFO - تم توليد 10 حساب
2025-07-24 04:26:40,012 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 440 (من generate_all)
2025-07-24 04:26:40,018 - INFO - 🚀 إرسال الكرت إلى MikroTik...
2025-07-24 04:26:40,020 - INFO - 🧹 تم مسح بيانات العمليات السابقة
2025-07-24 04:26:40,039 - INFO - محاولة الاتصال بـ *********:8728 (عادي)
2025-07-24 04:26:40,103 - INFO - نجح الاتصال مع *********
2025-07-24 04:26:40,726 - INFO - ✅ تم إرسال المستخدم: 0190180644
2025-07-24 04:26:41,004 - INFO - ✅ تم إرسال المستخدم: 0171362452
2025-07-24 04:26:41,275 - INFO - ✅ تم إرسال المستخدم: 0173790829
2025-07-24 04:26:41,554 - INFO - ✅ تم إرسال المستخدم: 0104912685
2025-07-24 04:26:41,821 - INFO - ✅ تم إرسال المستخدم: 0119776664
2025-07-24 04:26:42,084 - INFO - ✅ تم إرسال المستخدم: 0142664808
2025-07-24 04:26:42,355 - INFO - ✅ تم إرسال المستخدم: 0154592631
2025-07-24 04:26:42,644 - INFO - ✅ تم إرسال المستخدم: 0148162697
2025-07-24 04:26:42,915 - INFO - ✅ تم إرسال المستخدم: 0171697156
2025-07-24 04:26:43,174 - INFO - ✅ تم إرسال المستخدم: 0175735371
2025-07-24 04:26:43,404 - INFO - 📊 بدء إرسال التقرير النهائي: card_type=single, success=10, failed=0
2025-07-24 04:26:43,630 - INFO - ✅ تم إرسال التقرير النهائي: 10 ناجح، 0 فاشل
2025-07-24 04:26:43,630 - INFO - ℹ️ لا توجد كروت فاشلة لحفظها: failed_cards=0
2025-07-24 04:26:43,630 - INFO - 🔍 تشخيص حفظ الكروت للكرت الواحد: failed_count=0, success_count=10, system_type=hotspot
2025-07-24 04:26:43,631 - INFO - ❌ لم يتم حفظ الكروت الناجحة للكرت الواحد - الشروط غير مستوفاة
2025-07-24 04:26:43,631 - INFO - ❌ لم يتم حفظ الكروت الفاشلة للكرت الواحد - الشروط غير مستوفاة
2025-07-24 04:26:43,631 - INFO - ✅ كرت واحد: تم إرسال 10/10 مستخدم بنجاح
2025-07-24 04:26:43,888 - INFO - ✅ تم إرسال تفاصيل 10 كرت عبر التلجرام بنجاح
2025-07-24 04:27:00,334 - INFO - معالجة callback: cards_template_10_10
2025-07-24 04:27:00,543 - INFO - 🎴 بدء إنشاء 10 كرت باستخدام القالب: 10
2025-07-24 04:27:00,785 - INFO - 🔄 تفعيل نظام Hotspot مع تحديث الواجهة...
2025-07-24 04:27:00,786 - INFO - النظام hotspot مفعل بالفعل
2025-07-24 04:27:00,787 - INFO - ✅ تم تبديل النظام من hotspot إلى hotspot
2025-07-24 04:27:00,787 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot
2025-07-24 04:27:00,866 - INFO - 📋 تطبيق القالب مع مزامنة شاملة: 10
2025-07-24 04:27:00,867 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-24 04:27:00,867 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-24 04:27:00,887 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-24 04:27:00,888 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-24 04:27:00,943 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-24 04:27:00,943 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-24 04:27:00,946 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-24 04:27:00,951 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-24 04:27:00,951 - INFO - ✅ تم تطبيق القالب '10' بنجاح
2025-07-24 04:27:00,951 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-24 04:27:00,955 - INFO - ✅ تم تحديث معاينة الكارت
2025-07-24 04:27:00,970 - INFO - ✅ تم تحديث حالة عناصر الواجهة
2025-07-24 04:27:00,973 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-24 04:27:00,974 - INFO - 🔢 تعيين عدد الكروت إلى 10...
2025-07-24 04:27:00,975 - INFO - ✅ تم تعيين عدد الكروت إلى 10
2025-07-24 04:27:00,976 - INFO - ✅ تم تحديث جميع المتغيرات الداخلية
2025-07-24 04:27:00,976 - INFO - 🏭 بدء توليد الكرت...
2025-07-24 04:27:00,984 - INFO - تم توليد 10 حساب
2025-07-24 04:27:00,985 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 450 (من generate_all)
2025-07-24 04:27:00,993 - INFO - 🚀 إرسال الكرت إلى MikroTik...
2025-07-24 04:27:01,003 - INFO - 🧹 تم مسح بيانات العمليات السابقة
2025-07-24 04:27:01,042 - WARNING - الاتصال الحالي غير صالح: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:27:01,042 - INFO - تم قطع الاتصال مع MikroTik
2025-07-24 04:27:01,046 - INFO - محاولة الاتصال بـ *********:8728 (عادي)
2025-07-24 04:27:01,114 - INFO - نجح الاتصال مع *********
2025-07-24 04:27:01,627 - INFO - ✅ تم إرسال المستخدم: 0113446966
2025-07-24 04:27:01,904 - INFO - ✅ تم إرسال المستخدم: 0170395553
2025-07-24 04:27:02,186 - INFO - ✅ تم إرسال المستخدم: 0100076446
2025-07-24 04:27:02,444 - INFO - ✅ تم إرسال المستخدم: 0131942401
2025-07-24 04:27:02,716 - INFO - ✅ تم إرسال المستخدم: 0116034888
2025-07-24 04:27:03,004 - INFO - ✅ تم إرسال المستخدم: 0156334428
2025-07-24 04:27:03,286 - INFO - ✅ تم إرسال المستخدم: 0182439727
2025-07-24 04:27:03,575 - INFO - ✅ تم إرسال المستخدم: 0122027248
2025-07-24 04:27:03,846 - INFO - ✅ تم إرسال المستخدم: 0183211219
2025-07-24 04:27:04,154 - INFO - ✅ تم إرسال المستخدم: 0138009860
2025-07-24 04:27:04,392 - INFO - 📊 بدء إرسال التقرير النهائي: card_type=single, success=10, failed=0
2025-07-24 04:27:04,655 - INFO - ✅ تم إرسال التقرير النهائي: 10 ناجح، 0 فاشل
2025-07-24 04:27:04,656 - INFO - ℹ️ لا توجد كروت فاشلة لحفظها: failed_cards=0
2025-07-24 04:27:04,656 - INFO - 🔍 تشخيص حفظ الكروت للكرت الواحد: failed_count=0, success_count=10, system_type=hotspot
2025-07-24 04:27:04,656 - INFO - ❌ لم يتم حفظ الكروت الناجحة للكرت الواحد - الشروط غير مستوفاة
2025-07-24 04:27:04,657 - INFO - ❌ لم يتم حفظ الكروت الفاشلة للكرت الواحد - الشروط غير مستوفاة
2025-07-24 04:27:04,657 - INFO - ✅ كرت واحد: تم إرسال 10/10 مستخدم بنجاح
2025-07-24 04:27:04,916 - INFO - ✅ تم إرسال تفاصيل 10 كرت عبر التلجرام بنجاح
2025-07-24 04:27:12,152 - INFO - معالجة callback: card_count_20
2025-07-24 04:27:12,367 - INFO - 📋 عرض قوالب Hotspot لإنشاء 20 كرت
2025-07-24 04:27:12,610 - INFO - ✅ تم إرسال قائمة قوالب لإنشاء 20 كرت - 5 قالب
2025-07-24 04:27:16,607 - INFO - معالجة callback: cards_template_20_10
2025-07-24 04:27:16,817 - INFO - 🎴 بدء إنشاء 20 كرت باستخدام القالب: 10
2025-07-24 04:27:17,070 - INFO - 🔄 تفعيل نظام Hotspot مع تحديث الواجهة...
2025-07-24 04:27:17,071 - INFO - النظام hotspot مفعل بالفعل
2025-07-24 04:27:17,071 - INFO - ✅ تم تبديل النظام من hotspot إلى hotspot
2025-07-24 04:27:17,072 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot
2025-07-24 04:27:17,133 - INFO - 📋 تطبيق القالب مع مزامنة شاملة: 10
2025-07-24 04:27:17,135 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-24 04:27:17,135 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-24 04:27:17,157 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-24 04:27:17,158 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-24 04:27:17,199 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-24 04:27:17,199 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-24 04:27:17,202 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-24 04:27:17,211 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-24 04:27:17,222 - INFO - ✅ تم تطبيق القالب '10' بنجاح
2025-07-24 04:27:17,222 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-24 04:27:17,227 - INFO - ✅ تم تحديث معاينة الكارت
2025-07-24 04:27:17,252 - INFO - ✅ تم تحديث حالة عناصر الواجهة
2025-07-24 04:27:17,255 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-24 04:27:17,256 - INFO - 🔢 تعيين عدد الكروت إلى 20...
2025-07-24 04:27:17,257 - INFO - ✅ تم تعيين عدد الكروت إلى 20
2025-07-24 04:27:17,258 - INFO - ✅ تم تحديث جميع المتغيرات الداخلية
2025-07-24 04:27:17,259 - INFO - 🏭 بدء توليد الكرت...
2025-07-24 04:27:17,271 - INFO - تم توليد 20 حساب
2025-07-24 04:27:17,273 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 470 (من generate_all)
2025-07-24 04:27:17,290 - INFO - 🚀 إرسال الكرت إلى MikroTik...
2025-07-24 04:27:17,290 - INFO - 🧹 تم مسح بيانات العمليات السابقة
2025-07-24 04:27:17,291 - WARNING - الاتصال الحالي غير صالح: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:27:17,291 - INFO - تم قطع الاتصال مع MikroTik
2025-07-24 04:27:17,294 - INFO - محاولة الاتصال بـ *********:8728 (عادي)
2025-07-24 04:27:17,354 - INFO - نجح الاتصال مع *********
2025-07-24 04:27:17,857 - INFO - ✅ تم إرسال المستخدم: 0161454974
2025-07-24 04:27:18,135 - INFO - ✅ تم إرسال المستخدم: 0145421654
2025-07-24 04:27:18,406 - INFO - ✅ تم إرسال المستخدم: 0161360603
2025-07-24 04:27:18,675 - INFO - ✅ تم إرسال المستخدم: 0182318064
2025-07-24 04:27:18,936 - INFO - ✅ تم إرسال المستخدم: 0121033921
2025-07-24 04:27:19,205 - INFO - ✅ تم إرسال المستخدم: 0190011580
2025-07-24 04:27:19,466 - INFO - ✅ تم إرسال المستخدم: 0177467390
2025-07-24 04:27:19,735 - INFO - ✅ تم إرسال المستخدم: 0109746862
2025-07-24 04:27:20,016 - INFO - ✅ تم إرسال المستخدم: 0127778749
2025-07-24 04:27:20,285 - INFO - ✅ تم إرسال المستخدم: 0180036299
2025-07-24 04:27:20,556 - INFO - ✅ تم إرسال المستخدم: 0171103196
2025-07-24 04:27:20,825 - INFO - ✅ تم إرسال المستخدم: 0110925428
2025-07-24 04:27:21,086 - INFO - ✅ تم إرسال المستخدم: 0161436218
2025-07-24 04:27:21,462 - INFO - ✅ تم إرسال المستخدم: 0125357900
2025-07-24 04:27:36,723 - ERROR - ❌ فشل في إرسال المستخدم 0169309987: timed out
2025-07-24 04:27:37,057 - ERROR - ❌ فشل في إرسال المستخدم 0137097071: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:27:37,293 - ERROR - ❌ فشل في إرسال المستخدم 0129749512: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:27:37,538 - ERROR - ❌ فشل في إرسال المستخدم 0180171957: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:27:37,767 - ERROR - ❌ فشل في إرسال المستخدم 0168901952: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:27:38,019 - ERROR - ❌ فشل في إرسال المستخدم 0130998685: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:27:38,276 - INFO - 📊 بدء إرسال التقرير النهائي: card_type=single, success=14, failed=6
2025-07-24 04:27:38,537 - INFO - ✅ تم إرسال التقرير النهائي: 14 ناجح، 6 فاشل
2025-07-24 04:27:38,537 - INFO - 💾 حفظ معلومات الكروت الفاشلة: card_type=single, count=6
2025-07-24 04:27:38,538 - ERROR - خطأ في إرسال التقرير النهائي للكروت: name 'time' is not defined
2025-07-24 04:27:38,538 - INFO - 🔍 تشخيص حفظ الكروت للكرت الواحد: failed_count=6, success_count=14, system_type=hotspot
2025-07-24 04:27:38,538 - INFO - 💾 حفظ 14 كرت ناجح لعرض خيار حذف الكروت المرسلة بنجاح للكرت الواحد
2025-07-24 04:27:38,539 - INFO - ✅ تم حفظ معلومات الكروت الناجحة للكرت الواحد: 14 كرت
2025-07-24 04:27:38,539 - INFO - 💾 حفظ 6 كرت فاشل لعرض خيار إعادة المحاولة للكرت الواحد
2025-07-24 04:27:38,539 - INFO - ✅ تم حفظ معلومات الكروت الفاشلة للكرت الواحد: 6 كرت
2025-07-24 04:27:38,539 - INFO - ✅ كرت واحد: تم إرسال 14/20 مستخدم بنجاح
2025-07-24 04:27:38,801 - INFO - ✅ تم إرسال تفاصيل 20 كرت عبر التلجرام بنجاح
2025-07-24 04:50:08,410 - INFO - تم بدء تشغيل التطبيق
2025-07-24 04:50:08,627 - INFO - تم إنشاء المجلدات الأساسية
2025-07-24 04:50:08,660 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-24 04:50:08,661 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-24 04:50:08,764 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-24 04:50:24,138 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-24 04:50:24,138 - INFO - تم إعداد التطبيق بنجاح
2025-07-24 04:50:26,219 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-24 04:50:27,469 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-24 04:50:27,470 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-24 04:50:30,477 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-24 04:50:30,511 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-24 04:50:30,750 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-24 04:50:30,750 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-24 04:50:37,977 - INFO - معالجة callback: single_card
2025-07-24 04:50:38,190 - INFO - 🎴 بدء معالجة طلب إنشاء كروت
2025-07-24 04:50:38,191 - INFO - 🔢 عرض خيارات عدد الكروت
2025-07-24 04:50:38,430 - INFO - ✅ تم عرض خيارات عدد الكروت
2025-07-24 04:50:41,738 - INFO - معالجة callback: card_count_20
2025-07-24 04:50:41,949 - INFO - 📋 عرض قوالب Hotspot لإنشاء 20 كرت
2025-07-24 04:50:42,187 - INFO - ✅ تم إرسال قائمة قوالب لإنشاء 20 كرت - 5 قالب
2025-07-24 04:50:44,346 - INFO - معالجة callback: cards_template_20_10
2025-07-24 04:50:44,553 - INFO - 🎴 بدء إنشاء 20 كرت باستخدام القالب: 10
2025-07-24 04:50:44,816 - INFO - 🔄 تفعيل نظام Hotspot مع تحديث الواجهة...
2025-07-24 04:50:44,817 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-24 04:50:44,950 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-24 04:50:45,052 - WARNING - تحذير: فشل في حفظ الإعدادات: 'MikroTikCardGenerator' object has no attribute 'version_combo'
2025-07-24 04:50:45,053 - INFO - تم تعيين النظام الجديد: hotspot
2025-07-24 04:50:45,053 - INFO - ✅ تم تبديل النظام من None إلى hotspot
2025-07-24 04:50:45,053 - INFO - 🔄 إعادة بناء الواجهة للنظام: hotspot
2025-07-24 04:50:45,054 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-24 04:50:45,056 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-24 04:50:45,879 - INFO - تم إعداد تبويب عرض الكروت لـ Hotspot بنجاح
2025-07-24 04:50:46,233 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-24 04:50:46,234 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-24 04:50:46,504 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام hotspot
2025-07-24 04:50:46,504 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-24 04:50:46,506 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot
2025-07-24 04:50:47,170 - INFO - 📋 تطبيق القالب مع مزامنة شاملة: 10
2025-07-24 04:50:47,172 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-24 04:50:47,172 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-24 04:50:47,291 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-24 04:50:47,291 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-24 04:50:47,331 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-24 04:50:47,332 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-24 04:50:47,350 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-24 04:50:47,354 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-24 04:50:47,355 - INFO - ✅ تم تطبيق القالب '10' بنجاح
2025-07-24 04:50:47,357 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-24 04:50:47,361 - INFO - ✅ تم تحديث معاينة الكارت
2025-07-24 04:50:47,389 - INFO - ✅ تم تحديث حالة عناصر الواجهة
2025-07-24 04:50:47,392 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-24 04:50:47,393 - INFO - 🔢 تعيين عدد الكروت إلى 20...
2025-07-24 04:50:47,396 - INFO - ✅ تم تعيين عدد الكروت إلى 20
2025-07-24 04:50:47,398 - INFO - ✅ تم تحديث جميع المتغيرات الداخلية
2025-07-24 04:50:47,399 - INFO - 🏭 بدء توليد الكرت...
2025-07-24 04:50:47,432 - INFO - تم توليد 20 حساب
2025-07-24 04:50:47,442 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 490 (من generate_all)
2025-07-24 04:50:47,485 - INFO - 🚀 إرسال الكرت إلى MikroTik...
2025-07-24 04:50:47,486 - INFO - محاولة الاتصال بـ *********:8728 (عادي)
2025-07-24 04:50:47,546 - INFO - نجح الاتصال مع *********
2025-07-24 04:50:48,196 - INFO - ✅ تم إرسال المستخدم: 0174438578
2025-07-24 04:50:48,457 - INFO - ✅ تم إرسال المستخدم: 0113281604
2025-07-24 04:50:48,729 - INFO - ✅ تم إرسال المستخدم: 0131358805
2025-07-24 04:50:48,998 - INFO - ✅ تم إرسال المستخدم: 0119741214
2025-07-24 04:50:49,269 - INFO - ✅ تم إرسال المستخدم: 0159241578
2025-07-24 04:50:49,579 - INFO - ✅ تم إرسال المستخدم: 0176889394
2025-07-24 04:51:04,833 - ERROR - ❌ فشل في إرسال المستخدم 0163658030: timed out
2025-07-24 04:51:05,110 - ERROR - ❌ فشل في إرسال المستخدم 0113577840: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:51:05,358 - ERROR - ❌ فشل في إرسال المستخدم 0113717566: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:51:05,614 - ERROR - ❌ فشل في إرسال المستخدم 0143695494: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:51:05,919 - ERROR - ❌ فشل في إرسال المستخدم 0134261141: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:51:06,144 - ERROR - ❌ فشل في إرسال المستخدم 0128594388: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:51:06,372 - ERROR - ❌ فشل في إرسال المستخدم 0149796344: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:51:06,602 - ERROR - ❌ فشل في إرسال المستخدم 0186823180: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:51:06,828 - ERROR - ❌ فشل في إرسال المستخدم 0178518284: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:51:07,053 - ERROR - ❌ فشل في إرسال المستخدم 0185942756: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:51:07,285 - ERROR - ❌ فشل في إرسال المستخدم 0134772044: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:51:07,507 - ERROR - ❌ فشل في إرسال المستخدم 0144226869: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:51:07,739 - ERROR - ❌ فشل في إرسال المستخدم 0197383886: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:51:07,969 - ERROR - ❌ فشل في إرسال المستخدم 0123376751: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:51:09,636 - INFO - 📊 بدء إرسال التقرير النهائي: card_type=single, success=6, failed=14
2025-07-24 04:51:09,869 - INFO - ✅ تم إرسال التقرير النهائي: 6 ناجح، 14 فاشل
2025-07-24 04:51:09,869 - INFO - 💾 حفظ معلومات الكروت الفاشلة: card_type=single, count=14
2025-07-24 04:51:09,869 - ERROR - خطأ في إرسال التقرير النهائي للكروت: name 'time' is not defined
2025-07-24 04:51:09,870 - INFO - 🔍 تشخيص حفظ الكروت للكرت الواحد: failed_count=14, success_count=6, system_type=hotspot
2025-07-24 04:51:09,870 - INFO - 💾 حفظ 14 كرت فاشل لعرض خيار إعادة المحاولة للكرت الواحد
2025-07-24 04:51:09,870 - INFO - ✅ تم حفظ معلومات الكروت الفاشلة للكرت الواحد: 14 كرت
2025-07-24 04:51:09,870 - INFO - ✅ كرت واحد: تم إرسال 6/20 مستخدم بنجاح
2025-07-24 04:51:10,136 - INFO - ✅ تم إرسال تفاصيل 20 كرت عبر التلجرام بنجاح
2025-07-24 04:51:33,820 - INFO - معالجة الأمر: /start
2025-07-24 04:51:34,057 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-24 04:51:35,604 - INFO - معالجة callback: select_system_hs
2025-07-24 04:51:36,061 - INFO - تم إرسال 5 قالب مفلتر لنظام Hotspot
2025-07-24 04:51:37,614 - INFO - معالجة callback: independent_template_hs_10
2025-07-24 04:51:38,108 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-24 04:51:38,109 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-24 04:51:38,109 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-24 04:51:38,136 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-24 04:51:38,137 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-24 04:51:38,167 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-24 04:51:38,168 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-24 04:51:38,171 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-24 04:51:38,176 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-24 04:51:38,177 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-24 04:51:38,189 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-24 04:51:38,438 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-24 04:51:40,658 - INFO - معالجة callback: independent_create_hs_10_lightning
2025-07-24 04:51:46,616 - INFO - معالجة callback: independent_count_hs_10_lightning_200
2025-07-24 04:51:47,230 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-24 04:51:47,231 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-24 04:51:47,249 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-24 04:51:47,249 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-24 04:51:47,285 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-24 04:51:47,287 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-24 04:51:47,291 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-24 04:51:47,296 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-24 04:51:47,594 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-24 04:51:47,594 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-24 04:51:47,623 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-24 04:51:47,623 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-24 04:51:47,662 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-24 04:51:47,662 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-24 04:51:47,665 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-24 04:51:47,669 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-24 04:51:47,670 - INFO - ⚡ البرق: تم الحفاظ على العدد المحدد: 5
2025-07-24 04:51:47,671 - INFO - تم تطبيق إعدادات القالب '10' بنجاح
2025-07-24 04:51:47,672 - INFO - ⚡ بدء البرق التلقائي الموحد لنظام Hotspot
2025-07-24 04:51:47,676 - INFO - ⚡ البرق: استخدام العدد من التلجرام بوت: 200
2025-07-24 04:51:47,689 - INFO - ⚡ البرق الموحد: العدد المطلوب 200 حساب لنظام Hotspot
2025-07-24 04:51:47,690 - INFO - ⚡ البرق: تم تحديث عدد الحسابات في الواجهة إلى: 200
2025-07-24 04:51:47,695 - INFO - ⚡ البرق الموحد: تم توليد 200 حساب لنظام Hotspot
2025-07-24 04:51:48,307 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 200
2025-07-24 04:51:48,501 - INFO - تم إنشاء PDF بنجاح: exports\كروت_(10)_ب20-200 كارت-24-07-2025-04-51-47-hotspot.pdf
2025-07-24 04:51:48,501 - INFO - تم حفظ ملف PDF: exports\كروت_(10)_ب20-200 كارت-24-07-2025-04-51-47-hotspot.pdf
2025-07-24 04:51:52,105 - INFO - ⚡ البرق الموحد: تم حفظ PDF: exports\كروت_(10)_ب20-200 كارت-24-07-2025-04-51-47-hotspot.pdf
2025-07-24 04:51:52,109 - INFO - ⚡ البرق الموحد: تم حفظ ملف .rsc الاحتياطي: exports\كروت_(10)_ب20-200 كارت-24-07-2025-04-51-47-hotspot.rsc
2025-07-24 04:51:52,644 - INFO - تم إرسال الملف بنجاح: كروت_(10)_ب20-200 كارت-24-07-2025-04-51-47-hotspot.pdf
2025-07-24 04:51:52,645 - INFO - ⚡ البرق الموحد: تم إرسال ملف PDF عبر التلجرام: exports\كروت_(10)_ب20-200 كارت-24-07-2025-04-51-47-hotspot.pdf
2025-07-24 04:51:52,869 - INFO - ⚡ البرق الموحد: بدء إرسال الكروت إلى MikroTik باستخدام آلية send_to_mikrotik
2025-07-24 04:51:52,870 - INFO - 🔍 بدء تنفيذ send_to_mikrotik_silent() - الدالة المخصصة للبرق الموحد
2025-07-24 04:51:52,871 - WARNING - الاتصال الحالي غير صالح: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:51:52,871 - INFO - تم قطع الاتصال مع MikroTik
2025-07-24 04:51:52,872 - INFO - محاولة الاتصال بـ *********:8728 (عادي)
2025-07-24 04:51:52,941 - INFO - نجح الاتصال مع *********
2025-07-24 04:51:52,942 - INFO - 🔍 تأكيد: system_type = 'hotspot' - مناسب للبرق الموحد
2025-07-24 04:51:53,185 - INFO - ⚡ البرق الموحد: بدء إرسال 200 مستخدم إلى MikroTik
2025-07-24 04:51:53,187 - INFO - 🔍 تشخيص قبل بدء الإرسال: total=200, existing_users=267
2025-07-24 04:52:11,508 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0110761940: timed out
2025-07-24 04:52:12,037 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0109827953: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:12,273 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0133833642: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:12,533 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0102789109: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:12,797 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0119850732: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:13,112 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0149817941: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:13,349 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0175585674: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:13,607 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0167357982: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:13,870 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0110669749: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:14,143 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0102691758: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:14,408 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0155619232: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:14,679 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0114515330: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:14,905 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0122489647: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:15,168 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0104267185: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:15,434 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0173308972: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:15,660 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0172656747: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:15,889 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0113095847: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:16,122 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0155251493: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:16,348 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0190334008: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:16,574 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0142695222: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:16,820 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0106573652: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:17,066 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0192230336: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:17,294 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0105733531: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:17,521 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0139188063: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:17,746 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0124945116: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:17,968 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0132638938: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:18,202 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0155192174: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:18,430 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0171855207: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:18,661 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0126381571: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:18,893 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0155904323: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:19,118 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0171854220: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:19,441 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0117205861: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:19,727 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0151563424: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:19,949 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0148782433: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:20,207 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0117681267: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:20,427 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0121761397: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:20,653 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0194626228: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:20,874 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0118451394: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:21,098 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0187300723: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:21,330 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0146485547: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:21,573 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0197170096: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:21,803 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0168081471: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:22,030 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0180225313: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:22,267 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0102429335: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:22,492 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0125250674: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:22,718 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0185592273: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:22,951 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0144325296: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:23,178 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0191109889: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:23,448 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0149958359: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:23,675 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0171094893: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:23,904 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0182429791: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:24,128 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0176451180: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:24,353 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0136715354: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:24,580 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0128997337: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:24,808 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0140685810: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:25,037 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0170828287: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:25,283 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0173339829: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:25,536 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0103618175: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:25,792 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0151031050: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:26,032 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0124933007: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:26,271 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0184257779: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:26,811 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0130529737: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:27,069 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0138649471: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:27,402 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0136878502: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:27,639 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0102118868: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:27,874 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0106082962: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:28,105 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0121765252: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:28,509 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0131964184: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:28,738 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0107667224: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:29,058 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0154452655: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:29,445 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0133954093: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:29,824 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0107159764: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:30,077 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0130750760: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:30,302 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0145739020: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:30,549 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0169246929: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:30,775 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0104744054: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:31,006 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0192546190: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:31,252 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0141699415: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:31,477 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0161629456: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:31,703 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0195285975: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:31,945 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0118270142: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:32,182 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0125735689: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:32,448 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0126025444: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:32,678 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0175680375: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:32,905 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0141719673: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:33,128 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0127051112: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:33,357 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0195391146: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:33,581 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0154836462: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:33,821 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0122987876: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:34,045 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0119225304: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:34,279 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0162637609: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:34,502 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0196315586: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:34,727 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0162431141: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:34,955 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0172342712: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:35,181 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0152332739: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:35,438 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0109313126: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:35,675 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0141216175: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:35,900 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0188551077: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:36,129 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0189286648: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:36,353 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0121572073: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:44,600 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0100694317: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:44,927 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0125322133: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:45,163 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0168759503: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:45,391 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0190074346: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:45,616 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0181776400: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:45,840 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0127138572: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:46,069 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0105680271: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:46,294 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0193807458: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:46,546 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0123381226: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:46,780 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0146010997: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:47,043 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0167915657: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:47,262 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0109409017: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:47,491 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0128671633: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:47,725 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0152135183: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:47,951 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0196562994: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:48,180 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0138637094: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:48,452 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0119878739: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:48,681 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0121759031: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:48,918 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0167350679: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:49,158 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0133093350: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:49,455 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0148181493: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:49,778 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0153707909: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:50,013 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0190494167: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:50,243 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0100404162: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:50,468 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0176413917: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:50,723 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0167779597: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:50,969 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0157269985: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:51,196 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0151451337: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:51,477 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0156979043: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:51,810 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0196152247: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:52,105 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0139667135: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:52,329 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0129678848: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:52,551 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0163821261: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:52,792 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0151622461: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:53,030 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0131235326: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:53,264 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0190669292: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:53,487 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0104832375: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:53,749 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0169408450: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:54,007 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0102637977: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:54,261 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0142444435: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:54,516 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0103823813: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:54,748 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0126635535: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:54,991 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0108905678: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:55,233 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0118830383: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:55,482 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0189460801: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:55,706 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0144543527: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:55,932 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0189290860: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:56,162 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0115390196: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:56,462 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0125337577: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:57,134 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0152634287: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:57,359 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0194112317: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:57,589 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0126426028: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:57,816 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0191916886: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:58,052 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0115965506: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:58,279 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0120509787: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:58,501 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0101951899: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:58,727 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0108461961: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:58,951 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0173443924: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:59,191 - ERROR - ⚡ البرق الموحد: فشل إرسال المستخدم 0199275076: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:52:59,465 - INFO - ⚡ البرق الموحد: النتائج النهائية - نجح: 41, فشل: 159, مكرر: 0
2025-07-24 04:52:59,466 - INFO - 📊 بدء إرسال التقرير النهائي: card_type=lightning, success=41, failed=159
2025-07-24 04:52:59,700 - INFO - ✅ تم إرسال التقرير النهائي: 41 ناجح، 159 فاشل
2025-07-24 04:52:59,701 - INFO - 💾 حفظ معلومات الكروت الفاشلة: card_type=lightning, count=159
2025-07-24 04:52:59,702 - ERROR - خطأ في إرسال التقرير النهائي للكروت: name 'time' is not defined
2025-07-24 04:52:59,703 - INFO - 🔍 تم حفظ last_send_stats: success=41, failed=159, successful_usernames=41
2025-07-24 04:52:59,703 - INFO - 🔍 تشخيص حفظ الكروت الناجحة: failed_count=159, success_count=41, system_type=hotspot
2025-07-24 04:52:59,704 - INFO - 🔍 عدد الكروت الناجحة في القائمة: 41
2025-07-24 04:52:59,704 - INFO - 💾 حفظ 41 كرت ناجح لعرض خيار حذف الكروت المرسلة بنجاح
2025-07-24 04:52:59,704 - INFO - ✅ تم حفظ معلومات الكروت الناجحة: 41 كرت
2025-07-24 04:52:59,705 - INFO - 💾 حفظ 159 كرت فاشل لعرض خيار إعادة المحاولة للبرق
2025-07-24 04:52:59,707 - INFO - ✅ تم حفظ معلومات الكروت الفاشلة للبرق: 159 كرت
2025-07-24 04:52:59,708 - WARNING - ⚡ البرق الموحد: تم إنشاء 200 كارت وحفظ PDF، لكن فشل إرسال الكروت إلى MikroTik
2025-07-24 04:53:00,540 - INFO - 🔍 تشخيص إحصائيات البرق:
2025-07-24 04:53:00,540 - INFO -    - hasattr last_send_stats: True
2025-07-24 04:53:00,548 - INFO -    - success_count من last_send_stats: 41
2025-07-24 04:53:00,549 - INFO -    - failed_count من last_send_stats: 159
2025-07-24 04:53:00,550 - INFO -    - duplicates_count من last_send_stats: 0
2025-07-24 04:53:00,550 - INFO - ⚡ إرسال إشعار التأكيد إلى المستخدم 998535391...
2025-07-24 04:53:00,551 - INFO - 🔍 تشخيص شروط زر الحذف:
2025-07-24 04:53:00,551 - INFO -    - failed_count > 0: True (failed_count=159)
2025-07-24 04:53:00,551 - INFO -    - success_count > 0: True (success_count=41)
2025-07-24 04:53:00,552 - INFO -    - hasattr lightning_successful_cards: True
2025-07-24 04:53:00,552 - INFO -    - bool(lightning_successful_cards): True (عدد=41)
2025-07-24 04:53:00,552 - INFO - 🔍 نتيجة تقييم شروط زر الحذف: True
2025-07-24 04:53:00,552 - INFO - 🗑️ سيتم إضافة زر حذف الكروت المرسلة بنجاح لـ 41 كرت ناجح
2025-07-24 04:53:00,553 - INFO - 🔄 بدء إرسال إشعار البرق مع زر حذف الكروت المرسلة بنجاح
2025-07-24 04:53:00,554 - INFO - 🔍 تشخيص معاملات دالة إرسال الإشعار مع زر الحذف:
2025-07-24 04:53:00,554 - INFO -    - bot_token: موجود
2025-07-24 04:53:00,555 - INFO -    - chat_id: 998535391
2025-07-24 04:53:00,555 - INFO -    - message: موجود (طول: 584)
2025-07-24 04:53:00,555 - INFO -    - success_count: 41
2025-07-24 04:53:00,555 - INFO -    - failed_count: 159
2025-07-24 04:53:00,556 - INFO - 🔍 تم إنشاء لوحة المفاتيح مع callback_data: lightning_delete_successful_41
2025-07-24 04:53:00,557 - INFO - 🌐 إرسال طلب إلى Telegram API...
2025-07-24 04:53:00,806 - INFO - ✅ تم إرسال إشعار البرق مع زر حذف الكروت المرسلة بنجاح
2025-07-24 04:53:00,806 - INFO - ✅ تم إرسال إشعار التأكيد بنجاح عبر التلجرام: 200 كرت، حالة الإرسال: False
2025-07-24 04:53:00,807 - INFO - 🗑️ تم إضافة زر حذف الكروت المرسلة بنجاح لـ 41 كرت مرسل بنجاح
2025-07-24 04:53:00,987 - INFO - تم إرسال 200 كرت عبر البرق الموحد باستخدام قالب 10
2025-07-24 04:53:04,964 - INFO - معالجة callback: lightning_delete_successful_41
2025-07-24 04:53:05,235 - INFO - 🗑️ معالجة callback لحذف الكروت المرسلة بنجاح: lightning_delete_successful_41
2025-07-24 04:53:05,235 - INFO - 🗑️ طلب حذف أولي للكروت المرسلة بنجاح: 41 كرت
2025-07-24 04:53:05,236 - INFO - 🗑️ بدء معالجة طلب حذف الكروت المرسلة بنجاح: 41 كرت
2025-07-24 04:53:05,496 - INFO - ✅ تم إرسال رسالة تأكيد حذف الكروت المرسلة بنجاح
2025-07-24 04:53:07,296 - INFO - معالجة callback: lightning_delete_successful_confirm_41
2025-07-24 04:53:07,514 - INFO - 🗑️ معالجة callback لحذف الكروت المرسلة بنجاح: lightning_delete_successful_confirm_41
2025-07-24 04:53:07,514 - INFO - ✅ تأكيد حذف الكروت المرسلة بنجاح: 41 كرت
2025-07-24 04:53:07,514 - INFO - 🗑️ بدء تنفيذ حذف الكروت المرسلة بنجاح: حذف 41 كرت من MikroTik
2025-07-24 04:53:07,754 - WARNING - الاتصال الحالي غير صالح: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 04:53:07,754 - INFO - تم قطع الاتصال مع MikroTik
2025-07-24 04:53:07,756 - INFO - محاولة الاتصال بـ *********:8728 (عادي)
2025-07-24 04:53:07,818 - INFO - نجح الاتصال مع *********
2025-07-24 04:53:07,818 - INFO - 🗑️ بدء حذف 41 كرت ناجح من MikroTik...
2025-07-24 04:53:09,788 - INFO - 🗑️ نتائج الحذف: تم حذف 41 من 41 كرت (معدل النجاح: 100.0%)
2025-07-24 04:53:10,041 - INFO - 🗑️ تم تنفيذ حذف الكروت المرسلة بنجاح: حُذف 41/41 كرت
2025-07-24 05:13:38,151 - INFO - تم بدء تشغيل التطبيق
2025-07-24 05:13:38,247 - INFO - تم إنشاء المجلدات الأساسية
2025-07-24 05:13:39,653 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-24 05:13:39,653 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-24 05:13:40,563 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-24 05:13:46,783 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-24 05:13:46,784 - INFO - تم إعداد التطبيق بنجاح
2025-07-24 05:13:48,836 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-24 05:13:49,216 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-24 05:13:49,217 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-24 05:13:52,222 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-24 05:13:52,251 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-24 05:13:52,502 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-24 05:13:52,503 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-24 05:13:56,210 - INFO - معالجة callback: single_card
2025-07-24 05:13:56,465 - INFO - 🎴 بدء معالجة طلب إنشاء كروت
2025-07-24 05:13:56,466 - INFO - 🔢 عرض خيارات عدد الكروت
2025-07-24 05:13:56,711 - INFO - ✅ تم عرض خيارات عدد الكروت
2025-07-24 05:13:59,048 - INFO - معالجة callback: card_count_20
2025-07-24 05:13:59,256 - INFO - 📋 عرض قوالب Hotspot لإنشاء 20 كرت
2025-07-24 05:13:59,516 - INFO - ✅ تم إرسال قائمة قوالب لإنشاء 20 كرت - 5 قالب
2025-07-24 05:14:00,495 - INFO - معالجة callback: cards_template_20_10
2025-07-24 05:14:00,705 - INFO - 🎴 بدء إنشاء 20 كرت باستخدام القالب: 10
2025-07-24 05:14:00,976 - INFO - 🔄 تفعيل نظام Hotspot مع تحديث الواجهة...
2025-07-24 05:14:00,977 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-24 05:14:01,065 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-24 05:14:01,166 - WARNING - تحذير: فشل في حفظ الإعدادات: 'MikroTikCardGenerator' object has no attribute 'version_combo'
2025-07-24 05:14:01,166 - INFO - تم تعيين النظام الجديد: hotspot
2025-07-24 05:14:01,167 - INFO - ✅ تم تبديل النظام من None إلى hotspot
2025-07-24 05:14:01,167 - INFO - 🔄 إعادة بناء الواجهة للنظام: hotspot
2025-07-24 05:14:01,167 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-24 05:14:01,170 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-24 05:14:01,899 - INFO - تم إعداد تبويب عرض الكروت لـ Hotspot بنجاح
2025-07-24 05:14:02,148 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-24 05:14:02,149 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-24 05:14:02,344 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام hotspot
2025-07-24 05:14:02,344 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-24 05:14:02,345 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot
2025-07-24 05:14:02,922 - INFO - 📋 تطبيق القالب مع مزامنة شاملة: 10
2025-07-24 05:14:02,931 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-24 05:14:02,932 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-24 05:14:03,059 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-24 05:14:03,059 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-24 05:14:03,105 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-24 05:14:03,105 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-24 05:14:03,110 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-24 05:14:03,131 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-24 05:14:03,132 - INFO - ✅ تم تطبيق القالب '10' بنجاح
2025-07-24 05:14:03,134 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-24 05:14:03,138 - INFO - ✅ تم تحديث معاينة الكارت
2025-07-24 05:14:03,149 - INFO - ✅ تم تحديث حالة عناصر الواجهة
2025-07-24 05:14:03,168 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-24 05:14:03,168 - INFO - 🔢 تعيين عدد الكروت إلى 20...
2025-07-24 05:14:03,171 - INFO - ✅ تم تعيين عدد الكروت إلى 20
2025-07-24 05:14:03,173 - INFO - ✅ تم تحديث جميع المتغيرات الداخلية
2025-07-24 05:14:03,173 - INFO - 🏭 بدء توليد الكرت...
2025-07-24 05:14:03,206 - INFO - تم توليد 20 حساب
2025-07-24 05:14:03,213 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 220 (من generate_all)
2025-07-24 05:14:03,271 - INFO - 🚀 إرسال الكرت إلى MikroTik...
2025-07-24 05:14:03,287 - INFO - محاولة الاتصال بـ *********:8728 (عادي)
2025-07-24 05:14:03,348 - INFO - نجح الاتصال مع *********
2025-07-24 05:14:03,929 - INFO - ✅ تم إرسال المستخدم: 0173373145
2025-07-24 05:14:04,199 - INFO - ✅ تم إرسال المستخدم: 0109853870
2025-07-24 05:14:04,490 - INFO - ✅ تم إرسال المستخدم: 0105484891
2025-07-24 05:14:04,759 - INFO - ✅ تم إرسال المستخدم: 0122220068
2025-07-24 05:14:05,030 - INFO - ✅ تم إرسال المستخدم: 0138608004
2025-07-24 05:14:05,309 - INFO - ✅ تم إرسال المستخدم: 0100709316
2025-07-24 05:14:05,610 - INFO - ✅ تم إرسال المستخدم: 0117589853
2025-07-24 05:14:05,878 - INFO - ✅ تم إرسال المستخدم: 0114239117
2025-07-24 05:14:06,150 - INFO - ✅ تم إرسال المستخدم: 0105268436
2025-07-24 05:14:06,419 - INFO - ✅ تم إرسال المستخدم: 0127858177
2025-07-24 05:14:06,700 - INFO - ✅ تم إرسال المستخدم: 0108114070
2025-07-24 05:14:06,959 - INFO - ✅ تم إرسال المستخدم: 0198567274
2025-07-24 05:14:07,220 - INFO - ✅ تم إرسال المستخدم: 0118785002
2025-07-24 05:14:07,489 - INFO - ✅ تم إرسال المستخدم: 0126191335
2025-07-24 05:14:07,763 - INFO - ✅ تم إرسال المستخدم: 0151465404
2025-07-24 05:14:08,126 - INFO - ✅ تم إرسال المستخدم: 0189942413
2025-07-24 05:14:23,359 - ERROR - ❌ فشل في إرسال المستخدم 0176211274: timed out
2025-07-24 05:14:23,604 - ERROR - ❌ فشل في إرسال المستخدم 0164399772: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 05:14:23,840 - ERROR - ❌ فشل في إرسال المستخدم 0176306523: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 05:14:24,062 - ERROR - ❌ فشل في إرسال المستخدم 0100033522: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 05:14:24,286 - INFO - 📊 بدء إرسال التقرير النهائي: card_type=single, success=16, failed=4
2025-07-24 05:14:24,515 - INFO - ✅ تم إرسال التقرير النهائي: 16 ناجح، 4 فاشل
2025-07-24 05:14:24,516 - INFO - 💾 حفظ معلومات الكروت الفاشلة: card_type=single, count=4
2025-07-24 05:14:24,516 - ERROR - خطأ في إرسال التقرير النهائي للكروت: name 'time' is not defined
2025-07-24 05:14:24,516 - INFO - 🔍 تشخيص حفظ الكروت للكرت الواحد: failed_count=4, success_count=16, system_type=hotspot
2025-07-24 05:14:24,517 - INFO - 💾 حفظ 16 كرت ناجح لعرض خيار حذف الكروت المرسلة بنجاح للكرت الواحد
2025-07-24 05:14:24,517 - INFO - ✅ تم حفظ معلومات الكروت الناجحة للكرت الواحد: 16 كرت
2025-07-24 05:14:24,517 - INFO - 💾 حفظ 4 كرت فاشل لعرض خيار إعادة المحاولة للكرت الواحد
2025-07-24 05:14:24,518 - INFO - ✅ تم حفظ معلومات الكروت الفاشلة للكرت الواحد: 4 كرت
2025-07-24 05:14:24,518 - INFO - ✅ كرت واحد: تم إرسال 16/20 مستخدم بنجاح
2025-07-24 05:14:24,777 - INFO - ✅ تم إرسال تفاصيل 20 كرت عبر التلجرام بنجاح
2025-07-24 05:29:10,095 - INFO - تم بدء تشغيل التطبيق
2025-07-24 05:29:10,174 - INFO - تم إنشاء المجلدات الأساسية
2025-07-24 05:29:10,622 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-24 05:29:10,622 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-24 05:29:12,851 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-24 05:29:21,257 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-24 05:29:21,258 - INFO - تم إعداد التطبيق بنجاح
2025-07-24 05:29:23,339 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-24 05:29:23,934 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-24 05:29:23,935 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-24 05:29:26,950 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-24 05:29:27,003 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-24 05:29:27,239 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-24 05:29:27,240 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-24 05:29:43,238 - INFO - بدء إغلاق التطبيق
2025-07-24 05:29:43,244 - ERROR - خطأ أثناء إغلاق التطبيق: 'MikroTikCardGenerator' object has no attribute 'version_combo'
2025-07-24 05:30:01,842 - INFO - تم بدء تشغيل التطبيق
2025-07-24 05:30:01,970 - INFO - تم إنشاء المجلدات الأساسية
2025-07-24 05:30:01,973 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-24 05:30:01,980 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-24 05:30:02,084 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-24 05:30:02,643 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-24 05:30:02,645 - INFO - تم إعداد التطبيق بنجاح
2025-07-24 05:30:04,653 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-24 05:30:04,942 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-24 05:30:04,943 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-24 05:30:07,954 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-24 05:30:07,956 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-24 05:30:08,198 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-24 05:30:08,198 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-24 05:30:32,678 - INFO - معالجة callback: single_card
2025-07-24 05:30:32,897 - INFO - 🎴 بدء معالجة طلب إنشاء كروت
2025-07-24 05:30:32,898 - INFO - 🔢 عرض خيارات عدد الكروت
2025-07-24 05:30:33,170 - INFO - ✅ تم عرض خيارات عدد الكروت
2025-07-24 05:30:37,416 - INFO - معالجة callback: card_count_1
2025-07-24 05:30:37,663 - INFO - 📋 عرض قوالب Hotspot لإنشاء 1 كرت
2025-07-24 05:30:37,897 - INFO - ✅ تم إرسال قائمة قوالب لإنشاء 1 كرت - 5 قالب
2025-07-24 05:30:41,209 - INFO - معالجة callback: cards_template_1_10
2025-07-24 05:30:41,483 - INFO - 🎴 بدء إنشاء 1 كرت باستخدام القالب: 10
2025-07-24 05:30:41,734 - INFO - 🔄 تفعيل نظام Hotspot مع تحديث الواجهة...
2025-07-24 05:30:41,736 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-24 05:30:41,836 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-24 05:30:41,937 - WARNING - تحذير: فشل في حفظ الإعدادات: 'MikroTikCardGenerator' object has no attribute 'version_combo'
2025-07-24 05:30:41,937 - INFO - تم تعيين النظام الجديد: hotspot
2025-07-24 05:30:41,938 - INFO - ✅ تم تبديل النظام من None إلى hotspot
2025-07-24 05:30:41,938 - INFO - 🔄 إعادة بناء الواجهة للنظام: hotspot
2025-07-24 05:30:41,938 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-24 05:30:41,941 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-24 05:30:42,630 - INFO - تم إعداد تبويب عرض الكروت لـ Hotspot بنجاح
2025-07-24 05:30:42,808 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-24 05:30:42,808 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-24 05:30:43,012 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام hotspot
2025-07-24 05:30:43,013 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-24 05:30:43,013 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot
2025-07-24 05:30:43,526 - INFO - 📋 تطبيق القالب مع مزامنة شاملة: 10
2025-07-24 05:30:43,528 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-24 05:30:43,529 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-24 05:30:43,589 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-24 05:30:43,589 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-24 05:30:43,628 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-24 05:30:43,629 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-24 05:30:43,632 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-24 05:30:43,637 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-24 05:30:43,638 - INFO - ✅ تم تطبيق القالب '10' بنجاح
2025-07-24 05:30:43,638 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-24 05:30:43,644 - INFO - ✅ تم تحديث معاينة الكارت
2025-07-24 05:30:43,668 - INFO - ✅ تم تحديث حالة عناصر الواجهة
2025-07-24 05:30:43,672 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-24 05:30:43,672 - INFO - 🔢 تعيين عدد الكروت إلى 1...
2025-07-24 05:30:43,674 - INFO - ✅ تم تعيين عدد الكروت إلى 1
2025-07-24 05:30:43,675 - INFO - ✅ تم تحديث جميع المتغيرات الداخلية
2025-07-24 05:30:43,675 - INFO - 🏭 بدء توليد الكرت...
2025-07-24 05:30:43,700 - INFO - تم توليد 1 حساب
2025-07-24 05:30:43,701 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 221 (من generate_all)
2025-07-24 05:30:43,718 - INFO - 🚀 إرسال الكرت إلى MikroTik...
2025-07-24 05:30:43,726 - INFO - محاولة الاتصال بـ *********:8728 (عادي)
2025-07-24 05:30:43,793 - INFO - نجح الاتصال مع *********
2025-07-24 05:30:44,442 - INFO - ✅ تم إرسال المستخدم: 0197354699
2025-07-24 05:30:44,725 - INFO - 📊 بدء إرسال التقرير النهائي: card_type=single, success=1, failed=0
2025-07-24 05:30:44,955 - INFO - ✅ تم إرسال التقرير النهائي: 1 ناجح، 0 فاشل
2025-07-24 05:30:44,955 - INFO - ℹ️ لا توجد كروت فاشلة لحفظها: failed_cards=0
2025-07-24 05:30:44,956 - INFO - 🔍 تشخيص حفظ الكروت للكرت الواحد: failed_count=0, success_count=1, system_type=hotspot
2025-07-24 05:30:44,956 - INFO - 💾 حفظ 1 كرت ناجح لعرض خيار حذف الكروت المرسلة بنجاح للكرت الواحد
2025-07-24 05:30:44,956 - INFO - ✅ تم حفظ معلومات الكروت الناجحة للكرت الواحد: 1 كرت
2025-07-24 05:30:44,957 - INFO - ❌ لم يتم حفظ الكروت الفاشلة للكرت الواحد - الشروط غير مستوفاة
2025-07-24 05:30:44,957 - INFO - ✅ كرت واحد: تم إرسال 1/1 مستخدم بنجاح
2025-07-24 05:30:44,957 - INFO - 🔍 تشخيص شروط أزرار إدارة الكروت للكرت الواحد:
2025-07-24 05:30:44,957 - INFO -    - failed_count > 0: False (failed_count=0)
2025-07-24 05:30:44,958 - INFO -    - success_count > 0: True (success_count=1)
2025-07-24 05:30:44,958 - INFO -    - hasattr failed_cards_info: False
2025-07-24 05:30:44,958 - INFO - 🔍 تشخيص شروط زر حذف الكروت الناجحة للكرت الواحد:
2025-07-24 05:30:44,958 - INFO -    - success_count > 0: True (success_count=1)
2025-07-24 05:30:44,959 - INFO -    - system_type == 'hotspot': True
2025-07-24 05:30:44,959 - INFO -    - hasattr single_card_successful_cards: True
2025-07-24 05:30:44,959 - INFO -    - bool(single_card_successful_cards): True (عدد=1)
2025-07-24 05:30:44,960 - INFO - 🔍 نتيجة تقييم شروط الأزرار للكرت الواحد:
2025-07-24 05:30:44,969 - INFO -    - show_retry_failed_button: False
2025-07-24 05:30:44,970 - INFO -    - show_delete_successful_button: True
2025-07-24 05:30:45,217 - INFO - 🗑️ تم إضافة زر حذف الكروت المرسلة بنجاح للكرت الواحد: 1 كرت
2025-07-24 05:30:45,217 - INFO - ✅ تم إرسال تفاصيل الكرت الواحد عبر التلجرام بنجاح
2025-07-24 05:31:00,712 - INFO - معالجة callback: single_card_delete_successful_1
2025-07-24 05:31:00,938 - INFO - 🗑️ معالجة callback لحذف الكروت المرسلة بنجاح للكرت الواحد: single_card_delete_successful_1
2025-07-24 05:31:00,938 - INFO - 🗑️ طلب حذف أولي للكروت المرسلة بنجاح للكرت الواحد: 1 كرت
2025-07-24 05:31:00,939 - INFO - 🗑️ بدء معالجة طلب حذف الكروت المرسلة بنجاح للكرت الواحد: 1 كرت
2025-07-24 05:31:01,180 - INFO - ✅ تم إرسال رسالة تأكيد حذف الكروت المرسلة بنجاح للكرت الواحد
2025-07-24 05:31:03,803 - INFO - معالجة callback: single_card_delete_successful_confirm_1
2025-07-24 05:31:04,011 - INFO - 🗑️ معالجة callback لحذف الكروت المرسلة بنجاح للكرت الواحد: single_card_delete_successful_confirm_1
2025-07-24 05:31:04,012 - INFO - ✅ تأكيد حذف الكروت المرسلة بنجاح للكرت الواحد: 1 كرت
2025-07-24 05:31:04,012 - INFO - 🗑️ بدء تنفيذ حذف الكروت المرسلة بنجاح للكرت الواحد: حذف 1 كرت من MikroTik
2025-07-24 05:31:04,038 - WARNING - الاتصال الحالي غير صالح: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 05:31:04,039 - INFO - تم قطع الاتصال مع MikroTik
2025-07-24 05:31:04,041 - INFO - محاولة الاتصال بـ *********:8728 (عادي)
2025-07-24 05:31:04,103 - INFO - نجح الاتصال مع *********
2025-07-24 05:31:04,104 - INFO - 🗑️ بدء حذف 1 كرت ناجح من MikroTik...
2025-07-24 05:31:04,182 - INFO - 🗑️ نتائج الحذف: تم حذف 1 من 1 كرت (معدل النجاح: 100.0%)
2025-07-24 05:31:04,454 - INFO - 🗑️ تم تنفيذ حذف الكروت المرسلة بنجاح للكرت الواحد: حُذف 1/1 كرت
2025-07-24 05:31:15,049 - INFO - معالجة callback: single_card
2025-07-24 05:31:15,262 - INFO - 🎴 بدء معالجة طلب إنشاء كروت
2025-07-24 05:31:15,263 - INFO - 🔢 عرض خيارات عدد الكروت
2025-07-24 05:31:15,536 - INFO - ✅ تم عرض خيارات عدد الكروت
2025-07-24 05:31:19,211 - INFO - معالجة callback: card_count_20
2025-07-24 05:31:19,457 - INFO - 📋 عرض قوالب Hotspot لإنشاء 20 كرت
2025-07-24 05:31:19,701 - INFO - ✅ تم إرسال قائمة قوالب لإنشاء 20 كرت - 5 قالب
2025-07-24 05:31:34,826 - INFO - معالجة callback: cards_template_20_10
2025-07-24 05:31:35,055 - INFO - 🎴 بدء إنشاء 20 كرت باستخدام القالب: 10
2025-07-24 05:31:35,295 - INFO - 🔄 تفعيل نظام Hotspot مع تحديث الواجهة...
2025-07-24 05:31:35,297 - INFO - النظام hotspot مفعل بالفعل
2025-07-24 05:31:35,297 - INFO - ✅ تم تبديل النظام من hotspot إلى hotspot
2025-07-24 05:31:35,298 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot
2025-07-24 05:31:35,356 - INFO - 📋 تطبيق القالب مع مزامنة شاملة: 10
2025-07-24 05:31:35,358 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-24 05:31:35,358 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-24 05:31:35,391 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-24 05:31:35,393 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-24 05:31:35,441 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-24 05:31:35,441 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-24 05:31:35,445 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-24 05:31:35,449 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-24 05:31:35,449 - INFO - ✅ تم تطبيق القالب '10' بنجاح
2025-07-24 05:31:35,450 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-24 05:31:35,466 - INFO - ✅ تم تحديث معاينة الكارت
2025-07-24 05:31:35,479 - INFO - ✅ تم تحديث حالة عناصر الواجهة
2025-07-24 05:31:35,482 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-24 05:31:35,483 - INFO - 🔢 تعيين عدد الكروت إلى 20...
2025-07-24 05:31:35,484 - INFO - ✅ تم تعيين عدد الكروت إلى 20
2025-07-24 05:31:35,485 - INFO - ✅ تم تحديث جميع المتغيرات الداخلية
2025-07-24 05:31:35,485 - INFO - 🏭 بدء توليد الكرت...
2025-07-24 05:31:35,507 - INFO - تم توليد 20 حساب
2025-07-24 05:31:35,508 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 241 (من generate_all)
2025-07-24 05:31:35,514 - INFO - 🚀 إرسال الكرت إلى MikroTik...
2025-07-24 05:31:35,517 - INFO - استخدام الاتصال الحالي
2025-07-24 05:31:36,036 - INFO - ✅ تم إرسال المستخدم: 0164441176
2025-07-24 05:31:36,314 - INFO - ✅ تم إرسال المستخدم: 0170653256
2025-07-24 05:31:36,586 - INFO - ✅ تم إرسال المستخدم: 0101305955
2025-07-24 05:31:36,874 - INFO - ✅ تم إرسال المستخدم: 0159955992
2025-07-24 05:31:37,146 - INFO - ✅ تم إرسال المستخدم: 0131985323
2025-07-24 05:31:37,414 - INFO - ✅ تم إرسال المستخدم: 0156762827
2025-07-24 05:31:37,701 - INFO - ✅ تم إرسال المستخدم: 0143925289
2025-07-24 05:31:37,964 - INFO - ✅ تم إرسال المستخدم: 0139500451
2025-07-24 05:31:38,235 - INFO - ✅ تم إرسال المستخدم: 0130353425
2025-07-24 05:31:38,514 - INFO - ✅ تم إرسال المستخدم: 0190552934
2025-07-24 05:31:38,785 - INFO - ✅ تم إرسال المستخدم: 0172971291
2025-07-24 05:31:39,104 - INFO - ✅ تم إرسال المستخدم: 0130680897
2025-07-24 05:31:39,375 - INFO - ✅ تم إرسال المستخدم: 0161626152
2025-07-24 05:31:39,664 - INFO - ✅ تم إرسال المستخدم: 0154915188
2025-07-24 05:31:39,935 - INFO - ✅ تم إرسال المستخدم: 0101835146
2025-07-24 05:31:40,204 - INFO - ✅ تم إرسال المستخدم: 0112056254
2025-07-24 05:31:40,485 - INFO - ✅ تم إرسال المستخدم: 0179219271
2025-07-24 05:31:40,764 - INFO - ✅ تم إرسال المستخدم: 0183753457
2025-07-24 05:31:41,026 - INFO - ✅ تم إرسال المستخدم: 0176380795
2025-07-24 05:31:41,354 - INFO - ✅ تم إرسال المستخدم: 0159281988
2025-07-24 05:31:41,585 - INFO - 📊 بدء إرسال التقرير النهائي: card_type=single, success=20, failed=0
2025-07-24 05:31:41,812 - INFO - ✅ تم إرسال التقرير النهائي: 20 ناجح، 0 فاشل
2025-07-24 05:31:41,812 - INFO - ℹ️ لا توجد كروت فاشلة لحفظها: failed_cards=0
2025-07-24 05:31:41,813 - INFO - 🔍 تشخيص حفظ الكروت للكرت الواحد: failed_count=0, success_count=20, system_type=hotspot
2025-07-24 05:31:41,813 - INFO - 💾 حفظ 20 كرت ناجح لعرض خيار حذف الكروت المرسلة بنجاح للكرت الواحد
2025-07-24 05:31:41,814 - INFO - ✅ تم حفظ معلومات الكروت الناجحة للكرت الواحد: 20 كرت
2025-07-24 05:31:41,814 - INFO - ❌ لم يتم حفظ الكروت الفاشلة للكرت الواحد - الشروط غير مستوفاة
2025-07-24 05:31:41,814 - INFO - ✅ كرت واحد: تم إرسال 20/20 مستخدم بنجاح
2025-07-24 05:31:42,093 - INFO - ✅ تم إرسال تفاصيل 20 كرت عبر التلجرام بنجاح
2025-07-24 05:32:01,408 - INFO - معالجة callback: single_card
2025-07-24 05:32:01,619 - INFO - 🎴 بدء معالجة طلب إنشاء كروت
2025-07-24 05:32:01,620 - INFO - 🔢 عرض خيارات عدد الكروت
2025-07-24 05:32:01,859 - INFO - ✅ تم عرض خيارات عدد الكروت
2025-07-24 05:32:04,926 - INFO - معالجة callback: card_count_1
2025-07-24 05:32:05,134 - INFO - 📋 عرض قوالب Hotspot لإنشاء 1 كرت
2025-07-24 05:32:05,365 - INFO - ✅ تم إرسال قائمة قوالب لإنشاء 1 كرت - 5 قالب
2025-07-24 05:32:06,713 - INFO - معالجة callback: cards_template_1_10
2025-07-24 05:32:07,003 - INFO - 🎴 بدء إنشاء 1 كرت باستخدام القالب: 10
2025-07-24 05:32:07,284 - INFO - 🔄 تفعيل نظام Hotspot مع تحديث الواجهة...
2025-07-24 05:32:07,285 - INFO - النظام hotspot مفعل بالفعل
2025-07-24 05:32:07,285 - INFO - ✅ تم تبديل النظام من hotspot إلى hotspot
2025-07-24 05:32:07,286 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot
2025-07-24 05:32:07,356 - INFO - 📋 تطبيق القالب مع مزامنة شاملة: 10
2025-07-24 05:32:07,359 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-24 05:32:07,359 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-24 05:32:07,376 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-24 05:32:07,390 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-24 05:32:07,442 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-24 05:32:07,443 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-24 05:32:07,447 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-24 05:32:07,451 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-24 05:32:07,452 - INFO - ✅ تم تطبيق القالب '10' بنجاح
2025-07-24 05:32:07,470 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-24 05:32:07,474 - INFO - ✅ تم تحديث معاينة الكارت
2025-07-24 05:32:07,487 - INFO - ✅ تم تحديث حالة عناصر الواجهة
2025-07-24 05:32:07,503 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-24 05:32:07,504 - INFO - 🔢 تعيين عدد الكروت إلى 1...
2025-07-24 05:32:07,505 - INFO - ✅ تم تعيين عدد الكروت إلى 1
2025-07-24 05:32:07,505 - INFO - ✅ تم تحديث جميع المتغيرات الداخلية
2025-07-24 05:32:07,505 - INFO - 🏭 بدء توليد الكرت...
2025-07-24 05:32:07,512 - INFO - تم توليد 1 حساب
2025-07-24 05:32:07,513 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 242 (من generate_all)
2025-07-24 05:32:07,519 - INFO - 🚀 إرسال الكرت إلى MikroTik...
2025-07-24 05:32:07,519 - WARNING - الاتصال الحالي غير صالح: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 05:32:07,520 - INFO - تم قطع الاتصال مع MikroTik
2025-07-24 05:32:07,534 - INFO - محاولة الاتصال بـ *********:8728 (عادي)
2025-07-24 05:32:07,594 - INFO - نجح الاتصال مع *********
2025-07-24 05:32:08,207 - INFO - ✅ تم إرسال المستخدم: 0101255755
2025-07-24 05:32:08,469 - INFO - 📊 بدء إرسال التقرير النهائي: card_type=single, success=1, failed=0
2025-07-24 05:32:08,730 - INFO - ✅ تم إرسال التقرير النهائي: 1 ناجح، 0 فاشل
2025-07-24 05:32:08,730 - INFO - ℹ️ لا توجد كروت فاشلة لحفظها: failed_cards=0
2025-07-24 05:32:08,731 - INFO - 🔍 تشخيص حفظ الكروت للكرت الواحد: failed_count=0, success_count=1, system_type=hotspot
2025-07-24 05:32:08,731 - INFO - 💾 حفظ 1 كرت ناجح لعرض خيار حذف الكروت المرسلة بنجاح للكرت الواحد
2025-07-24 05:32:08,731 - INFO - ✅ تم حفظ معلومات الكروت الناجحة للكرت الواحد: 1 كرت
2025-07-24 05:32:08,732 - INFO - ❌ لم يتم حفظ الكروت الفاشلة للكرت الواحد - الشروط غير مستوفاة
2025-07-24 05:32:08,732 - INFO - ✅ كرت واحد: تم إرسال 1/1 مستخدم بنجاح
2025-07-24 05:32:08,732 - INFO - 🔍 تشخيص شروط أزرار إدارة الكروت للكرت الواحد:
2025-07-24 05:32:08,733 - INFO -    - failed_count > 0: False (failed_count=0)
2025-07-24 05:32:08,733 - INFO -    - success_count > 0: True (success_count=1)
2025-07-24 05:32:08,733 - INFO -    - hasattr failed_cards_info: False
2025-07-24 05:32:08,733 - INFO - 🔍 تشخيص شروط زر حذف الكروت الناجحة للكرت الواحد:
2025-07-24 05:32:08,734 - INFO -    - success_count > 0: True (success_count=1)
2025-07-24 05:32:08,734 - INFO -    - system_type == 'hotspot': True
2025-07-24 05:32:08,734 - INFO -    - hasattr single_card_successful_cards: True
2025-07-24 05:32:08,734 - INFO -    - bool(single_card_successful_cards): True (عدد=1)
2025-07-24 05:32:08,735 - INFO - 🔍 نتيجة تقييم شروط الأزرار للكرت الواحد:
2025-07-24 05:32:08,735 - INFO -    - show_retry_failed_button: False
2025-07-24 05:32:08,735 - INFO -    - show_delete_successful_button: True
2025-07-24 05:32:08,994 - INFO - 🗑️ تم إضافة زر حذف الكروت المرسلة بنجاح للكرت الواحد: 1 كرت
2025-07-24 05:32:08,996 - INFO - ✅ تم إرسال تفاصيل الكرت الواحد عبر التلجرام بنجاح
2025-07-24 05:32:13,787 - INFO - معالجة callback: single_card
2025-07-24 05:32:13,998 - INFO - 🎴 بدء معالجة طلب إنشاء كروت
2025-07-24 05:32:13,999 - INFO - 🔢 عرض خيارات عدد الكروت
2025-07-24 05:32:14,277 - INFO - ✅ تم عرض خيارات عدد الكروت
2025-07-24 05:32:16,752 - INFO - معالجة callback: card_count_2
2025-07-24 05:32:16,967 - INFO - 📋 عرض قوالب Hotspot لإنشاء 2 كرت
2025-07-24 05:32:17,234 - INFO - ✅ تم إرسال قائمة قوالب لإنشاء 2 كرت - 5 قالب
2025-07-24 05:32:18,839 - INFO - معالجة callback: cards_template_2_10
2025-07-24 05:32:19,050 - INFO - 🎴 بدء إنشاء 2 كرت باستخدام القالب: 10
2025-07-24 05:32:19,291 - INFO - 🔄 تفعيل نظام Hotspot مع تحديث الواجهة...
2025-07-24 05:32:19,292 - INFO - النظام hotspot مفعل بالفعل
2025-07-24 05:32:19,292 - INFO - ✅ تم تبديل النظام من hotspot إلى hotspot
2025-07-24 05:32:19,293 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot
2025-07-24 05:32:19,339 - INFO - 📋 تطبيق القالب مع مزامنة شاملة: 10
2025-07-24 05:32:19,340 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-24 05:32:19,340 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-24 05:32:19,371 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-24 05:32:19,372 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-24 05:32:19,415 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-24 05:32:19,416 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-24 05:32:19,419 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-24 05:32:19,424 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-24 05:32:19,424 - INFO - ✅ تم تطبيق القالب '10' بنجاح
2025-07-24 05:32:19,440 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-24 05:32:19,444 - INFO - ✅ تم تحديث معاينة الكارت
2025-07-24 05:32:19,457 - INFO - ✅ تم تحديث حالة عناصر الواجهة
2025-07-24 05:32:19,471 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-24 05:32:19,472 - INFO - 🔢 تعيين عدد الكروت إلى 2...
2025-07-24 05:32:19,474 - INFO - ✅ تم تعيين عدد الكروت إلى 2
2025-07-24 05:32:19,474 - INFO - ✅ تم تحديث جميع المتغيرات الداخلية
2025-07-24 05:32:19,474 - INFO - 🏭 بدء توليد الكرت...
2025-07-24 05:32:19,480 - INFO - تم توليد 2 حساب
2025-07-24 05:32:19,482 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 244 (من generate_all)
2025-07-24 05:32:19,488 - INFO - 🚀 إرسال الكرت إلى MikroTik...
2025-07-24 05:32:19,488 - WARNING - الاتصال الحالي غير صالح: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 05:32:19,489 - INFO - تم قطع الاتصال مع MikroTik
2025-07-24 05:32:19,503 - INFO - محاولة الاتصال بـ *********:8728 (عادي)
2025-07-24 05:32:19,564 - INFO - نجح الاتصال مع *********
2025-07-24 05:32:20,138 - INFO - ✅ تم إرسال المستخدم: 0159752181
2025-07-24 05:32:20,405 - INFO - ✅ تم إرسال المستخدم: 0190835926
2025-07-24 05:32:20,628 - INFO - 📊 بدء إرسال التقرير النهائي: card_type=single, success=2, failed=0
2025-07-24 05:32:20,857 - INFO - ✅ تم إرسال التقرير النهائي: 2 ناجح، 0 فاشل
2025-07-24 05:32:20,858 - INFO - ℹ️ لا توجد كروت فاشلة لحفظها: failed_cards=0
2025-07-24 05:32:20,858 - INFO - 🔍 تشخيص حفظ الكروت للكرت الواحد: failed_count=0, success_count=2, system_type=hotspot
2025-07-24 05:32:20,858 - INFO - 💾 حفظ 2 كرت ناجح لعرض خيار حذف الكروت المرسلة بنجاح للكرت الواحد
2025-07-24 05:32:20,859 - INFO - ✅ تم حفظ معلومات الكروت الناجحة للكرت الواحد: 2 كرت
2025-07-24 05:32:20,859 - INFO - ❌ لم يتم حفظ الكروت الفاشلة للكرت الواحد - الشروط غير مستوفاة
2025-07-24 05:32:20,859 - INFO - ✅ كرت واحد: تم إرسال 2/2 مستخدم بنجاح
2025-07-24 05:32:21,133 - INFO - ✅ تم إرسال تفاصيل 2 كرت عبر التلجرام بنجاح
2025-07-24 05:33:43,274 - INFO - تم بدء تشغيل التطبيق
2025-07-24 05:33:43,715 - INFO - تم إنشاء المجلدات الأساسية
2025-07-24 05:33:43,718 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-24 05:33:43,718 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-24 05:33:43,821 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-24 05:33:44,267 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-24 05:33:44,268 - INFO - تم إعداد التطبيق بنجاح
2025-07-24 05:33:46,267 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-24 05:33:46,578 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-24 05:33:46,581 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-24 05:33:49,585 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-24 05:33:49,586 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-24 05:33:49,846 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-24 05:33:49,846 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-24 05:33:53,978 - INFO - معالجة callback: single_card
2025-07-24 05:33:54,221 - INFO - 🎴 بدء معالجة طلب إنشاء كروت
2025-07-24 05:33:54,221 - INFO - 🔢 عرض خيارات عدد الكروت
2025-07-24 05:33:54,460 - INFO - ✅ تم عرض خيارات عدد الكروت
2025-07-24 05:33:55,610 - INFO - معالجة callback: card_count_1
2025-07-24 05:33:55,822 - INFO - 📋 عرض قوالب Hotspot لإنشاء 1 كرت
2025-07-24 05:33:56,074 - INFO - ✅ تم إرسال قائمة قوالب لإنشاء 1 كرت - 5 قالب
2025-07-24 05:33:57,663 - INFO - معالجة callback: cards_template_1_10
2025-07-24 05:33:57,880 - INFO - 🎴 بدء إنشاء 1 كرت باستخدام القالب: 10
2025-07-24 05:33:58,129 - INFO - 🔄 تفعيل نظام Hotspot مع تحديث الواجهة...
2025-07-24 05:33:58,131 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-24 05:33:58,147 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-24 05:33:58,248 - WARNING - تحذير: فشل في حفظ الإعدادات: 'MikroTikCardGenerator' object has no attribute 'version_combo'
2025-07-24 05:33:58,248 - INFO - تم تعيين النظام الجديد: hotspot
2025-07-24 05:33:58,249 - INFO - 🔄 إعادة بناء الواجهة للنظام: hotspot
2025-07-24 05:33:58,249 - INFO - ✅ تم تبديل النظام من None إلى hotspot
2025-07-24 05:33:58,249 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-24 05:33:58,251 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-24 05:33:58,777 - INFO - تم إعداد تبويب عرض الكروت لـ Hotspot بنجاح
2025-07-24 05:33:58,922 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-24 05:33:58,923 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-24 05:33:59,047 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام hotspot
2025-07-24 05:33:59,048 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-24 05:33:59,050 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot
2025-07-24 05:33:59,360 - INFO - 📋 تطبيق القالب مع مزامنة شاملة: 10
2025-07-24 05:33:59,361 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-24 05:33:59,362 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-24 05:33:59,447 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-24 05:33:59,447 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-24 05:33:59,474 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-24 05:33:59,474 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-24 05:33:59,477 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-24 05:33:59,481 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-24 05:33:59,481 - INFO - ✅ تم تطبيق القالب '10' بنجاح
2025-07-24 05:33:59,482 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-24 05:33:59,486 - INFO - ✅ تم تحديث معاينة الكارت
2025-07-24 05:33:59,496 - INFO - ✅ تم تحديث حالة عناصر الواجهة
2025-07-24 05:33:59,499 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-24 05:33:59,500 - INFO - 🔢 تعيين عدد الكروت إلى 1...
2025-07-24 05:33:59,501 - INFO - ✅ تم تعيين عدد الكروت إلى 1
2025-07-24 05:33:59,502 - INFO - ✅ تم تحديث جميع المتغيرات الداخلية
2025-07-24 05:33:59,502 - INFO - 🏭 بدء توليد الكرت...
2025-07-24 05:33:59,507 - INFO - تم توليد 1 حساب
2025-07-24 05:33:59,507 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 245 (من generate_all)
2025-07-24 05:33:59,513 - INFO - 🚀 إرسال الكرت إلى MikroTik...
2025-07-24 05:33:59,513 - INFO - محاولة الاتصال بـ *********:8728 (عادي)
2025-07-24 05:33:59,575 - INFO - نجح الاتصال مع *********
2025-07-24 05:34:00,218 - INFO - ✅ تم إرسال المستخدم: 0177748239
2025-07-24 05:34:00,450 - INFO - 📊 بدء إرسال التقرير النهائي: card_type=single, success=1, failed=0
2025-07-24 05:34:00,684 - INFO - ✅ تم إرسال التقرير النهائي: 1 ناجح، 0 فاشل
2025-07-24 05:34:00,685 - INFO - ℹ️ لا توجد كروت فاشلة لحفظها: failed_cards=0
2025-07-24 05:34:00,685 - INFO - 🔍 تشخيص حفظ الكروت للكرت الواحد: failed_count=0, success_count=1, system_type=hotspot
2025-07-24 05:34:00,685 - INFO - ❌ لم يتم حفظ الكروت الناجحة للكرت الواحد - الشروط غير مستوفاة
2025-07-24 05:34:00,686 - INFO - ❌ لم يتم حفظ الكروت الفاشلة للكرت الواحد - الشروط غير مستوفاة
2025-07-24 05:34:00,686 - INFO - ✅ كرت واحد: تم إرسال 1/1 مستخدم بنجاح
2025-07-24 05:34:00,686 - INFO - 🔍 تشخيص شروط أزرار إدارة الكروت للكرت الواحد:
2025-07-24 05:34:00,686 - INFO -    - failed_count > 0: False (failed_count=0)
2025-07-24 05:34:00,686 - INFO -    - success_count > 0: True (success_count=1)
2025-07-24 05:34:00,687 - INFO -    - hasattr single_card_successful_cards: False
2025-07-24 05:34:00,687 - INFO -    - hasattr failed_cards_info: False
2025-07-24 05:34:00,687 - INFO - 🔍 نتيجة تقييم شروط الأزرار للكرت الواحد:
2025-07-24 05:34:00,687 - INFO -    - show_delete_successful_button: False
2025-07-24 05:34:00,687 - INFO -    - show_retry_failed_button: False
2025-07-24 05:34:00,945 - INFO - ✅ تم إرسال تفاصيل الكرت الواحد عبر التلجرام بنجاح
2025-07-24 05:34:36,678 - INFO - معالجة callback: card_count_20
2025-07-24 05:34:36,888 - INFO - 📋 عرض قوالب Hotspot لإنشاء 20 كرت
2025-07-24 05:34:37,143 - INFO - ✅ تم إرسال قائمة قوالب لإنشاء 20 كرت - 5 قالب
2025-07-24 05:34:39,131 - INFO - معالجة callback: cards_template_20_10
2025-07-24 05:34:39,340 - INFO - 🎴 بدء إنشاء 20 كرت باستخدام القالب: 10
2025-07-24 05:34:39,586 - INFO - 🔄 تفعيل نظام Hotspot مع تحديث الواجهة...
2025-07-24 05:34:39,588 - INFO - النظام hotspot مفعل بالفعل
2025-07-24 05:34:39,588 - INFO - ✅ تم تبديل النظام من hotspot إلى hotspot
2025-07-24 05:34:39,589 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot
2025-07-24 05:34:39,630 - INFO - 📋 تطبيق القالب مع مزامنة شاملة: 10
2025-07-24 05:34:39,631 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-24 05:34:39,631 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-24 05:34:39,650 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-24 05:34:39,651 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-24 05:34:39,680 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-24 05:34:39,681 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-24 05:34:39,684 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-24 05:34:39,689 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-24 05:34:39,689 - INFO - ✅ تم تطبيق القالب '10' بنجاح
2025-07-24 05:34:39,689 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-24 05:34:39,693 - INFO - ✅ تم تحديث معاينة الكارت
2025-07-24 05:34:39,705 - INFO - ✅ تم تحديث حالة عناصر الواجهة
2025-07-24 05:34:39,710 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-24 05:34:39,717 - INFO - 🔢 تعيين عدد الكروت إلى 20...
2025-07-24 05:34:39,729 - INFO - ✅ تم تعيين عدد الكروت إلى 20
2025-07-24 05:34:39,729 - INFO - ✅ تم تحديث جميع المتغيرات الداخلية
2025-07-24 05:34:39,729 - INFO - 🏭 بدء توليد الكرت...
2025-07-24 05:34:39,748 - INFO - تم توليد 20 حساب
2025-07-24 05:34:39,751 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 265 (من generate_all)
2025-07-24 05:34:39,757 - INFO - 🚀 إرسال الكرت إلى MikroTik...
2025-07-24 05:34:39,758 - WARNING - الاتصال الحالي غير صالح: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 05:34:39,758 - INFO - تم قطع الاتصال مع MikroTik
2025-07-24 05:34:39,760 - INFO - محاولة الاتصال بـ *********:8728 (عادي)
2025-07-24 05:34:39,826 - INFO - نجح الاتصال مع *********
2025-07-24 05:34:40,398 - INFO - ✅ تم إرسال المستخدم: 0191344935
2025-07-24 05:34:40,666 - INFO - ✅ تم إرسال المستخدم: 0185929020
2025-07-24 05:34:40,937 - INFO - ✅ تم إرسال المستخدم: 0168727652
2025-07-24 05:34:41,226 - INFO - ✅ تم إرسال المستخدم: 0135980875
2025-07-24 05:34:41,498 - INFO - ✅ تم إرسال المستخدم: 0133267657
2025-07-24 05:34:41,786 - INFO - ✅ تم إرسال المستخدم: 0178420069
2025-07-24 05:34:42,070 - INFO - ✅ تم إرسال المستخدم: 0164000532
2025-07-24 05:34:42,337 - INFO - ✅ تم إرسال المستخدم: 0132418354
2025-07-24 05:34:42,617 - INFO - ✅ تم إرسال المستخدم: 0179014011
2025-07-24 05:34:42,886 - INFO - ✅ تم إرسال المستخدم: 0110399888
2025-07-24 05:34:43,148 - INFO - ✅ تم إرسال المستخدم: 0181005882
2025-07-24 05:34:43,416 - INFO - ✅ تم إرسال المستخدم: 0188460140
2025-07-24 05:34:43,717 - INFO - ✅ تم إرسال المستخدم: 0146018315
2025-07-24 05:34:44,026 - INFO - ✅ تم إرسال المستخدم: 0193877212
2025-07-24 05:34:44,308 - INFO - ✅ تم إرسال المستخدم: 0112001842
2025-07-24 05:34:44,593 - INFO - ✅ تم إرسال المستخدم: 0139931750
2025-07-24 05:34:59,828 - ERROR - ❌ فشل في إرسال المستخدم 0146710504: timed out
2025-07-24 05:35:00,060 - ERROR - ❌ فشل في إرسال المستخدم 0187440536: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 05:35:00,288 - ERROR - ❌ فشل في إرسال المستخدم 0184045574: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 05:35:00,515 - ERROR - ❌ فشل في إرسال المستخدم 0142074000: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 05:35:00,765 - INFO - 📊 بدء إرسال التقرير النهائي: card_type=single, success=16, failed=4
2025-07-24 05:35:01,004 - INFO - ✅ تم إرسال التقرير النهائي: 16 ناجح، 4 فاشل
2025-07-24 05:35:01,004 - INFO - 💾 حفظ معلومات الكروت الفاشلة: card_type=single, count=4
2025-07-24 05:35:01,005 - ERROR - خطأ في إرسال التقرير النهائي للكروت: name 'time' is not defined
2025-07-24 05:35:01,005 - INFO - 🔍 تشخيص حفظ الكروت للكرت الواحد: failed_count=4, success_count=16, system_type=hotspot
2025-07-24 05:35:01,005 - INFO - 💾 حفظ 16 كرت ناجح لعرض خيار حذف الكروت المرسلة بنجاح للكرت الواحد
2025-07-24 05:35:01,005 - INFO - ✅ تم حفظ معلومات الكروت الناجحة للكرت الواحد: 16 كرت
2025-07-24 05:35:01,005 - INFO - 💾 حفظ 4 كرت فاشل لعرض خيار إعادة المحاولة للكرت الواحد
2025-07-24 05:35:01,006 - INFO - ✅ تم حفظ معلومات الكروت الفاشلة للكرت الواحد: 4 كرت
2025-07-24 05:35:01,006 - INFO - ✅ كرت واحد: تم إرسال 16/20 مستخدم بنجاح
2025-07-24 05:35:01,312 - INFO - ✅ تم إرسال تفاصيل 20 كرت عبر التلجرام بنجاح
2025-07-24 05:35:18,692 - INFO - بدء إغلاق التطبيق
2025-07-24 05:35:18,693 - INFO - تم قطع الاتصال مع MikroTik
2025-07-24 05:35:18,699 - INFO - تم تسجيل العملية: إغلاق التطبيق - تم إغلاق التطبيق بنجاح
2025-07-24 05:35:18,700 - INFO - تم إغلاق التطبيق بنجاح
2025-07-24 05:50:12,071 - INFO - تم بدء تشغيل التطبيق
2025-07-24 05:50:12,115 - INFO - تم إنشاء المجلدات الأساسية
2025-07-24 05:50:12,365 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-24 05:50:12,365 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-24 05:50:14,361 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-24 05:50:25,354 - INFO - تم بدء تشغيل التطبيق
2025-07-24 05:50:25,471 - INFO - تم إنشاء المجلدات الأساسية
2025-07-24 05:50:25,473 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-24 05:50:25,475 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-24 05:50:25,578 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-24 05:50:26,878 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-24 05:50:26,878 - INFO - تم إعداد التطبيق بنجاح
2025-07-24 05:50:28,922 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-24 05:50:29,636 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-24 05:50:29,637 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-24 05:50:32,646 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-24 05:50:32,696 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-24 05:50:32,934 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-24 05:50:32,934 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-24 05:50:49,954 - INFO - معالجة callback: single_card
2025-07-24 05:50:50,216 - INFO - 🎴 بدء معالجة طلب إنشاء كروت
2025-07-24 05:50:50,217 - INFO - 🔢 عرض خيارات عدد الكروت
2025-07-24 05:50:50,451 - INFO - ✅ تم عرض خيارات عدد الكروت
2025-07-24 05:50:51,766 - INFO - معالجة callback: card_count_5
2025-07-24 05:50:51,977 - INFO - 📋 عرض قوالب Hotspot لإنشاء 5 كرت
2025-07-24 05:50:52,216 - INFO - ✅ تم إرسال قائمة قوالب لإنشاء 5 كرت - 5 قالب
2025-07-24 05:50:53,330 - INFO - معالجة callback: cards_template_5_10
2025-07-24 05:50:53,595 - INFO - 🎴 بدء إنشاء 5 كرت باستخدام القالب: 10
2025-07-24 05:50:53,886 - INFO - 🔄 تفعيل نظام Hotspot مع تحديث الواجهة...
2025-07-24 05:50:53,887 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-24 05:50:53,952 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-24 05:50:54,053 - WARNING - تحذير: فشل في حفظ الإعدادات: 'MikroTikCardGenerator' object has no attribute 'version_combo'
2025-07-24 05:50:54,053 - INFO - تم تعيين النظام الجديد: hotspot
2025-07-24 05:50:54,054 - INFO - ✅ تم تبديل النظام من None إلى hotspot
2025-07-24 05:50:54,054 - INFO - 🔄 إعادة بناء الواجهة للنظام: hotspot
2025-07-24 05:50:54,055 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-24 05:50:54,057 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-24 05:50:54,945 - INFO - تم إعداد تبويب عرض الكروت لـ Hotspot بنجاح
2025-07-24 05:50:55,107 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-24 05:50:55,108 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-24 05:50:55,264 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام hotspot
2025-07-24 05:50:55,264 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-24 05:50:55,266 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot
2025-07-24 05:50:55,754 - INFO - 📋 تطبيق القالب مع مزامنة شاملة: 10
2025-07-24 05:50:55,755 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-24 05:50:55,756 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-24 05:50:55,793 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-24 05:50:55,794 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-24 05:50:55,844 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-24 05:50:55,845 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-24 05:50:55,849 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-24 05:50:55,854 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-24 05:50:55,855 - INFO - ✅ تم تطبيق القالب '10' بنجاح
2025-07-24 05:50:55,855 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-24 05:50:55,859 - INFO - ✅ تم تحديث معاينة الكارت
2025-07-24 05:50:55,884 - INFO - ✅ تم تحديث حالة عناصر الواجهة
2025-07-24 05:50:55,888 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-24 05:50:55,888 - INFO - 🔢 تعيين عدد الكروت إلى 5...
2025-07-24 05:50:55,889 - INFO - ✅ تم تعيين عدد الكروت إلى 5
2025-07-24 05:50:55,890 - INFO - ✅ تم تحديث جميع المتغيرات الداخلية
2025-07-24 05:50:55,891 - INFO - 🏭 بدء توليد الكرت...
2025-07-24 05:50:55,911 - INFO - تم توليد 5 حساب
2025-07-24 05:50:55,912 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 270 (من generate_all)
2025-07-24 05:50:55,932 - INFO - 🚀 إرسال الكرت إلى MikroTik...
2025-07-24 05:50:55,933 - INFO - محاولة الاتصال بـ *********:8728 (عادي)
2025-07-24 05:50:55,994 - INFO - نجح الاتصال مع *********
2025-07-24 05:50:56,578 - INFO - ✅ تم إرسال المستخدم: 0183030248
2025-07-24 05:50:56,844 - INFO - ✅ تم إرسال المستخدم: 0116128023
2025-07-24 05:50:57,177 - INFO - ✅ تم إرسال المستخدم: 0161670509
2025-07-24 05:50:57,410 - INFO - ✅ تم إرسال المستخدم: 0139931867
2025-07-24 05:50:57,778 - INFO - ✅ تم إرسال المستخدم: 0119649349
2025-07-24 05:50:58,008 - INFO - 📊 بدء إرسال التقرير النهائي: card_type=single, success=5, failed=0
2025-07-24 05:50:58,233 - INFO - ✅ تم إرسال التقرير النهائي: 5 ناجح، 0 فاشل
2025-07-24 05:50:58,233 - INFO - ℹ️ لا توجد كروت فاشلة لحفظها: failed_cards=0
2025-07-24 05:50:58,234 - INFO - 🔍 تشخيص حفظ الكروت للكرت الواحد: failed_count=0, success_count=5, system_type=hotspot
2025-07-24 05:50:58,234 - INFO - 💾 حفظ 5 كرت ناجح لعرض خيار حذف الكروت المرسلة بنجاح للكرت الواحد
2025-07-24 05:50:58,234 - INFO - ✅ تم حفظ معلومات الكروت الناجحة للكرت الواحد: 5 كرت
2025-07-24 05:50:58,235 - INFO - ❌ لم يتم حفظ الكروت الفاشلة للكرت الواحد - الشروط غير مستوفاة
2025-07-24 05:50:58,235 - INFO - ✅ كرت واحد: تم إرسال 5/5 مستخدم بنجاح
2025-07-24 05:50:58,235 - INFO - 🔍 تشخيص شروط زر حذف الكروت الناجحة للكروت المتعددة:
2025-07-24 05:50:58,236 - INFO -    - success_count > 0: True (success_count=5)
2025-07-24 05:50:58,236 - INFO -    - system_type == 'hotspot': True
2025-07-24 05:50:58,236 - INFO -    - hasattr single_card_successful_cards: True
2025-07-24 05:50:58,236 - INFO -    - bool(single_card_successful_cards): True (عدد=5)
2025-07-24 05:50:58,237 - INFO - 🔍 نتيجة تقييم شروط زر حذف الكروت الناجحة للكروت المتعددة:
2025-07-24 05:50:58,237 - INFO -    - show_delete_successful_button: True
2025-07-24 05:50:58,556 - INFO - 🗑️ تم إضافة زر حذف الكروت المرسلة بنجاح للكروت المتعددة: 5 كرت
2025-07-24 05:50:58,556 - INFO - ✅ تم إرسال تفاصيل 5 كرت عبر التلجرام بنجاح
2025-07-24 05:51:13,007 - INFO - معالجة callback: single_card_delete_successful_5
2025-07-24 05:51:13,220 - INFO - 🗑️ معالجة callback لحذف الكروت المرسلة بنجاح للكرت الواحد: single_card_delete_successful_5
2025-07-24 05:51:13,221 - INFO - 🗑️ طلب حذف أولي للكروت المرسلة بنجاح للكرت الواحد: 5 كرت
2025-07-24 05:51:13,221 - INFO - 🗑️ بدء معالجة طلب حذف الكروت المرسلة بنجاح للكرت الواحد: 5 كرت
2025-07-24 05:51:13,481 - INFO - ✅ تم إرسال رسالة تأكيد حذف الكروت المرسلة بنجاح للكرت الواحد
2025-07-24 05:51:20,147 - INFO - معالجة callback: single_card_delete_successful_confirm_5
2025-07-24 05:51:20,358 - INFO - 🗑️ معالجة callback لحذف الكروت المرسلة بنجاح للكرت الواحد: single_card_delete_successful_confirm_5
2025-07-24 05:51:20,358 - INFO - ✅ تأكيد حذف الكروت المرسلة بنجاح للكرت الواحد: 5 كرت
2025-07-24 05:51:20,359 - INFO - 🗑️ بدء تنفيذ حذف الكروت المرسلة بنجاح للكرت الواحد: حذف 5 كرت من MikroTik
2025-07-24 05:51:20,423 - WARNING - الاتصال الحالي غير صالح: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 05:51:20,424 - INFO - تم قطع الاتصال مع MikroTik
2025-07-24 05:51:20,426 - INFO - محاولة الاتصال بـ *********:8728 (عادي)
2025-07-24 05:51:20,494 - INFO - نجح الاتصال مع *********
2025-07-24 05:51:20,495 - INFO - 🗑️ بدء حذف 5 كرت ناجح من MikroTik...
2025-07-24 05:51:20,704 - INFO - 🗑️ نتائج الحذف: تم حذف 5 من 5 كرت (معدل النجاح: 100.0%)
2025-07-24 05:51:20,962 - INFO - 🗑️ تم تنفيذ حذف الكروت المرسلة بنجاح للكرت الواحد: حُذف 5/5 كرت
2025-07-24 05:51:48,031 - INFO - بدء إغلاق التطبيق
2025-07-24 05:51:48,033 - INFO - تم قطع الاتصال مع MikroTik
2025-07-24 05:51:48,039 - INFO - تم تسجيل العملية: إغلاق التطبيق - تم إغلاق التطبيق بنجاح
2025-07-24 05:51:48,042 - INFO - تم إغلاق التطبيق بنجاح
2025-07-24 05:52:15,975 - INFO - تم بدء تشغيل التطبيق
2025-07-24 05:52:16,075 - INFO - تم إنشاء المجلدات الأساسية
2025-07-24 05:52:16,077 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-24 05:52:16,078 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-24 05:52:16,180 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-24 05:52:16,615 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-24 05:52:16,615 - INFO - تم إعداد التطبيق بنجاح
2025-07-24 05:52:18,616 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-24 05:52:18,906 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-24 05:52:18,952 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-24 05:52:21,959 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-24 05:52:21,960 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-24 05:52:22,202 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-24 05:52:22,202 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-24 05:52:23,865 - INFO - معالجة callback: single_card
2025-07-24 05:52:24,071 - INFO - 🎴 بدء معالجة طلب إنشاء كروت
2025-07-24 05:52:24,072 - INFO - 🔢 عرض خيارات عدد الكروت
2025-07-24 05:52:24,310 - INFO - ✅ تم عرض خيارات عدد الكروت
2025-07-24 05:52:25,490 - INFO - معالجة callback: card_count_5
2025-07-24 05:52:25,755 - INFO - 📋 عرض قوالب Hotspot لإنشاء 5 كرت
2025-07-24 05:52:26,009 - INFO - ✅ تم إرسال قائمة قوالب لإنشاء 5 كرت - 5 قالب
2025-07-24 05:52:40,899 - INFO - معالجة callback: cards_template_5_10
2025-07-24 05:52:41,163 - INFO - 🎴 بدء إنشاء 5 كرت باستخدام القالب: 10
2025-07-24 05:52:41,405 - INFO - 🔄 تفعيل نظام Hotspot مع تحديث الواجهة...
2025-07-24 05:52:41,406 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-24 05:52:41,423 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-24 05:52:41,524 - WARNING - تحذير: فشل في حفظ الإعدادات: 'MikroTikCardGenerator' object has no attribute 'version_combo'
2025-07-24 05:52:41,524 - INFO - تم تعيين النظام الجديد: hotspot
2025-07-24 05:52:41,525 - INFO - 🔄 إعادة بناء الواجهة للنظام: hotspot
2025-07-24 05:52:41,525 - INFO - ✅ تم تبديل النظام من None إلى hotspot
2025-07-24 05:52:41,525 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-24 05:52:41,527 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-24 05:52:42,069 - INFO - تم إعداد تبويب عرض الكروت لـ Hotspot بنجاح
2025-07-24 05:52:42,207 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-24 05:52:42,208 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-24 05:52:42,372 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام hotspot
2025-07-24 05:52:42,374 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-24 05:52:42,379 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot
2025-07-24 05:52:42,766 - INFO - 📋 تطبيق القالب مع مزامنة شاملة: 10
2025-07-24 05:52:42,769 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-24 05:52:42,772 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-24 05:52:42,852 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-24 05:52:42,853 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-24 05:52:42,880 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-24 05:52:42,881 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-24 05:52:42,886 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-24 05:52:42,890 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-24 05:52:42,890 - INFO - ✅ تم تطبيق القالب '10' بنجاح
2025-07-24 05:52:42,891 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-24 05:52:42,894 - INFO - ✅ تم تحديث معاينة الكارت
2025-07-24 05:52:42,906 - INFO - ✅ تم تحديث حالة عناصر الواجهة
2025-07-24 05:52:42,909 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-24 05:52:42,909 - INFO - 🔢 تعيين عدد الكروت إلى 5...
2025-07-24 05:52:42,911 - INFO - ✅ تم تعيين عدد الكروت إلى 5
2025-07-24 05:52:42,911 - INFO - ✅ تم تحديث جميع المتغيرات الداخلية
2025-07-24 05:52:42,911 - INFO - 🏭 بدء توليد الكرت...
2025-07-24 05:52:42,922 - INFO - تم توليد 5 حساب
2025-07-24 05:52:42,922 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 275 (من generate_all)
2025-07-24 05:52:42,927 - INFO - 🚀 إرسال الكرت إلى MikroTik...
2025-07-24 05:52:42,928 - INFO - محاولة الاتصال بـ *********:8728 (عادي)
2025-07-24 05:52:42,996 - INFO - نجح الاتصال مع *********
2025-07-24 05:52:43,559 - INFO - ✅ تم إرسال المستخدم: 0131985149
2025-07-24 05:52:43,836 - INFO - ✅ تم إرسال المستخدم: 0190495834
2025-07-24 05:52:44,160 - INFO - ✅ تم إرسال المستخدم: 0151382002
2025-07-24 05:52:44,456 - INFO - ✅ تم إرسال المستخدم: 0140318729
2025-07-24 05:52:44,768 - INFO - ✅ تم إرسال المستخدم: 0138203357
2025-07-24 05:52:45,125 - INFO - 📊 بدء إرسال التقرير النهائي: card_type=single, success=5, failed=0
2025-07-24 05:52:45,356 - INFO - ✅ تم إرسال التقرير النهائي: 5 ناجح، 0 فاشل
2025-07-24 05:52:45,356 - INFO - ℹ️ لا توجد كروت فاشلة لحفظها: failed_cards=0
2025-07-24 05:52:45,356 - INFO - 🔍 تشخيص حفظ الكروت للكرت الواحد: failed_count=0, success_count=5, system_type=hotspot
2025-07-24 05:52:45,357 - INFO - 💾 حفظ 5 كرت ناجح لعرض خيار حذف الكروت المرسلة بنجاح للكرت الواحد
2025-07-24 05:52:45,357 - INFO - ✅ تم حفظ معلومات الكروت الناجحة للكرت الواحد: 5 كرت
2025-07-24 05:52:45,357 - INFO - ❌ لم يتم حفظ الكروت الفاشلة للكرت الواحد - الشروط غير مستوفاة
2025-07-24 05:52:45,357 - INFO - ✅ كرت واحد: تم إرسال 5/5 مستخدم بنجاح
2025-07-24 05:52:45,358 - INFO - 🔍 تشخيص شروط زر حذف الكروت الناجحة للكروت المتعددة:
2025-07-24 05:52:45,358 - INFO -    - success_count > 0: True (success_count=5)
2025-07-24 05:52:45,358 - INFO -    - system_type == 'hotspot': True
2025-07-24 05:52:45,358 - INFO -    - hasattr single_card_successful_cards: True
2025-07-24 05:52:45,358 - INFO -    - bool(single_card_successful_cards): True (عدد=5)
2025-07-24 05:52:45,358 - INFO - 🔍 نتيجة تقييم شروط زر حذف الكروت الناجحة للكروت المتعددة:
2025-07-24 05:52:45,358 - INFO -    - show_delete_successful_button: True
2025-07-24 05:52:45,674 - INFO - 🗑️ تم إضافة زر حذف الكروت المرسلة بنجاح للكروت المتعددة: 5 كرت
2025-07-24 05:52:45,674 - INFO - ✅ تم إرسال تفاصيل 5 كرت عبر التلجرام بنجاح
2025-07-24 05:52:47,235 - INFO - معالجة callback: single_card_delete_successful_5
2025-07-24 05:52:47,466 - INFO - 🗑️ معالجة callback لحذف الكروت المرسلة بنجاح للكرت الواحد: single_card_delete_successful_5
2025-07-24 05:52:47,466 - INFO - 🗑️ طلب حذف أولي للكروت المرسلة بنجاح للكرت الواحد: 5 كرت
2025-07-24 05:52:47,467 - INFO - 🗑️ بدء معالجة طلب حذف الكروت المرسلة بنجاح للكرت الواحد: 5 كرت
2025-07-24 05:52:47,723 - INFO - ✅ تم إرسال رسالة تأكيد حذف الكروت المرسلة بنجاح للكرت الواحد
2025-07-24 05:52:58,144 - INFO - معالجة callback: single_card_delete_successful_confirm_5
2025-07-24 05:52:58,357 - INFO - 🗑️ معالجة callback لحذف الكروت المرسلة بنجاح للكرت الواحد: single_card_delete_successful_confirm_5
2025-07-24 05:52:58,358 - INFO - ✅ تأكيد حذف الكروت المرسلة بنجاح للكرت الواحد: 5 كرت
2025-07-24 05:52:58,358 - INFO - 🗑️ بدء تنفيذ حذف الكروت المرسلة بنجاح للكرت الواحد: حذف 5 كرت من MikroTik
2025-07-24 05:52:58,358 - WARNING - الاتصال الحالي غير صالح: [WinError 10038] An operation was attempted on something that is not a socket
2025-07-24 05:52:58,358 - INFO - تم قطع الاتصال مع MikroTik
2025-07-24 05:52:58,362 - INFO - محاولة الاتصال بـ *********:8728 (عادي)
2025-07-24 05:52:58,426 - INFO - نجح الاتصال مع *********
2025-07-24 05:52:58,427 - INFO - 🗑️ بدء حذف 5 كرت ناجح من MikroTik...
2025-07-24 05:52:58,636 - INFO - 🗑️ نتائج الحذف: تم حذف 5 من 5 كرت (معدل النجاح: 100.0%)
2025-07-24 05:52:58,899 - INFO - 🗑️ تم تنفيذ حذف الكروت المرسلة بنجاح للكرت الواحد: حُذف 5/5 كرت
2025-07-24 05:53:25,273 - INFO - بدء إغلاق التطبيق
2025-07-24 05:53:25,275 - INFO - تم قطع الاتصال مع MikroTik
2025-07-24 05:53:25,282 - INFO - تم تسجيل العملية: إغلاق التطبيق - تم إغلاق التطبيق بنجاح
2025-07-24 05:53:25,282 - INFO - تم إغلاق التطبيق بنجاح
2025-07-24 13:05:47,511 - INFO - تم بدء تشغيل التطبيق
2025-07-24 13:05:47,530 - INFO - تم إنشاء المجلدات الأساسية
2025-07-24 13:05:47,671 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-24 13:05:47,671 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-24 13:05:47,774 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-24 13:05:49,248 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-24 13:05:49,249 - INFO - تم إعداد التطبيق بنجاح
2025-07-24 13:05:51,251 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-24 13:05:51,531 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-24 13:05:51,532 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-24 13:05:54,534 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-24 13:05:54,535 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-24 13:05:54,814 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-24 13:05:54,814 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-24 13:06:03,508 - INFO - معالجة callback: select_system_um
2025-07-24 13:06:04,079 - INFO - تم إرسال 2 قالب مفلتر لنظام User Manager
2025-07-24 13:06:05,697 - INFO - معالجة callback: independent_template_um_10
2025-07-24 13:06:06,300 - INFO - 🔄 تبديل النظام تلقائياً من None إلى user_manager
2025-07-24 13:06:06,300 - INFO - 🔄 طلب تبديل النظام من التلجرام: None → user_manager
2025-07-24 13:06:06,604 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-24 13:06:06,633 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-24 13:06:06,734 - WARNING - تحذير: فشل في حفظ الإعدادات: 'MikroTikCardGenerator' object has no attribute 'version_combo'
2025-07-24 13:06:06,734 - INFO - تم تعيين النظام الجديد: user_manager
2025-07-24 13:06:06,735 - INFO - 🔄 إعادة بناء الواجهة للنظام: user_manager
2025-07-24 13:06:06,735 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-24 13:06:06,738 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-24 13:06:07,202 - INFO - تم إعداد تبويب عرض الكروت لـ User Manager بنجاح
2025-07-24 13:06:07,379 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-24 13:06:07,380 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-24 13:06:07,520 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام user_manager
2025-07-24 13:06:07,521 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-24 13:06:07,594 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager
2025-07-24 13:06:07,602 - INFO - تم تسجيل العملية: تبديل النظام من التلجرام - تم تبديل النظام من None إلى user_manager
2025-07-24 13:06:07,623 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-24 13:06:07,624 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-24 13:06:07,630 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-24 13:06:07,890 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-24 13:06:07,891 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-24 13:06:07,926 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-24 13:06:07,936 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-24 13:06:07,940 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-24 13:06:07,945 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-24 13:06:07,948 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-24 13:06:07,949 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager - القالب: 10
2025-07-24 13:06:08,388 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-24 13:06:12,742 - INFO - معالجة callback: independent_create_um_10_lightning
2025-07-24 13:06:15,303 - INFO - معالجة callback: independent_count_um_10_lightning_5
2025-07-24 13:06:15,837 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-24 13:06:15,837 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-24 13:06:15,851 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-24 13:06:15,851 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-24 13:06:15,884 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-24 13:06:15,885 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-24 13:06:15,888 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-24 13:06:15,892 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-24 13:06:16,154 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-24 13:06:16,154 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-24 13:06:16,166 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-24 13:06:16,178 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-24 13:06:16,203 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-24 13:06:16,203 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-24 13:06:16,206 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-24 13:06:16,219 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-24 13:06:16,220 - INFO - ⚡ البرق: تم الحفاظ على العدد المحدد: 200
2025-07-24 13:06:16,221 - INFO - تم تطبيق إعدادات القالب '10' بنجاح
2025-07-24 13:06:16,221 - INFO - ⚡ استخدام البرق للتلجرام (مع جدولة مؤقتة وتنظيف تلقائي)
2025-07-24 13:06:16,221 - INFO - ⚡ بدء البرق التلقائي للتلجرام (مع جدولة مؤقتة وتنظيف تلقائي)
2025-07-24 13:06:16,222 - INFO - ⚡ البرق: استخدام العدد من التلجرام بوت: 5
2025-07-24 13:06:16,222 - INFO - ⚡ البرق للتلجرام: العدد المطلوب 5 حساب
2025-07-24 13:06:16,222 - INFO - ⚡ البرق: تم تحديث عدد الحسابات في الواجهة إلى: 5
2025-07-24 13:06:16,223 - ERROR - حقل 'العميل' مطلوب لـ User Manager
2025-07-24 13:06:16,223 - ERROR - ⚡ البرق للتلجرام: فشل في توليد الحسابات
2025-07-24 13:06:39,229 - ERROR - خطأ في thread إنشاء الكروت: فشل في إنشاء الكروت
2025-07-24 13:08:58,815 - INFO - معالجة callback: independent_count_um_10_lightning_5
2025-07-24 13:08:59,443 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-24 13:08:59,443 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-24 13:08:59,456 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-24 13:08:59,457 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-24 13:08:59,482 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-24 13:08:59,482 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-24 13:08:59,485 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-24 13:08:59,490 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-24 13:08:59,755 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-24 13:08:59,755 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-24 13:08:59,769 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-24 13:08:59,769 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-24 13:08:59,817 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-24 13:08:59,817 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-24 13:08:59,820 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-24 13:08:59,827 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-24 13:08:59,828 - INFO - ⚡ البرق: تم الحفاظ على العدد المحدد: 200
2025-07-24 13:08:59,831 - INFO - تم تطبيق إعدادات القالب '10' بنجاح
2025-07-24 13:08:59,831 - INFO - ⚡ استخدام البرق للتلجرام (مع جدولة مؤقتة وتنظيف تلقائي)
2025-07-24 13:08:59,831 - INFO - ⚡ بدء البرق التلقائي للتلجرام (مع جدولة مؤقتة وتنظيف تلقائي)
2025-07-24 13:08:59,832 - INFO - ⚡ البرق: استخدام العدد من التلجرام بوت: 5
2025-07-24 13:08:59,832 - INFO - ⚡ البرق للتلجرام: العدد المطلوب 5 حساب
2025-07-24 13:08:59,832 - INFO - ⚡ البرق: تم تحديث عدد الحسابات في الواجهة إلى: 5
2025-07-24 13:08:59,833 - ERROR - حقل 'العميل' مطلوب لـ User Manager
2025-07-24 13:08:59,833 - ERROR - ⚡ البرق للتلجرام: فشل في توليد الحسابات
2025-07-24 13:09:22,839 - ERROR - خطأ في thread إنشاء الكروت: فشل في إنشاء الكروت
2025-07-24 13:10:22,430 - INFO - تم حفظ القالب: 10
2025-07-24 13:10:22,942 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-24 13:10:22,943 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-24 13:10:23,208 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-24 13:10:23,208 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-24 13:10:28,614 - INFO - معالجة callback: select_system_um
2025-07-24 13:10:29,159 - INFO - تم إرسال 2 قالب مفلتر لنظام User Manager
2025-07-24 13:10:31,186 - INFO - معالجة callback: independent_template_um_10
2025-07-24 13:10:31,666 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-24 13:10:31,667 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-24 13:10:31,667 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-24 13:10:31,682 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-24 13:10:31,683 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-24 13:10:31,730 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-24 13:10:31,730 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-24 13:10:31,734 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-24 13:10:31,739 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-24 13:10:31,740 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-24 13:10:31,753 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager - القالب: 10
2025-07-24 13:10:32,054 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-24 13:10:34,467 - INFO - معالجة callback: independent_create_um_10_lightning
2025-07-24 13:10:36,039 - INFO - معالجة callback: independent_count_um_10_lightning_5
2025-07-24 13:10:36,553 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-24 13:10:36,553 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-24 13:10:36,568 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-24 13:10:36,568 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-24 13:10:36,610 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-24 13:10:36,610 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-24 13:10:36,613 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-24 13:10:36,630 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-24 13:10:36,868 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-24 13:10:36,868 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-24 13:10:36,882 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-24 13:10:36,893 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-24 13:10:36,928 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-24 13:10:36,929 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-24 13:10:36,941 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-24 13:10:36,946 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-24 13:10:36,946 - INFO - ⚡ البرق: تم الحفاظ على العدد المحدد: 200
2025-07-24 13:10:36,947 - INFO - تم تطبيق إعدادات القالب '10' بنجاح
2025-07-24 13:10:36,947 - INFO - ⚡ استخدام البرق للتلجرام (مع جدولة مؤقتة وتنظيف تلقائي)
2025-07-24 13:10:36,949 - INFO - ⚡ بدء البرق التلقائي للتلجرام (مع جدولة مؤقتة وتنظيف تلقائي)
2025-07-24 13:10:36,949 - INFO - ⚡ البرق: استخدام العدد من التلجرام بوت: 5
2025-07-24 13:10:36,949 - INFO - ⚡ البرق للتلجرام: العدد المطلوب 5 حساب
2025-07-24 13:10:36,949 - INFO - ⚡ البرق: تم تحديث عدد الحسابات في الواجهة إلى: 5
2025-07-24 13:10:36,950 - INFO - ⚡ البرق للتلجرام: تم توليد 5 حساب
2025-07-24 13:10:36,950 - INFO - ⚡ البرق للتلجرام: تنفيذ مجدول لـ 5 كارت (حد التقسيم: 100)
2025-07-24 13:10:36,950 - INFO - ⚡ البرق للتلجرام: تخطي حفظ الملفات المحلية - استخدام الجدولة المؤقتة
2025-07-24 13:10:36,951 - INFO - محاولة الاتصال بـ *********:8728 (عادي)
2025-07-24 13:10:37,017 - INFO - نجح الاتصال مع *********
2025-07-24 13:10:37,435 - INFO - ⚡ تم إضافة السكريبت: telegram_lightning_user_manager_20250724_131037
2025-07-24 13:10:37,477 - INFO - ⚡ وقت MikroTik الحالي: 10:10:36
2025-07-24 13:10:37,513 - INFO - ⚡ تم تعديل وقت التنفيذ لليوم التالي: 10:10:39
2025-07-24 13:10:37,513 - INFO - ⚡ سيتم تنفيذ السكريبت في: 10:10:39 (بعد 3 ثواني من وقت MikroTik)
2025-07-24 13:10:37,559 - INFO - ⚡ تم إنشاء الجدولة المؤقتة: telegram_schedule_20250724_131037 للتنفيذ في 10:10:39
2025-07-24 13:10:37,564 - INFO - ⚡ تم إعداد البرق المجدول للتلجرام بنجاح - السكريبت: telegram_lightning_user_manager_20250724_131037, الجدولة: telegram_schedule_20250724_131037
2025-07-24 13:10:37,565 - INFO - ⚡ سيتم تنفيذ السكريبت في 10:10:39 مع تنظيف تلقائي بعد الانتهاء
2025-07-24 13:10:37,565 - INFO - ⚡ البرق للتلجرام مكتمل: تم جدولة إنشاء وإرسال 5 كارت بنجاح (مع تنظيف تلقائي)
2025-07-24 13:10:40,111 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 5
2025-07-24 13:10:40,169 - INFO - تم إنشاء PDF بنجاح: exports/كروت_(10)_ب20-5 كارت-24-07-2025-13-10-39-um.pdf
2025-07-24 13:10:40,170 - INFO - تم حفظ ملف .rsc الاحتياطي: exports/كروت_(10)_ب20-5 كارت-24-07-2025-13-10-39-um.rsc
2025-07-24 13:10:41,444 - INFO - تم إرسال الملف بنجاح: كروت_(10)_ب20-5 كارت-24-07-2025-13-10-39-um.pdf
2025-07-24 13:10:41,693 - INFO - تم إرسال 5 كرت عبر التلجرام باستخدام قالب 10
2025-07-24 13:11:16,457 - INFO - معالجة callback: independent_count_um_10_lightning_200
2025-07-24 13:11:16,941 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-24 13:11:16,942 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-24 13:11:16,956 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-24 13:11:16,956 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-24 13:11:17,006 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-24 13:11:17,007 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-24 13:11:17,009 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-24 13:11:17,013 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-24 13:11:17,269 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-24 13:11:17,269 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-24 13:11:17,284 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-24 13:11:17,284 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-24 13:11:17,323 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-24 13:11:17,325 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-24 13:11:17,328 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-24 13:11:17,333 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-24 13:11:17,349 - INFO - ⚡ البرق: تم الحفاظ على العدد المحدد: 200
2025-07-24 13:11:17,351 - INFO - تم تطبيق إعدادات القالب '10' بنجاح
2025-07-24 13:11:17,351 - INFO - ⚡ استخدام البرق للتلجرام (مع جدولة مؤقتة وتنظيف تلقائي)
2025-07-24 13:11:17,352 - INFO - ⚡ بدء البرق التلقائي للتلجرام (مع جدولة مؤقتة وتنظيف تلقائي)
2025-07-24 13:11:17,353 - INFO - ⚡ البرق: استخدام العدد من التلجرام بوت: 200
2025-07-24 13:11:17,354 - INFO - ⚡ البرق للتلجرام: العدد المطلوب 200 حساب
2025-07-24 13:11:17,354 - INFO - ⚡ البرق: تم تحديث عدد الحسابات في الواجهة إلى: 200
2025-07-24 13:11:17,358 - INFO - ⚡ البرق للتلجرام: تم توليد 200 حساب
2025-07-24 13:11:17,358 - INFO - ⚡ البرق للتلجرام: تنفيذ متسلسل مجدول لـ 200 كارت (حد التقسيم: 100)
2025-07-24 13:11:17,359 - INFO - ⚡ البرق للتلجرام: بدء معالجة الأعداد الكبيرة مع جدولة واحدة
2025-07-24 13:11:17,359 - INFO - ⚡ البرق للتلجرام: تم تقسيم 200 كارت إلى 2 مجموعة للتنفيذ المتسلسل
2025-07-24 13:11:17,360 - INFO - ⚡ البرق للتلجرام: إنشاء السكريبت للمجموعة 1/2 (100 كارت)
2025-07-24 13:11:17,360 - INFO - ⚡ البرق للتلجرام: تم إعداد السكريبت telegram_lightning_batch1_user_manager_20250724_131117
2025-07-24 13:11:17,361 - INFO - ⚡ البرق للتلجرام: إنشاء السكريبت للمجموعة 2/2 (100 كارت)
2025-07-24 13:11:17,361 - INFO - ⚡ البرق للتلجرام: تم إعداد السكريبت telegram_lightning_batch2_user_manager_20250724_131117
2025-07-24 13:11:17,365 - INFO - استخدام الاتصال الحالي
2025-07-24 13:11:17,450 - INFO - ⚡ تم إرسال السكريبت المترابط: telegram_lightning_batch1_user_manager_20250724_131117
2025-07-24 13:11:17,493 - INFO - ⚡ تم إرسال السكريبت المترابط: telegram_lightning_batch2_user_manager_20250724_131117
2025-07-24 13:11:17,497 - INFO - ⚡ وقت MikroTik الحالي: 10:11:16
2025-07-24 13:11:17,498 - INFO - ⚡ تم تعديل وقت التنفيذ لليوم التالي: 10:11:19
2025-07-24 13:11:17,498 - INFO - ⚡ السكريبت الأول سينفذ في: 10:11:19 (بعد 3 ثواني من وقت MikroTik)
2025-07-24 13:11:17,540 - INFO - ⚡ تم إنشاء الجدولة الرئيسية: telegram_main_schedule_20250724_131117 لتشغيل telegram_lightning_batch1_user_manager_20250724_131117 في 10:11:19
2025-07-24 13:11:17,543 - INFO - ⚡ تم إعداد البرق المترابط بنجاح - جدولة واحدة: telegram_main_schedule_20250724_131117, 2 سكريبت مترابط
2025-07-24 13:11:17,545 - INFO - ⚡ البرق للتلجرام مكتمل: تم إنشاء جدولة واحدة لـ 2 مجموعة متسلسلة (200 كارت)
2025-07-24 13:11:20,438 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 200
2025-07-24 13:11:20,456 - INFO - تم إنشاء PDF بنجاح: exports/كروت_(10)_ب20-200 كارت-24-07-2025-13-11-20-um.pdf
2025-07-24 13:11:20,473 - INFO - تم حفظ ملف .rsc الاحتياطي: exports/كروت_(10)_ب20-200 كارت-24-07-2025-13-11-20-um.rsc
2025-07-24 13:11:21,363 - INFO - تم إرسال الملف بنجاح: كروت_(10)_ب20-200 كارت-24-07-2025-13-11-20-um.pdf
2025-07-24 13:11:21,620 - INFO - تم إرسال 200 كرت عبر التلجرام باستخدام قالب 10
2025-07-24 13:11:47,435 - INFO - تم حفظ القالب: 10
2025-07-24 13:11:47,945 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-24 13:11:47,947 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-24 13:11:48,230 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-24 13:11:48,237 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-24 13:12:12,461 - INFO - معالجة callback: select_system_um
2025-07-24 13:12:13,080 - INFO - تم إرسال 2 قالب مفلتر لنظام User Manager
2025-07-24 13:12:14,446 - INFO - معالجة callback: independent_template_um_10
2025-07-24 13:12:14,911 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-24 13:12:14,911 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-24 13:12:14,912 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-24 13:12:14,916 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-24 13:12:14,918 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-24 13:12:14,971 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-24 13:12:14,971 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-24 13:12:14,972 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-24 13:12:14,977 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-24 13:12:14,978 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-24 13:12:14,978 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager - القالب: 10
2025-07-24 13:12:15,238 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-24 13:12:18,732 - INFO - معالجة callback: independent_create_um_10_lightning
2025-07-24 13:12:28,978 - ERROR - خطأ في إرسال رسالة مع لوحة المفاتيح: <urlopen error [WinError 10051] A socket operation was attempted to an unreachable network>
2025-07-24 13:12:29,185 - INFO - معالجة callback: independent_create_um_10_lightning
2025-07-24 13:12:31,152 - INFO - معالجة callback: independent_custom_um_10_lightning
2025-07-24 13:12:35,080 - INFO - معالجة الأمر: 400
2025-07-24 13:12:35,336 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-24 13:12:35,336 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-24 13:12:35,342 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-24 13:12:35,342 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-24 13:12:35,396 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-24 13:12:35,405 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-24 13:12:35,406 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-24 13:12:35,412 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-24 13:12:35,655 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-24 13:12:35,655 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-24 13:12:35,659 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-24 13:12:35,660 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-24 13:12:35,706 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-24 13:12:35,706 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-24 13:12:35,708 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-24 13:12:35,721 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-24 13:12:35,721 - INFO - ⚡ البرق: تم الحفاظ على العدد المحدد: 200
2025-07-24 13:12:35,722 - INFO - تم تطبيق إعدادات القالب '10' بنجاح
2025-07-24 13:12:35,722 - INFO - ⚡ استخدام البرق للتلجرام (مع جدولة مؤقتة وتنظيف تلقائي)
2025-07-24 13:12:35,722 - INFO - ⚡ بدء البرق التلقائي للتلجرام (مع جدولة مؤقتة وتنظيف تلقائي)
2025-07-24 13:12:35,722 - INFO - ⚡ البرق: استخدام العدد من التلجرام بوت: 400
2025-07-24 13:12:35,723 - INFO - ⚡ البرق للتلجرام: العدد المطلوب 400 حساب
2025-07-24 13:12:35,723 - INFO - ⚡ البرق: تم تحديث عدد الحسابات في الواجهة إلى: 400
2025-07-24 13:12:35,728 - INFO - ⚡ البرق للتلجرام: تم توليد 400 حساب
2025-07-24 13:12:35,728 - INFO - ⚡ البرق للتلجرام: تنفيذ متسلسل مجدول لـ 400 كارت (حد التقسيم: 100)
2025-07-24 13:12:35,729 - INFO - ⚡ البرق للتلجرام: بدء معالجة الأعداد الكبيرة مع جدولة واحدة
2025-07-24 13:12:35,730 - INFO - ⚡ البرق للتلجرام: تم تقسيم 400 كارت إلى 4 مجموعة للتنفيذ المتسلسل
2025-07-24 13:12:35,730 - INFO - ⚡ البرق للتلجرام: إنشاء السكريبت للمجموعة 1/4 (100 كارت)
2025-07-24 13:12:35,730 - INFO - ⚡ البرق للتلجرام: تم إعداد السكريبت telegram_lightning_batch1_user_manager_20250724_131235
2025-07-24 13:12:35,730 - INFO - ⚡ البرق للتلجرام: إنشاء السكريبت للمجموعة 2/4 (100 كارت)
2025-07-24 13:12:35,731 - INFO - ⚡ البرق للتلجرام: تم إعداد السكريبت telegram_lightning_batch2_user_manager_20250724_131235
2025-07-24 13:12:35,731 - INFO - ⚡ البرق للتلجرام: إنشاء السكريبت للمجموعة 3/4 (100 كارت)
2025-07-24 13:12:35,731 - INFO - ⚡ البرق للتلجرام: تم إعداد السكريبت telegram_lightning_batch3_user_manager_20250724_131235
2025-07-24 13:12:35,731 - INFO - ⚡ البرق للتلجرام: إنشاء السكريبت للمجموعة 4/4 (100 كارت)
2025-07-24 13:12:35,732 - INFO - ⚡ البرق للتلجرام: تم إعداد السكريبت telegram_lightning_batch4_user_manager_20250724_131235
2025-07-24 13:12:35,735 - INFO - استخدام الاتصال الحالي
2025-07-24 13:12:35,774 - INFO - ⚡ تم إرسال السكريبت المترابط: telegram_lightning_batch1_user_manager_20250724_131235
2025-07-24 13:12:35,813 - INFO - ⚡ تم إرسال السكريبت المترابط: telegram_lightning_batch2_user_manager_20250724_131235
2025-07-24 13:12:35,844 - INFO - ⚡ تم إرسال السكريبت المترابط: telegram_lightning_batch3_user_manager_20250724_131235
2025-07-24 13:12:35,886 - INFO - ⚡ تم إرسال السكريبت المترابط: telegram_lightning_batch4_user_manager_20250724_131235
2025-07-24 13:12:35,929 - INFO - ⚡ وقت MikroTik الحالي: 10:12:34
2025-07-24 13:12:35,937 - INFO - ⚡ تم تعديل وقت التنفيذ لليوم التالي: 10:12:37
2025-07-24 13:12:35,938 - INFO - ⚡ السكريبت الأول سينفذ في: 10:12:37 (بعد 3 ثواني من وقت MikroTik)
2025-07-24 13:12:35,981 - INFO - ⚡ تم إنشاء الجدولة الرئيسية: telegram_main_schedule_20250724_131235 لتشغيل telegram_lightning_batch1_user_manager_20250724_131235 في 10:12:37
2025-07-24 13:12:35,983 - INFO - ⚡ تم إعداد البرق المترابط بنجاح - جدولة واحدة: telegram_main_schedule_20250724_131235, 4 سكريبت مترابط
2025-07-24 13:12:35,983 - INFO - ⚡ البرق للتلجرام مكتمل: تم إنشاء جدولة واحدة لـ 4 مجموعة متسلسلة (400 كارت)
2025-07-24 13:12:38,869 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 400
2025-07-24 13:12:38,916 - INFO - تم إنشاء PDF بنجاح: exports/كروت_(10)_ب20-400 كارت-24-07-2025-13-12-38-um.pdf
2025-07-24 13:12:38,919 - INFO - تم حفظ ملف .rsc الاحتياطي: exports/كروت_(10)_ب20-400 كارت-24-07-2025-13-12-38-um.rsc
2025-07-24 13:12:39,653 - INFO - تم إرسال الملف بنجاح: كروت_(10)_ب20-400 كارت-24-07-2025-13-12-38-um.pdf
2025-07-24 13:12:39,895 - INFO - تم إرسال 400 كرت عبر التلجرام باستخدام قالب 10
2025-07-24 13:14:36,422 - INFO - بدء إغلاق التطبيق
2025-07-24 13:14:36,424 - INFO - تم قطع الاتصال مع MikroTik
2025-07-24 13:14:37,516 - INFO - تم تسجيل العملية: إغلاق التطبيق - تم إغلاق التطبيق بنجاح
2025-07-24 13:14:37,517 - INFO - تم إغلاق التطبيق بنجاح
2025-07-24 13:52:09,114 - INFO - تم بدء تشغيل التطبيق
2025-07-24 13:52:09,155 - INFO - تم إنشاء المجلدات الأساسية
2025-07-24 13:52:09,315 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-24 13:52:09,315 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-24 13:52:10,620 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-24 13:52:13,441 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-24 13:52:13,442 - INFO - تم إعداد التطبيق بنجاح
2025-07-24 13:52:15,464 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-24 13:52:15,861 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-24 13:52:15,870 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-24 13:52:18,878 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-24 13:52:18,993 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-24 13:52:19,255 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-24 13:52:19,255 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-24 13:52:21,425 - INFO - معالجة callback: select_system_um
2025-07-24 13:52:21,897 - INFO - تم إرسال 2 قالب مفلتر لنظام User Manager
2025-07-24 13:52:23,116 - INFO - معالجة callback: independent_template_um_10
2025-07-24 13:52:23,605 - INFO - 🔄 تبديل النظام تلقائياً من None إلى user_manager
2025-07-24 13:52:23,605 - INFO - 🔄 طلب تبديل النظام من التلجرام: None → user_manager
2025-07-24 13:52:23,858 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-24 13:52:23,953 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-24 13:52:24,054 - WARNING - تحذير: فشل في حفظ الإعدادات: 'MikroTikCardGenerator' object has no attribute 'version_combo'
2025-07-24 13:52:24,054 - INFO - تم تعيين النظام الجديد: user_manager
2025-07-24 13:52:24,055 - INFO - 🔄 إعادة بناء الواجهة للنظام: user_manager
2025-07-24 13:52:24,056 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-24 13:52:24,059 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-24 13:52:24,714 - INFO - تم إعداد تبويب عرض الكروت لـ User Manager بنجاح
2025-07-24 13:52:24,975 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-24 13:52:24,979 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-24 13:52:25,193 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام user_manager
2025-07-24 13:52:25,194 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-24 13:52:25,295 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager
2025-07-24 13:52:25,358 - INFO - تم تسجيل العملية: تبديل النظام من التلجرام - تم تبديل النظام من None إلى user_manager
2025-07-24 13:52:25,379 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-24 13:52:25,380 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-24 13:52:25,381 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-24 13:52:25,524 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-24 13:52:25,535 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-24 13:52:25,637 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-24 13:52:25,637 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-24 13:52:25,641 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-24 13:52:25,645 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-24 13:52:25,648 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-24 13:52:25,659 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager - القالب: 10
2025-07-24 13:52:26,072 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-24 13:52:28,834 - INFO - معالجة callback: independent_create_um_10_lightning
2025-07-24 13:52:34,341 - INFO - معالجة callback: independent_count_um_10_lightning_200
2025-07-24 13:52:35,027 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-24 13:52:35,027 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-24 13:52:35,042 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-24 13:52:35,042 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-24 13:52:35,081 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-24 13:52:35,081 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-24 13:52:35,084 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-24 13:52:35,090 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-24 13:52:35,402 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-24 13:52:35,402 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-24 13:52:35,417 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-24 13:52:35,418 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-24 13:52:35,455 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-24 13:52:35,455 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-24 13:52:35,468 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-24 13:52:35,472 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-24 13:52:35,472 - INFO - ⚡ البرق: تم الحفاظ على العدد المحدد: 200
2025-07-24 13:52:35,474 - INFO - تم تطبيق إعدادات القالب '10' بنجاح
2025-07-24 13:52:35,474 - INFO - ⚡ استخدام البرق للتلجرام (مع جدولة مؤقتة وتنظيف تلقائي)
2025-07-24 13:52:35,474 - INFO - ⚡ بدء البرق التلقائي للتلجرام (مع جدولة مؤقتة وتنظيف تلقائي)
2025-07-24 13:52:35,475 - INFO - ⚡ البرق: استخدام العدد من التلجرام بوت: 200
2025-07-24 13:52:35,476 - INFO - ⚡ البرق للتلجرام: العدد المطلوب 200 حساب
2025-07-24 13:52:35,476 - INFO - ⚡ البرق: تم تحديث عدد الحسابات في الواجهة إلى: 200
2025-07-24 13:52:35,479 - INFO - ⚡ البرق للتلجرام: تم توليد 200 حساب
2025-07-24 13:52:35,479 - INFO - ⚡ البرق للتلجرام: تنفيذ متسلسل مجدول لـ 200 كارت (حد التقسيم: 100)
2025-07-24 13:52:35,480 - INFO - ⚡ البرق للتلجرام: بدء معالجة الأعداد الكبيرة مع جدولة واحدة
2025-07-24 13:52:35,481 - INFO - ⚡ البرق للتلجرام: تم تقسيم 200 كارت إلى 2 مجموعة للتنفيذ المتسلسل
2025-07-24 13:52:35,481 - INFO - ⚡ البرق للتلجرام: إنشاء السكريبت للمجموعة 1/2 (100 كارت)
2025-07-24 13:52:35,481 - INFO - ⚡ البرق للتلجرام: تم إعداد السكريبت telegram_lightning_batch1_user_manager_20250724_135235
2025-07-24 13:52:35,482 - INFO - ⚡ البرق للتلجرام: إنشاء السكريبت للمجموعة 2/2 (100 كارت)
2025-07-24 13:52:35,482 - INFO - ⚡ البرق للتلجرام: تم إعداد السكريبت telegram_lightning_batch2_user_manager_20250724_135235
2025-07-24 13:52:35,482 - INFO - محاولة الاتصال بـ *********:8728 (عادي)
2025-07-24 13:52:35,544 - INFO - نجح الاتصال مع *********
2025-07-24 13:52:35,600 - INFO - ⚡ تم إرسال السكريبت المترابط: telegram_lightning_batch1_user_manager_20250724_135235
2025-07-24 13:52:35,639 - INFO - ⚡ تم إرسال السكريبت المترابط: telegram_lightning_batch2_user_manager_20250724_135235
2025-07-24 13:52:35,675 - INFO - ⚡ وقت MikroTik الحالي: 10:52:34
2025-07-24 13:52:35,723 - INFO - ⚡ تم تعديل وقت التنفيذ لليوم التالي: 10:52:37
2025-07-24 13:52:35,723 - INFO - ⚡ السكريبت الأول سينفذ في: 10:52:37 (بعد 3 ثواني من وقت MikroTik)
2025-07-24 13:52:35,767 - INFO - ⚡ تم إنشاء الجدولة الرئيسية: telegram_main_schedule_20250724_135235 لتشغيل telegram_lightning_batch1_user_manager_20250724_135235 في 10:52:37
2025-07-24 13:52:35,770 - INFO - ⚡ تم إعداد البرق المترابط بنجاح - جدولة واحدة: telegram_main_schedule_20250724_135235, 2 سكريبت مترابط
2025-07-24 13:52:35,780 - INFO - ⚡ البرق للتلجرام مكتمل: تم إنشاء جدولة واحدة لـ 2 مجموعة متسلسلة (200 كارت)
2025-07-24 13:52:38,658 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 200
2025-07-24 13:52:38,705 - INFO - تم إنشاء PDF بنجاح: exports/كروت_(10)_ب20-200 كارت-24-07-2025-13-52-38-um.pdf
2025-07-24 13:52:38,731 - INFO - تم حفظ ملف .rsc الاحتياطي: exports/كروت_(10)_ب20-200 كارت-24-07-2025-13-52-38-um.rsc
2025-07-24 13:52:39,510 - INFO - تم إرسال الملف بنجاح: كروت_(10)_ب20-200 كارت-24-07-2025-13-52-38-um.pdf
2025-07-24 13:52:39,755 - INFO - تم إرسال 200 كرت عبر التلجرام باستخدام قالب 10
2025-07-24 13:53:09,060 - INFO - معالجة callback: independent_count_um_10_lightning_200
2025-07-24 13:53:09,540 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-24 13:53:09,540 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-24 13:53:09,554 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-24 13:53:09,565 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-24 13:53:09,603 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-24 13:53:09,603 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-24 13:53:09,607 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-24 13:53:09,611 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-24 13:53:09,893 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-24 13:53:09,894 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-24 13:53:09,907 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-24 13:53:09,908 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-24 13:53:09,957 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-24 13:53:09,957 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-24 13:53:09,959 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-24 13:53:09,964 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-24 13:53:09,965 - INFO - ⚡ البرق: تم الحفاظ على العدد المحدد: 200
2025-07-24 13:53:09,966 - INFO - تم تطبيق إعدادات القالب '10' بنجاح
2025-07-24 13:53:09,967 - INFO - ⚡ استخدام البرق للتلجرام (مع جدولة مؤقتة وتنظيف تلقائي)
2025-07-24 13:53:09,967 - INFO - ⚡ بدء البرق التلقائي للتلجرام (مع جدولة مؤقتة وتنظيف تلقائي)
2025-07-24 13:53:09,967 - INFO - ⚡ البرق: استخدام العدد من التلجرام بوت: 200
2025-07-24 13:53:09,967 - INFO - ⚡ البرق للتلجرام: العدد المطلوب 200 حساب
2025-07-24 13:53:09,968 - INFO - ⚡ البرق: تم تحديث عدد الحسابات في الواجهة إلى: 200
2025-07-24 13:53:09,970 - INFO - ⚡ البرق للتلجرام: تم توليد 200 حساب
2025-07-24 13:53:09,971 - INFO - ⚡ البرق للتلجرام: تنفيذ متسلسل مجدول لـ 200 كارت (حد التقسيم: 100)
2025-07-24 13:53:09,971 - INFO - ⚡ البرق للتلجرام: بدء معالجة الأعداد الكبيرة مع جدولة واحدة
2025-07-24 13:53:09,972 - INFO - ⚡ البرق للتلجرام: تم تقسيم 200 كارت إلى 2 مجموعة للتنفيذ المتسلسل
2025-07-24 13:53:09,973 - INFO - ⚡ البرق للتلجرام: إنشاء السكريبت للمجموعة 1/2 (100 كارت)
2025-07-24 13:53:09,973 - INFO - ⚡ البرق للتلجرام: تم إعداد السكريبت telegram_lightning_batch1_user_manager_20250724_135309
2025-07-24 13:53:09,974 - INFO - ⚡ البرق للتلجرام: إنشاء السكريبت للمجموعة 2/2 (100 كارت)
2025-07-24 13:53:09,974 - INFO - ⚡ البرق للتلجرام: تم إعداد السكريبت telegram_lightning_batch2_user_manager_20250724_135309
2025-07-24 13:53:09,977 - INFO - استخدام الاتصال الحالي
2025-07-24 13:53:10,031 - INFO - ⚡ تم إرسال السكريبت المترابط: telegram_lightning_batch1_user_manager_20250724_135309
2025-07-24 13:53:10,069 - INFO - ⚡ تم إرسال السكريبت المترابط: telegram_lightning_batch2_user_manager_20250724_135309
2025-07-24 13:53:10,113 - INFO - ⚡ وقت MikroTik الحالي: 10:53:09
2025-07-24 13:53:10,114 - INFO - ⚡ تم تعديل وقت التنفيذ لليوم التالي: 10:53:12
2025-07-24 13:53:10,125 - INFO - ⚡ السكريبت الأول سينفذ في: 10:53:12 (بعد 3 ثواني من وقت MikroTik)
2025-07-24 13:53:10,165 - INFO - ⚡ تم إنشاء الجدولة الرئيسية: telegram_main_schedule_20250724_135309 لتشغيل telegram_lightning_batch1_user_manager_20250724_135309 في 10:53:12
2025-07-24 13:53:10,168 - INFO - ⚡ تم إعداد البرق المترابط بنجاح - جدولة واحدة: telegram_main_schedule_20250724_135309, 2 سكريبت مترابط
2025-07-24 13:53:10,168 - INFO - ⚡ البرق للتلجرام مكتمل: تم إنشاء جدولة واحدة لـ 2 مجموعة متسلسلة (200 كارت)
2025-07-24 13:53:13,053 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 200
2025-07-24 13:53:13,071 - INFO - تم إنشاء PDF بنجاح: exports/كروت_(10)_ب20-200 كارت-24-07-2025-13-53-12-um.pdf
2025-07-24 13:53:13,086 - INFO - تم حفظ ملف .rsc الاحتياطي: exports/كروت_(10)_ب20-200 كارت-24-07-2025-13-53-12-um.rsc
2025-07-24 13:53:13,689 - INFO - تم إرسال الملف بنجاح: كروت_(10)_ب20-200 كارت-24-07-2025-13-53-12-um.pdf
2025-07-24 13:53:13,922 - INFO - تم إرسال 200 كرت عبر التلجرام باستخدام قالب 10
2025-07-24 13:53:38,326 - INFO - معالجة callback: independent_count_um_10_lightning_200
2025-07-24 13:53:38,820 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-24 13:53:38,820 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-24 13:53:38,835 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-24 13:53:38,835 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-24 13:53:38,883 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-24 13:53:38,884 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-24 13:53:38,886 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-24 13:53:38,890 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-24 13:53:39,172 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-24 13:53:39,173 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-24 13:53:39,188 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-24 13:53:39,188 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-24 13:53:39,241 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-24 13:53:39,241 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-24 13:53:39,244 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-24 13:53:39,248 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-24 13:53:39,248 - INFO - ⚡ البرق: تم الحفاظ على العدد المحدد: 200
2025-07-24 13:53:39,250 - INFO - تم تطبيق إعدادات القالب '10' بنجاح
2025-07-24 13:53:39,250 - INFO - ⚡ استخدام البرق للتلجرام (مع جدولة مؤقتة وتنظيف تلقائي)
2025-07-24 13:53:39,250 - INFO - ⚡ بدء البرق التلقائي للتلجرام (مع جدولة مؤقتة وتنظيف تلقائي)
2025-07-24 13:53:39,251 - INFO - ⚡ البرق: استخدام العدد من التلجرام بوت: 200
2025-07-24 13:53:39,251 - INFO - ⚡ البرق للتلجرام: العدد المطلوب 200 حساب
2025-07-24 13:53:39,252 - INFO - ⚡ البرق: تم تحديث عدد الحسابات في الواجهة إلى: 200
2025-07-24 13:53:39,255 - INFO - ⚡ البرق للتلجرام: تم توليد 200 حساب
2025-07-24 13:53:39,255 - INFO - ⚡ البرق للتلجرام: تنفيذ متسلسل مجدول لـ 200 كارت (حد التقسيم: 100)
2025-07-24 13:53:39,256 - INFO - ⚡ البرق للتلجرام: بدء معالجة الأعداد الكبيرة مع جدولة واحدة
2025-07-24 13:53:39,257 - INFO - ⚡ البرق للتلجرام: تم تقسيم 200 كارت إلى 2 مجموعة للتنفيذ المتسلسل
2025-07-24 13:53:39,257 - INFO - ⚡ البرق للتلجرام: إنشاء السكريبت للمجموعة 1/2 (100 كارت)
2025-07-24 13:53:39,257 - INFO - ⚡ البرق للتلجرام: تم إعداد السكريبت telegram_lightning_batch1_user_manager_20250724_135339
2025-07-24 13:53:39,258 - INFO - ⚡ البرق للتلجرام: إنشاء السكريبت للمجموعة 2/2 (100 كارت)
2025-07-24 13:53:39,258 - INFO - ⚡ البرق للتلجرام: تم إعداد السكريبت telegram_lightning_batch2_user_manager_20250724_135339
2025-07-24 13:53:39,258 - WARNING - الاتصال الحالي غير صالح: 
2025-07-24 13:53:39,259 - INFO - تم قطع الاتصال مع MikroTik
2025-07-24 13:53:39,259 - INFO - محاولة الاتصال بـ *********:8728 (عادي)
2025-07-24 13:53:39,323 - INFO - نجح الاتصال مع *********
2025-07-24 13:53:39,374 - INFO - ⚡ تم إرسال السكريبت المترابط: telegram_lightning_batch1_user_manager_20250724_135339
2025-07-24 13:53:39,413 - INFO - ⚡ تم إرسال السكريبت المترابط: telegram_lightning_batch2_user_manager_20250724_135339
2025-07-24 13:53:39,416 - INFO - ⚡ وقت MikroTik الحالي: 10:53:38
2025-07-24 13:53:39,417 - INFO - ⚡ تم تعديل وقت التنفيذ لليوم التالي: 10:53:41
2025-07-24 13:53:39,417 - INFO - ⚡ السكريبت الأول سينفذ في: 10:53:41 (بعد 3 ثواني من وقت MikroTik)
2025-07-24 13:53:39,465 - INFO - ⚡ تم إنشاء الجدولة الرئيسية: telegram_main_schedule_20250724_135339 لتشغيل telegram_lightning_batch1_user_manager_20250724_135339 في 10:53:41
2025-07-24 13:53:39,472 - INFO - ⚡ تم إعداد البرق المترابط بنجاح - جدولة واحدة: telegram_main_schedule_20250724_135339, 2 سكريبت مترابط
2025-07-24 13:53:39,472 - INFO - ⚡ البرق للتلجرام مكتمل: تم إنشاء جدولة واحدة لـ 2 مجموعة متسلسلة (200 كارت)
2025-07-24 13:53:42,341 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 200
2025-07-24 13:53:42,376 - INFO - تم إنشاء PDF بنجاح: exports/كروت_(10)_ب20-200 كارت-24-07-2025-13-53-42-um.pdf
2025-07-24 13:53:42,379 - INFO - تم حفظ ملف .rsc الاحتياطي: exports/كروت_(10)_ب20-200 كارت-24-07-2025-13-53-42-um.rsc
2025-07-24 13:53:43,039 - INFO - تم إرسال الملف بنجاح: كروت_(10)_ب20-200 كارت-24-07-2025-13-53-42-um.pdf
2025-07-24 13:53:43,276 - INFO - تم إرسال 200 كرت عبر التلجرام باستخدام قالب 10
2025-07-24 14:19:12,832 - INFO - تم بدء تشغيل التطبيق
2025-07-24 14:19:12,874 - INFO - تم إنشاء المجلدات الأساسية
2025-07-24 14:19:13,833 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-24 14:19:13,833 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-24 14:19:14,806 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-24 14:19:18,269 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-24 14:19:18,269 - INFO - تم إعداد التطبيق بنجاح
2025-07-24 14:19:20,307 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-24 14:19:20,779 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-24 14:19:20,780 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-24 14:19:23,792 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-24 14:19:23,846 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-24 14:19:24,181 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-24 14:19:24,182 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-24 14:19:50,238 - INFO - معالجة callback: select_system_um
2025-07-24 14:19:50,721 - INFO - تم إرسال 2 قالب مفلتر لنظام User Manager
2025-07-24 14:19:51,943 - INFO - معالجة callback: independent_template_um_10
2025-07-24 14:19:52,569 - INFO - 🔄 تبديل النظام تلقائياً من None إلى user_manager
2025-07-24 14:19:52,570 - INFO - 🔄 طلب تبديل النظام من التلجرام: None → user_manager
2025-07-24 14:19:52,850 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-24 14:19:52,945 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-24 14:19:53,046 - WARNING - تحذير: فشل في حفظ الإعدادات: 'MikroTikCardGenerator' object has no attribute 'version_combo'
2025-07-24 14:19:53,046 - INFO - تم تعيين النظام الجديد: user_manager
2025-07-24 14:19:53,047 - INFO - 🔄 إعادة بناء الواجهة للنظام: user_manager
2025-07-24 14:19:53,047 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-24 14:19:53,052 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-24 14:19:53,765 - INFO - تم إعداد تبويب عرض الكروت لـ User Manager بنجاح
2025-07-24 14:19:53,965 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-24 14:19:53,966 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-24 14:19:54,206 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام user_manager
2025-07-24 14:19:54,207 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-24 14:19:54,329 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager
2025-07-24 14:19:54,349 - INFO - تم تسجيل العملية: تبديل النظام من التلجرام - تم تبديل النظام من None إلى user_manager
2025-07-24 14:19:54,350 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-24 14:19:54,393 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-24 14:19:54,410 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-24 14:19:54,840 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-24 14:19:54,841 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-24 14:19:54,877 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-24 14:19:54,878 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-24 14:19:54,881 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-24 14:19:54,886 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-24 14:19:54,889 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-24 14:19:54,890 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager - القالب: 10
2025-07-24 14:19:55,165 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-24 14:19:56,741 - INFO - معالجة callback: independent_create_um_10_lightning
2025-07-24 14:19:59,321 - INFO - معالجة callback: independent_count_um_10_lightning_200
2025-07-24 14:19:59,956 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-24 14:19:59,956 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-24 14:19:59,972 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-24 14:19:59,973 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-24 14:20:00,007 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-24 14:20:00,009 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-24 14:20:00,011 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-24 14:20:00,016 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-24 14:20:00,271 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-24 14:20:00,271 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-24 14:20:00,285 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-24 14:20:00,286 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-24 14:20:00,331 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-24 14:20:00,332 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-24 14:20:00,335 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-24 14:20:00,339 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-24 14:20:00,340 - INFO - ⚡ البرق: تم الحفاظ على العدد المحدد: 200
2025-07-24 14:20:00,341 - INFO - تم تطبيق إعدادات القالب '10' بنجاح
2025-07-24 14:20:00,341 - INFO - ⚡ استخدام البرق للتلجرام (مع جدولة مؤقتة وتنظيف تلقائي)
2025-07-24 14:20:00,341 - INFO - ⚡ بدء البرق التلقائي للتلجرام (مع جدولة مؤقتة وتنظيف تلقائي)
2025-07-24 14:20:00,342 - INFO - ⚡ البرق: استخدام العدد من التلجرام بوت: 200
2025-07-24 14:20:00,342 - INFO - ⚡ البرق للتلجرام: العدد المطلوب 200 حساب
2025-07-24 14:20:00,342 - INFO - ⚡ البرق: تم تحديث عدد الحسابات في الواجهة إلى: 200
2025-07-24 14:20:00,345 - INFO - ⚡ البرق للتلجرام: تم توليد 200 حساب
2025-07-24 14:20:00,345 - INFO - ⚡ البرق للتلجرام: تنفيذ متسلسل مجدول لـ 200 كارت (حد التقسيم: 100)
2025-07-24 14:20:00,346 - INFO - ⚡ البرق للتلجرام: بدء معالجة الأعداد الكبيرة مع جدولة واحدة
2025-07-24 14:20:00,347 - INFO - ⚡ البرق للتلجرام: تم تقسيم 200 كارت إلى 2 مجموعة للتنفيذ المتسلسل
2025-07-24 14:20:00,348 - INFO - ⚡ البرق للتلجرام: إنشاء السكريبت للمجموعة 1/2 (100 كارت)
2025-07-24 14:20:00,348 - INFO - ⚡ البرق للتلجرام: تم إعداد السكريبت telegram_lightning_batch1_user_manager_20250724_142000
2025-07-24 14:20:00,348 - INFO - ⚡ البرق للتلجرام: إنشاء السكريبت للمجموعة 2/2 (100 كارت)
2025-07-24 14:20:00,349 - INFO - ⚡ البرق للتلجرام: تم إعداد السكريبت telegram_lightning_batch2_user_manager_20250724_142000
2025-07-24 14:20:00,349 - INFO - محاولة الاتصال بـ *********:8728 (عادي)
2025-07-24 14:20:00,417 - INFO - نجح الاتصال مع *********
2025-07-24 14:20:00,489 - INFO - ⚡ تم إرسال السكريبت المترابط: telegram_lightning_batch1_user_manager_20250724_142000
2025-07-24 14:20:00,532 - INFO - ⚡ تم إرسال السكريبت المترابط: telegram_lightning_batch2_user_manager_20250724_142000
2025-07-24 14:20:00,567 - INFO - ⚡ وقت MikroTik الحالي: 11:19:59
2025-07-24 14:20:00,633 - INFO - ⚡ تم تعديل وقت التنفيذ لليوم التالي: 11:20:02
2025-07-24 14:20:00,633 - INFO - ⚡ السكريبت الأول سينفذ في: 11:20:02 (بعد 3 ثواني من وقت MikroTik)
2025-07-24 14:20:00,679 - INFO - ⚡ تم إنشاء الجدولة الرئيسية: telegram_main_schedule_20250724_142000 لتشغيل telegram_lightning_batch1_user_manager_20250724_142000 في 11:20:02
2025-07-24 14:20:00,690 - INFO - ⚡ تم إعداد البرق المترابط بنجاح - جدولة واحدة: telegram_main_schedule_20250724_142000, 2 سكريبت مترابط
2025-07-24 14:20:00,691 - INFO - ⚡ البرق للتلجرام مكتمل: تم إنشاء جدولة واحدة لـ 2 مجموعة متسلسلة (200 كارت)
2025-07-24 14:20:03,617 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 200
2025-07-24 14:20:03,661 - INFO - تم إنشاء PDF بنجاح: exports/كروت_(10)_ب20-200 كارت-24-07-2025-14-20-03-um.pdf
2025-07-24 14:20:03,663 - INFO - تم حفظ ملف .rsc الاحتياطي: exports/كروت_(10)_ب20-200 كارت-24-07-2025-14-20-03-um.rsc
2025-07-24 14:20:05,565 - INFO - تم إرسال الملف بنجاح: كروت_(10)_ب20-200 كارت-24-07-2025-14-20-03-um.pdf
2025-07-24 14:20:05,804 - INFO - تم إرسال 200 كرت عبر التلجرام باستخدام قالب 10
2025-07-24 16:35:18,638 - INFO - تم بدء تشغيل التطبيق
2025-07-24 16:35:18,691 - INFO - تم إنشاء المجلدات الأساسية
2025-07-24 16:35:19,323 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-24 16:35:19,323 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-24 16:35:19,716 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-24 16:35:24,454 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-24 16:35:24,455 - INFO - تم إعداد التطبيق بنجاح
2025-07-24 16:35:26,472 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-24 16:35:26,894 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-24 16:35:26,895 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-24 16:35:29,896 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-24 16:35:29,946 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-24 16:35:30,390 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-24 16:35:30,390 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-24 16:35:44,164 - INFO - معالجة callback: select_system_hs
2025-07-24 16:35:44,695 - INFO - تم إرسال 5 قالب مفلتر لنظام Hotspot
2025-07-24 16:35:47,758 - INFO - معالجة callback: independent_template_hs_10
2025-07-24 16:35:48,248 - INFO - 🔄 تبديل النظام تلقائياً من None إلى hotspot
2025-07-24 16:35:48,248 - INFO - 🔄 طلب تبديل النظام من التلجرام: None → hotspot
2025-07-24 16:35:48,556 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-24 16:35:48,643 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-24 16:35:48,744 - WARNING - تحذير: فشل في حفظ الإعدادات: 'MikroTikCardGenerator' object has no attribute 'version_combo'
2025-07-24 16:35:48,744 - INFO - تم تعيين النظام الجديد: hotspot
2025-07-24 16:35:48,745 - INFO - 🔄 إعادة بناء الواجهة للنظام: hotspot
2025-07-24 16:35:48,746 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-24 16:35:48,749 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-24 16:35:49,542 - INFO - تم إعداد تبويب عرض الكروت لـ Hotspot بنجاح
2025-07-24 16:35:49,720 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-24 16:35:49,721 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-24 16:35:49,890 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام hotspot
2025-07-24 16:35:49,891 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-24 16:35:49,984 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot
2025-07-24 16:35:50,011 - INFO - تم تسجيل العملية: تبديل النظام من التلجرام - تم تبديل النظام من None إلى hotspot
2025-07-24 16:35:50,014 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-24 16:35:50,015 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-24 16:35:50,017 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-24 16:35:50,372 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-24 16:35:50,372 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-24 16:35:50,409 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-24 16:35:50,410 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-24 16:35:50,414 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-24 16:35:50,418 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-24 16:35:50,422 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-24 16:35:50,424 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-24 16:35:50,698 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-24 16:35:52,416 - INFO - معالجة callback: independent_create_hs_10_lightning
2025-07-24 16:36:05,026 - INFO - معالجة callback: independent_count_hs_10_lightning_200
2025-07-24 16:36:05,516 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-24 16:36:05,517 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-24 16:36:05,538 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-24 16:36:05,543 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-24 16:36:05,579 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-24 16:36:05,579 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-24 16:36:05,583 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-24 16:36:05,598 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-24 16:36:05,855 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-24 16:36:05,855 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-24 16:36:05,882 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-24 16:36:05,883 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-24 16:36:05,921 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-24 16:36:05,922 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-24 16:36:05,925 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-24 16:36:05,930 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-24 16:36:05,931 - INFO - ⚡ البرق: تم الحفاظ على العدد المحدد: 5
2025-07-24 16:36:05,932 - INFO - تم تطبيق إعدادات القالب '10' بنجاح
2025-07-24 16:36:05,932 - INFO - ⚡ بدء البرق التلقائي الموحد لنظام Hotspot
2025-07-24 16:36:05,938 - INFO - ⚡ البرق: استخدام العدد من التلجرام بوت: 200
2025-07-24 16:36:05,939 - INFO - ⚡ البرق الموحد: العدد المطلوب 200 حساب لنظام Hotspot
2025-07-24 16:36:05,940 - INFO - ⚡ البرق: تم تحديث عدد الحسابات في الواجهة إلى: 200
2025-07-24 16:36:05,945 - INFO - ⚡ البرق الموحد: تم توليد 200 حساب لنظام Hotspot
2025-07-24 16:36:06,143 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 200
2025-07-24 16:36:06,220 - INFO - تم إنشاء PDF بنجاح: exports\كروت_(10)_ب20-200 كارت-24-07-2025-16-36-05-hotspot.pdf
2025-07-24 16:36:06,221 - INFO - تم حفظ ملف PDF: exports\كروت_(10)_ب20-200 كارت-24-07-2025-16-36-05-hotspot.pdf
2025-07-24 16:36:08,203 - INFO - ⚡ البرق الموحد: تم حفظ PDF: exports\كروت_(10)_ب20-200 كارت-24-07-2025-16-36-05-hotspot.pdf
2025-07-24 16:36:08,220 - INFO - ⚡ البرق الموحد: تم حفظ ملف .rsc الاحتياطي: exports\كروت_(10)_ب20-200 كارت-24-07-2025-16-36-05-hotspot.rsc
2025-07-24 16:36:08,869 - INFO - تم إرسال الملف بنجاح: كروت_(10)_ب20-200 كارت-24-07-2025-16-36-05-hotspot.pdf
2025-07-24 16:36:08,870 - INFO - ⚡ البرق الموحد: تم إرسال ملف PDF عبر التلجرام: exports\كروت_(10)_ب20-200 كارت-24-07-2025-16-36-05-hotspot.pdf
2025-07-24 16:36:09,115 - INFO - ⚡ البرق الموحد: بدء إرسال الكروت إلى MikroTik باستخدام آلية send_to_mikrotik
2025-07-24 16:36:09,115 - INFO - 🔍 بدء تنفيذ send_to_mikrotik_silent() - الدالة المخصصة للبرق الموحد
2025-07-24 16:36:09,116 - INFO - محاولة الاتصال بـ *********:8728 (عادي)
2025-07-24 16:36:09,183 - INFO - نجح الاتصال مع *********
2025-07-24 16:36:09,183 - INFO - 🔍 تأكيد: system_type = 'hotspot' - مناسب للبرق الموحد
2025-07-24 16:36:09,595 - INFO - ⚡ البرق الموحد: بدء إرسال 200 مستخدم إلى MikroTik
2025-07-24 16:36:09,596 - INFO - 🔍 تشخيص قبل بدء الإرسال: total=200, existing_users=323
2025-07-24 16:36:13,294 - INFO - ⚡ البرق الموحد: تم إرسال 50/200 مستخدم
2025-07-24 16:36:23,887 - INFO - ⚡ البرق الموحد: تم إرسال 100/200 مستخدم
2025-07-24 16:36:39,100 - INFO - ⚡ البرق الموحد: تم إرسال 150/200 مستخدم
2025-07-24 16:36:55,065 - INFO - ⚡ البرق الموحد: تم إرسال 200/200 مستخدم
2025-07-24 16:36:55,574 - INFO - ⚡ البرق الموحد: النتائج النهائية - نجح: 200, فشل: 0, مكرر: 0
2025-07-24 16:36:55,617 - INFO - 📊 بدء إرسال التقرير النهائي: card_type=lightning, success=200, failed=0
2025-07-24 16:36:55,903 - INFO - ✅ تم إرسال التقرير النهائي: 200 ناجح، 0 فاشل
2025-07-24 16:36:56,011 - INFO - ℹ️ لا توجد كروت فاشلة لحفظها: failed_cards=0
2025-07-24 16:36:56,018 - INFO - 🔍 تم حفظ last_send_stats: success=200, failed=0, successful_usernames=200
2025-07-24 16:36:56,022 - INFO - 🔍 تشخيص حفظ الكروت الناجحة: failed_count=0, success_count=200, system_type=hotspot
2025-07-24 16:36:56,027 - INFO - 🔍 عدد الكروت الناجحة في القائمة: 200
2025-07-24 16:36:56,029 - INFO - ❌ لم يتم حفظ الكروت الناجحة - الشروط غير مستوفاة
2025-07-24 16:36:56,031 - INFO - ❌ لم يتم حفظ الكروت الفاشلة للبرق - الشروط غير مستوفاة
2025-07-24 16:36:56,036 - INFO - ⚡ البرق الموحد مكتمل: تم إنشاء وإرسال 200 كارت بنجاح إلى MikroTik
2025-07-24 16:36:56,103 - INFO - 🔍 تشخيص إحصائيات البرق:
2025-07-24 16:36:56,112 - INFO -    - hasattr last_send_stats: True
2025-07-24 16:36:56,114 - INFO -    - success_count من last_send_stats: 200
2025-07-24 16:36:56,116 - INFO -    - failed_count من last_send_stats: 0
2025-07-24 16:36:56,117 - INFO -    - duplicates_count من last_send_stats: 0
2025-07-24 16:36:56,120 - INFO - ⚡ إرسال إشعار التأكيد إلى المستخدم 998535391...
2025-07-24 16:36:56,121 - INFO - 🔍 تشخيص شروط زر الحذف:
2025-07-24 16:36:56,121 - INFO -    - failed_count > 0: False (failed_count=0)
2025-07-24 16:36:56,123 - INFO -    - success_count > 0: True (success_count=200)
2025-07-24 16:36:56,124 - INFO -    - hasattr lightning_successful_cards: False
2025-07-24 16:36:56,126 - INFO - 🔍 نتيجة تقييم شروط زر الحذف: False
2025-07-24 16:36:56,127 - INFO - 📤 إرسال رسالة عادية بدون زر الحذف
2025-07-24 16:36:56,528 - INFO - ✅ تم إرسال إشعار التأكيد بنجاح عبر التلجرام: 200 كرت، حالة الإرسال: True
2025-07-24 16:36:56,782 - INFO - تم إرسال 200 كرت عبر البرق الموحد باستخدام قالب 10
2025-07-24 16:37:13,121 - INFO - معالجة callback: select_system_um
2025-07-24 16:37:13,682 - INFO - تم إرسال 2 قالب مفلتر لنظام User Manager
2025-07-24 16:37:15,953 - INFO - معالجة callback: independent_template_um_10
2025-07-24 16:37:16,439 - INFO - 🔄 تبديل النظام تلقائياً من hotspot إلى user_manager
2025-07-24 16:37:16,440 - INFO - 🔄 طلب تبديل النظام من التلجرام: hotspot → user_manager
2025-07-24 16:37:16,688 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-24 16:37:16,688 - INFO - ✅ تم قطع الاتصال الموجود بنجاح
2025-07-24 16:37:16,689 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-24 16:37:16,840 - INFO - تم حفظ الإعدادات قبل التبديل
2025-07-24 16:37:16,841 - INFO - تم تعيين النظام الجديد: user_manager
2025-07-24 16:37:16,842 - INFO - 🔄 إعادة بناء الواجهة للنظام: user_manager
2025-07-24 16:37:16,842 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-24 16:37:16,844 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-24 16:37:17,945 - INFO - تم إعداد تبويب عرض الكروت لـ User Manager بنجاح
2025-07-24 16:37:18,248 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-24 16:37:18,249 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-24 16:37:19,766 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام user_manager
2025-07-24 16:37:19,766 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-24 16:37:20,027 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager
2025-07-24 16:37:20,425 - INFO - تم تسجيل العملية: تبديل النظام من التلجرام - تم تبديل النظام من hotspot إلى user_manager
2025-07-24 16:37:20,426 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-24 16:37:20,426 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-24 16:37:20,427 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-24 16:37:25,908 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-24 16:37:25,910 - INFO - ✅ تم تحديث حد تقسيم البرق:  → 100
2025-07-24 16:37:25,911 - INFO - 🔄 تم جدولة تحديث معلومات الخادم في البوت
2025-07-24 16:37:25,911 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 1 إعدادات
2025-07-24 16:37:25,938 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 69 إعدادات
2025-07-24 16:37:25,939 - INFO - ✅ تم تطبيق 69 إعدادات من القالب '10' بمزامنة شاملة
2025-07-24 16:37:25,942 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-24 16:37:25,947 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-24 16:37:25,950 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-24 16:37:25,951 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager - القالب: 10
2025-07-24 16:37:26,214 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-24 16:37:28,972 - INFO - معالجة callback: independent_create_um_10_lightning
2025-07-24 16:37:31,110 - INFO - معالجة callback: independent_count_um_10_lightning_200
2025-07-24 16:37:31,585 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-24 16:37:31,585 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-24 16:37:31,600 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-24 16:37:31,611 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-24 16:37:31,636 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-24 16:37:31,637 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-24 16:37:31,640 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-24 16:37:31,644 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-24 16:37:31,873 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-24 16:37:31,873 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-24 16:37:31,889 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-24 16:37:31,898 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-24 16:37:31,930 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-24 16:37:31,931 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-24 16:37:31,935 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-24 16:37:31,952 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-24 16:37:31,954 - INFO - ⚡ البرق: تم الحفاظ على العدد المحدد: 200
2025-07-24 16:37:31,954 - INFO - تم تطبيق إعدادات القالب '10' بنجاح
2025-07-24 16:37:31,955 - INFO - ⚡ استخدام البرق للتلجرام (مع جدولة مؤقتة وتنظيف تلقائي)
2025-07-24 16:37:31,955 - INFO - ⚡ بدء البرق التلقائي للتلجرام (مع جدولة مؤقتة وتنظيف تلقائي)
2025-07-24 16:37:31,955 - INFO - ⚡ البرق: استخدام العدد من التلجرام بوت: 200
2025-07-24 16:37:31,955 - INFO - ⚡ البرق للتلجرام: العدد المطلوب 200 حساب
2025-07-24 16:37:31,956 - INFO - ⚡ البرق: تم تحديث عدد الحسابات في الواجهة إلى: 200
2025-07-24 16:37:31,960 - INFO - ⚡ البرق للتلجرام: تم توليد 200 حساب
2025-07-24 16:37:31,961 - INFO - ⚡ البرق للتلجرام: تنفيذ متسلسل مجدول لـ 200 كارت (حد التقسيم: 100)
2025-07-24 16:37:31,962 - INFO - ⚡ البرق للتلجرام: بدء معالجة الأعداد الكبيرة مع جدولة واحدة
2025-07-24 16:37:31,963 - INFO - ⚡ البرق للتلجرام: تم تقسيم 200 كارت إلى 2 مجموعة للتنفيذ المتسلسل
2025-07-24 16:37:31,963 - INFO - تم إنشاء اسكربت الإشعار قبل الإنشاء: lightning_pre_notification_20250724_163731
2025-07-24 16:37:31,964 - INFO - ⚡ تم إضافة اسكربت الإشعار قبل الإنشاء: lightning_pre_notification_20250724_163731
2025-07-24 16:37:31,964 - INFO - ⚡ البرق للتلجرام: إنشاء السكريبت للمجموعة 1/2 (100 كارت)
2025-07-24 16:37:31,964 - INFO - ⚡ البرق للتلجرام: تم إعداد السكريبت telegram_lightning_batch1_user_manager_20250724_163731
2025-07-24 16:37:31,965 - INFO - ⚡ البرق للتلجرام: إنشاء السكريبت للمجموعة 2/2 (100 كارت)
2025-07-24 16:37:31,965 - INFO - ⚡ البرق للتلجرام: تم إعداد السكريبت telegram_lightning_batch2_user_manager_20250724_163731
2025-07-24 16:37:31,965 - INFO - تم إنشاء اسكربت الإشعار بعد الإنشاء: lightning_post_notification_20250724_163731
2025-07-24 16:37:31,968 - INFO - ⚡ تم إضافة اسكربت الإشعار بعد الإنشاء: lightning_post_notification_20250724_163731
2025-07-24 16:37:31,968 - INFO - محاولة الاتصال بـ *********:8728 (عادي)
2025-07-24 16:37:32,034 - INFO - نجح الاتصال مع *********
2025-07-24 16:37:32,099 - INFO - ⚡ تم إرسال السكريبت المترابط: lightning_pre_notification_20250724_163731
2025-07-24 16:37:32,231 - INFO - ⚡ تم إرسال السكريبت المترابط: telegram_lightning_batch1_user_manager_20250724_163731
2025-07-24 16:37:32,268 - INFO - ⚡ تم إرسال السكريبت المترابط: telegram_lightning_batch2_user_manager_20250724_163731
2025-07-24 16:37:32,278 - INFO - ⚡ تم إرسال السكريبت المترابط: lightning_post_notification_20250724_163731
2025-07-24 16:37:32,314 - INFO - ⚡ وقت MikroTik الحالي: 13:37:31
2025-07-24 16:37:32,598 - INFO - ⚡ تم تعديل وقت التنفيذ لليوم التالي: 13:37:34
2025-07-24 16:37:32,599 - INFO - ⚡ السكريبت الأول سينفذ في: 13:37:34 (بعد 3 ثواني من وقت MikroTik)
2025-07-24 16:37:32,783 - INFO - ⚡ تم إنشاء الجدولة الرئيسية: telegram_main_schedule_20250724_163732 لتشغيل lightning_pre_notification_20250724_163731 في 13:37:34
2025-07-24 16:37:32,787 - INFO - ⚡ تم إعداد البرق المترابط بنجاح - جدولة واحدة: telegram_main_schedule_20250724_163732, 4 سكريبت مترابط
2025-07-24 16:37:32,787 - INFO - ⚡ البرق للتلجرام مكتمل: تم إنشاء جدولة واحدة لـ 2 مجموعة متسلسلة (200 كارت)
2025-07-24 16:37:35,240 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 200
2025-07-24 16:37:35,410 - INFO - تم إنشاء PDF بنجاح: exports/كروت_(10)_ب20-200 كارت-24-07-2025-16-37-34-um.pdf
2025-07-24 16:37:35,486 - INFO - تم حفظ ملف .rsc الاحتياطي: exports/كروت_(10)_ب20-200 كارت-24-07-2025-16-37-34-um.rsc
2025-07-24 16:37:38,605 - INFO - تم إرسال الملف بنجاح: كروت_(10)_ب20-200 كارت-24-07-2025-16-37-34-um.pdf
2025-07-24 16:37:38,854 - INFO - تم إرسال 200 كرت عبر التلجرام باستخدام قالب 10
2025-07-24 17:44:36,439 - INFO - تم بدء تشغيل التطبيق
2025-07-24 17:44:36,457 - INFO - تم إنشاء المجلدات الأساسية
2025-07-24 17:44:36,470 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-24 17:44:36,471 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-24 17:44:36,588 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-24 17:44:37,047 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-24 17:44:37,048 - INFO - تم إعداد التطبيق بنجاح
2025-07-24 17:44:37,645 - INFO - تم اختيار النظام: user_manager
2025-07-24 17:44:37,949 - INFO - تم إعداد تبويب عرض الكروت لـ User Manager بنجاح
2025-07-24 17:44:38,189 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-24 17:44:38,190 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-24 17:44:38,427 - INFO - تم تسجيل العملية: اختيار النظام - تم اختيار نظام user_manager
2025-07-24 17:44:39,035 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-24 17:44:39,319 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-24 17:44:39,342 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-24 17:44:37,880 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:44:42,118 - INFO - تم حفظ القالب: 10
2025-07-24 17:44:42,345 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-24 17:44:42,356 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-24 17:44:42,664 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-24 17:44:42,666 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-24 17:44:42,667 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-24 17:44:42,674 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-24 17:44:42,791 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:44:42,933 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-24 17:44:42,935 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-24 17:44:41,302 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:45:47,571 - INFO - تم بدء تشغيل التطبيق
2025-07-24 17:45:47,591 - INFO - تم إنشاء المجلدات الأساسية
2025-07-24 17:45:47,607 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-24 17:45:47,608 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-24 17:45:47,723 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-24 17:45:48,165 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-24 17:45:48,166 - INFO - تم إعداد التطبيق بنجاح
2025-07-24 17:45:50,174 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-24 17:45:50,509 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-24 17:45:50,510 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-24 17:45:49,038 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:45:53,508 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-24 17:45:53,525 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-24 17:45:53,844 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-24 17:45:53,845 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-24 17:45:53,959 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:45:52,574 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:45:55,125 - INFO - معالجة callback: select_system_um
2025-07-24 17:45:57,546 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:45:57,868 - INFO - معالجة callback: select_system_um
2025-07-24 17:45:56,814 - INFO - تم إرسال 2 قالب مفلتر لنظام User Manager
2025-07-24 17:45:58,583 - INFO - تم إرسال 2 قالب مفلتر لنظام User Manager
2025-07-24 17:45:57,169 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:45:58,159 - INFO - معالجة callback: independent_template_um_10
2025-07-24 17:45:58,878 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-24 17:45:58,878 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-24 17:45:58,879 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-24 17:45:58,895 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-24 17:45:58,898 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-24 17:45:58,926 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-24 17:45:58,929 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-24 17:45:58,951 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-24 17:45:58,955 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-24 17:45:58,963 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-24 17:45:58,975 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager - القالب: 10
2025-07-24 17:45:59,313 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-24 17:46:02,091 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:46:00,686 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:46:02,562 - INFO - معالجة callback: independent_create_um_10_lightning
2025-07-24 17:46:00,912 - INFO - معالجة callback: independent_create_um_10_lightning
2025-07-24 17:46:06,809 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:46:05,658 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:46:06,698 - INFO - معالجة callback: independent_create_um_10_lightning
2025-07-24 17:46:10,578 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:46:09,091 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:46:10,193 - INFO - معالجة callback: independent_count_um_10_lightning_200
2025-07-24 17:46:10,879 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-24 17:46:10,880 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-24 17:46:10,893 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-24 17:46:10,894 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-24 17:46:10,922 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-24 17:46:10,922 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-24 17:46:10,925 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-24 17:46:10,930 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-24 17:46:11,329 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-24 17:46:11,329 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-24 17:46:11,341 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-24 17:46:11,352 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-24 17:46:11,391 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-24 17:46:11,391 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-24 17:46:11,395 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-24 17:46:11,410 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-24 17:46:11,411 - INFO - ⚡ البرق: تم الحفاظ على العدد المحدد: 200
2025-07-24 17:46:11,412 - INFO - تم تطبيق إعدادات القالب '10' بنجاح
2025-07-24 17:46:11,412 - INFO - ⚡ استخدام البرق للتلجرام (مع جدولة مؤقتة وتنظيف تلقائي)
2025-07-24 17:46:11,413 - INFO - ⚡ بدء البرق التلقائي للتلجرام (مع جدولة مؤقتة وتنظيف تلقائي)
2025-07-24 17:46:11,413 - INFO - ⚡ البرق: استخدام العدد من التلجرام بوت: 200
2025-07-24 17:46:11,413 - INFO - ⚡ البرق للتلجرام: العدد المطلوب 200 حساب
2025-07-24 17:46:11,414 - INFO - ⚡ البرق: تم تحديث عدد الحسابات في الواجهة إلى: 200
2025-07-24 17:46:11,417 - INFO - ⚡ البرق للتلجرام: تم توليد 200 حساب
2025-07-24 17:46:11,417 - INFO - ⚡ البرق للتلجرام: تنفيذ متسلسل مجدول لـ 200 كارت (حد التقسيم: 100)
2025-07-24 17:46:11,417 - INFO - ⚡ البرق للتلجرام: بدء معالجة الأعداد الكبيرة مع جدولة واحدة
2025-07-24 17:46:11,418 - INFO - ⚡ البرق للتلجرام: تم تقسيم 200 كارت إلى 2 مجموعة للتنفيذ المتسلسل
2025-07-24 17:46:11,419 - INFO - تم إنشاء اسكربت الإشعار قبل الإنشاء: lightning_pre_notification_20250724_174611
2025-07-24 17:46:11,420 - INFO - ⚡ تم إضافة اسكربت الإشعار قبل الإنشاء: lightning_pre_notification_20250724_174611
2025-07-24 17:46:11,421 - INFO - ⚡ البرق للتلجرام: إنشاء السكريبت للمجموعة 1/2 (100 كارت)
2025-07-24 17:46:11,421 - INFO - ⚡ البرق للتلجرام: تم إعداد السكريبت telegram_lightning_batch1_user_manager_20250724_174611
2025-07-24 17:46:11,423 - INFO - ⚡ البرق للتلجرام: إنشاء السكريبت للمجموعة 2/2 (100 كارت)
2025-07-24 17:46:11,424 - INFO - ⚡ البرق للتلجرام: تم إعداد السكريبت telegram_lightning_batch2_user_manager_20250724_174611
2025-07-24 17:46:11,425 - INFO - تم إنشاء اسكربت الإشعار بعد الإنشاء: lightning_post_notification_20250724_174611
2025-07-24 17:46:11,425 - INFO - ⚡ تم إضافة اسكربت الإشعار بعد الإنشاء: lightning_post_notification_20250724_174611
2025-07-24 17:46:11,430 - INFO - استخدام الاتصال الحالي
2025-07-24 17:46:11,517 - INFO - ⚡ تم إرسال السكريبت المترابط: lightning_pre_notification_20250724_174611
2025-07-24 17:46:11,557 - INFO - ⚡ تم إرسال السكريبت المترابط: telegram_lightning_batch1_user_manager_20250724_174611
2025-07-24 17:46:11,693 - INFO - ⚡ تم إرسال السكريبت المترابط: telegram_lightning_batch2_user_manager_20250724_174611
2025-07-24 17:46:11,704 - INFO - ⚡ تم إرسال السكريبت المترابط: lightning_post_notification_20250724_174611
2025-07-24 17:46:11,742 - INFO - ⚡ وقت MikroTik الحالي: 14:46:10
2025-07-24 17:46:11,749 - INFO - ⚡ تم تعديل وقت التنفيذ لليوم التالي: 14:46:13
2025-07-24 17:46:11,749 - INFO - ⚡ السكريبت الأول سينفذ في: 14:46:13 (بعد 3 ثواني من وقت MikroTik)
2025-07-24 17:46:11,794 - INFO - ⚡ تم إنشاء الجدولة الرئيسية: telegram_main_schedule_20250724_174611 لتشغيل lightning_pre_notification_20250724_174611 في 14:46:13
2025-07-24 17:46:11,797 - INFO - ⚡ تم إعداد البرق المترابط بنجاح - جدولة واحدة: telegram_main_schedule_20250724_174611, 4 سكريبت مترابط
2025-07-24 17:46:11,798 - INFO - ⚡ البرق للتلجرام مكتمل: تم إنشاء جدولة واحدة لـ 2 مجموعة متسلسلة (200 كارت)
2025-07-24 17:46:14,045 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:46:12,557 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:46:14,503 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 200
2025-07-24 17:46:14,521 - INFO - تم إنشاء PDF بنجاح: exports/كروت_(10)_ب20-200 كارت-24-07-2025-17-46-14-um.pdf
2025-07-24 17:46:14,541 - INFO - تم حفظ ملف .rsc الاحتياطي: exports/كروت_(10)_ب20-200 كارت-24-07-2025-17-46-14-um.rsc
2025-07-24 17:46:15,194 - INFO - تم إرسال الملف بنجاح: كروت_(10)_ب20-200 كارت-24-07-2025-17-46-14-um.pdf
2025-07-24 17:46:15,483 - INFO - تم إرسال 200 كرت عبر التلجرام باستخدام قالب 10
2025-07-24 17:46:17,524 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:46:16,222 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:46:21,183 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:46:19,729 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:46:24,648 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:46:23,159 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:46:28,073 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:46:26,586 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:46:31,490 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:46:30,008 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:46:34,912 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:46:33,427 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:46:38,337 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:46:36,847 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:46:41,755 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:46:40,312 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:46:45,277 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:46:43,801 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:46:48,708 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:46:47,222 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:46:52,172 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:46:50,687 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:46:55,594 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:46:54,155 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:46:59,073 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:46:57,600 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:47:02,513 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:47:01,038 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:47:05,944 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:47:04,456 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:47:09,411 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:47:07,967 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:47:12,936 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:47:11,533 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:47:16,439 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:47:14,951 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:47:19,871 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:47:18,384 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:47:23,286 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:47:21,828 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:47:26,734 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:47:25,259 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:47:30,166 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:47:28,683 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:47:33,586 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:47:32,100 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:47:37,005 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:47:35,522 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:47:40,429 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:47:38,951 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:47:43,858 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:47:42,384 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:47:47,569 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:47:46,224 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:47:51,191 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:47:49,773 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:47:54,742 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:47:53,335 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:47:58,293 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:47:56,816 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:48:01,721 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:48:00,236 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:48:05,168 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:48:03,683 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:48:08,586 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:48:07,115 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:48:12,081 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:48:10,597 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:48:15,507 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:48:14,023 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:48:18,935 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:48:17,449 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:48:22,353 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:48:20,895 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:48:25,799 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:48:24,315 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:48:29,232 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:48:27,746 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:48:32,667 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:48:31,186 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:48:36,086 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:48:34,603 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:48:39,504 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:48:38,036 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:48:42,936 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:48:41,455 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:48:46,361 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:48:44,876 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:48:49,784 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:48:48,315 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:48:53,217 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:48:51,737 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:48:56,696 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:48:55,270 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:49:00,169 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:48:58,688 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:49:03,587 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:49:02,123 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:49:07,044 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:49:05,575 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:49:10,480 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:49:08,999 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:49:13,917 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:49:12,633 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:49:17,593 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:49:16,191 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:49:21,146 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:49:19,693 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:49:24,638 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:49:23,165 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:49:28,074 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:49:26,593 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:49:31,492 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:49:30,025 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:49:34,924 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:49:33,450 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:49:38,369 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:49:36,892 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:49:41,806 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:49:40,334 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:49:45,235 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:49:43,800 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:49:48,698 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:49:47,225 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:49:52,129 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:49:50,649 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:49:55,551 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:49:54,119 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:49:59,090 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:49:57,612 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:50:02,536 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:50:01,068 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:50:05,965 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:50:04,483 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:50:09,387 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:50:07,905 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:50:12,803 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:50:11,325 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:50:16,274 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:50:14,814 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:50:19,713 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:50:18,232 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:50:23,138 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:50:21,659 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:50:26,559 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:50:25,099 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:50:30,000 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:50:28,528 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:50:33,422 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:50:31,954 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:50:36,853 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:50:35,390 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:50:40,291 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:50:38,813 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:50:43,714 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:50:42,283 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:50:47,180 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:50:45,715 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:50:50,618 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:50:49,159 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:50:54,059 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:50:52,583 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:50:57,479 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:50:56,070 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:51:00,967 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:50:59,493 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:51:04,436 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:51:02,967 - ERROR - خطأ في API التلجرام: 409
2025-07-24 17:51:06,361 - INFO - بدء إغلاق التطبيق
2025-07-24 17:51:06,363 - ERROR - خطأ أثناء إغلاق التطبيق: 'MikroTikCardGenerator' object has no attribute 'version_combo'
2025-07-24 19:29:13,829 - INFO - تم بدء تشغيل التطبيق
2025-07-24 19:29:13,893 - INFO - تم إنشاء المجلدات الأساسية
2025-07-24 19:29:14,006 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-24 19:29:14,007 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-24 19:29:15,128 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-24 19:29:18,530 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-24 19:29:18,532 - INFO - تم إعداد التطبيق بنجاح
2025-07-24 19:29:20,565 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-24 19:29:20,965 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-24 19:29:20,966 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-24 19:29:23,977 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-24 19:29:24,072 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-24 19:29:24,369 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-24 19:29:24,369 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-24 19:29:35,132 - INFO - معالجة callback: select_system_um
2025-07-24 19:29:35,593 - INFO - تم إرسال 2 قالب مفلتر لنظام User Manager
2025-07-24 19:29:36,740 - INFO - معالجة callback: independent_template_um_10
2025-07-24 19:29:37,215 - INFO - 🔄 تبديل النظام تلقائياً من None إلى user_manager
2025-07-24 19:29:37,216 - INFO - 🔄 طلب تبديل النظام من التلجرام: None → user_manager
2025-07-24 19:29:37,498 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-24 19:29:37,595 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-24 19:29:37,696 - WARNING - تحذير: فشل في حفظ الإعدادات: 'MikroTikCardGenerator' object has no attribute 'version_combo'
2025-07-24 19:29:37,696 - INFO - تم تعيين النظام الجديد: user_manager
2025-07-24 19:29:37,697 - INFO - 🔄 إعادة بناء الواجهة للنظام: user_manager
2025-07-24 19:29:37,698 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-24 19:29:37,702 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-24 19:29:38,361 - INFO - تم إعداد تبويب عرض الكروت لـ User Manager بنجاح
2025-07-24 19:29:38,545 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-24 19:29:38,545 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-24 19:29:38,705 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام user_manager
2025-07-24 19:29:38,706 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-24 19:29:38,784 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager
2025-07-24 19:29:38,808 - INFO - تم تسجيل العملية: تبديل النظام من التلجرام - تم تبديل النظام من None إلى user_manager
2025-07-24 19:29:38,810 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-24 19:29:38,811 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-24 19:29:38,812 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-24 19:29:39,238 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-24 19:29:39,238 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-24 19:29:39,275 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-24 19:29:39,276 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-24 19:29:39,279 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-24 19:29:39,283 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-24 19:29:39,286 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-24 19:29:39,287 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager - القالب: 10
2025-07-24 19:29:39,695 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-24 19:29:41,220 - INFO - معالجة callback: independent_create_um_10_lightning
2025-07-24 19:29:44,623 - INFO - معالجة callback: independent_count_um_10_lightning_200
2025-07-24 19:29:45,212 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-24 19:29:45,212 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-24 19:29:45,227 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-24 19:29:45,228 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-24 19:29:45,260 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-24 19:29:45,262 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-24 19:29:45,263 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-24 19:29:45,268 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-24 19:29:45,590 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-24 19:29:45,590 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-24 19:29:45,604 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-24 19:29:45,615 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-24 19:29:45,641 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-24 19:29:45,641 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-24 19:29:45,644 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-24 19:29:45,658 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-24 19:29:45,659 - INFO - ⚡ البرق: تم الحفاظ على العدد المحدد: 200
2025-07-24 19:29:45,659 - INFO - تم تطبيق إعدادات القالب '10' بنجاح
2025-07-24 19:29:45,660 - INFO - ⚡ استخدام البرق للتلجرام (مع جدولة مؤقتة وتنظيف تلقائي)
2025-07-24 19:29:45,660 - INFO - ⚡ بدء البرق التلقائي للتلجرام (مع جدولة مؤقتة وتنظيف تلقائي)
2025-07-24 19:29:45,661 - INFO - ⚡ البرق: استخدام العدد من التلجرام بوت: 200
2025-07-24 19:29:45,662 - INFO - ⚡ البرق للتلجرام: العدد المطلوب 200 حساب
2025-07-24 19:29:45,662 - INFO - ⚡ البرق: تم تحديث عدد الحسابات في الواجهة إلى: 200
2025-07-24 19:29:45,665 - INFO - ⚡ البرق للتلجرام: تم توليد 200 حساب
2025-07-24 19:29:45,665 - INFO - ⚡ البرق للتلجرام: تنفيذ متسلسل مجدول لـ 200 كارت (حد التقسيم: 100)
2025-07-24 19:29:45,665 - INFO - ⚡ البرق للتلجرام: بدء معالجة الأعداد الكبيرة مع جدولة واحدة
2025-07-24 19:29:45,666 - INFO - ⚡ البرق للتلجرام: تم تقسيم 200 كارت إلى 2 مجموعة للتنفيذ المتسلسل
2025-07-24 19:29:45,667 - INFO - ⚡ البرق للتلجرام: إنشاء السكريبت للمجموعة 1/2 (100 كارت)
2025-07-24 19:29:45,667 - INFO - ⚡ البرق للتلجرام: تم إعداد السكريبت telegram_lightning_batch1_user_manager_20250724_192945
2025-07-24 19:29:45,667 - INFO - ⚡ البرق للتلجرام: إنشاء السكريبت للمجموعة 2/2 (100 كارت)
2025-07-24 19:29:45,668 - INFO - ⚡ البرق للتلجرام: تم إعداد السكريبت telegram_lightning_batch2_user_manager_20250724_192945
2025-07-24 19:29:45,668 - INFO - محاولة الاتصال بـ *********:8728 (عادي)
2025-07-24 19:29:45,730 - INFO - نجح الاتصال مع *********
2025-07-24 19:29:45,786 - INFO - ⚡ تم إرسال السكريبت المترابط: telegram_lightning_batch1_user_manager_20250724_192945
2025-07-24 19:29:45,827 - INFO - ⚡ تم إرسال السكريبت المترابط: telegram_lightning_batch2_user_manager_20250724_192945
2025-07-24 19:29:45,870 - INFO - ⚡ وقت MikroTik الحالي: 16:29:44
2025-07-24 19:29:45,965 - INFO - ⚡ تم تعديل وقت التنفيذ لليوم التالي: 16:29:47
2025-07-24 19:29:45,965 - INFO - ⚡ السكريبت الأول سينفذ في: 16:29:47 (بعد 3 ثواني من وقت MikroTik)
2025-07-24 19:29:46,012 - INFO - ⚡ تم إنشاء الجدولة الرئيسية: telegram_main_schedule_20250724_192945 لتشغيل telegram_lightning_batch1_user_manager_20250724_192945 في 16:29:47
2025-07-24 19:29:46,015 - INFO - ⚡ تم إعداد البرق المترابط بنجاح - جدولة واحدة: telegram_main_schedule_20250724_192945, 2 سكريبت مترابط
2025-07-24 19:29:46,016 - INFO - ⚡ البرق للتلجرام مكتمل: تم إنشاء جدولة واحدة لـ 2 مجموعة متسلسلة (200 كارت)
2025-07-24 19:29:48,869 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 200
2025-07-24 19:29:48,947 - INFO - تم إنشاء PDF بنجاح: exports/كروت_(10)_ب20-200 كارت-24-07-2025-19-29-48-um.pdf
2025-07-24 19:29:48,969 - INFO - تم حفظ ملف .rsc الاحتياطي: exports/كروت_(10)_ب20-200 كارت-24-07-2025-19-29-48-um.rsc
2025-07-24 19:29:49,644 - INFO - تم إرسال الملف بنجاح: كروت_(10)_ب20-200 كارت-24-07-2025-19-29-48-um.pdf
2025-07-24 19:29:49,904 - INFO - تم إرسال 200 كرت عبر التلجرام باستخدام قالب 10
2025-07-24 19:39:33,474 - ERROR - خطأ في معالجة تحديثات التلجرام: HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot5161769536:AAHFeXIB-kCvIfo_NwfR7dwiWOUgXJF-p-Y/getUpdates?offset=858841483&timeout=10 (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001B70CFAC690>: Failed to establish a new connection: [WinError 10051] A socket operation was attempted to an unreachable network'))
2025-07-24 19:45:40,551 - INFO - معالجة الأمر: /start
2025-07-24 19:45:40,795 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-24 19:45:42,097 - INFO - معالجة callback: select_system_um
2025-07-24 19:45:42,565 - INFO - تم إرسال 2 قالب مفلتر لنظام User Manager
2025-07-24 19:45:43,933 - INFO - معالجة callback: independent_template_um_10
2025-07-24 19:45:44,413 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-24 19:45:44,414 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-24 19:45:44,414 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-24 19:45:44,420 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-24 19:45:44,427 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-24 19:45:44,468 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-24 19:45:44,468 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-24 19:45:44,469 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-24 19:45:44,474 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-24 19:45:44,476 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-24 19:45:44,481 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager - القالب: 10
2025-07-24 19:45:44,758 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-24 19:46:18,925 - INFO - معالجة callback: independent_create_um_10_lightning
2025-07-24 19:46:22,081 - INFO - معالجة callback: independent_count_um_10_lightning_200
2025-07-24 19:46:22,609 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-24 19:46:22,609 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-24 19:46:22,631 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-24 19:46:22,635 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-24 19:46:22,661 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-24 19:46:22,661 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-24 19:46:22,667 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-24 19:46:22,680 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-24 19:46:22,980 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-24 19:46:22,981 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-24 19:46:22,995 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-24 19:46:22,996 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-24 19:46:23,035 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-24 19:46:23,036 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-24 19:46:23,038 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-24 19:46:23,053 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-24 19:46:23,054 - INFO - ⚡ البرق: تم الحفاظ على العدد المحدد: 200
2025-07-24 19:46:23,055 - INFO - تم تطبيق إعدادات القالب '10' بنجاح
2025-07-24 19:46:23,055 - INFO - ⚡ استخدام البرق للتلجرام (مع جدولة مؤقتة وتنظيف تلقائي)
2025-07-24 19:46:23,058 - INFO - ⚡ بدء البرق التلقائي للتلجرام (مع جدولة مؤقتة وتنظيف تلقائي)
2025-07-24 19:46:23,058 - INFO - ⚡ البرق: استخدام العدد من التلجرام بوت: 200
2025-07-24 19:46:23,059 - INFO - ⚡ البرق للتلجرام: العدد المطلوب 200 حساب
2025-07-24 19:46:23,060 - INFO - ⚡ البرق: تم تحديث عدد الحسابات في الواجهة إلى: 200
2025-07-24 19:46:23,063 - INFO - ⚡ البرق للتلجرام: تم توليد 200 حساب
2025-07-24 19:46:23,064 - INFO - ⚡ البرق للتلجرام: تنفيذ متسلسل مجدول لـ 200 كارت (حد التقسيم: 100)
2025-07-24 19:46:23,064 - INFO - ⚡ البرق للتلجرام: بدء معالجة الأعداد الكبيرة مع جدولة واحدة
2025-07-24 19:46:23,064 - INFO - ⚡ البرق للتلجرام: تم تقسيم 200 كارت إلى 2 مجموعة للتنفيذ المتسلسل
2025-07-24 19:46:23,064 - INFO - ⚡ البرق للتلجرام: إنشاء السكريبت للمجموعة 1/2 (100 كارت)
2025-07-24 19:46:23,065 - INFO - ⚡ البرق للتلجرام: تم إعداد السكريبت telegram_lightning_batch1_user_manager_20250724_194623
2025-07-24 19:46:23,066 - INFO - ⚡ البرق للتلجرام: إنشاء السكريبت للمجموعة 2/2 (100 كارت)
2025-07-24 19:46:23,066 - INFO - ⚡ البرق للتلجرام: تم إعداد السكريبت telegram_lightning_batch2_user_manager_20250724_194623
2025-07-24 19:46:23,067 - WARNING - الاتصال الحالي غير صالح: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-07-24 19:46:23,067 - INFO - تم قطع الاتصال مع MikroTik
2025-07-24 19:46:23,067 - INFO - محاولة الاتصال بـ *********:8728 (عادي)
2025-07-24 19:46:23,137 - INFO - نجح الاتصال مع *********
2025-07-24 19:46:23,208 - INFO - ⚡ تم إرسال السكريبت المترابط: telegram_lightning_batch1_user_manager_20250724_194623
2025-07-24 19:46:23,351 - INFO - ⚡ تم إرسال السكريبت المترابط: telegram_lightning_batch2_user_manager_20250724_194623
2025-07-24 19:46:23,356 - INFO - ⚡ وقت MikroTik الحالي: 16:46:23
2025-07-24 19:46:23,356 - INFO - ⚡ تم تعديل وقت التنفيذ لليوم التالي: 16:46:26
2025-07-24 19:46:23,357 - INFO - ⚡ السكريبت الأول سينفذ في: 16:46:26 (بعد 3 ثواني من وقت MikroTik)
2025-07-24 19:46:23,406 - INFO - ⚡ تم إنشاء الجدولة الرئيسية: telegram_main_schedule_20250724_194623 لتشغيل telegram_lightning_batch1_user_manager_20250724_194623 في 16:46:26
2025-07-24 19:46:23,410 - INFO - ⚡ تم إعداد البرق المترابط بنجاح - جدولة واحدة: telegram_main_schedule_20250724_194623, 2 سكريبت مترابط
2025-07-24 19:46:23,410 - INFO - ⚡ البرق للتلجرام مكتمل: تم إنشاء جدولة واحدة لـ 2 مجموعة متسلسلة (200 كارت)
2025-07-24 19:46:26,145 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 200
2025-07-24 19:46:26,186 - INFO - تم إنشاء PDF بنجاح: exports/كروت_(10)_ب20-200 كارت-24-07-2025-19-46-26-um.pdf
2025-07-24 19:46:26,189 - INFO - تم حفظ ملف .rsc الاحتياطي: exports/كروت_(10)_ب20-200 كارت-24-07-2025-19-46-26-um.rsc
2025-07-24 19:46:26,884 - INFO - تم إرسال الملف بنجاح: كروت_(10)_ب20-200 كارت-24-07-2025-19-46-26-um.pdf
2025-07-24 19:46:27,194 - INFO - تم إرسال 200 كرت عبر التلجرام باستخدام قالب 10
2025-07-24 19:49:41,753 - INFO - بدء إغلاق التطبيق
2025-07-24 19:49:42,097 - INFO - تم قطع الاتصال مع MikroTik
2025-07-24 19:49:42,711 - INFO - تم تسجيل العملية: إغلاق التطبيق - تم إغلاق التطبيق بنجاح
2025-07-24 19:49:42,711 - INFO - تم إغلاق التطبيق بنجاح
2025-07-24 20:36:50,133 - INFO - تم بدء تشغيل التطبيق
2025-07-24 20:36:50,661 - INFO - تم إنشاء المجلدات الأساسية
2025-07-24 20:36:50,689 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-24 20:36:50,874 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-24 20:36:50,997 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-24 20:36:51,893 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-24 20:36:51,937 - INFO - تم إعداد التطبيق بنجاح
2025-07-24 20:36:53,946 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-24 20:36:54,537 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-24 20:36:54,538 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-24 20:36:57,552 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-24 20:36:57,555 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-24 20:36:57,828 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-24 20:36:57,828 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-24 20:37:00,819 - INFO - معالجة callback: select_system_um
2025-07-24 20:37:01,407 - INFO - تم إرسال 2 قالب مفلتر لنظام User Manager
2025-07-24 20:37:04,466 - INFO - معالجة callback: independent_template_um_10
2025-07-24 20:37:04,950 - INFO - 🔄 تبديل النظام تلقائياً من None إلى user_manager
2025-07-24 20:37:04,950 - INFO - 🔄 طلب تبديل النظام من التلجرام: None → user_manager
2025-07-24 20:37:05,251 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-24 20:37:05,276 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-24 20:37:05,376 - WARNING - تحذير: فشل في حفظ الإعدادات: 'MikroTikCardGenerator' object has no attribute 'version_combo'
2025-07-24 20:37:05,377 - INFO - تم تعيين النظام الجديد: user_manager
2025-07-24 20:37:05,378 - INFO - 🔄 إعادة بناء الواجهة للنظام: user_manager
2025-07-24 20:37:05,379 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-24 20:37:05,385 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-24 20:37:05,877 - INFO - تم إعداد تبويب عرض الكروت لـ User Manager بنجاح
2025-07-24 20:37:06,060 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-24 20:37:06,061 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-24 20:37:06,223 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام user_manager
2025-07-24 20:37:06,224 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-24 20:37:06,314 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager
2025-07-24 20:37:06,315 - INFO - تم تسجيل العملية: تبديل النظام من التلجرام - تم تبديل النظام من None إلى user_manager
2025-07-24 20:37:06,324 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-24 20:37:06,326 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-24 20:37:06,327 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-24 20:37:06,619 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-24 20:37:06,620 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-24 20:37:06,653 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-24 20:37:06,653 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-24 20:37:06,656 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-24 20:37:06,661 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-24 20:37:06,664 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-24 20:37:06,673 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager - القالب: 10
2025-07-24 20:37:06,979 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-24 20:37:07,449 - INFO - معالجة callback: header_filtered_um
2025-07-24 20:37:08,876 - INFO - معالجة callback: independent_create_um_10_lightning
2025-07-24 20:37:11,175 - INFO - معالجة callback: independent_count_um_10_lightning_200
2025-07-24 20:37:11,771 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-24 20:37:11,771 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-24 20:37:11,786 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-24 20:37:11,787 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-24 20:37:11,820 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-24 20:37:11,823 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-24 20:37:11,829 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-24 20:37:11,836 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-24 20:37:12,193 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-24 20:37:12,193 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-24 20:37:12,215 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-24 20:37:12,216 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-24 20:37:12,253 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-24 20:37:12,253 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-24 20:37:12,257 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-24 20:37:12,270 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-24 20:37:12,271 - INFO - ⚡ البرق: تم الحفاظ على العدد المحدد: 200
2025-07-24 20:37:12,274 - INFO - تم تطبيق إعدادات القالب '10' بنجاح
2025-07-24 20:37:12,274 - INFO - ⚡ استخدام البرق للتلجرام (مع جدولة مؤقتة وتنظيف تلقائي)
2025-07-24 20:37:12,275 - INFO - ⚡ بدء البرق التلقائي للتلجرام (مع جدولة مؤقتة وتنظيف تلقائي)
2025-07-24 20:37:12,275 - INFO - ⚡ البرق: استخدام العدد من التلجرام بوت: 200
2025-07-24 20:37:12,275 - INFO - ⚡ البرق للتلجرام: العدد المطلوب 200 حساب
2025-07-24 20:37:12,276 - INFO - ⚡ البرق: تم تحديث عدد الحسابات في الواجهة إلى: 200
2025-07-24 20:37:12,278 - INFO - ⚡ البرق للتلجرام: تم توليد 200 حساب
2025-07-24 20:37:12,278 - INFO - ⚡ البرق للتلجرام: تنفيذ متسلسل مجدول لـ 200 كارت (حد التقسيم: 100)
2025-07-24 20:37:12,281 - INFO - ⚡ البرق للتلجرام: بدء معالجة الأعداد الكبيرة مع جدولة واحدة
2025-07-24 20:37:12,282 - INFO - ⚡ البرق للتلجرام: تم تقسيم 200 كارت إلى 2 مجموعة للتنفيذ المتسلسل
2025-07-24 20:37:12,282 - INFO - ⚡ البرق للتلجرام: إنشاء السكريبت للمجموعة 1/2 (100 كارت)
2025-07-24 20:37:12,282 - INFO - ⚡ البرق للتلجرام: تم إعداد السكريبت telegram_lightning_batch1_user_manager_20250724_203712
2025-07-24 20:37:12,282 - INFO - ⚡ البرق للتلجرام: إنشاء السكريبت للمجموعة 2/2 (100 كارت)
2025-07-24 20:37:12,283 - INFO - ⚡ البرق للتلجرام: تم إعداد السكريبت telegram_lightning_batch2_user_manager_20250724_203712
2025-07-24 20:37:12,283 - INFO - محاولة الاتصال بـ *********:8728 (عادي)
2025-07-24 20:37:12,378 - INFO - نجح الاتصال مع *********
2025-07-24 20:37:12,537 - INFO - ⚡ تم إرسال السكريبت المترابط: telegram_lightning_batch1_user_manager_20250724_203712
2025-07-24 20:37:12,577 - INFO - ⚡ تم إرسال السكريبت المترابط: telegram_lightning_batch2_user_manager_20250724_203712
2025-07-24 20:37:12,582 - INFO - ⚡ وقت MikroTik الحالي: 17:37:12
2025-07-24 20:37:12,597 - INFO - ⚡ تم تعديل وقت التنفيذ لليوم التالي: 17:37:15
2025-07-24 20:37:12,597 - INFO - ⚡ السكريبت الأول سينفذ في: 17:37:15 (بعد 3 ثواني من وقت MikroTik)
2025-07-24 20:37:12,647 - INFO - ⚡ تم إنشاء الجدولة الرئيسية: telegram_main_schedule_20250724_203712 لتشغيل telegram_lightning_batch1_user_manager_20250724_203712 في 17:37:15
2025-07-24 20:37:12,650 - INFO - ⚡ تم إعداد البرق المترابط بنجاح - جدولة واحدة: telegram_main_schedule_20250724_203712, 2 سكريبت مترابط
2025-07-24 20:37:12,651 - INFO - ⚡ البرق للتلجرام مكتمل: تم إنشاء جدولة واحدة لـ 2 مجموعة متسلسلة (200 كارت)
2025-07-24 20:37:15,393 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 200
2025-07-24 20:37:15,428 - INFO - تم إنشاء PDF بنجاح: exports/كروت_(10)_ب20-200 كارت-24-07-2025-20-37-15-um.pdf
2025-07-24 20:37:15,430 - INFO - تم حفظ ملف .rsc الاحتياطي: exports/كروت_(10)_ب20-200 كارت-24-07-2025-20-37-15-um.rsc
2025-07-24 20:37:16,123 - INFO - تم إرسال الملف بنجاح: كروت_(10)_ب20-200 كارت-24-07-2025-20-37-15-um.pdf
2025-07-24 20:37:16,365 - INFO - تم إرسال 200 كرت عبر التلجرام باستخدام قالب 10
2025-07-24 20:47:53,026 - INFO - بدء إغلاق التطبيق
2025-07-24 20:47:53,028 - INFO - تم قطع الاتصال مع MikroTik
2025-07-24 20:47:53,055 - INFO - تم تسجيل العملية: إغلاق التطبيق - تم إغلاق التطبيق بنجاح
2025-07-24 20:47:53,056 - INFO - تم إغلاق التطبيق بنجاح
2025-07-24 23:29:27,246 - INFO - تم بدء تشغيل التطبيق
2025-07-24 23:29:27,375 - INFO - تم إنشاء المجلدات الأساسية
2025-07-24 23:29:27,714 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-24 23:29:27,714 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-24 23:29:28,927 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-24 23:29:34,132 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-24 23:29:34,133 - INFO - تم إعداد التطبيق بنجاح
2025-07-24 23:29:36,155 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-24 23:29:36,593 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-24 23:29:36,594 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-24 23:29:39,602 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-24 23:29:39,645 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-24 23:29:39,979 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-24 23:29:39,979 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-24 23:29:52,253 - INFO - معالجة callback: select_system_um
2025-07-24 23:29:52,784 - INFO - تم إرسال 2 قالب مفلتر لنظام User Manager
2025-07-24 23:29:54,389 - INFO - معالجة callback: independent_template_um_10
2025-07-24 23:29:55,033 - INFO - 🔄 تبديل النظام تلقائياً من None إلى user_manager
2025-07-24 23:29:55,033 - INFO - 🔄 طلب تبديل النظام من التلجرام: None → user_manager
2025-07-24 23:29:55,303 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-24 23:29:55,575 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-24 23:29:55,676 - WARNING - تحذير: فشل في حفظ الإعدادات: 'MikroTikCardGenerator' object has no attribute 'version_combo'
2025-07-24 23:29:55,676 - INFO - تم تعيين النظام الجديد: user_manager
2025-07-24 23:29:55,677 - INFO - 🔄 إعادة بناء الواجهة للنظام: user_manager
2025-07-24 23:29:55,678 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-24 23:29:55,684 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-24 23:29:56,421 - INFO - تم إعداد تبويب عرض الكروت لـ User Manager بنجاح
2025-07-24 23:29:56,630 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-24 23:29:56,631 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-24 23:29:56,825 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام user_manager
2025-07-24 23:29:56,825 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-24 23:29:56,933 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager
2025-07-24 23:29:56,992 - INFO - تم تسجيل العملية: تبديل النظام من التلجرام - تم تبديل النظام من None إلى user_manager
2025-07-24 23:29:56,995 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-24 23:29:57,000 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-24 23:29:57,001 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-24 23:29:57,300 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-24 23:29:57,301 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-24 23:29:57,338 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-24 23:29:57,338 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-24 23:29:57,341 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-24 23:29:57,346 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-24 23:29:57,349 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-24 23:29:57,352 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager - القالب: 10
2025-07-24 23:29:57,630 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-24 23:30:00,163 - INFO - معالجة callback: independent_create_um_10_lightning
2025-07-24 23:30:03,134 - INFO - معالجة callback: independent_count_um_10_lightning_200
2025-07-24 23:30:03,708 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-24 23:30:03,709 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-24 23:30:03,723 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-24 23:30:03,723 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-24 23:30:03,775 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-24 23:30:03,776 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-24 23:30:03,779 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-24 23:30:03,783 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-24 23:30:04,084 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-24 23:30:04,085 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-24 23:30:04,099 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-24 23:30:04,100 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-24 23:30:04,149 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-24 23:30:04,149 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-24 23:30:04,153 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-24 23:30:04,157 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-24 23:30:04,157 - INFO - ⚡ البرق: تم الحفاظ على العدد المحدد: 200
2025-07-24 23:30:04,158 - INFO - تم تطبيق إعدادات القالب '10' بنجاح
2025-07-24 23:30:04,159 - INFO - ⚡ استخدام البرق للتلجرام (مع جدولة مؤقتة وتنظيف تلقائي)
2025-07-24 23:30:04,160 - INFO - ⚡ بدء البرق التلقائي للتلجرام (مع جدولة مؤقتة وتنظيف تلقائي)
2025-07-24 23:30:04,161 - INFO - ⚡ البرق: استخدام العدد من التلجرام بوت: 200
2025-07-24 23:30:04,161 - INFO - ⚡ البرق للتلجرام: العدد المطلوب 200 حساب
2025-07-24 23:30:04,161 - INFO - ⚡ البرق: تم تحديث عدد الحسابات في الواجهة إلى: 200
2025-07-24 23:30:04,165 - INFO - ⚡ البرق للتلجرام: تم توليد 200 حساب
2025-07-24 23:30:04,166 - INFO - ⚡ البرق للتلجرام: تنفيذ متسلسل مجدول لـ 200 كارت (حد التقسيم: 100)
2025-07-24 23:30:04,167 - INFO - ⚡ البرق للتلجرام: بدء معالجة الأعداد الكبيرة مع جدولة واحدة
2025-07-24 23:30:04,168 - INFO - ⚡ البرق للتلجرام: تم تقسيم 200 كارت إلى 2 مجموعة للتنفيذ المتسلسل
2025-07-24 23:30:04,169 - INFO - ⚡ البرق للتلجرام: إنشاء السكريبت للمجموعة 1/2 (100 كارت)
2025-07-24 23:30:04,171 - INFO - ⚡ البرق للتلجرام: تم إعداد السكريبت telegram_lightning_batch1_user_manager_20250724_233004
2025-07-24 23:30:04,182 - INFO - ⚡ البرق للتلجرام: إنشاء السكريبت للمجموعة 2/2 (100 كارت)
2025-07-24 23:30:04,186 - INFO - ⚡ البرق للتلجرام: تم إعداد السكريبت telegram_lightning_batch2_user_manager_20250724_233004
2025-07-24 23:30:04,189 - INFO - محاولة الاتصال بـ *********:8728 (عادي)
2025-07-24 23:30:04,314 - INFO - نجح الاتصال مع *********
2025-07-24 23:30:04,369 - INFO - ⚡ تم إرسال السكريبت المترابط: telegram_lightning_batch1_user_manager_20250724_233004
2025-07-24 23:30:04,406 - INFO - ⚡ تم إرسال السكريبت المترابط: telegram_lightning_batch2_user_manager_20250724_233004
2025-07-24 23:30:04,441 - INFO - ⚡ وقت MikroTik الحالي: 20:30:04
2025-07-24 23:30:04,472 - INFO - ⚡ تم تعديل وقت التنفيذ لليوم التالي: 20:30:07
2025-07-24 23:30:04,472 - INFO - ⚡ السكريبت الأول سينفذ في: 20:30:07 (بعد 3 ثواني من وقت MikroTik)
2025-07-24 23:30:04,523 - INFO - ⚡ تم إنشاء الجدولة الرئيسية: telegram_main_schedule_20250724_233004 لتشغيل telegram_lightning_batch1_user_manager_20250724_233004 في 20:30:07
2025-07-24 23:30:04,527 - INFO - ⚡ تم إعداد البرق المترابط بنجاح - جدولة واحدة: telegram_main_schedule_20250724_233004, 2 سكريبت مترابط
2025-07-24 23:30:04,527 - INFO - ⚡ البرق للتلجرام مكتمل: تم إنشاء جدولة واحدة لـ 2 مجموعة متسلسلة (200 كارت)
2025-07-24 23:30:07,351 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 200
2025-07-24 23:30:07,397 - INFO - تم إنشاء PDF بنجاح: exports/كروت_(10)_ب20-200 كارت-24-07-2025-23-30-07-um.pdf
2025-07-24 23:30:07,399 - INFO - تم حفظ ملف .rsc الاحتياطي: exports/كروت_(10)_ب20-200 كارت-24-07-2025-23-30-07-um.rsc
2025-07-24 23:30:08,589 - INFO - تم إرسال الملف بنجاح: كروت_(10)_ب20-200 كارت-24-07-2025-23-30-07-um.pdf
2025-07-24 23:30:08,830 - INFO - تم إرسال 200 كرت عبر التلجرام باستخدام قالب 10
2025-07-24 23:59:51,981 - INFO - تم بدء تشغيل التطبيق
2025-07-24 23:59:52,423 - INFO - تم إنشاء المجلدات الأساسية
2025-07-24 23:59:52,516 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-24 23:59:52,518 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-24 23:59:54,187 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-25 00:00:09,058 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-25 00:00:09,058 - INFO - تم إعداد التطبيق بنجاح
2025-07-25 00:00:11,133 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-25 00:00:12,478 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-25 00:00:12,479 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-25 00:00:15,483 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-25 00:00:15,605 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-25 00:00:15,898 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-25 00:00:15,898 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-25 00:01:10,755 - INFO - معالجة callback: select_system_um
2025-07-25 00:01:11,244 - INFO - تم إرسال 2 قالب مفلتر لنظام User Manager
2025-07-25 00:01:16,136 - INFO - معالجة callback: independent_template_um_10
2025-07-25 00:01:16,627 - INFO - 🔄 تبديل النظام تلقائياً من None إلى user_manager
2025-07-25 00:01:16,627 - INFO - 🔄 طلب تبديل النظام من التلجرام: None → user_manager
2025-07-25 00:01:16,897 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-25 00:01:17,098 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-25 00:01:17,199 - WARNING - تحذير: فشل في حفظ الإعدادات: 'MikroTikCardGenerator' object has no attribute 'version_combo'
2025-07-25 00:01:17,199 - INFO - تم تعيين النظام الجديد: user_manager
2025-07-25 00:01:17,200 - INFO - 🔄 إعادة بناء الواجهة للنظام: user_manager
2025-07-25 00:01:17,200 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-25 00:01:17,204 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-25 00:01:17,849 - INFO - تم إعداد تبويب عرض الكروت لـ User Manager بنجاح
2025-07-25 00:01:18,060 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-25 00:01:18,061 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-25 00:01:18,215 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام user_manager
2025-07-25 00:01:18,216 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-25 00:01:18,328 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager
2025-07-25 00:01:18,335 - INFO - تم تسجيل العملية: تبديل النظام من التلجرام - تم تبديل النظام من None إلى user_manager
2025-07-25 00:01:18,369 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-25 00:01:18,370 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-25 00:01:18,371 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-25 00:01:18,820 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-25 00:01:18,821 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-25 00:01:18,864 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-25 00:01:18,864 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-25 00:01:18,868 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-25 00:01:18,872 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-25 00:01:18,875 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-25 00:01:18,876 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager - القالب: 10
2025-07-25 00:01:19,244 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-25 00:01:21,855 - INFO - معالجة callback: independent_create_um_10_lightning
2025-07-25 00:01:25,864 - INFO - معالجة callback: independent_count_um_10_lightning_200
2025-07-25 00:01:26,343 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-25 00:01:26,343 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-25 00:01:26,356 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-25 00:01:26,376 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-25 00:01:26,404 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-25 00:01:26,405 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-25 00:01:26,408 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-25 00:01:26,412 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-25 00:01:26,658 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-25 00:01:26,659 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-25 00:01:26,690 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-25 00:01:26,691 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-25 00:01:26,750 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-25 00:01:26,750 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-25 00:01:26,754 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-25 00:01:26,757 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-25 00:01:26,759 - INFO - ⚡ البرق: تم الحفاظ على العدد المحدد: 200
2025-07-25 00:01:26,760 - INFO - تم تطبيق إعدادات القالب '10' بنجاح
2025-07-25 00:01:26,760 - INFO - تم تحديد معلومات المستخدم الحالي: مستخدم التلجرام بوت
2025-07-25 00:01:26,761 - INFO - ⚡ استخدام البرق للتلجرام (مع جدولة مؤقتة وتنظيف تلقائي)
2025-07-25 00:01:26,761 - INFO - ⚡ بدء البرق التلقائي للتلجرام (مع جدولة مؤقتة وتنظيف تلقائي)
2025-07-25 00:01:26,761 - INFO - ⚡ البرق: استخدام العدد من التلجرام بوت: 200
2025-07-25 00:01:26,762 - INFO - ⚡ البرق للتلجرام: العدد المطلوب 200 حساب
2025-07-25 00:01:26,762 - INFO - ⚡ البرق: تم تحديث عدد الحسابات في الواجهة إلى: 200
2025-07-25 00:01:26,764 - INFO - ⚡ البرق للتلجرام: تم توليد 200 حساب
2025-07-25 00:01:26,764 - INFO - ⚡ البرق للتلجرام: تنفيذ متسلسل مجدول لـ 200 كارت (حد التقسيم: 100)
2025-07-25 00:01:26,765 - INFO - ⚡ البرق للتلجرام: بدء معالجة الأعداد الكبيرة مع جدولة واحدة
2025-07-25 00:01:26,765 - INFO - ⚡ البرق للتلجرام: تم تقسيم 200 كارت إلى 2 مجموعة للتنفيذ المتسلسل
2025-07-25 00:01:26,768 - INFO - ⚡ البرق للتلجرام: إنشاء السكريبت للمجموعة 1/2 (100 كارت)
2025-07-25 00:01:26,768 - INFO - ⚡ البرق للتلجرام: تم إعداد السكريبت telegram_lightning_batch1_user_manager_20250725_000126
2025-07-25 00:01:26,769 - INFO - ⚡ البرق للتلجرام: إنشاء السكريبت للمجموعة 2/2 (100 كارت)
2025-07-25 00:01:26,770 - ERROR - خطأ في البرق للأعداد الكبيرة للتلجرام: name 'bot_token' is not defined
2025-07-25 00:01:49,923 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 100
2025-07-25 00:01:50,029 - INFO - تم إنشاء PDF بنجاح: exports/كروت_(10)_ب20-100 كارت-25-07-2025-00-01-49-um.pdf
2025-07-25 00:01:50,059 - INFO - تم حفظ ملف .rsc الاحتياطي: exports/كروت_(10)_ب20-100 كارت-25-07-2025-00-01-49-um.rsc
2025-07-25 00:01:51,944 - INFO - تم إرسال الملف بنجاح: كروت_(10)_ب20-100 كارت-25-07-2025-00-01-49-um.pdf
2025-07-25 00:01:52,195 - INFO - تم إرسال 100 كرت عبر التلجرام باستخدام قالب 10
