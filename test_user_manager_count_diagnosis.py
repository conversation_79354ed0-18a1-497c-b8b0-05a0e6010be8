#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار تشخيص مشكلة جلب العدد الحالي للكروت في User Manager
تم إنشاؤه: 2025-07-24
الهدف: تشخيص سبب ظهور "0" بدلاً من العدد الفعلي في الإشعار
"""

import unittest
import sys
import os
from unittest.mock import Mock, patch, MagicMock

# إضافة مسار المشروع
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

class TestUserManagerCountDiagnosis(unittest.TestCase):
    """اختبار تشخيص مشكلة جلب العدد الحالي للكروت"""
    
    def setUp(self):
        """إعداد الاختبار"""
        # إنشاء mock للتطبيق
        self.mock_app = Mock()
        self.mock_app.logger = Mock()
        
        # محاكاة بيانات الاتصال
        self.mock_app.api_ip_entry = Mock()
        self.mock_app.api_ip_entry.get.return_value = "***********"
        self.mock_app.api_username_entry = Mock()
        self.mock_app.api_username_entry.get.return_value = "admin"
        self.mock_app.api_password_entry = Mock()
        self.mock_app.api_password_entry.get.return_value = "password"
        self.mock_app.use_ssl_var = Mock()
        self.mock_app.use_ssl_var.get.return_value = False
        self.mock_app.api_port_entry = Mock()
        self.mock_app.api_port_entry.get.return_value = "8728"

    def test_routeros_availability_check(self):
        """اختبار توفر مكتبة routeros_api"""
        
        # محاكاة فحص توفر المكتبة
        def mock_check_routeros_availability():
            try:
                import routeros_api
                return True, "مكتبة routeros_api متوفرة"
            except ImportError:
                return False, "مكتبة routeros_api غير مثبتة"
        
        # تنفيذ الاختبار
        available, message = mock_check_routeros_availability()
        
        # التحقق من النتائج
        if available:
            print(f"✅ {message}")
            self.assertTrue(available)
        else:
            print(f"❌ {message}")
            self.fail("مكتبة routeros_api غير متوفرة - يجب تثبيتها أولاً")

    def test_connection_parameters_validation(self):
        """اختبار صحة بارامترات الاتصال"""
        
        # محاكاة فحص بارامترات الاتصال
        def mock_validate_connection_params(host, username, password, port):
            issues = []
            
            if not host or host.strip() == "":
                issues.append("عنوان IP فارغ")
            elif not host.replace(".", "").replace(":", "").isalnum():
                issues.append("عنوان IP غير صالح")
            
            if not username or username.strip() == "":
                issues.append("اسم المستخدم فارغ")
            
            if not password or password.strip() == "":
                issues.append("كلمة المرور فارغة")
            
            try:
                port_num = int(port)
                if port_num < 1 or port_num > 65535:
                    issues.append("رقم المنفذ غير صالح")
            except ValueError:
                issues.append("رقم المنفذ ليس رقماً")
            
            return len(issues) == 0, issues
        
        # تنفيذ الاختبار
        valid, issues = mock_validate_connection_params("***********", "admin", "password", "8728")
        
        # التحقق من النتائج
        if valid:
            print("✅ جميع بارامترات الاتصال صحيحة")
            self.assertTrue(valid)
        else:
            print(f"❌ مشاكل في بارامترات الاتصال: {', '.join(issues)}")
            self.fail(f"بارامترات الاتصال غير صحيحة: {issues}")

    def test_api_connection_simulation(self):
        """اختبار محاكاة الاتصال بـ API"""
        
        # محاكاة الاتصال بـ MikroTik
        def mock_connect_to_mikrotik(host, username, password, port, use_ssl=False):
            try:
                # محاكاة إنشاء الاتصال
                connection_info = {
                    "host": host,
                    "username": username,
                    "port": port,
                    "ssl": use_ssl,
                    "connected": True
                }
                
                # محاكاة اختبار الاتصال
                identity_response = [{"name": "MikroTik-Test"}]
                
                return True, connection_info, identity_response
                
            except Exception as e:
                return False, None, str(e)
        
        # تنفيذ الاختبار
        success, connection, response = mock_connect_to_mikrotik("***********", "admin", "password", 8728)
        
        # التحقق من النتائج
        if success:
            print(f"✅ نجح الاتصال بـ {connection['host']}:{connection['port']}")
            print(f"📋 اسم النظام: {response[0]['name']}")
            self.assertTrue(success)
        else:
            print(f"❌ فشل في الاتصال: {response}")
            self.fail(f"فشل في الاتصال: {response}")

    def test_user_manager_data_retrieval(self):
        """اختبار جلب بيانات User Manager"""
        
        # محاكاة جلب بيانات المستخدمين
        def mock_get_user_manager_users():
            # محاكاة قائمة مستخدمين
            mock_users = [
                {"name": "user001", "profile": "1day", "password": "pass001"},
                {"name": "user002", "profile": "1day", "password": "pass002"},
                {"name": "user003", "profile": "1week", "password": "pass003"},
                {"name": "user004", "profile": "1day", "password": "pass004"},
                {"name": "user005", "profile": "1month", "password": "pass005"}
            ]
            
            return True, mock_users, len(mock_users)
        
        # تنفيذ الاختبار
        success, users, count = mock_get_user_manager_users()
        
        # التحقق من النتائج
        if success:
            print(f"✅ تم جلب {count} مستخدم من User Manager")
            print("👥 عينة من المستخدمين:")
            for i, user in enumerate(users[:3]):
                print(f"  {i+1}. {user['name']} (البروفايل: {user['profile']})")
            self.assertTrue(success)
            self.assertGreater(count, 0)
        else:
            print("❌ فشل في جلب بيانات User Manager")
            self.fail("فشل في جلب بيانات User Manager")

    def test_count_calculation_accuracy(self):
        """اختبار دقة حساب العدد"""
        
        # محاكاة حساب العدد من قائمة المستخدمين
        def mock_calculate_user_count(user_list):
            if not user_list:
                return 0, "قائمة المستخدمين فارغة"
            
            if not isinstance(user_list, list):
                return 0, "قائمة المستخدمين ليست من نوع list"
            
            count = len(user_list)
            return count, f"تم حساب {count} مستخدم"
        
        # اختبار حالات مختلفة
        test_cases = [
            ([], 0, "قائمة فارغة"),
            ([{"name": "user1"}], 1, "مستخدم واحد"),
            ([{"name": f"user{i}"} for i in range(1, 6)], 5, "5 مستخدمين"),
            ([{"name": f"user{i}"} for i in range(1, 101)], 100, "100 مستخدم"),
        ]
        
        for user_list, expected_count, description in test_cases:
            count, message = mock_calculate_user_count(user_list)
            print(f"🧮 {description}: {count} (متوقع: {expected_count})")
            self.assertEqual(count, expected_count, f"خطأ في حساب العدد لـ {description}")
        
        print("✅ جميع اختبارات حساب العدد نجحت")

    def test_error_handling_scenarios(self):
        """اختبار سيناريوهات معالجة الأخطاء"""
        
        # محاكاة سيناريوهات الأخطاء المختلفة
        def mock_handle_error_scenarios():
            scenarios = [
                {
                    "name": "مكتبة غير متوفرة",
                    "error": "ImportError",
                    "expected_count": 0,
                    "should_log": True
                },
                {
                    "name": "فشل في الاتصال",
                    "error": "ConnectionError",
                    "expected_count": 0,
                    "should_log": True
                },
                {
                    "name": "خطأ في API",
                    "error": "APIError",
                    "expected_count": 0,
                    "should_log": True
                },
                {
                    "name": "مهلة انتهت",
                    "error": "TimeoutError",
                    "expected_count": 0,
                    "should_log": True
                }
            ]
            
            results = []
            for scenario in scenarios:
                # محاكاة معالجة الخطأ
                try:
                    if scenario["error"] == "ImportError":
                        raise ImportError("No module named 'routeros_api'")
                    elif scenario["error"] == "ConnectionError":
                        raise ConnectionError("Connection refused")
                    elif scenario["error"] == "APIError":
                        raise Exception("API call failed")
                    elif scenario["error"] == "TimeoutError":
                        raise TimeoutError("Request timed out")
                except Exception as e:
                    # معالجة الخطأ وإرجاع القيمة الافتراضية
                    count = 0
                    logged = True
                    results.append({
                        "scenario": scenario["name"],
                        "count": count,
                        "logged": logged,
                        "error": str(e)
                    })
            
            return results
        
        # تنفيذ الاختبار
        results = mock_handle_error_scenarios()
        
        # التحقق من النتائج
        for result in results:
            print(f"🔍 {result['scenario']}: العدد={result['count']}, مسجل={result['logged']}")
            self.assertEqual(result['count'], 0, f"العدد يجب أن يكون 0 في حالة {result['scenario']}")
            self.assertTrue(result['logged'], f"يجب تسجيل الخطأ في حالة {result['scenario']}")
        
        print("✅ جميع اختبارات معالجة الأخطاء نجحت")

    def test_notification_message_formatting(self):
        """اختبار تنسيق رسالة الإشعار مع العدد الصحيح"""
        
        # محاكاة تنسيق رسالة الإشعار
        def mock_format_notification_message(template_name, system_name, count, time_str, date_str):
            if count == 0:
                count_status = "⚠️ لا توجد كروت حالياً"
            elif count < 10:
                count_status = f"📊 عدد قليل ({count})"
            elif count < 100:
                count_status = f"📈 عدد متوسط ({count})"
            else:
                count_status = f"📊 عدد كبير ({count})"
            
            message = f"""📊 **إشعار اختيار البرق**

🎯 **القالب المختار:** {template_name}
🔧 **النظام:** {system_name}
⚡ **الطريقة:** البرق (Lightning)
📈 **العدد الحالي للكروت:** {count}
{count_status}

⏰ **الوقت:** {time_str}
📅 **التاريخ:** {date_str}

✅ **الحالة:** جاهز لبدء عملية البرق"""
            
            return message
        
        # اختبار تنسيق الرسالة مع أعداد مختلفة
        test_cases = [
            (0, "لا توجد كروت"),
            (5, "عدد قليل"),
            (50, "عدد متوسط"),
            (500, "عدد كبير")
        ]
        
        for count, description in test_cases:
            message = mock_format_notification_message(
                "قالب_اختبار", "User Manager", count, "14:30:25", "2025-07-24"
            )
            
            print(f"📝 {description} ({count}):")
            print("─" * 50)
            print(message)
            print("─" * 50)
            
            # التحقق من وجود العدد في الرسالة
            self.assertIn(str(count), message, f"العدد {count} يجب أن يظهر في الرسالة")
            self.assertIn("قالب_اختبار", message, "اسم القالب يجب أن يظهر في الرسالة")
            self.assertIn("User Manager", message, "نوع النظام يجب أن يظهر في الرسالة")
        
        print("✅ جميع اختبارات تنسيق الرسالة نجحت")

def run_diagnosis():
    """تشغيل جميع اختبارات التشخيص"""
    print("🔍 بدء تشخيص مشكلة جلب العدد الحالي للكروت في User Manager")
    print("=" * 80)
    
    # إنشاء مجموعة الاختبارات
    test_suite = unittest.TestLoader().loadTestsFromTestCase(TestUserManagerCountDiagnosis)
    
    # تشغيل الاختبارات
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # عرض النتائج والتوصيات
    print("\n" + "=" * 80)
    if result.wasSuccessful():
        print("🎉 جميع اختبارات التشخيص نجحت!")
        print(f"✅ تم تشغيل {result.testsRun} اختبار بنجاح")
        print("\n📋 التوصيات:")
        print("1. تأكد من تثبيت مكتبة routeros_api")
        print("2. تحقق من صحة بيانات الاتصال (IP, Username, Password)")
        print("3. تأكد من أن MikroTik يقبل الاتصالات على المنفذ المحدد")
        print("4. تحقق من وجود مستخدمين فعلاً في User Manager")
        print("5. راجع ملف السجل للحصول على تفاصيل أكثر")
    else:
        print("❌ بعض اختبارات التشخيص فشلت!")
        print(f"❌ فشل {len(result.failures)} اختبار")
        print(f"⚠️ خطأ في {len(result.errors)} اختبار")
        print("\n🔧 خطوات الإصلاح:")
        print("1. راجع الأخطاء المعروضة أعلاه")
        print("2. تأكد من تثبيت جميع المتطلبات")
        print("3. تحقق من إعدادات الشبكة والاتصال")
    
    return result.wasSuccessful()

if __name__ == "__main__":
    success = run_diagnosis()
    sys.exit(0 if success else 1)
