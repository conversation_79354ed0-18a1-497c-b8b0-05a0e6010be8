# MikroTik Script - نسخة احتياطية للكروت
# تم الإنشاء: 2025-07-25 23:11:38
# القالب: 10
# النظام: user_manager
# عدد الكروت: 200
# تم إنشاؤه بواسطة: مولد كروت MikroTik

:put "🚀 بدء تنفيذ النسخة الاحتياطية للكروت";
:put "📋 القالب: 10";
:put "🎯 النظام: user_manager";
:put "📊 عدد الكروت: 200";

:local success 0;
:local errors 0;
:local total 200;

:put "⏳ بدء إضافة الكروت...";

# إضافة مستخدمي User Manager
:put "👤 إضافة 200 مستخدم User Manager...";

# المستخدم 1: 0142227354
:do {
    /tool user-manager user add customer="admin" username="0142227354" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0142227354";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0142227354";
};

# المستخدم 2: 0149660655
:do {
    /tool user-manager user add customer="admin" username="0149660655" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0149660655";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0149660655";
};

# المستخدم 3: 0104789425
:do {
    /tool user-manager user add customer="admin" username="0104789425" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0104789425";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0104789425";
};

# المستخدم 4: 0120535194
:do {
    /tool user-manager user add customer="admin" username="0120535194" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0120535194";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0120535194";
};

# المستخدم 5: 0111651473
:do {
    /tool user-manager user add customer="admin" username="0111651473" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0111651473";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0111651473";
};

# المستخدم 6: 0102851791
:do {
    /tool user-manager user add customer="admin" username="0102851791" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0102851791";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0102851791";
};

# المستخدم 7: 0161059317
:do {
    /tool user-manager user add customer="admin" username="0161059317" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0161059317";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0161059317";
};

# المستخدم 8: 0140358509
:do {
    /tool user-manager user add customer="admin" username="0140358509" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0140358509";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0140358509";
};

# المستخدم 9: 0104632059
:do {
    /tool user-manager user add customer="admin" username="0104632059" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0104632059";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0104632059";
};

# المستخدم 10: 0118007346
:do {
    /tool user-manager user add customer="admin" username="0118007346" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0118007346";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0118007346";
};

# المستخدم 11: 0161238017
:do {
    /tool user-manager user add customer="admin" username="0161238017" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0161238017";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0161238017";
};

# المستخدم 12: 0140960808
:do {
    /tool user-manager user add customer="admin" username="0140960808" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0140960808";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0140960808";
};

# المستخدم 13: 0123999965
:do {
    /tool user-manager user add customer="admin" username="0123999965" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0123999965";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0123999965";
};

# المستخدم 14: 0105087959
:do {
    /tool user-manager user add customer="admin" username="0105087959" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0105087959";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0105087959";
};

# المستخدم 15: 0165006225
:do {
    /tool user-manager user add customer="admin" username="0165006225" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0165006225";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0165006225";
};

# المستخدم 16: 0145630563
:do {
    /tool user-manager user add customer="admin" username="0145630563" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0145630563";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0145630563";
};

# المستخدم 17: 0167703793
:do {
    /tool user-manager user add customer="admin" username="0167703793" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0167703793";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0167703793";
};

# المستخدم 18: 0147033089
:do {
    /tool user-manager user add customer="admin" username="0147033089" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0147033089";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0147033089";
};

# المستخدم 19: 0176195151
:do {
    /tool user-manager user add customer="admin" username="0176195151" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0176195151";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0176195151";
};

# المستخدم 20: 0107824045
:do {
    /tool user-manager user add customer="admin" username="0107824045" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0107824045";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0107824045";
};

# المستخدم 21: 0109335819
:do {
    /tool user-manager user add customer="admin" username="0109335819" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0109335819";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0109335819";
};

# المستخدم 22: 0125246072
:do {
    /tool user-manager user add customer="admin" username="0125246072" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0125246072";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0125246072";
};

# المستخدم 23: 0195815552
:do {
    /tool user-manager user add customer="admin" username="0195815552" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0195815552";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0195815552";
};

# المستخدم 24: 0153946747
:do {
    /tool user-manager user add customer="admin" username="0153946747" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0153946747";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0153946747";
};

# المستخدم 25: 0163596279
:do {
    /tool user-manager user add customer="admin" username="0163596279" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0163596279";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0163596279";
};

# المستخدم 26: 0105132904
:do {
    /tool user-manager user add customer="admin" username="0105132904" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0105132904";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0105132904";
};

# المستخدم 27: 0127041135
:do {
    /tool user-manager user add customer="admin" username="0127041135" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0127041135";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0127041135";
};

# المستخدم 28: 0162970278
:do {
    /tool user-manager user add customer="admin" username="0162970278" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0162970278";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0162970278";
};

# المستخدم 29: 0168615052
:do {
    /tool user-manager user add customer="admin" username="0168615052" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0168615052";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0168615052";
};

# المستخدم 30: 0123508087
:do {
    /tool user-manager user add customer="admin" username="0123508087" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0123508087";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0123508087";
};

# المستخدم 31: 0182201999
:do {
    /tool user-manager user add customer="admin" username="0182201999" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0182201999";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0182201999";
};

# المستخدم 32: 0136779928
:do {
    /tool user-manager user add customer="admin" username="0136779928" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0136779928";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0136779928";
};

# المستخدم 33: 0111879160
:do {
    /tool user-manager user add customer="admin" username="0111879160" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0111879160";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0111879160";
};

# المستخدم 34: 0123876743
:do {
    /tool user-manager user add customer="admin" username="0123876743" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0123876743";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0123876743";
};

# المستخدم 35: 0100072243
:do {
    /tool user-manager user add customer="admin" username="0100072243" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0100072243";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0100072243";
};

# المستخدم 36: 0177576123
:do {
    /tool user-manager user add customer="admin" username="0177576123" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0177576123";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0177576123";
};

# المستخدم 37: 0194558183
:do {
    /tool user-manager user add customer="admin" username="0194558183" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0194558183";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0194558183";
};

# المستخدم 38: 0108549086
:do {
    /tool user-manager user add customer="admin" username="0108549086" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0108549086";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0108549086";
};

# المستخدم 39: 0129946326
:do {
    /tool user-manager user add customer="admin" username="0129946326" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0129946326";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0129946326";
};

# المستخدم 40: 0180942702
:do {
    /tool user-manager user add customer="admin" username="0180942702" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0180942702";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0180942702";
};

# المستخدم 41: 0188018247
:do {
    /tool user-manager user add customer="admin" username="0188018247" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0188018247";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0188018247";
};

# المستخدم 42: 0186696950
:do {
    /tool user-manager user add customer="admin" username="0186696950" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0186696950";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0186696950";
};

# المستخدم 43: 0128621597
:do {
    /tool user-manager user add customer="admin" username="0128621597" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0128621597";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0128621597";
};

# المستخدم 44: 0138047355
:do {
    /tool user-manager user add customer="admin" username="0138047355" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0138047355";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0138047355";
};

# المستخدم 45: 0150112308
:do {
    /tool user-manager user add customer="admin" username="0150112308" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0150112308";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0150112308";
};

# المستخدم 46: 0130818945
:do {
    /tool user-manager user add customer="admin" username="0130818945" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0130818945";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0130818945";
};

# المستخدم 47: 0117282271
:do {
    /tool user-manager user add customer="admin" username="0117282271" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0117282271";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0117282271";
};

# المستخدم 48: 0104145360
:do {
    /tool user-manager user add customer="admin" username="0104145360" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0104145360";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0104145360";
};

# المستخدم 49: 0126621939
:do {
    /tool user-manager user add customer="admin" username="0126621939" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0126621939";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0126621939";
};

# المستخدم 50: 0116411703
:do {
    /tool user-manager user add customer="admin" username="0116411703" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0116411703";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0116411703";
};

# المستخدم 51: 0148305406
:do {
    /tool user-manager user add customer="admin" username="0148305406" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0148305406";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0148305406";
};

# المستخدم 52: 0180757765
:do {
    /tool user-manager user add customer="admin" username="0180757765" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0180757765";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0180757765";
};

# المستخدم 53: 0179022660
:do {
    /tool user-manager user add customer="admin" username="0179022660" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0179022660";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0179022660";
};

# المستخدم 54: 0188618037
:do {
    /tool user-manager user add customer="admin" username="0188618037" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0188618037";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0188618037";
};

# المستخدم 55: 0192923091
:do {
    /tool user-manager user add customer="admin" username="0192923091" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0192923091";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0192923091";
};

# المستخدم 56: 0154335547
:do {
    /tool user-manager user add customer="admin" username="0154335547" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0154335547";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0154335547";
};

# المستخدم 57: 0163597761
:do {
    /tool user-manager user add customer="admin" username="0163597761" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0163597761";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0163597761";
};

# المستخدم 58: 0195699814
:do {
    /tool user-manager user add customer="admin" username="0195699814" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0195699814";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0195699814";
};

# المستخدم 59: 0156550927
:do {
    /tool user-manager user add customer="admin" username="0156550927" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0156550927";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0156550927";
};

# المستخدم 60: 0194286505
:do {
    /tool user-manager user add customer="admin" username="0194286505" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0194286505";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0194286505";
};

# المستخدم 61: 0191808930
:do {
    /tool user-manager user add customer="admin" username="0191808930" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0191808930";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0191808930";
};

# المستخدم 62: 0186433857
:do {
    /tool user-manager user add customer="admin" username="0186433857" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0186433857";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0186433857";
};

# المستخدم 63: 0157997513
:do {
    /tool user-manager user add customer="admin" username="0157997513" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0157997513";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0157997513";
};

# المستخدم 64: 0110896116
:do {
    /tool user-manager user add customer="admin" username="0110896116" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0110896116";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0110896116";
};

# المستخدم 65: 0150863277
:do {
    /tool user-manager user add customer="admin" username="0150863277" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0150863277";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0150863277";
};

# المستخدم 66: 0150025369
:do {
    /tool user-manager user add customer="admin" username="0150025369" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0150025369";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0150025369";
};

# المستخدم 67: 0162618332
:do {
    /tool user-manager user add customer="admin" username="0162618332" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0162618332";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0162618332";
};

# المستخدم 68: 0189464511
:do {
    /tool user-manager user add customer="admin" username="0189464511" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0189464511";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0189464511";
};

# المستخدم 69: 0186504375
:do {
    /tool user-manager user add customer="admin" username="0186504375" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0186504375";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0186504375";
};

# المستخدم 70: 0169264001
:do {
    /tool user-manager user add customer="admin" username="0169264001" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0169264001";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0169264001";
};

# المستخدم 71: 0159545407
:do {
    /tool user-manager user add customer="admin" username="0159545407" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0159545407";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0159545407";
};

# المستخدم 72: 0103225054
:do {
    /tool user-manager user add customer="admin" username="0103225054" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0103225054";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0103225054";
};

# المستخدم 73: 0189176518
:do {
    /tool user-manager user add customer="admin" username="0189176518" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0189176518";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0189176518";
};

# المستخدم 74: 0150456373
:do {
    /tool user-manager user add customer="admin" username="0150456373" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0150456373";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0150456373";
};

# المستخدم 75: 0117697375
:do {
    /tool user-manager user add customer="admin" username="0117697375" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0117697375";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0117697375";
};

# المستخدم 76: 0175155979
:do {
    /tool user-manager user add customer="admin" username="0175155979" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0175155979";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0175155979";
};

# المستخدم 77: 0178628383
:do {
    /tool user-manager user add customer="admin" username="0178628383" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0178628383";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0178628383";
};

# المستخدم 78: 0187610264
:do {
    /tool user-manager user add customer="admin" username="0187610264" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0187610264";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0187610264";
};

# المستخدم 79: 0162301795
:do {
    /tool user-manager user add customer="admin" username="0162301795" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0162301795";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0162301795";
};

# المستخدم 80: 0146890949
:do {
    /tool user-manager user add customer="admin" username="0146890949" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0146890949";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0146890949";
};

# المستخدم 81: 0137577050
:do {
    /tool user-manager user add customer="admin" username="0137577050" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0137577050";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0137577050";
};

# المستخدم 82: 0193478671
:do {
    /tool user-manager user add customer="admin" username="0193478671" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0193478671";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0193478671";
};

# المستخدم 83: 0130820070
:do {
    /tool user-manager user add customer="admin" username="0130820070" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0130820070";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0130820070";
};

# المستخدم 84: 0173444834
:do {
    /tool user-manager user add customer="admin" username="0173444834" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0173444834";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0173444834";
};

# المستخدم 85: 0139993429
:do {
    /tool user-manager user add customer="admin" username="0139993429" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0139993429";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0139993429";
};

# المستخدم 86: 0122397923
:do {
    /tool user-manager user add customer="admin" username="0122397923" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0122397923";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0122397923";
};

# المستخدم 87: 0119916396
:do {
    /tool user-manager user add customer="admin" username="0119916396" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0119916396";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0119916396";
};

# المستخدم 88: 0110671368
:do {
    /tool user-manager user add customer="admin" username="0110671368" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0110671368";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0110671368";
};

# المستخدم 89: 0164719141
:do {
    /tool user-manager user add customer="admin" username="0164719141" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0164719141";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0164719141";
};

# المستخدم 90: 0188234828
:do {
    /tool user-manager user add customer="admin" username="0188234828" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0188234828";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0188234828";
};

# المستخدم 91: 0156335452
:do {
    /tool user-manager user add customer="admin" username="0156335452" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0156335452";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0156335452";
};

# المستخدم 92: 0171201421
:do {
    /tool user-manager user add customer="admin" username="0171201421" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0171201421";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0171201421";
};

# المستخدم 93: 0192595929
:do {
    /tool user-manager user add customer="admin" username="0192595929" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0192595929";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0192595929";
};

# المستخدم 94: 0135758061
:do {
    /tool user-manager user add customer="admin" username="0135758061" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0135758061";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0135758061";
};

# المستخدم 95: 0194407451
:do {
    /tool user-manager user add customer="admin" username="0194407451" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0194407451";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0194407451";
};

# المستخدم 96: 0158378944
:do {
    /tool user-manager user add customer="admin" username="0158378944" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0158378944";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0158378944";
};

# المستخدم 97: 0141264683
:do {
    /tool user-manager user add customer="admin" username="0141264683" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0141264683";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0141264683";
};

# المستخدم 98: 0123902826
:do {
    /tool user-manager user add customer="admin" username="0123902826" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0123902826";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0123902826";
};

# المستخدم 99: 0183752448
:do {
    /tool user-manager user add customer="admin" username="0183752448" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0183752448";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0183752448";
};

# المستخدم 100: 0174241584
:do {
    /tool user-manager user add customer="admin" username="0174241584" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0174241584";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0174241584";
};

# المستخدم 101: 0172255869
:do {
    /tool user-manager user add customer="admin" username="0172255869" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0172255869";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0172255869";
};

# المستخدم 102: 0133338974
:do {
    /tool user-manager user add customer="admin" username="0133338974" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0133338974";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0133338974";
};

# المستخدم 103: 0124052586
:do {
    /tool user-manager user add customer="admin" username="0124052586" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0124052586";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0124052586";
};

# المستخدم 104: 0131769323
:do {
    /tool user-manager user add customer="admin" username="0131769323" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0131769323";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0131769323";
};

# المستخدم 105: 0108538724
:do {
    /tool user-manager user add customer="admin" username="0108538724" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0108538724";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0108538724";
};

# المستخدم 106: 0118812701
:do {
    /tool user-manager user add customer="admin" username="0118812701" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0118812701";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0118812701";
};

# المستخدم 107: 0165220794
:do {
    /tool user-manager user add customer="admin" username="0165220794" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0165220794";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0165220794";
};

# المستخدم 108: 0157657599
:do {
    /tool user-manager user add customer="admin" username="0157657599" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0157657599";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0157657599";
};

# المستخدم 109: 0157197612
:do {
    /tool user-manager user add customer="admin" username="0157197612" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0157197612";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0157197612";
};

# المستخدم 110: 0114283322
:do {
    /tool user-manager user add customer="admin" username="0114283322" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0114283322";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0114283322";
};

# المستخدم 111: 0168675006
:do {
    /tool user-manager user add customer="admin" username="0168675006" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0168675006";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0168675006";
};

# المستخدم 112: 0192771569
:do {
    /tool user-manager user add customer="admin" username="0192771569" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0192771569";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0192771569";
};

# المستخدم 113: 0110008531
:do {
    /tool user-manager user add customer="admin" username="0110008531" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0110008531";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0110008531";
};

# المستخدم 114: 0177397681
:do {
    /tool user-manager user add customer="admin" username="0177397681" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0177397681";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0177397681";
};

# المستخدم 115: 0178278870
:do {
    /tool user-manager user add customer="admin" username="0178278870" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0178278870";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0178278870";
};

# المستخدم 116: 0163609479
:do {
    /tool user-manager user add customer="admin" username="0163609479" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0163609479";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0163609479";
};

# المستخدم 117: 0113521766
:do {
    /tool user-manager user add customer="admin" username="0113521766" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0113521766";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0113521766";
};

# المستخدم 118: 0114761140
:do {
    /tool user-manager user add customer="admin" username="0114761140" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0114761140";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0114761140";
};

# المستخدم 119: 0143977598
:do {
    /tool user-manager user add customer="admin" username="0143977598" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0143977598";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0143977598";
};

# المستخدم 120: 0142428069
:do {
    /tool user-manager user add customer="admin" username="0142428069" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0142428069";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0142428069";
};

# المستخدم 121: 0132731256
:do {
    /tool user-manager user add customer="admin" username="0132731256" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0132731256";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0132731256";
};

# المستخدم 122: 0157333374
:do {
    /tool user-manager user add customer="admin" username="0157333374" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0157333374";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0157333374";
};

# المستخدم 123: 0176425697
:do {
    /tool user-manager user add customer="admin" username="0176425697" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0176425697";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0176425697";
};

# المستخدم 124: 0177928607
:do {
    /tool user-manager user add customer="admin" username="0177928607" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0177928607";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0177928607";
};

# المستخدم 125: 0198214262
:do {
    /tool user-manager user add customer="admin" username="0198214262" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0198214262";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0198214262";
};

# المستخدم 126: 0180267477
:do {
    /tool user-manager user add customer="admin" username="0180267477" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0180267477";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0180267477";
};

# المستخدم 127: 0197094396
:do {
    /tool user-manager user add customer="admin" username="0197094396" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0197094396";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0197094396";
};

# المستخدم 128: 0185891220
:do {
    /tool user-manager user add customer="admin" username="0185891220" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0185891220";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0185891220";
};

# المستخدم 129: 0190095236
:do {
    /tool user-manager user add customer="admin" username="0190095236" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0190095236";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0190095236";
};

# المستخدم 130: 0198053632
:do {
    /tool user-manager user add customer="admin" username="0198053632" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0198053632";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0198053632";
};

# المستخدم 131: 0126240998
:do {
    /tool user-manager user add customer="admin" username="0126240998" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0126240998";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0126240998";
};

# المستخدم 132: 0184517873
:do {
    /tool user-manager user add customer="admin" username="0184517873" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0184517873";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0184517873";
};

# المستخدم 133: 0194722242
:do {
    /tool user-manager user add customer="admin" username="0194722242" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0194722242";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0194722242";
};

# المستخدم 134: 0163709245
:do {
    /tool user-manager user add customer="admin" username="0163709245" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0163709245";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0163709245";
};

# المستخدم 135: 0194669394
:do {
    /tool user-manager user add customer="admin" username="0194669394" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0194669394";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0194669394";
};

# المستخدم 136: 0166698411
:do {
    /tool user-manager user add customer="admin" username="0166698411" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0166698411";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0166698411";
};

# المستخدم 137: 0124817774
:do {
    /tool user-manager user add customer="admin" username="0124817774" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0124817774";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0124817774";
};

# المستخدم 138: 0177114195
:do {
    /tool user-manager user add customer="admin" username="0177114195" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0177114195";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0177114195";
};

# المستخدم 139: 0196779234
:do {
    /tool user-manager user add customer="admin" username="0196779234" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0196779234";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0196779234";
};

# المستخدم 140: 0110421231
:do {
    /tool user-manager user add customer="admin" username="0110421231" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0110421231";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0110421231";
};

# المستخدم 141: 0114532726
:do {
    /tool user-manager user add customer="admin" username="0114532726" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0114532726";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0114532726";
};

# المستخدم 142: 0111257729
:do {
    /tool user-manager user add customer="admin" username="0111257729" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0111257729";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0111257729";
};

# المستخدم 143: 0156723334
:do {
    /tool user-manager user add customer="admin" username="0156723334" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0156723334";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0156723334";
};

# المستخدم 144: 0181104605
:do {
    /tool user-manager user add customer="admin" username="0181104605" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0181104605";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0181104605";
};

# المستخدم 145: 0125982035
:do {
    /tool user-manager user add customer="admin" username="0125982035" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0125982035";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0125982035";
};

# المستخدم 146: 0156964264
:do {
    /tool user-manager user add customer="admin" username="0156964264" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0156964264";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0156964264";
};

# المستخدم 147: 0100981476
:do {
    /tool user-manager user add customer="admin" username="0100981476" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0100981476";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0100981476";
};

# المستخدم 148: 0150177595
:do {
    /tool user-manager user add customer="admin" username="0150177595" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0150177595";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0150177595";
};

# المستخدم 149: 0169598159
:do {
    /tool user-manager user add customer="admin" username="0169598159" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0169598159";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0169598159";
};

# المستخدم 150: 0194212629
:do {
    /tool user-manager user add customer="admin" username="0194212629" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0194212629";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0194212629";
};

# المستخدم 151: 0152666387
:do {
    /tool user-manager user add customer="admin" username="0152666387" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0152666387";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0152666387";
};

# المستخدم 152: 0112607912
:do {
    /tool user-manager user add customer="admin" username="0112607912" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0112607912";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0112607912";
};

# المستخدم 153: 0163014243
:do {
    /tool user-manager user add customer="admin" username="0163014243" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0163014243";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0163014243";
};

# المستخدم 154: 0136079050
:do {
    /tool user-manager user add customer="admin" username="0136079050" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0136079050";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0136079050";
};

# المستخدم 155: 0186318920
:do {
    /tool user-manager user add customer="admin" username="0186318920" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0186318920";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0186318920";
};

# المستخدم 156: 0156089405
:do {
    /tool user-manager user add customer="admin" username="0156089405" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0156089405";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0156089405";
};

# المستخدم 157: 0174560841
:do {
    /tool user-manager user add customer="admin" username="0174560841" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0174560841";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0174560841";
};

# المستخدم 158: 0151846773
:do {
    /tool user-manager user add customer="admin" username="0151846773" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0151846773";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0151846773";
};

# المستخدم 159: 0184780948
:do {
    /tool user-manager user add customer="admin" username="0184780948" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0184780948";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0184780948";
};

# المستخدم 160: 0197690806
:do {
    /tool user-manager user add customer="admin" username="0197690806" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0197690806";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0197690806";
};

# المستخدم 161: 0127654331
:do {
    /tool user-manager user add customer="admin" username="0127654331" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0127654331";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0127654331";
};

# المستخدم 162: 0134741186
:do {
    /tool user-manager user add customer="admin" username="0134741186" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0134741186";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0134741186";
};

# المستخدم 163: 0177434782
:do {
    /tool user-manager user add customer="admin" username="0177434782" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0177434782";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0177434782";
};

# المستخدم 164: 0171146264
:do {
    /tool user-manager user add customer="admin" username="0171146264" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0171146264";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0171146264";
};

# المستخدم 165: 0146668012
:do {
    /tool user-manager user add customer="admin" username="0146668012" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0146668012";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0146668012";
};

# المستخدم 166: 0149623975
:do {
    /tool user-manager user add customer="admin" username="0149623975" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0149623975";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0149623975";
};

# المستخدم 167: 0165035987
:do {
    /tool user-manager user add customer="admin" username="0165035987" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0165035987";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0165035987";
};

# المستخدم 168: 0163894977
:do {
    /tool user-manager user add customer="admin" username="0163894977" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0163894977";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0163894977";
};

# المستخدم 169: 0197800163
:do {
    /tool user-manager user add customer="admin" username="0197800163" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0197800163";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0197800163";
};

# المستخدم 170: 0147842804
:do {
    /tool user-manager user add customer="admin" username="0147842804" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0147842804";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0147842804";
};

# المستخدم 171: 0128541213
:do {
    /tool user-manager user add customer="admin" username="0128541213" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0128541213";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0128541213";
};

# المستخدم 172: 0171725345
:do {
    /tool user-manager user add customer="admin" username="0171725345" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0171725345";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0171725345";
};

# المستخدم 173: 0159422113
:do {
    /tool user-manager user add customer="admin" username="0159422113" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0159422113";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0159422113";
};

# المستخدم 174: 0173631603
:do {
    /tool user-manager user add customer="admin" username="0173631603" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0173631603";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0173631603";
};

# المستخدم 175: 0102092903
:do {
    /tool user-manager user add customer="admin" username="0102092903" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0102092903";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0102092903";
};

# المستخدم 176: 0143379612
:do {
    /tool user-manager user add customer="admin" username="0143379612" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0143379612";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0143379612";
};

# المستخدم 177: 0169411990
:do {
    /tool user-manager user add customer="admin" username="0169411990" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0169411990";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0169411990";
};

# المستخدم 178: 0102022289
:do {
    /tool user-manager user add customer="admin" username="0102022289" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0102022289";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0102022289";
};

# المستخدم 179: 0188727088
:do {
    /tool user-manager user add customer="admin" username="0188727088" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0188727088";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0188727088";
};

# المستخدم 180: 0195838884
:do {
    /tool user-manager user add customer="admin" username="0195838884" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0195838884";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0195838884";
};

# المستخدم 181: 0165319710
:do {
    /tool user-manager user add customer="admin" username="0165319710" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0165319710";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0165319710";
};

# المستخدم 182: 0122018249
:do {
    /tool user-manager user add customer="admin" username="0122018249" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0122018249";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0122018249";
};

# المستخدم 183: 0148423856
:do {
    /tool user-manager user add customer="admin" username="0148423856" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0148423856";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0148423856";
};

# المستخدم 184: 0181607605
:do {
    /tool user-manager user add customer="admin" username="0181607605" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0181607605";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0181607605";
};

# المستخدم 185: 0171464165
:do {
    /tool user-manager user add customer="admin" username="0171464165" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0171464165";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0171464165";
};

# المستخدم 186: 0102752168
:do {
    /tool user-manager user add customer="admin" username="0102752168" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0102752168";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0102752168";
};

# المستخدم 187: 0197119652
:do {
    /tool user-manager user add customer="admin" username="0197119652" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0197119652";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0197119652";
};

# المستخدم 188: 0138133952
:do {
    /tool user-manager user add customer="admin" username="0138133952" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0138133952";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0138133952";
};

# المستخدم 189: 0174689843
:do {
    /tool user-manager user add customer="admin" username="0174689843" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0174689843";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0174689843";
};

# المستخدم 190: 0178701127
:do {
    /tool user-manager user add customer="admin" username="0178701127" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0178701127";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0178701127";
};

# المستخدم 191: 0157079651
:do {
    /tool user-manager user add customer="admin" username="0157079651" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0157079651";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0157079651";
};

# المستخدم 192: 0195757276
:do {
    /tool user-manager user add customer="admin" username="0195757276" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0195757276";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0195757276";
};

# المستخدم 193: 0141340977
:do {
    /tool user-manager user add customer="admin" username="0141340977" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0141340977";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0141340977";
};

# المستخدم 194: 0165321033
:do {
    /tool user-manager user add customer="admin" username="0165321033" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0165321033";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0165321033";
};

# المستخدم 195: 0152167445
:do {
    /tool user-manager user add customer="admin" username="0152167445" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0152167445";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0152167445";
};

# المستخدم 196: 0109608578
:do {
    /tool user-manager user add customer="admin" username="0109608578" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0109608578";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0109608578";
};

# المستخدم 197: 0122772099
:do {
    /tool user-manager user add customer="admin" username="0122772099" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0122772099";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0122772099";
};

# المستخدم 198: 0159276994
:do {
    /tool user-manager user add customer="admin" username="0159276994" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0159276994";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0159276994";
};

# المستخدم 199: 0167517164
:do {
    /tool user-manager user add customer="admin" username="0167517164" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0167517164";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0167517164";
};

# المستخدم 200: 0197493039
:do {
    /tool user-manager user add customer="admin" username="0197493039" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0197493039";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0197493039";
};


:put "📊 تم الانتهاء من النسخة الاحتياطية";
:put "✅ نجح: $success كرت";
:put "❌ فشل: $errors كرت";
:put "📈 الإجمالي: $total كرت";

:put "🎉 تم استعادة جميع كروت القالب: 10";
:put "💡 النسخة الاحتياطية مكتملة بنجاح!";
