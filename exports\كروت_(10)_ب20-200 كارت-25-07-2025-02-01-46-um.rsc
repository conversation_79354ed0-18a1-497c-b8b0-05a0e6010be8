# MikroTik Script - نسخة احتياطية للكروت
# تم الإنشاء: 2025-07-25 02:01:47
# القالب: 10
# النظام: user_manager
# عدد الكروت: 200
# تم إنشاؤه بواسطة: مولد كروت MikroTik

:put "🚀 بدء تنفيذ النسخة الاحتياطية للكروت";
:put "📋 القالب: 10";
:put "🎯 النظام: user_manager";
:put "📊 عدد الكروت: 200";

:local success 0;
:local errors 0;
:local total 200;

:put "⏳ بدء إضافة الكروت...";

# إضافة مستخدمي User Manager
:put "👤 إضافة 200 مستخدم User Manager...";

# المستخدم 1: 0182984690
:do {
    /tool user-manager user add customer="admin" username="0182984690" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0182984690";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0182984690";
};

# المستخدم 2: 0178774104
:do {
    /tool user-manager user add customer="admin" username="0178774104" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0178774104";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0178774104";
};

# المستخدم 3: 0146666109
:do {
    /tool user-manager user add customer="admin" username="0146666109" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0146666109";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0146666109";
};

# المستخدم 4: 0115393590
:do {
    /tool user-manager user add customer="admin" username="0115393590" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0115393590";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0115393590";
};

# المستخدم 5: 0140940846
:do {
    /tool user-manager user add customer="admin" username="0140940846" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0140940846";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0140940846";
};

# المستخدم 6: 0126955687
:do {
    /tool user-manager user add customer="admin" username="0126955687" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0126955687";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0126955687";
};

# المستخدم 7: 0106754582
:do {
    /tool user-manager user add customer="admin" username="0106754582" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0106754582";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0106754582";
};

# المستخدم 8: 0183671975
:do {
    /tool user-manager user add customer="admin" username="0183671975" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0183671975";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0183671975";
};

# المستخدم 9: 0137274591
:do {
    /tool user-manager user add customer="admin" username="0137274591" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0137274591";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0137274591";
};

# المستخدم 10: 0156552312
:do {
    /tool user-manager user add customer="admin" username="0156552312" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0156552312";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0156552312";
};

# المستخدم 11: 0165640103
:do {
    /tool user-manager user add customer="admin" username="0165640103" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0165640103";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0165640103";
};

# المستخدم 12: 0157453552
:do {
    /tool user-manager user add customer="admin" username="0157453552" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0157453552";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0157453552";
};

# المستخدم 13: 0172367653
:do {
    /tool user-manager user add customer="admin" username="0172367653" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0172367653";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0172367653";
};

# المستخدم 14: 0102478637
:do {
    /tool user-manager user add customer="admin" username="0102478637" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0102478637";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0102478637";
};

# المستخدم 15: 0128002737
:do {
    /tool user-manager user add customer="admin" username="0128002737" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0128002737";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0128002737";
};

# المستخدم 16: 0105712391
:do {
    /tool user-manager user add customer="admin" username="0105712391" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0105712391";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0105712391";
};

# المستخدم 17: 0143258756
:do {
    /tool user-manager user add customer="admin" username="0143258756" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0143258756";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0143258756";
};

# المستخدم 18: 0193527103
:do {
    /tool user-manager user add customer="admin" username="0193527103" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0193527103";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0193527103";
};

# المستخدم 19: 0198228269
:do {
    /tool user-manager user add customer="admin" username="0198228269" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0198228269";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0198228269";
};

# المستخدم 20: 0198242938
:do {
    /tool user-manager user add customer="admin" username="0198242938" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0198242938";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0198242938";
};

# المستخدم 21: 0122470058
:do {
    /tool user-manager user add customer="admin" username="0122470058" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0122470058";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0122470058";
};

# المستخدم 22: 0162944712
:do {
    /tool user-manager user add customer="admin" username="0162944712" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0162944712";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0162944712";
};

# المستخدم 23: 0158400453
:do {
    /tool user-manager user add customer="admin" username="0158400453" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0158400453";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0158400453";
};

# المستخدم 24: 0169522906
:do {
    /tool user-manager user add customer="admin" username="0169522906" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0169522906";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0169522906";
};

# المستخدم 25: 0155087624
:do {
    /tool user-manager user add customer="admin" username="0155087624" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0155087624";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0155087624";
};

# المستخدم 26: 0138317580
:do {
    /tool user-manager user add customer="admin" username="0138317580" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0138317580";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0138317580";
};

# المستخدم 27: 0148868914
:do {
    /tool user-manager user add customer="admin" username="0148868914" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0148868914";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0148868914";
};

# المستخدم 28: 0149562591
:do {
    /tool user-manager user add customer="admin" username="0149562591" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0149562591";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0149562591";
};

# المستخدم 29: 0162282584
:do {
    /tool user-manager user add customer="admin" username="0162282584" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0162282584";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0162282584";
};

# المستخدم 30: 0150496701
:do {
    /tool user-manager user add customer="admin" username="0150496701" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0150496701";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0150496701";
};

# المستخدم 31: 0143867478
:do {
    /tool user-manager user add customer="admin" username="0143867478" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0143867478";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0143867478";
};

# المستخدم 32: 0103130369
:do {
    /tool user-manager user add customer="admin" username="0103130369" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0103130369";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0103130369";
};

# المستخدم 33: 0179777134
:do {
    /tool user-manager user add customer="admin" username="0179777134" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0179777134";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0179777134";
};

# المستخدم 34: 0171899026
:do {
    /tool user-manager user add customer="admin" username="0171899026" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0171899026";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0171899026";
};

# المستخدم 35: 0155300008
:do {
    /tool user-manager user add customer="admin" username="0155300008" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0155300008";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0155300008";
};

# المستخدم 36: 0158255741
:do {
    /tool user-manager user add customer="admin" username="0158255741" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0158255741";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0158255741";
};

# المستخدم 37: 0139504029
:do {
    /tool user-manager user add customer="admin" username="0139504029" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0139504029";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0139504029";
};

# المستخدم 38: 0156830063
:do {
    /tool user-manager user add customer="admin" username="0156830063" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0156830063";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0156830063";
};

# المستخدم 39: 0160315180
:do {
    /tool user-manager user add customer="admin" username="0160315180" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0160315180";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0160315180";
};

# المستخدم 40: 0153264392
:do {
    /tool user-manager user add customer="admin" username="0153264392" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0153264392";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0153264392";
};

# المستخدم 41: 0171685405
:do {
    /tool user-manager user add customer="admin" username="0171685405" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0171685405";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0171685405";
};

# المستخدم 42: 0191647461
:do {
    /tool user-manager user add customer="admin" username="0191647461" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0191647461";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0191647461";
};

# المستخدم 43: 0129529364
:do {
    /tool user-manager user add customer="admin" username="0129529364" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0129529364";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0129529364";
};

# المستخدم 44: 0184728944
:do {
    /tool user-manager user add customer="admin" username="0184728944" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0184728944";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0184728944";
};

# المستخدم 45: 0126651294
:do {
    /tool user-manager user add customer="admin" username="0126651294" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0126651294";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0126651294";
};

# المستخدم 46: 0129572701
:do {
    /tool user-manager user add customer="admin" username="0129572701" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0129572701";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0129572701";
};

# المستخدم 47: 0121596324
:do {
    /tool user-manager user add customer="admin" username="0121596324" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0121596324";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0121596324";
};

# المستخدم 48: 0193541314
:do {
    /tool user-manager user add customer="admin" username="0193541314" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0193541314";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0193541314";
};

# المستخدم 49: 0124508985
:do {
    /tool user-manager user add customer="admin" username="0124508985" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0124508985";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0124508985";
};

# المستخدم 50: 0157626175
:do {
    /tool user-manager user add customer="admin" username="0157626175" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0157626175";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0157626175";
};

# المستخدم 51: 0149346827
:do {
    /tool user-manager user add customer="admin" username="0149346827" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0149346827";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0149346827";
};

# المستخدم 52: 0106046323
:do {
    /tool user-manager user add customer="admin" username="0106046323" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0106046323";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0106046323";
};

# المستخدم 53: 0118978576
:do {
    /tool user-manager user add customer="admin" username="0118978576" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0118978576";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0118978576";
};

# المستخدم 54: 0177118219
:do {
    /tool user-manager user add customer="admin" username="0177118219" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0177118219";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0177118219";
};

# المستخدم 55: 0198555572
:do {
    /tool user-manager user add customer="admin" username="0198555572" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0198555572";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0198555572";
};

# المستخدم 56: 0134466108
:do {
    /tool user-manager user add customer="admin" username="0134466108" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0134466108";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0134466108";
};

# المستخدم 57: 0130327880
:do {
    /tool user-manager user add customer="admin" username="0130327880" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0130327880";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0130327880";
};

# المستخدم 58: 0197382164
:do {
    /tool user-manager user add customer="admin" username="0197382164" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0197382164";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0197382164";
};

# المستخدم 59: 0141665594
:do {
    /tool user-manager user add customer="admin" username="0141665594" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0141665594";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0141665594";
};

# المستخدم 60: 0136565504
:do {
    /tool user-manager user add customer="admin" username="0136565504" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0136565504";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0136565504";
};

# المستخدم 61: 0102003034
:do {
    /tool user-manager user add customer="admin" username="0102003034" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0102003034";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0102003034";
};

# المستخدم 62: 0163486798
:do {
    /tool user-manager user add customer="admin" username="0163486798" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0163486798";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0163486798";
};

# المستخدم 63: 0175414146
:do {
    /tool user-manager user add customer="admin" username="0175414146" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0175414146";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0175414146";
};

# المستخدم 64: 0197985914
:do {
    /tool user-manager user add customer="admin" username="0197985914" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0197985914";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0197985914";
};

# المستخدم 65: 0196802776
:do {
    /tool user-manager user add customer="admin" username="0196802776" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0196802776";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0196802776";
};

# المستخدم 66: 0131229593
:do {
    /tool user-manager user add customer="admin" username="0131229593" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0131229593";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0131229593";
};

# المستخدم 67: 0129926015
:do {
    /tool user-manager user add customer="admin" username="0129926015" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0129926015";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0129926015";
};

# المستخدم 68: 0186308076
:do {
    /tool user-manager user add customer="admin" username="0186308076" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0186308076";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0186308076";
};

# المستخدم 69: 0133822533
:do {
    /tool user-manager user add customer="admin" username="0133822533" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0133822533";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0133822533";
};

# المستخدم 70: 0119869026
:do {
    /tool user-manager user add customer="admin" username="0119869026" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0119869026";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0119869026";
};

# المستخدم 71: 0131597204
:do {
    /tool user-manager user add customer="admin" username="0131597204" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0131597204";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0131597204";
};

# المستخدم 72: 0124022071
:do {
    /tool user-manager user add customer="admin" username="0124022071" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0124022071";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0124022071";
};

# المستخدم 73: 0120927304
:do {
    /tool user-manager user add customer="admin" username="0120927304" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0120927304";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0120927304";
};

# المستخدم 74: 0160849678
:do {
    /tool user-manager user add customer="admin" username="0160849678" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0160849678";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0160849678";
};

# المستخدم 75: 0132492704
:do {
    /tool user-manager user add customer="admin" username="0132492704" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0132492704";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0132492704";
};

# المستخدم 76: 0109587772
:do {
    /tool user-manager user add customer="admin" username="0109587772" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0109587772";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0109587772";
};

# المستخدم 77: 0117846544
:do {
    /tool user-manager user add customer="admin" username="0117846544" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0117846544";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0117846544";
};

# المستخدم 78: 0180042009
:do {
    /tool user-manager user add customer="admin" username="0180042009" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0180042009";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0180042009";
};

# المستخدم 79: 0186555024
:do {
    /tool user-manager user add customer="admin" username="0186555024" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0186555024";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0186555024";
};

# المستخدم 80: 0119595025
:do {
    /tool user-manager user add customer="admin" username="0119595025" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0119595025";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0119595025";
};

# المستخدم 81: 0132442568
:do {
    /tool user-manager user add customer="admin" username="0132442568" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0132442568";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0132442568";
};

# المستخدم 82: 0128680834
:do {
    /tool user-manager user add customer="admin" username="0128680834" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0128680834";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0128680834";
};

# المستخدم 83: 0135711584
:do {
    /tool user-manager user add customer="admin" username="0135711584" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0135711584";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0135711584";
};

# المستخدم 84: 0157183576
:do {
    /tool user-manager user add customer="admin" username="0157183576" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0157183576";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0157183576";
};

# المستخدم 85: 0100464847
:do {
    /tool user-manager user add customer="admin" username="0100464847" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0100464847";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0100464847";
};

# المستخدم 86: 0148605517
:do {
    /tool user-manager user add customer="admin" username="0148605517" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0148605517";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0148605517";
};

# المستخدم 87: 0120732279
:do {
    /tool user-manager user add customer="admin" username="0120732279" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0120732279";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0120732279";
};

# المستخدم 88: 0100840814
:do {
    /tool user-manager user add customer="admin" username="0100840814" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0100840814";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0100840814";
};

# المستخدم 89: 0124041053
:do {
    /tool user-manager user add customer="admin" username="0124041053" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0124041053";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0124041053";
};

# المستخدم 90: 0131538877
:do {
    /tool user-manager user add customer="admin" username="0131538877" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0131538877";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0131538877";
};

# المستخدم 91: 0110103794
:do {
    /tool user-manager user add customer="admin" username="0110103794" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0110103794";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0110103794";
};

# المستخدم 92: 0173609110
:do {
    /tool user-manager user add customer="admin" username="0173609110" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0173609110";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0173609110";
};

# المستخدم 93: 0179891694
:do {
    /tool user-manager user add customer="admin" username="0179891694" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0179891694";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0179891694";
};

# المستخدم 94: 0118405042
:do {
    /tool user-manager user add customer="admin" username="0118405042" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0118405042";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0118405042";
};

# المستخدم 95: 0101327585
:do {
    /tool user-manager user add customer="admin" username="0101327585" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0101327585";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0101327585";
};

# المستخدم 96: 0193315333
:do {
    /tool user-manager user add customer="admin" username="0193315333" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0193315333";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0193315333";
};

# المستخدم 97: 0154912918
:do {
    /tool user-manager user add customer="admin" username="0154912918" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0154912918";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0154912918";
};

# المستخدم 98: 0158334844
:do {
    /tool user-manager user add customer="admin" username="0158334844" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0158334844";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0158334844";
};

# المستخدم 99: 0140230417
:do {
    /tool user-manager user add customer="admin" username="0140230417" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0140230417";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0140230417";
};

# المستخدم 100: 0186393850
:do {
    /tool user-manager user add customer="admin" username="0186393850" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0186393850";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0186393850";
};

# المستخدم 101: 0115125136
:do {
    /tool user-manager user add customer="admin" username="0115125136" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0115125136";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0115125136";
};

# المستخدم 102: 0187728729
:do {
    /tool user-manager user add customer="admin" username="0187728729" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0187728729";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0187728729";
};

# المستخدم 103: 0135360403
:do {
    /tool user-manager user add customer="admin" username="0135360403" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0135360403";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0135360403";
};

# المستخدم 104: 0102333636
:do {
    /tool user-manager user add customer="admin" username="0102333636" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0102333636";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0102333636";
};

# المستخدم 105: 0182479523
:do {
    /tool user-manager user add customer="admin" username="0182479523" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0182479523";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0182479523";
};

# المستخدم 106: 0163229013
:do {
    /tool user-manager user add customer="admin" username="0163229013" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0163229013";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0163229013";
};

# المستخدم 107: 0147458321
:do {
    /tool user-manager user add customer="admin" username="0147458321" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0147458321";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0147458321";
};

# المستخدم 108: 0151321675
:do {
    /tool user-manager user add customer="admin" username="0151321675" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0151321675";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0151321675";
};

# المستخدم 109: 0166035716
:do {
    /tool user-manager user add customer="admin" username="0166035716" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0166035716";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0166035716";
};

# المستخدم 110: 0143783522
:do {
    /tool user-manager user add customer="admin" username="0143783522" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0143783522";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0143783522";
};

# المستخدم 111: 0105657879
:do {
    /tool user-manager user add customer="admin" username="0105657879" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0105657879";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0105657879";
};

# المستخدم 112: 0191092787
:do {
    /tool user-manager user add customer="admin" username="0191092787" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0191092787";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0191092787";
};

# المستخدم 113: 0179485241
:do {
    /tool user-manager user add customer="admin" username="0179485241" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0179485241";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0179485241";
};

# المستخدم 114: 0156769059
:do {
    /tool user-manager user add customer="admin" username="0156769059" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0156769059";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0156769059";
};

# المستخدم 115: 0161434324
:do {
    /tool user-manager user add customer="admin" username="0161434324" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0161434324";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0161434324";
};

# المستخدم 116: 0147498360
:do {
    /tool user-manager user add customer="admin" username="0147498360" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0147498360";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0147498360";
};

# المستخدم 117: 0188765487
:do {
    /tool user-manager user add customer="admin" username="0188765487" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0188765487";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0188765487";
};

# المستخدم 118: 0161383223
:do {
    /tool user-manager user add customer="admin" username="0161383223" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0161383223";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0161383223";
};

# المستخدم 119: 0143420689
:do {
    /tool user-manager user add customer="admin" username="0143420689" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0143420689";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0143420689";
};

# المستخدم 120: 0136490580
:do {
    /tool user-manager user add customer="admin" username="0136490580" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0136490580";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0136490580";
};

# المستخدم 121: 0161711796
:do {
    /tool user-manager user add customer="admin" username="0161711796" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0161711796";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0161711796";
};

# المستخدم 122: 0106866045
:do {
    /tool user-manager user add customer="admin" username="0106866045" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0106866045";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0106866045";
};

# المستخدم 123: 0100231311
:do {
    /tool user-manager user add customer="admin" username="0100231311" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0100231311";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0100231311";
};

# المستخدم 124: 0180741733
:do {
    /tool user-manager user add customer="admin" username="0180741733" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0180741733";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0180741733";
};

# المستخدم 125: 0165476934
:do {
    /tool user-manager user add customer="admin" username="0165476934" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0165476934";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0165476934";
};

# المستخدم 126: 0179382882
:do {
    /tool user-manager user add customer="admin" username="0179382882" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0179382882";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0179382882";
};

# المستخدم 127: 0158024099
:do {
    /tool user-manager user add customer="admin" username="0158024099" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0158024099";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0158024099";
};

# المستخدم 128: 0196225673
:do {
    /tool user-manager user add customer="admin" username="0196225673" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0196225673";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0196225673";
};

# المستخدم 129: 0182224460
:do {
    /tool user-manager user add customer="admin" username="0182224460" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0182224460";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0182224460";
};

# المستخدم 130: 0106774614
:do {
    /tool user-manager user add customer="admin" username="0106774614" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0106774614";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0106774614";
};

# المستخدم 131: 0125584686
:do {
    /tool user-manager user add customer="admin" username="0125584686" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0125584686";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0125584686";
};

# المستخدم 132: 0184420263
:do {
    /tool user-manager user add customer="admin" username="0184420263" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0184420263";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0184420263";
};

# المستخدم 133: 0193267962
:do {
    /tool user-manager user add customer="admin" username="0193267962" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0193267962";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0193267962";
};

# المستخدم 134: 0132988513
:do {
    /tool user-manager user add customer="admin" username="0132988513" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0132988513";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0132988513";
};

# المستخدم 135: 0185602851
:do {
    /tool user-manager user add customer="admin" username="0185602851" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0185602851";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0185602851";
};

# المستخدم 136: 0136473292
:do {
    /tool user-manager user add customer="admin" username="0136473292" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0136473292";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0136473292";
};

# المستخدم 137: 0165294127
:do {
    /tool user-manager user add customer="admin" username="0165294127" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0165294127";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0165294127";
};

# المستخدم 138: 0150852371
:do {
    /tool user-manager user add customer="admin" username="0150852371" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0150852371";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0150852371";
};

# المستخدم 139: 0112248519
:do {
    /tool user-manager user add customer="admin" username="0112248519" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0112248519";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0112248519";
};

# المستخدم 140: 0165701513
:do {
    /tool user-manager user add customer="admin" username="0165701513" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0165701513";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0165701513";
};

# المستخدم 141: 0144834666
:do {
    /tool user-manager user add customer="admin" username="0144834666" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0144834666";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0144834666";
};

# المستخدم 142: 0147400092
:do {
    /tool user-manager user add customer="admin" username="0147400092" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0147400092";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0147400092";
};

# المستخدم 143: 0168420512
:do {
    /tool user-manager user add customer="admin" username="0168420512" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0168420512";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0168420512";
};

# المستخدم 144: 0146196965
:do {
    /tool user-manager user add customer="admin" username="0146196965" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0146196965";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0146196965";
};

# المستخدم 145: 0110549101
:do {
    /tool user-manager user add customer="admin" username="0110549101" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0110549101";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0110549101";
};

# المستخدم 146: 0134057274
:do {
    /tool user-manager user add customer="admin" username="0134057274" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0134057274";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0134057274";
};

# المستخدم 147: 0168388085
:do {
    /tool user-manager user add customer="admin" username="0168388085" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0168388085";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0168388085";
};

# المستخدم 148: 0111821133
:do {
    /tool user-manager user add customer="admin" username="0111821133" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0111821133";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0111821133";
};

# المستخدم 149: 0150948468
:do {
    /tool user-manager user add customer="admin" username="0150948468" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0150948468";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0150948468";
};

# المستخدم 150: 0103456731
:do {
    /tool user-manager user add customer="admin" username="0103456731" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0103456731";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0103456731";
};

# المستخدم 151: 0170664569
:do {
    /tool user-manager user add customer="admin" username="0170664569" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0170664569";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0170664569";
};

# المستخدم 152: 0163776299
:do {
    /tool user-manager user add customer="admin" username="0163776299" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0163776299";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0163776299";
};

# المستخدم 153: 0184818517
:do {
    /tool user-manager user add customer="admin" username="0184818517" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0184818517";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0184818517";
};

# المستخدم 154: 0142346151
:do {
    /tool user-manager user add customer="admin" username="0142346151" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0142346151";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0142346151";
};

# المستخدم 155: 0143258374
:do {
    /tool user-manager user add customer="admin" username="0143258374" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0143258374";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0143258374";
};

# المستخدم 156: 0115522562
:do {
    /tool user-manager user add customer="admin" username="0115522562" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0115522562";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0115522562";
};

# المستخدم 157: 0168995571
:do {
    /tool user-manager user add customer="admin" username="0168995571" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0168995571";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0168995571";
};

# المستخدم 158: 0159825639
:do {
    /tool user-manager user add customer="admin" username="0159825639" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0159825639";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0159825639";
};

# المستخدم 159: 0133069862
:do {
    /tool user-manager user add customer="admin" username="0133069862" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0133069862";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0133069862";
};

# المستخدم 160: 0106666184
:do {
    /tool user-manager user add customer="admin" username="0106666184" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0106666184";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0106666184";
};

# المستخدم 161: 0115773523
:do {
    /tool user-manager user add customer="admin" username="0115773523" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0115773523";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0115773523";
};

# المستخدم 162: 0135988596
:do {
    /tool user-manager user add customer="admin" username="0135988596" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0135988596";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0135988596";
};

# المستخدم 163: 0168529964
:do {
    /tool user-manager user add customer="admin" username="0168529964" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0168529964";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0168529964";
};

# المستخدم 164: 0146215801
:do {
    /tool user-manager user add customer="admin" username="0146215801" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0146215801";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0146215801";
};

# المستخدم 165: 0196465996
:do {
    /tool user-manager user add customer="admin" username="0196465996" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0196465996";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0196465996";
};

# المستخدم 166: 0131960862
:do {
    /tool user-manager user add customer="admin" username="0131960862" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0131960862";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0131960862";
};

# المستخدم 167: 0139103672
:do {
    /tool user-manager user add customer="admin" username="0139103672" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0139103672";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0139103672";
};

# المستخدم 168: 0163188447
:do {
    /tool user-manager user add customer="admin" username="0163188447" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0163188447";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0163188447";
};

# المستخدم 169: 0183606181
:do {
    /tool user-manager user add customer="admin" username="0183606181" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0183606181";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0183606181";
};

# المستخدم 170: 0167036773
:do {
    /tool user-manager user add customer="admin" username="0167036773" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0167036773";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0167036773";
};

# المستخدم 171: 0144056160
:do {
    /tool user-manager user add customer="admin" username="0144056160" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0144056160";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0144056160";
};

# المستخدم 172: 0116750808
:do {
    /tool user-manager user add customer="admin" username="0116750808" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0116750808";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0116750808";
};

# المستخدم 173: 0144777833
:do {
    /tool user-manager user add customer="admin" username="0144777833" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0144777833";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0144777833";
};

# المستخدم 174: 0191904045
:do {
    /tool user-manager user add customer="admin" username="0191904045" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0191904045";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0191904045";
};

# المستخدم 175: 0187884840
:do {
    /tool user-manager user add customer="admin" username="0187884840" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0187884840";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0187884840";
};

# المستخدم 176: 0117677468
:do {
    /tool user-manager user add customer="admin" username="0117677468" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0117677468";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0117677468";
};

# المستخدم 177: 0159038327
:do {
    /tool user-manager user add customer="admin" username="0159038327" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0159038327";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0159038327";
};

# المستخدم 178: 0191019349
:do {
    /tool user-manager user add customer="admin" username="0191019349" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0191019349";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0191019349";
};

# المستخدم 179: 0126112504
:do {
    /tool user-manager user add customer="admin" username="0126112504" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0126112504";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0126112504";
};

# المستخدم 180: 0198804634
:do {
    /tool user-manager user add customer="admin" username="0198804634" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0198804634";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0198804634";
};

# المستخدم 181: 0145048982
:do {
    /tool user-manager user add customer="admin" username="0145048982" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0145048982";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0145048982";
};

# المستخدم 182: 0186278901
:do {
    /tool user-manager user add customer="admin" username="0186278901" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0186278901";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0186278901";
};

# المستخدم 183: 0181271654
:do {
    /tool user-manager user add customer="admin" username="0181271654" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0181271654";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0181271654";
};

# المستخدم 184: 0100792572
:do {
    /tool user-manager user add customer="admin" username="0100792572" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0100792572";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0100792572";
};

# المستخدم 185: 0178693185
:do {
    /tool user-manager user add customer="admin" username="0178693185" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0178693185";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0178693185";
};

# المستخدم 186: 0121161928
:do {
    /tool user-manager user add customer="admin" username="0121161928" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0121161928";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0121161928";
};

# المستخدم 187: 0181021925
:do {
    /tool user-manager user add customer="admin" username="0181021925" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0181021925";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0181021925";
};

# المستخدم 188: 0129181648
:do {
    /tool user-manager user add customer="admin" username="0129181648" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0129181648";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0129181648";
};

# المستخدم 189: 0166650551
:do {
    /tool user-manager user add customer="admin" username="0166650551" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0166650551";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0166650551";
};

# المستخدم 190: 0118267573
:do {
    /tool user-manager user add customer="admin" username="0118267573" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0118267573";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0118267573";
};

# المستخدم 191: 0106799259
:do {
    /tool user-manager user add customer="admin" username="0106799259" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0106799259";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0106799259";
};

# المستخدم 192: 0199020910
:do {
    /tool user-manager user add customer="admin" username="0199020910" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0199020910";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0199020910";
};

# المستخدم 193: 0120053838
:do {
    /tool user-manager user add customer="admin" username="0120053838" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0120053838";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0120053838";
};

# المستخدم 194: 0131113461
:do {
    /tool user-manager user add customer="admin" username="0131113461" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0131113461";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0131113461";
};

# المستخدم 195: 0173221940
:do {
    /tool user-manager user add customer="admin" username="0173221940" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0173221940";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0173221940";
};

# المستخدم 196: 0197529764
:do {
    /tool user-manager user add customer="admin" username="0197529764" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0197529764";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0197529764";
};

# المستخدم 197: 0154689924
:do {
    /tool user-manager user add customer="admin" username="0154689924" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0154689924";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0154689924";
};

# المستخدم 198: 0184090810
:do {
    /tool user-manager user add customer="admin" username="0184090810" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0184090810";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0184090810";
};

# المستخدم 199: 0155693080
:do {
    /tool user-manager user add customer="admin" username="0155693080" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0155693080";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0155693080";
};

# المستخدم 200: 0175677691
:do {
    /tool user-manager user add customer="admin" username="0175677691" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0175677691";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0175677691";
};


:put "📊 تم الانتهاء من النسخة الاحتياطية";
:put "✅ نجح: $success كرت";
:put "❌ فشل: $errors كرت";
:put "📈 الإجمالي: $total كرت";

:put "🎉 تم استعادة جميع كروت القالب: 10";
:put "💡 النسخة الاحتياطية مكتملة بنجاح!";
