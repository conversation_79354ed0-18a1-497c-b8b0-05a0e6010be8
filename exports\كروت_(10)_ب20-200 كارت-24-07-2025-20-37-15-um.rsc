# MikroTik Script - نسخة احتياطية للكروت
# تم الإنشاء: 2025-07-24 20:37:15
# القالب: 10
# النظام: user_manager
# عدد الكروت: 200
# تم إنشاؤه بواسطة: مولد كروت MikroTik

:put "🚀 بدء تنفيذ النسخة الاحتياطية للكروت";
:put "📋 القالب: 10";
:put "🎯 النظام: user_manager";
:put "📊 عدد الكروت: 200";

:local success 0;
:local errors 0;
:local total 200;

:put "⏳ بدء إضافة الكروت...";

# إضافة مستخدمي User Manager
:put "👤 إضافة 200 مستخدم User Manager...";

# المستخدم 1: 0120773345
:do {
    /tool user-manager user add customer="admin" username="0120773345" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0120773345";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0120773345";
};

# المستخدم 2: 0196452096
:do {
    /tool user-manager user add customer="admin" username="0196452096" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0196452096";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0196452096";
};

# المستخدم 3: 0174998039
:do {
    /tool user-manager user add customer="admin" username="0174998039" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0174998039";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0174998039";
};

# المستخدم 4: 0152991512
:do {
    /tool user-manager user add customer="admin" username="0152991512" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0152991512";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0152991512";
};

# المستخدم 5: 0196439878
:do {
    /tool user-manager user add customer="admin" username="0196439878" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0196439878";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0196439878";
};

# المستخدم 6: 0134266091
:do {
    /tool user-manager user add customer="admin" username="0134266091" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0134266091";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0134266091";
};

# المستخدم 7: 0167339369
:do {
    /tool user-manager user add customer="admin" username="0167339369" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0167339369";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0167339369";
};

# المستخدم 8: 0192534734
:do {
    /tool user-manager user add customer="admin" username="0192534734" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0192534734";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0192534734";
};

# المستخدم 9: 0107797532
:do {
    /tool user-manager user add customer="admin" username="0107797532" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0107797532";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0107797532";
};

# المستخدم 10: 0159436798
:do {
    /tool user-manager user add customer="admin" username="0159436798" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0159436798";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0159436798";
};

# المستخدم 11: 0194633762
:do {
    /tool user-manager user add customer="admin" username="0194633762" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0194633762";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0194633762";
};

# المستخدم 12: 0114353966
:do {
    /tool user-manager user add customer="admin" username="0114353966" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0114353966";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0114353966";
};

# المستخدم 13: 0196437195
:do {
    /tool user-manager user add customer="admin" username="0196437195" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0196437195";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0196437195";
};

# المستخدم 14: 0152876109
:do {
    /tool user-manager user add customer="admin" username="0152876109" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0152876109";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0152876109";
};

# المستخدم 15: 0136987045
:do {
    /tool user-manager user add customer="admin" username="0136987045" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0136987045";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0136987045";
};

# المستخدم 16: 0102644677
:do {
    /tool user-manager user add customer="admin" username="0102644677" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0102644677";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0102644677";
};

# المستخدم 17: 0186907044
:do {
    /tool user-manager user add customer="admin" username="0186907044" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0186907044";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0186907044";
};

# المستخدم 18: 0177299402
:do {
    /tool user-manager user add customer="admin" username="0177299402" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0177299402";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0177299402";
};

# المستخدم 19: 0141397413
:do {
    /tool user-manager user add customer="admin" username="0141397413" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0141397413";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0141397413";
};

# المستخدم 20: 0106025133
:do {
    /tool user-manager user add customer="admin" username="0106025133" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0106025133";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0106025133";
};

# المستخدم 21: 0192890682
:do {
    /tool user-manager user add customer="admin" username="0192890682" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0192890682";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0192890682";
};

# المستخدم 22: 0154137800
:do {
    /tool user-manager user add customer="admin" username="0154137800" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0154137800";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0154137800";
};

# المستخدم 23: 0104172119
:do {
    /tool user-manager user add customer="admin" username="0104172119" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0104172119";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0104172119";
};

# المستخدم 24: 0167453042
:do {
    /tool user-manager user add customer="admin" username="0167453042" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0167453042";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0167453042";
};

# المستخدم 25: 0191493661
:do {
    /tool user-manager user add customer="admin" username="0191493661" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0191493661";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0191493661";
};

# المستخدم 26: 0164956179
:do {
    /tool user-manager user add customer="admin" username="0164956179" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0164956179";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0164956179";
};

# المستخدم 27: 0172659229
:do {
    /tool user-manager user add customer="admin" username="0172659229" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0172659229";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0172659229";
};

# المستخدم 28: 0137683186
:do {
    /tool user-manager user add customer="admin" username="0137683186" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0137683186";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0137683186";
};

# المستخدم 29: 0123907445
:do {
    /tool user-manager user add customer="admin" username="0123907445" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0123907445";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0123907445";
};

# المستخدم 30: 0170779072
:do {
    /tool user-manager user add customer="admin" username="0170779072" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0170779072";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0170779072";
};

# المستخدم 31: 0129358142
:do {
    /tool user-manager user add customer="admin" username="0129358142" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0129358142";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0129358142";
};

# المستخدم 32: 0152790214
:do {
    /tool user-manager user add customer="admin" username="0152790214" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0152790214";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0152790214";
};

# المستخدم 33: 0141788245
:do {
    /tool user-manager user add customer="admin" username="0141788245" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0141788245";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0141788245";
};

# المستخدم 34: 0126560046
:do {
    /tool user-manager user add customer="admin" username="0126560046" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0126560046";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0126560046";
};

# المستخدم 35: 0191640524
:do {
    /tool user-manager user add customer="admin" username="0191640524" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0191640524";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0191640524";
};

# المستخدم 36: 0173297026
:do {
    /tool user-manager user add customer="admin" username="0173297026" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0173297026";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0173297026";
};

# المستخدم 37: 0106800668
:do {
    /tool user-manager user add customer="admin" username="0106800668" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0106800668";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0106800668";
};

# المستخدم 38: 0121741950
:do {
    /tool user-manager user add customer="admin" username="0121741950" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0121741950";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0121741950";
};

# المستخدم 39: 0119696079
:do {
    /tool user-manager user add customer="admin" username="0119696079" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0119696079";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0119696079";
};

# المستخدم 40: 0192346949
:do {
    /tool user-manager user add customer="admin" username="0192346949" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0192346949";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0192346949";
};

# المستخدم 41: 0182908858
:do {
    /tool user-manager user add customer="admin" username="0182908858" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0182908858";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0182908858";
};

# المستخدم 42: 0155449719
:do {
    /tool user-manager user add customer="admin" username="0155449719" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0155449719";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0155449719";
};

# المستخدم 43: 0136606180
:do {
    /tool user-manager user add customer="admin" username="0136606180" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0136606180";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0136606180";
};

# المستخدم 44: 0166571933
:do {
    /tool user-manager user add customer="admin" username="0166571933" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0166571933";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0166571933";
};

# المستخدم 45: 0143008187
:do {
    /tool user-manager user add customer="admin" username="0143008187" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0143008187";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0143008187";
};

# المستخدم 46: 0145195350
:do {
    /tool user-manager user add customer="admin" username="0145195350" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0145195350";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0145195350";
};

# المستخدم 47: 0155118652
:do {
    /tool user-manager user add customer="admin" username="0155118652" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0155118652";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0155118652";
};

# المستخدم 48: 0109336101
:do {
    /tool user-manager user add customer="admin" username="0109336101" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0109336101";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0109336101";
};

# المستخدم 49: 0167124912
:do {
    /tool user-manager user add customer="admin" username="0167124912" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0167124912";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0167124912";
};

# المستخدم 50: 0132414386
:do {
    /tool user-manager user add customer="admin" username="0132414386" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0132414386";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0132414386";
};

# المستخدم 51: 0163413669
:do {
    /tool user-manager user add customer="admin" username="0163413669" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0163413669";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0163413669";
};

# المستخدم 52: 0162587060
:do {
    /tool user-manager user add customer="admin" username="0162587060" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0162587060";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0162587060";
};

# المستخدم 53: 0106311787
:do {
    /tool user-manager user add customer="admin" username="0106311787" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0106311787";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0106311787";
};

# المستخدم 54: 0166246953
:do {
    /tool user-manager user add customer="admin" username="0166246953" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0166246953";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0166246953";
};

# المستخدم 55: 0184610494
:do {
    /tool user-manager user add customer="admin" username="0184610494" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0184610494";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0184610494";
};

# المستخدم 56: 0137961400
:do {
    /tool user-manager user add customer="admin" username="0137961400" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0137961400";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0137961400";
};

# المستخدم 57: 0114888017
:do {
    /tool user-manager user add customer="admin" username="0114888017" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0114888017";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0114888017";
};

# المستخدم 58: 0153240656
:do {
    /tool user-manager user add customer="admin" username="0153240656" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0153240656";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0153240656";
};

# المستخدم 59: 0191527935
:do {
    /tool user-manager user add customer="admin" username="0191527935" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0191527935";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0191527935";
};

# المستخدم 60: 0129909213
:do {
    /tool user-manager user add customer="admin" username="0129909213" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0129909213";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0129909213";
};

# المستخدم 61: 0111390114
:do {
    /tool user-manager user add customer="admin" username="0111390114" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0111390114";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0111390114";
};

# المستخدم 62: 0146670413
:do {
    /tool user-manager user add customer="admin" username="0146670413" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0146670413";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0146670413";
};

# المستخدم 63: 0132657817
:do {
    /tool user-manager user add customer="admin" username="0132657817" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0132657817";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0132657817";
};

# المستخدم 64: 0188892511
:do {
    /tool user-manager user add customer="admin" username="0188892511" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0188892511";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0188892511";
};

# المستخدم 65: 0145034878
:do {
    /tool user-manager user add customer="admin" username="0145034878" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0145034878";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0145034878";
};

# المستخدم 66: 0118415236
:do {
    /tool user-manager user add customer="admin" username="0118415236" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0118415236";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0118415236";
};

# المستخدم 67: 0123121964
:do {
    /tool user-manager user add customer="admin" username="0123121964" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0123121964";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0123121964";
};

# المستخدم 68: 0115585102
:do {
    /tool user-manager user add customer="admin" username="0115585102" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0115585102";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0115585102";
};

# المستخدم 69: 0121665565
:do {
    /tool user-manager user add customer="admin" username="0121665565" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0121665565";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0121665565";
};

# المستخدم 70: 0171548802
:do {
    /tool user-manager user add customer="admin" username="0171548802" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0171548802";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0171548802";
};

# المستخدم 71: 0108723548
:do {
    /tool user-manager user add customer="admin" username="0108723548" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0108723548";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0108723548";
};

# المستخدم 72: 0142253366
:do {
    /tool user-manager user add customer="admin" username="0142253366" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0142253366";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0142253366";
};

# المستخدم 73: 0148459116
:do {
    /tool user-manager user add customer="admin" username="0148459116" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0148459116";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0148459116";
};

# المستخدم 74: 0120607111
:do {
    /tool user-manager user add customer="admin" username="0120607111" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0120607111";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0120607111";
};

# المستخدم 75: 0100804233
:do {
    /tool user-manager user add customer="admin" username="0100804233" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0100804233";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0100804233";
};

# المستخدم 76: 0108076020
:do {
    /tool user-manager user add customer="admin" username="0108076020" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0108076020";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0108076020";
};

# المستخدم 77: 0125550456
:do {
    /tool user-manager user add customer="admin" username="0125550456" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0125550456";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0125550456";
};

# المستخدم 78: 0108060284
:do {
    /tool user-manager user add customer="admin" username="0108060284" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0108060284";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0108060284";
};

# المستخدم 79: 0181967453
:do {
    /tool user-manager user add customer="admin" username="0181967453" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0181967453";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0181967453";
};

# المستخدم 80: 0130144880
:do {
    /tool user-manager user add customer="admin" username="0130144880" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0130144880";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0130144880";
};

# المستخدم 81: 0123889116
:do {
    /tool user-manager user add customer="admin" username="0123889116" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0123889116";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0123889116";
};

# المستخدم 82: 0140736204
:do {
    /tool user-manager user add customer="admin" username="0140736204" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0140736204";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0140736204";
};

# المستخدم 83: 0114132843
:do {
    /tool user-manager user add customer="admin" username="0114132843" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0114132843";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0114132843";
};

# المستخدم 84: 0123247424
:do {
    /tool user-manager user add customer="admin" username="0123247424" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0123247424";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0123247424";
};

# المستخدم 85: 0122307183
:do {
    /tool user-manager user add customer="admin" username="0122307183" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0122307183";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0122307183";
};

# المستخدم 86: 0111179380
:do {
    /tool user-manager user add customer="admin" username="0111179380" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0111179380";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0111179380";
};

# المستخدم 87: 0163847700
:do {
    /tool user-manager user add customer="admin" username="0163847700" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0163847700";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0163847700";
};

# المستخدم 88: 0137664984
:do {
    /tool user-manager user add customer="admin" username="0137664984" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0137664984";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0137664984";
};

# المستخدم 89: 0177494193
:do {
    /tool user-manager user add customer="admin" username="0177494193" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0177494193";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0177494193";
};

# المستخدم 90: 0158395038
:do {
    /tool user-manager user add customer="admin" username="0158395038" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0158395038";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0158395038";
};

# المستخدم 91: 0134514637
:do {
    /tool user-manager user add customer="admin" username="0134514637" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0134514637";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0134514637";
};

# المستخدم 92: 0113707372
:do {
    /tool user-manager user add customer="admin" username="0113707372" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0113707372";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0113707372";
};

# المستخدم 93: 0115957383
:do {
    /tool user-manager user add customer="admin" username="0115957383" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0115957383";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0115957383";
};

# المستخدم 94: 0121844949
:do {
    /tool user-manager user add customer="admin" username="0121844949" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0121844949";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0121844949";
};

# المستخدم 95: 0164245611
:do {
    /tool user-manager user add customer="admin" username="0164245611" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0164245611";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0164245611";
};

# المستخدم 96: 0152362110
:do {
    /tool user-manager user add customer="admin" username="0152362110" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0152362110";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0152362110";
};

# المستخدم 97: 0175282163
:do {
    /tool user-manager user add customer="admin" username="0175282163" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0175282163";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0175282163";
};

# المستخدم 98: 0186912529
:do {
    /tool user-manager user add customer="admin" username="0186912529" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0186912529";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0186912529";
};

# المستخدم 99: 0117660405
:do {
    /tool user-manager user add customer="admin" username="0117660405" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0117660405";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0117660405";
};

# المستخدم 100: 0130521800
:do {
    /tool user-manager user add customer="admin" username="0130521800" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0130521800";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0130521800";
};

# المستخدم 101: 0171875843
:do {
    /tool user-manager user add customer="admin" username="0171875843" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0171875843";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0171875843";
};

# المستخدم 102: 0151900866
:do {
    /tool user-manager user add customer="admin" username="0151900866" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0151900866";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0151900866";
};

# المستخدم 103: 0173312295
:do {
    /tool user-manager user add customer="admin" username="0173312295" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0173312295";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0173312295";
};

# المستخدم 104: 0118173463
:do {
    /tool user-manager user add customer="admin" username="0118173463" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0118173463";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0118173463";
};

# المستخدم 105: 0158154405
:do {
    /tool user-manager user add customer="admin" username="0158154405" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0158154405";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0158154405";
};

# المستخدم 106: 0116354954
:do {
    /tool user-manager user add customer="admin" username="0116354954" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0116354954";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0116354954";
};

# المستخدم 107: 0131730221
:do {
    /tool user-manager user add customer="admin" username="0131730221" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0131730221";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0131730221";
};

# المستخدم 108: 0123713412
:do {
    /tool user-manager user add customer="admin" username="0123713412" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0123713412";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0123713412";
};

# المستخدم 109: 0144377481
:do {
    /tool user-manager user add customer="admin" username="0144377481" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0144377481";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0144377481";
};

# المستخدم 110: 0121711087
:do {
    /tool user-manager user add customer="admin" username="0121711087" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0121711087";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0121711087";
};

# المستخدم 111: 0155022574
:do {
    /tool user-manager user add customer="admin" username="0155022574" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0155022574";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0155022574";
};

# المستخدم 112: 0167248892
:do {
    /tool user-manager user add customer="admin" username="0167248892" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0167248892";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0167248892";
};

# المستخدم 113: 0152211867
:do {
    /tool user-manager user add customer="admin" username="0152211867" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0152211867";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0152211867";
};

# المستخدم 114: 0170228065
:do {
    /tool user-manager user add customer="admin" username="0170228065" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0170228065";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0170228065";
};

# المستخدم 115: 0111889701
:do {
    /tool user-manager user add customer="admin" username="0111889701" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0111889701";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0111889701";
};

# المستخدم 116: 0151999095
:do {
    /tool user-manager user add customer="admin" username="0151999095" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0151999095";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0151999095";
};

# المستخدم 117: 0182068328
:do {
    /tool user-manager user add customer="admin" username="0182068328" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0182068328";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0182068328";
};

# المستخدم 118: 0141417172
:do {
    /tool user-manager user add customer="admin" username="0141417172" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0141417172";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0141417172";
};

# المستخدم 119: 0178478362
:do {
    /tool user-manager user add customer="admin" username="0178478362" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0178478362";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0178478362";
};

# المستخدم 120: 0177911707
:do {
    /tool user-manager user add customer="admin" username="0177911707" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0177911707";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0177911707";
};

# المستخدم 121: 0128785314
:do {
    /tool user-manager user add customer="admin" username="0128785314" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0128785314";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0128785314";
};

# المستخدم 122: 0131214987
:do {
    /tool user-manager user add customer="admin" username="0131214987" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0131214987";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0131214987";
};

# المستخدم 123: 0128033867
:do {
    /tool user-manager user add customer="admin" username="0128033867" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0128033867";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0128033867";
};

# المستخدم 124: 0194879576
:do {
    /tool user-manager user add customer="admin" username="0194879576" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0194879576";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0194879576";
};

# المستخدم 125: 0116012541
:do {
    /tool user-manager user add customer="admin" username="0116012541" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0116012541";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0116012541";
};

# المستخدم 126: 0108149795
:do {
    /tool user-manager user add customer="admin" username="0108149795" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0108149795";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0108149795";
};

# المستخدم 127: 0154430264
:do {
    /tool user-manager user add customer="admin" username="0154430264" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0154430264";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0154430264";
};

# المستخدم 128: 0188268568
:do {
    /tool user-manager user add customer="admin" username="0188268568" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0188268568";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0188268568";
};

# المستخدم 129: 0123364997
:do {
    /tool user-manager user add customer="admin" username="0123364997" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0123364997";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0123364997";
};

# المستخدم 130: 0135422268
:do {
    /tool user-manager user add customer="admin" username="0135422268" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0135422268";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0135422268";
};

# المستخدم 131: 0109157069
:do {
    /tool user-manager user add customer="admin" username="0109157069" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0109157069";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0109157069";
};

# المستخدم 132: 0136665300
:do {
    /tool user-manager user add customer="admin" username="0136665300" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0136665300";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0136665300";
};

# المستخدم 133: 0109894379
:do {
    /tool user-manager user add customer="admin" username="0109894379" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0109894379";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0109894379";
};

# المستخدم 134: 0178773471
:do {
    /tool user-manager user add customer="admin" username="0178773471" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0178773471";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0178773471";
};

# المستخدم 135: 0142985494
:do {
    /tool user-manager user add customer="admin" username="0142985494" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0142985494";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0142985494";
};

# المستخدم 136: 0160807112
:do {
    /tool user-manager user add customer="admin" username="0160807112" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0160807112";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0160807112";
};

# المستخدم 137: 0107612800
:do {
    /tool user-manager user add customer="admin" username="0107612800" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0107612800";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0107612800";
};

# المستخدم 138: 0190855587
:do {
    /tool user-manager user add customer="admin" username="0190855587" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0190855587";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0190855587";
};

# المستخدم 139: 0172228770
:do {
    /tool user-manager user add customer="admin" username="0172228770" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0172228770";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0172228770";
};

# المستخدم 140: 0133712984
:do {
    /tool user-manager user add customer="admin" username="0133712984" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0133712984";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0133712984";
};

# المستخدم 141: 0183819526
:do {
    /tool user-manager user add customer="admin" username="0183819526" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0183819526";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0183819526";
};

# المستخدم 142: 0128025288
:do {
    /tool user-manager user add customer="admin" username="0128025288" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0128025288";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0128025288";
};

# المستخدم 143: 0152093975
:do {
    /tool user-manager user add customer="admin" username="0152093975" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0152093975";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0152093975";
};

# المستخدم 144: 0194144064
:do {
    /tool user-manager user add customer="admin" username="0194144064" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0194144064";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0194144064";
};

# المستخدم 145: 0134733267
:do {
    /tool user-manager user add customer="admin" username="0134733267" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0134733267";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0134733267";
};

# المستخدم 146: 0142717312
:do {
    /tool user-manager user add customer="admin" username="0142717312" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0142717312";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0142717312";
};

# المستخدم 147: 0140767519
:do {
    /tool user-manager user add customer="admin" username="0140767519" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0140767519";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0140767519";
};

# المستخدم 148: 0171132299
:do {
    /tool user-manager user add customer="admin" username="0171132299" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0171132299";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0171132299";
};

# المستخدم 149: 0133581810
:do {
    /tool user-manager user add customer="admin" username="0133581810" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0133581810";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0133581810";
};

# المستخدم 150: 0181126573
:do {
    /tool user-manager user add customer="admin" username="0181126573" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0181126573";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0181126573";
};

# المستخدم 151: 0120537221
:do {
    /tool user-manager user add customer="admin" username="0120537221" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0120537221";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0120537221";
};

# المستخدم 152: 0181875284
:do {
    /tool user-manager user add customer="admin" username="0181875284" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0181875284";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0181875284";
};

# المستخدم 153: 0182312891
:do {
    /tool user-manager user add customer="admin" username="0182312891" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0182312891";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0182312891";
};

# المستخدم 154: 0105622520
:do {
    /tool user-manager user add customer="admin" username="0105622520" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0105622520";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0105622520";
};

# المستخدم 155: 0131925725
:do {
    /tool user-manager user add customer="admin" username="0131925725" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0131925725";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0131925725";
};

# المستخدم 156: 0132159688
:do {
    /tool user-manager user add customer="admin" username="0132159688" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0132159688";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0132159688";
};

# المستخدم 157: 0180043981
:do {
    /tool user-manager user add customer="admin" username="0180043981" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0180043981";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0180043981";
};

# المستخدم 158: 0133417494
:do {
    /tool user-manager user add customer="admin" username="0133417494" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0133417494";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0133417494";
};

# المستخدم 159: 0143914579
:do {
    /tool user-manager user add customer="admin" username="0143914579" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0143914579";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0143914579";
};

# المستخدم 160: 0157428912
:do {
    /tool user-manager user add customer="admin" username="0157428912" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0157428912";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0157428912";
};

# المستخدم 161: 0122098756
:do {
    /tool user-manager user add customer="admin" username="0122098756" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0122098756";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0122098756";
};

# المستخدم 162: 0174633747
:do {
    /tool user-manager user add customer="admin" username="0174633747" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0174633747";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0174633747";
};

# المستخدم 163: 0193357624
:do {
    /tool user-manager user add customer="admin" username="0193357624" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0193357624";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0193357624";
};

# المستخدم 164: 0109766332
:do {
    /tool user-manager user add customer="admin" username="0109766332" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0109766332";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0109766332";
};

# المستخدم 165: 0158601119
:do {
    /tool user-manager user add customer="admin" username="0158601119" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0158601119";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0158601119";
};

# المستخدم 166: 0136211105
:do {
    /tool user-manager user add customer="admin" username="0136211105" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0136211105";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0136211105";
};

# المستخدم 167: 0138125848
:do {
    /tool user-manager user add customer="admin" username="0138125848" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0138125848";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0138125848";
};

# المستخدم 168: 0185348411
:do {
    /tool user-manager user add customer="admin" username="0185348411" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0185348411";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0185348411";
};

# المستخدم 169: 0192265657
:do {
    /tool user-manager user add customer="admin" username="0192265657" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0192265657";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0192265657";
};

# المستخدم 170: 0160073458
:do {
    /tool user-manager user add customer="admin" username="0160073458" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0160073458";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0160073458";
};

# المستخدم 171: 0131667252
:do {
    /tool user-manager user add customer="admin" username="0131667252" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0131667252";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0131667252";
};

# المستخدم 172: 0187531525
:do {
    /tool user-manager user add customer="admin" username="0187531525" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0187531525";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0187531525";
};

# المستخدم 173: 0132637960
:do {
    /tool user-manager user add customer="admin" username="0132637960" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0132637960";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0132637960";
};

# المستخدم 174: 0173402603
:do {
    /tool user-manager user add customer="admin" username="0173402603" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0173402603";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0173402603";
};

# المستخدم 175: 0134643651
:do {
    /tool user-manager user add customer="admin" username="0134643651" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0134643651";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0134643651";
};

# المستخدم 176: 0146895503
:do {
    /tool user-manager user add customer="admin" username="0146895503" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0146895503";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0146895503";
};

# المستخدم 177: 0112214899
:do {
    /tool user-manager user add customer="admin" username="0112214899" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0112214899";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0112214899";
};

# المستخدم 178: 0145523296
:do {
    /tool user-manager user add customer="admin" username="0145523296" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0145523296";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0145523296";
};

# المستخدم 179: 0185588690
:do {
    /tool user-manager user add customer="admin" username="0185588690" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0185588690";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0185588690";
};

# المستخدم 180: 0121674737
:do {
    /tool user-manager user add customer="admin" username="0121674737" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0121674737";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0121674737";
};

# المستخدم 181: 0124205895
:do {
    /tool user-manager user add customer="admin" username="0124205895" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0124205895";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0124205895";
};

# المستخدم 182: 0124522924
:do {
    /tool user-manager user add customer="admin" username="0124522924" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0124522924";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0124522924";
};

# المستخدم 183: 0113731786
:do {
    /tool user-manager user add customer="admin" username="0113731786" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0113731786";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0113731786";
};

# المستخدم 184: 0137615263
:do {
    /tool user-manager user add customer="admin" username="0137615263" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0137615263";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0137615263";
};

# المستخدم 185: 0150264423
:do {
    /tool user-manager user add customer="admin" username="0150264423" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0150264423";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0150264423";
};

# المستخدم 186: 0132180312
:do {
    /tool user-manager user add customer="admin" username="0132180312" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0132180312";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0132180312";
};

# المستخدم 187: 0139073897
:do {
    /tool user-manager user add customer="admin" username="0139073897" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0139073897";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0139073897";
};

# المستخدم 188: 0126570178
:do {
    /tool user-manager user add customer="admin" username="0126570178" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0126570178";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0126570178";
};

# المستخدم 189: 0135858144
:do {
    /tool user-manager user add customer="admin" username="0135858144" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0135858144";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0135858144";
};

# المستخدم 190: 0140589202
:do {
    /tool user-manager user add customer="admin" username="0140589202" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0140589202";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0140589202";
};

# المستخدم 191: 0165814256
:do {
    /tool user-manager user add customer="admin" username="0165814256" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0165814256";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0165814256";
};

# المستخدم 192: 0177652213
:do {
    /tool user-manager user add customer="admin" username="0177652213" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0177652213";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0177652213";
};

# المستخدم 193: 0190181535
:do {
    /tool user-manager user add customer="admin" username="0190181535" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0190181535";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0190181535";
};

# المستخدم 194: 0165435163
:do {
    /tool user-manager user add customer="admin" username="0165435163" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0165435163";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0165435163";
};

# المستخدم 195: 0165834987
:do {
    /tool user-manager user add customer="admin" username="0165834987" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0165834987";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0165834987";
};

# المستخدم 196: 0171173132
:do {
    /tool user-manager user add customer="admin" username="0171173132" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0171173132";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0171173132";
};

# المستخدم 197: 0162323080
:do {
    /tool user-manager user add customer="admin" username="0162323080" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0162323080";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0162323080";
};

# المستخدم 198: 0113541026
:do {
    /tool user-manager user add customer="admin" username="0113541026" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0113541026";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0113541026";
};

# المستخدم 199: 0175526273
:do {
    /tool user-manager user add customer="admin" username="0175526273" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0175526273";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0175526273";
};

# المستخدم 200: 0191703681
:do {
    /tool user-manager user add customer="admin" username="0191703681" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0191703681";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0191703681";
};


:put "📊 تم الانتهاء من النسخة الاحتياطية";
:put "✅ نجح: $success كرت";
:put "❌ فشل: $errors كرت";
:put "📈 الإجمالي: $total كرت";

:put "🎉 تم استعادة جميع كروت القالب: 10";
:put "💡 النسخة الاحتياطية مكتملة بنجاح!";
