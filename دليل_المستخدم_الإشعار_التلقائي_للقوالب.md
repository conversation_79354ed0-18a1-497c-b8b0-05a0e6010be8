# دليل المستخدم: الإشعار التلقائي عند اختيار البرق في User Manager

## نظرة عامة

تم إضافة ميزة جديدة إلى بوت التلجرام تقوم بإرسال **إشعار تلقائي** يحتوي على العدد الحالي للكروت في قاعدة البيانات عند اختيار طريقة **البرق (Lightning)** في User Manager. هذا يساعدك على معرفة الحالة الحالية قبل بدء عملية البرق مباشرة.

## كيفية عمل الميزة

### التوقيت الجديد
- **بعد اختيار قالب User Manager من البوت**
- **ثم اختيار طريقة "⚡ برق" (Lightning)**
- **في هذه اللحظة تحديداً يظهر الإشعار**
- **قبل بدء عملية البرق مباشرة**

### المعلومات المتضمنة
- 📊 العدد الحالي للكروت في User Manager
- 🎯 اسم القالب المختار
- 🔧 نوع النظام (User Manager)
- ⚡ طريقة الإنشاء (البرق)
- ⏰ الوقت الحالي
- 📅 التاريخ الحالي
- ✅ حالة الاستعداد لعملية البرق

## أمثلة على الاستخدام

### مثال: اختيار البرق في User Manager

**الخطوات:**
1. افتح بوت التلجرام
2. اختر "User Manager" من القائمة الرئيسية
3. اختر قالب معين (مثل "قالب_10") ← **لا إشعار هنا**
4. اختر "⚡ برق" من خيارات الطرق ← **الإشعار يظهر هنا**

**الإشعار الذي ستحصل عليه:**
```
📊 إشعار اختيار البرق

🎯 القالب المختار: قالب_10
🔧 النظام: User Manager
⚡ الطريقة: البرق (Lightning)
📈 العدد الحالي للكروت: 1250

⏰ الوقت: 14:30:25
📅 التاريخ: 2025-07-24

✅ الحالة: جاهز لبدء عملية البرق
```

### الحالات التي لا يظهر فيها الإشعار

**لا يظهر الإشعار في الحالات التالية:**
- عند اختيار القالب (قبل اختيار الطريقة)
- عند اختيار الطريقة العادية في User Manager
- عند اختيار أي طريقة في Hotspot
- عند استخدام أي نظام غير User Manager

**مثال على عدم ظهور الإشعار:**
```
❌ اختيار قالب User Manager → لا إشعار
❌ اختيار "🔄 عادي" في User Manager → لا إشعار
❌ اختيار أي طريقة في Hotspot → لا إشعار
✅ اختيار "⚡ برق" في User Manager → إشعار يظهر
```

## الفوائد للمستخدم

### 1. معرفة الحالة الحالية قبل البرق
- **تعرف بالضبط كم كرت موجود** في User Manager قبل بدء عملية البرق
- **تتجنب الازدواجية** أو الإفراط في إنشاء الكروت
- **تخطط بشكل أفضل** لعدد الكروت المطلوب إنشاؤها بالبرق

### 2. اتخاذ قرارات مدروسة للبرق
- **إذا كان العدد الحالي كبير**: قد تقرر تقليل عدد كروت البرق
- **إذا كان العدد الحالي قليل**: قد تقرر زيادة عدد كروت البرق
- **إذا كان العدد مناسب**: تكمل عملية البرق كما هو مخطط

### 3. توفير الوقت والجهد
- **لا حاجة للدخول إلى MikroTik** للتحقق من العدد قبل البرق
- **معلومات فورية** في التوقيت المناسب
- **تركيز أكبر** على عملية البرق نفسها

### 4. التوقيت المثالي
- **الإشعار يأتي في اللحظة المناسبة** قبل بدء البرق مباشرة
- **لا إشعارات غير ضرورية** عند اختيار القوالب أو الطرق الأخرى
- **معلومات دقيقة** عندما تحتاجها فعلاً

## النظام المدعوم

### User Manager مع البرق
- ✅ جميع قوالب User Manager
- ✅ طريقة البرق (Lightning) فقط
- ✅ إشعار في التوقيت المثالي
- ✅ معلومات دقيقة ومفيدة

### الأنظمة غير المدعومة
- ❌ User Manager مع الطريقة العادية
- ❌ جميع قوالب وطرق Hotspot
- ❌ أي نظام آخر غير User Manager

## متطلبات التشغيل

### الإعدادات المطلوبة
- **Bot Token صحيح** في إعدادات التلجرام
- **Chat ID صحيح** في إعدادات التلجرام
- **اتصال فعال بـ MikroTik** لجلب العدد الحالي

### في حالة عدم توفر الإعدادات
- **لا يتم إرسال إشعار** (عمل صامت)
- **العملية تكمل بشكل طبيعي** دون تأثير
- **لا توجد رسائل خطأ** أو تعطيل

## استكشاف الأخطاء

### المشكلة: لا يصل الإشعار
**الحلول المحتملة:**
1. **تحقق من إعدادات التلجرام**
   - Bot Token صحيح؟
   - Chat ID صحيح؟
2. **تحقق من اتصال الإنترنت**
3. **تحقق من حالة البوت**

### المشكلة: العدد المعروض خاطئ
**الحلول المحتملة:**
1. **تحقق من اتصال MikroTik**
   - هل البرنامج متصل بـ MikroTik؟
   - هل بيانات الاتصال صحيحة؟
2. **أعد تشغيل البرنامج**
3. **تحقق من حالة قاعدة البيانات في MikroTik**

### المشكلة: الإشعار يصل متأخر
**الحلول المحتملة:**
1. **تحقق من سرعة الإنترنت**
2. **تحقق من حمولة الخادم**
3. **انتظر قليلاً - قد يكون تأخير طبيعي**

## نصائح للاستخدام الأمثل

### 1. راقب الأرقام بانتظام
- **لاحظ الاتجاهات** في أعداد الكروت
- **خطط مسبقاً** لاحتياجاتك
- **تجنب الإفراط** في إنشاء الكروت

### 2. استخدم المعلومات للتخطيط
- **إذا كان العدد > 1000**: فكر في التنظيف أولاً
- **إذا كان العدد < 100**: قد تحتاج لإنشاء المزيد
- **راعي سعة النظام** وقدرات MikroTik

### 3. استفد من التوقيت
- **الإشعار يأتي في الوقت المناسب** قبل البدء
- **لا تتجاهل المعلومات** المعروضة
- **استخدمها لاتخاذ قرارات أفضل**

## الأمان والخصوصية

### حماية البيانات
- **لا يتم إرسال كلمات مرور** عبر التلجرام
- **فقط الأرقام الإحصائية** يتم إرسالها
- **جميع الاتصالات مشفرة** عبر HTTPS

### التحكم في الميزة
- **الميزة تعمل تلقائياً** إذا كانت الإعدادات متوفرة
- **يمكن تعطيلها** بحذف إعدادات التلجرام
- **لا تؤثر على عمل النظام** إذا تم تعطيلها

## الدعم الفني

### للحصول على المساعدة
1. **راجع هذا الدليل** أولاً
2. **تحقق من إعدادات النظام**
3. **راجع ملف السجل** في البرنامج
4. **اتصل بالدعم الفني** مع تفاصيل المشكلة

### معلومات مفيدة للدعم
- إصدار البرنامج
- نوع MikroTik المستخدم
- رسالة الخطأ (إن وجدت)
- خطوات إعادة إنتاج المشكلة
- لقطة شاشة من الإشعار

---

**ملاحظة:** هذه الميزة مصممة لتحسين تجربتك وتوفير معلومات مفيدة. استخدمها بحكمة لاتخاذ قرارات أفضل في إدارة الكروت! 🎯
