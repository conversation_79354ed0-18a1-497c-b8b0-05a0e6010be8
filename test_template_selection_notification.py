#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار ميزة الإشعار التلقائي عند اختيار القوالب في بوت التلجرام
تم إنشاؤه: 2025-07-24
الهدف: اختبار إضافة الإشعار التلقائي بالعدد الحالي للكروت عند اختيار أي قالب
"""

import unittest
import sys
import os
import json
from unittest.mock import Mock, patch, MagicMock

# إضافة مسار المشروع
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

class TestTemplateSelectionNotification(unittest.TestCase):
    """اختبار ميزة الإشعار التلقائي عند اختيار القوالب"""
    
    def setUp(self):
        """إعداد الاختبار"""
        # إنشاء mock للتطبيق
        self.mock_app = Mock()
        self.mock_app.logger = Mock()
        self.mock_app.system_type = 'user_manager'
        
        # إعداد mock للدوال المطلوبة
        self.mock_app.send_telegram_message_direct = Mock(return_value=True)
        self.mock_app.get_current_user_manager_count = Mock(return_value=150)
        self.mock_app.get_current_hotspot_count = Mock(return_value=75)
        self.mock_app.get_current_time = Mock(return_value="14:30:25")
        self.mock_app.get_current_date = Mock(return_value="2025-07-24")
        
        # إعداد بيانات الاختبار
        self.bot_token = "test_bot_token"
        self.chat_id = "test_chat_id"
        self.template_name = "قالب_اختبار"

    def test_send_template_selection_notification_user_manager(self):
        """اختبار إرسال إشعار اختيار قالب User Manager"""
        
        # محاكاة دالة الإشعار
        def mock_send_notification(bot_token, chat_id, template_name, system_name):
            # التحقق من توفر إعدادات التلجرام
            if not bot_token or not chat_id:
                return False

            # جلب العدد الحالي للكروت
            current_count = 150 if system_name == "User Manager" else 75
            
            # إنشاء رسالة الإشعار
            notification_message = f"""📊 **إشعار اختيار القالب**

🎯 **القالب المختار:** {template_name}
🔧 **النظام:** {system_name}
📈 **العدد الحالي للكروت:** {current_count}

⏰ **الوقت:** 14:30:25
📅 **التاريخ:** 2025-07-24

✅ **الحالة:** جاهز لإنشاء كروت جديدة"""

            # محاكاة إرسال الإشعار
            return True
        
        # تنفيذ الاختبار
        result = mock_send_notification(self.bot_token, self.chat_id, self.template_name, "User Manager")
        
        # التحقق من النتائج
        self.assertTrue(result)
        print("✅ اختبار إشعار اختيار قالب User Manager نجح")

    def test_send_template_selection_notification_hotspot(self):
        """اختبار إرسال إشعار اختيار قالب Hotspot"""
        
        # محاكاة دالة الإشعار
        def mock_send_notification(bot_token, chat_id, template_name, system_name):
            # التحقق من توفر إعدادات التلجرام
            if not bot_token or not chat_id:
                return False

            # جلب العدد الحالي للكروت
            current_count = 150 if system_name == "User Manager" else 75
            
            # إنشاء رسالة الإشعار
            notification_message = f"""📊 **إشعار اختيار القالب**

🎯 **القالب المختار:** {template_name}
🔧 **النظام:** {system_name}
📈 **العدد الحالي للكروت:** {current_count}

⏰ **الوقت:** 14:30:25
📅 **التاريخ:** 2025-07-24

✅ **الحالة:** جاهز لإنشاء كروت جديدة"""

            # محاكاة إرسال الإشعار
            return True
        
        # تنفيذ الاختبار
        result = mock_send_notification(self.bot_token, self.chat_id, "قالب_هوت_سبوت", "Hotspot")
        
        # التحقق من النتائج
        self.assertTrue(result)
        print("✅ اختبار إشعار اختيار قالب Hotspot نجح")

    def test_notification_without_telegram_settings(self):
        """اختبار الإشعار بدون إعدادات التلجرام"""
        
        # محاكاة دالة الإشعار بدون إعدادات
        def mock_send_notification_no_settings(bot_token, chat_id, template_name, system_name):
            # التحقق من توفر إعدادات التلجرام
            if not bot_token or not chat_id:
                return False
            return True
        
        # تنفيذ الاختبار بدون إعدادات
        result = mock_send_notification_no_settings("", "", self.template_name, "User Manager")
        
        # التحقق من النتائج
        self.assertFalse(result)
        print("✅ اختبار الإشعار بدون إعدادات التلجرام نجح")

    def test_get_current_user_manager_count(self):
        """اختبار جلب العدد الحالي لكروت User Manager"""
        
        # محاكاة دالة جلب العدد
        def mock_get_user_manager_count():
            try:
                # محاكاة الاتصال بـ MikroTik
                # في الاختبار، نعيد رقم ثابت
                return 150
            except:
                return 0
        
        # تنفيذ الاختبار
        count = mock_get_user_manager_count()
        
        # التحقق من النتائج
        self.assertEqual(count, 150)
        print("✅ اختبار جلب عدد User Manager نجح")

    def test_get_current_hotspot_count(self):
        """اختبار جلب العدد الحالي لكروت Hotspot"""
        
        # محاكاة دالة جلب العدد
        def mock_get_hotspot_count():
            try:
                # محاكاة الاتصال بـ MikroTik
                # في الاختبار، نعيد رقم ثابت
                return 75
            except:
                return 0
        
        # تنفيذ الاختبار
        count = mock_get_hotspot_count()
        
        # التحقق من النتائج
        self.assertEqual(count, 75)
        print("✅ اختبار جلب عدد Hotspot نجح")

    def test_notification_message_format(self):
        """اختبار تنسيق رسالة الإشعار"""
        
        # محاكاة إنشاء رسالة الإشعار
        def mock_create_notification_message(template_name, system_name, current_count):
            notification_message = f"""📊 **إشعار اختيار القالب**

🎯 **القالب المختار:** {template_name}
🔧 **النظام:** {system_name}
📈 **العدد الحالي للكروت:** {current_count}

⏰ **الوقت:** 14:30:25
📅 **التاريخ:** 2025-07-24

✅ **الحالة:** جاهز لإنشاء كروت جديدة"""
            return notification_message
        
        # تنفيذ الاختبار
        message = mock_create_notification_message("قالب_اختبار", "User Manager", 150)
        
        # التحقق من وجود العناصر المطلوبة
        self.assertIn("📊", message)  # أيقونة الإحصائيات
        self.assertIn("🎯", message)  # أيقونة القالب
        self.assertIn("🔧", message)  # أيقونة النظام
        self.assertIn("📈", message)  # أيقونة العدد
        self.assertIn("⏰", message)  # أيقونة الوقت
        self.assertIn("📅", message)  # أيقونة التاريخ
        self.assertIn("✅", message)  # أيقونة الحالة
        self.assertIn("قالب_اختبار", message)  # اسم القالب
        self.assertIn("User Manager", message)  # نوع النظام
        self.assertIn("150", message)  # العدد الحالي
        
        print("✅ اختبار تنسيق رسالة الإشعار نجح")

    def test_independent_template_selection_integration(self):
        """اختبار تكامل الإشعار مع اختيار القوالب المستقلة"""
        
        # محاكاة معالجة اختيار قالب مستقل
        def mock_process_independent_template_selection(callback_data):
            # استخراج نوع النظام واسم القالب
            parts = callback_data.replace("independent_template_", "").split("_", 1)
            if len(parts) < 2:
                return False
            
            template_type = parts[0]  # um أو hs
            template_name = parts[1]
            
            # تحديد النظام
            if template_type == "um":
                system_name = "User Manager"
            elif template_type == "hs":
                system_name = "Hotspot"
            else:
                return False
            
            # محاكاة إرسال الإشعار
            return True
        
        # تنفيذ الاختبار
        result_um = mock_process_independent_template_selection("independent_template_um_قالب_يوزر_منجير")
        result_hs = mock_process_independent_template_selection("independent_template_hs_قالب_هوت_سبوت")
        
        # التحقق من النتائج
        self.assertTrue(result_um)
        self.assertTrue(result_hs)
        print("✅ اختبار تكامل الإشعار مع القوالب المستقلة نجح")

    def test_single_card_creation_integration(self):
        """اختبار تكامل الإشعار مع إنشاء الكرت الواحد"""
        
        # محاكاة معالجة إنشاء كرت واحد
        def mock_process_single_card_creation(template_name, card_count):
            # محاكاة إرسال رسالة البداية
            start_message = f"🎴 **بدء إنشاء {card_count} كرت**\n\n📋 **القالب:** {template_name}"
            
            # محاكاة إرسال الإشعار التلقائي
            notification_sent = True
            
            return start_message, notification_sent
        
        # تنفيذ الاختبار
        message, notification = mock_process_single_card_creation("قالب_هوت_سبوت", 1)
        
        # التحقق من النتائج
        self.assertIn("🎴", message)
        self.assertIn("قالب_هوت_سبوت", message)
        self.assertTrue(notification)
        print("✅ اختبار تكامل الإشعار مع إنشاء الكرت الواحد نجح")

def run_tests():
    """تشغيل جميع الاختبارات"""
    print("🧪 بدء اختبار ميزة الإشعار التلقائي عند اختيار القوالب")
    print("=" * 70)
    
    # إنشاء مجموعة الاختبارات
    test_suite = unittest.TestLoader().loadTestsFromTestCase(TestTemplateSelectionNotification)
    
    # تشغيل الاختبارات
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # عرض النتائج
    print("\n" + "=" * 70)
    if result.wasSuccessful():
        print("🎉 جميع الاختبارات نجحت!")
        print(f"✅ تم تشغيل {result.testsRun} اختبار بنجاح")
    else:
        print("❌ بعض الاختبارات فشلت!")
        print(f"❌ فشل {len(result.failures)} اختبار")
        print(f"⚠️ خطأ في {len(result.errors)} اختبار")
    
    return result.wasSuccessful()

if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1)
