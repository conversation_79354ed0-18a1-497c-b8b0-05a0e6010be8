# MikroTik Script - نسخة احتياطية للكروت
# تم الإنشاء: 2025-07-25 03:59:30
# القالب: 10
# النظام: user_manager
# عدد الكروت: 200
# تم إنشاؤه بواسطة: مولد كروت MikroTik

:put "🚀 بدء تنفيذ النسخة الاحتياطية للكروت";
:put "📋 القالب: 10";
:put "🎯 النظام: user_manager";
:put "📊 عدد الكروت: 200";

:local success 0;
:local errors 0;
:local total 200;

:put "⏳ بدء إضافة الكروت...";

# إضافة مستخدمي User Manager
:put "👤 إضافة 200 مستخدم User Manager...";

# المستخدم 1: 0118664419
:do {
    /tool user-manager user add customer="admin" username="0118664419" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0118664419";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0118664419";
};

# المستخدم 2: 0131711342
:do {
    /tool user-manager user add customer="admin" username="0131711342" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0131711342";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0131711342";
};

# المستخدم 3: 0118718233
:do {
    /tool user-manager user add customer="admin" username="0118718233" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0118718233";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0118718233";
};

# المستخدم 4: 0199682755
:do {
    /tool user-manager user add customer="admin" username="0199682755" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0199682755";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0199682755";
};

# المستخدم 5: 0153563544
:do {
    /tool user-manager user add customer="admin" username="0153563544" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0153563544";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0153563544";
};

# المستخدم 6: 0160415315
:do {
    /tool user-manager user add customer="admin" username="0160415315" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0160415315";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0160415315";
};

# المستخدم 7: 0196076462
:do {
    /tool user-manager user add customer="admin" username="0196076462" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0196076462";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0196076462";
};

# المستخدم 8: 0134449597
:do {
    /tool user-manager user add customer="admin" username="0134449597" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0134449597";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0134449597";
};

# المستخدم 9: 0160130163
:do {
    /tool user-manager user add customer="admin" username="0160130163" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0160130163";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0160130163";
};

# المستخدم 10: 0136961087
:do {
    /tool user-manager user add customer="admin" username="0136961087" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0136961087";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0136961087";
};

# المستخدم 11: 0178177081
:do {
    /tool user-manager user add customer="admin" username="0178177081" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0178177081";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0178177081";
};

# المستخدم 12: 0181564976
:do {
    /tool user-manager user add customer="admin" username="0181564976" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0181564976";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0181564976";
};

# المستخدم 13: 0106409100
:do {
    /tool user-manager user add customer="admin" username="0106409100" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0106409100";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0106409100";
};

# المستخدم 14: 0105879508
:do {
    /tool user-manager user add customer="admin" username="0105879508" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0105879508";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0105879508";
};

# المستخدم 15: 0182239087
:do {
    /tool user-manager user add customer="admin" username="0182239087" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0182239087";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0182239087";
};

# المستخدم 16: 0148022676
:do {
    /tool user-manager user add customer="admin" username="0148022676" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0148022676";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0148022676";
};

# المستخدم 17: 0111662447
:do {
    /tool user-manager user add customer="admin" username="0111662447" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0111662447";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0111662447";
};

# المستخدم 18: 0190396651
:do {
    /tool user-manager user add customer="admin" username="0190396651" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0190396651";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0190396651";
};

# المستخدم 19: 0103423197
:do {
    /tool user-manager user add customer="admin" username="0103423197" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0103423197";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0103423197";
};

# المستخدم 20: 0174347356
:do {
    /tool user-manager user add customer="admin" username="0174347356" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0174347356";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0174347356";
};

# المستخدم 21: 0104425319
:do {
    /tool user-manager user add customer="admin" username="0104425319" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0104425319";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0104425319";
};

# المستخدم 22: 0186761161
:do {
    /tool user-manager user add customer="admin" username="0186761161" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0186761161";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0186761161";
};

# المستخدم 23: 0182029347
:do {
    /tool user-manager user add customer="admin" username="0182029347" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0182029347";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0182029347";
};

# المستخدم 24: 0150783147
:do {
    /tool user-manager user add customer="admin" username="0150783147" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0150783147";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0150783147";
};

# المستخدم 25: 0146618494
:do {
    /tool user-manager user add customer="admin" username="0146618494" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0146618494";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0146618494";
};

# المستخدم 26: 0125172608
:do {
    /tool user-manager user add customer="admin" username="0125172608" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0125172608";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0125172608";
};

# المستخدم 27: 0132940347
:do {
    /tool user-manager user add customer="admin" username="0132940347" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0132940347";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0132940347";
};

# المستخدم 28: 0118018212
:do {
    /tool user-manager user add customer="admin" username="0118018212" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0118018212";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0118018212";
};

# المستخدم 29: 0168622253
:do {
    /tool user-manager user add customer="admin" username="0168622253" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0168622253";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0168622253";
};

# المستخدم 30: 0140224860
:do {
    /tool user-manager user add customer="admin" username="0140224860" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0140224860";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0140224860";
};

# المستخدم 31: 0153525188
:do {
    /tool user-manager user add customer="admin" username="0153525188" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0153525188";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0153525188";
};

# المستخدم 32: 0108493449
:do {
    /tool user-manager user add customer="admin" username="0108493449" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0108493449";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0108493449";
};

# المستخدم 33: 0172180961
:do {
    /tool user-manager user add customer="admin" username="0172180961" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0172180961";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0172180961";
};

# المستخدم 34: 0132740497
:do {
    /tool user-manager user add customer="admin" username="0132740497" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0132740497";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0132740497";
};

# المستخدم 35: 0118153363
:do {
    /tool user-manager user add customer="admin" username="0118153363" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0118153363";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0118153363";
};

# المستخدم 36: 0163762013
:do {
    /tool user-manager user add customer="admin" username="0163762013" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0163762013";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0163762013";
};

# المستخدم 37: 0177099546
:do {
    /tool user-manager user add customer="admin" username="0177099546" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0177099546";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0177099546";
};

# المستخدم 38: 0187886123
:do {
    /tool user-manager user add customer="admin" username="0187886123" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0187886123";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0187886123";
};

# المستخدم 39: 0157392355
:do {
    /tool user-manager user add customer="admin" username="0157392355" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0157392355";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0157392355";
};

# المستخدم 40: 0170079428
:do {
    /tool user-manager user add customer="admin" username="0170079428" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0170079428";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0170079428";
};

# المستخدم 41: 0135995762
:do {
    /tool user-manager user add customer="admin" username="0135995762" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0135995762";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0135995762";
};

# المستخدم 42: 0164748434
:do {
    /tool user-manager user add customer="admin" username="0164748434" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0164748434";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0164748434";
};

# المستخدم 43: 0151283098
:do {
    /tool user-manager user add customer="admin" username="0151283098" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0151283098";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0151283098";
};

# المستخدم 44: 0136387522
:do {
    /tool user-manager user add customer="admin" username="0136387522" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0136387522";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0136387522";
};

# المستخدم 45: 0146485154
:do {
    /tool user-manager user add customer="admin" username="0146485154" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0146485154";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0146485154";
};

# المستخدم 46: 0161088876
:do {
    /tool user-manager user add customer="admin" username="0161088876" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0161088876";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0161088876";
};

# المستخدم 47: 0114107180
:do {
    /tool user-manager user add customer="admin" username="0114107180" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0114107180";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0114107180";
};

# المستخدم 48: 0105181702
:do {
    /tool user-manager user add customer="admin" username="0105181702" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0105181702";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0105181702";
};

# المستخدم 49: 0166437882
:do {
    /tool user-manager user add customer="admin" username="0166437882" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0166437882";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0166437882";
};

# المستخدم 50: 0188425950
:do {
    /tool user-manager user add customer="admin" username="0188425950" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0188425950";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0188425950";
};

# المستخدم 51: 0120231410
:do {
    /tool user-manager user add customer="admin" username="0120231410" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0120231410";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0120231410";
};

# المستخدم 52: 0139239470
:do {
    /tool user-manager user add customer="admin" username="0139239470" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0139239470";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0139239470";
};

# المستخدم 53: 0117220929
:do {
    /tool user-manager user add customer="admin" username="0117220929" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0117220929";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0117220929";
};

# المستخدم 54: 0180291967
:do {
    /tool user-manager user add customer="admin" username="0180291967" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0180291967";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0180291967";
};

# المستخدم 55: 0137629303
:do {
    /tool user-manager user add customer="admin" username="0137629303" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0137629303";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0137629303";
};

# المستخدم 56: 0158224448
:do {
    /tool user-manager user add customer="admin" username="0158224448" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0158224448";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0158224448";
};

# المستخدم 57: 0178616982
:do {
    /tool user-manager user add customer="admin" username="0178616982" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0178616982";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0178616982";
};

# المستخدم 58: 0118477141
:do {
    /tool user-manager user add customer="admin" username="0118477141" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0118477141";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0118477141";
};

# المستخدم 59: 0195036336
:do {
    /tool user-manager user add customer="admin" username="0195036336" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0195036336";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0195036336";
};

# المستخدم 60: 0186051342
:do {
    /tool user-manager user add customer="admin" username="0186051342" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0186051342";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0186051342";
};

# المستخدم 61: 0108874253
:do {
    /tool user-manager user add customer="admin" username="0108874253" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0108874253";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0108874253";
};

# المستخدم 62: 0155344178
:do {
    /tool user-manager user add customer="admin" username="0155344178" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0155344178";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0155344178";
};

# المستخدم 63: 0116566026
:do {
    /tool user-manager user add customer="admin" username="0116566026" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0116566026";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0116566026";
};

# المستخدم 64: 0166138652
:do {
    /tool user-manager user add customer="admin" username="0166138652" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0166138652";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0166138652";
};

# المستخدم 65: 0176756961
:do {
    /tool user-manager user add customer="admin" username="0176756961" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0176756961";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0176756961";
};

# المستخدم 66: 0104121418
:do {
    /tool user-manager user add customer="admin" username="0104121418" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0104121418";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0104121418";
};

# المستخدم 67: 0122962227
:do {
    /tool user-manager user add customer="admin" username="0122962227" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0122962227";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0122962227";
};

# المستخدم 68: 0184619778
:do {
    /tool user-manager user add customer="admin" username="0184619778" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0184619778";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0184619778";
};

# المستخدم 69: 0130448557
:do {
    /tool user-manager user add customer="admin" username="0130448557" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0130448557";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0130448557";
};

# المستخدم 70: 0122168292
:do {
    /tool user-manager user add customer="admin" username="0122168292" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0122168292";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0122168292";
};

# المستخدم 71: 0171264315
:do {
    /tool user-manager user add customer="admin" username="0171264315" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0171264315";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0171264315";
};

# المستخدم 72: 0116540581
:do {
    /tool user-manager user add customer="admin" username="0116540581" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0116540581";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0116540581";
};

# المستخدم 73: 0188950598
:do {
    /tool user-manager user add customer="admin" username="0188950598" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0188950598";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0188950598";
};

# المستخدم 74: 0196623798
:do {
    /tool user-manager user add customer="admin" username="0196623798" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0196623798";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0196623798";
};

# المستخدم 75: 0123492293
:do {
    /tool user-manager user add customer="admin" username="0123492293" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0123492293";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0123492293";
};

# المستخدم 76: 0138349174
:do {
    /tool user-manager user add customer="admin" username="0138349174" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0138349174";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0138349174";
};

# المستخدم 77: 0132728208
:do {
    /tool user-manager user add customer="admin" username="0132728208" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0132728208";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0132728208";
};

# المستخدم 78: 0182533600
:do {
    /tool user-manager user add customer="admin" username="0182533600" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0182533600";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0182533600";
};

# المستخدم 79: 0152126639
:do {
    /tool user-manager user add customer="admin" username="0152126639" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0152126639";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0152126639";
};

# المستخدم 80: 0128005043
:do {
    /tool user-manager user add customer="admin" username="0128005043" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0128005043";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0128005043";
};

# المستخدم 81: 0109342324
:do {
    /tool user-manager user add customer="admin" username="0109342324" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0109342324";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0109342324";
};

# المستخدم 82: 0169609077
:do {
    /tool user-manager user add customer="admin" username="0169609077" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0169609077";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0169609077";
};

# المستخدم 83: 0183012625
:do {
    /tool user-manager user add customer="admin" username="0183012625" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0183012625";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0183012625";
};

# المستخدم 84: 0163629529
:do {
    /tool user-manager user add customer="admin" username="0163629529" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0163629529";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0163629529";
};

# المستخدم 85: 0160884972
:do {
    /tool user-manager user add customer="admin" username="0160884972" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0160884972";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0160884972";
};

# المستخدم 86: 0161014373
:do {
    /tool user-manager user add customer="admin" username="0161014373" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0161014373";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0161014373";
};

# المستخدم 87: 0168590073
:do {
    /tool user-manager user add customer="admin" username="0168590073" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0168590073";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0168590073";
};

# المستخدم 88: 0111665565
:do {
    /tool user-manager user add customer="admin" username="0111665565" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0111665565";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0111665565";
};

# المستخدم 89: 0130433009
:do {
    /tool user-manager user add customer="admin" username="0130433009" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0130433009";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0130433009";
};

# المستخدم 90: 0147216797
:do {
    /tool user-manager user add customer="admin" username="0147216797" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0147216797";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0147216797";
};

# المستخدم 91: 0130279947
:do {
    /tool user-manager user add customer="admin" username="0130279947" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0130279947";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0130279947";
};

# المستخدم 92: 0164900322
:do {
    /tool user-manager user add customer="admin" username="0164900322" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0164900322";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0164900322";
};

# المستخدم 93: 0167556587
:do {
    /tool user-manager user add customer="admin" username="0167556587" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0167556587";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0167556587";
};

# المستخدم 94: 0100252179
:do {
    /tool user-manager user add customer="admin" username="0100252179" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0100252179";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0100252179";
};

# المستخدم 95: 0177410478
:do {
    /tool user-manager user add customer="admin" username="0177410478" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0177410478";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0177410478";
};

# المستخدم 96: 0159845895
:do {
    /tool user-manager user add customer="admin" username="0159845895" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0159845895";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0159845895";
};

# المستخدم 97: 0149348840
:do {
    /tool user-manager user add customer="admin" username="0149348840" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0149348840";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0149348840";
};

# المستخدم 98: 0178119821
:do {
    /tool user-manager user add customer="admin" username="0178119821" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0178119821";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0178119821";
};

# المستخدم 99: 0199902359
:do {
    /tool user-manager user add customer="admin" username="0199902359" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0199902359";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0199902359";
};

# المستخدم 100: 0195035214
:do {
    /tool user-manager user add customer="admin" username="0195035214" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0195035214";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0195035214";
};

# المستخدم 101: 0157206339
:do {
    /tool user-manager user add customer="admin" username="0157206339" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0157206339";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0157206339";
};

# المستخدم 102: 0103236042
:do {
    /tool user-manager user add customer="admin" username="0103236042" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0103236042";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0103236042";
};

# المستخدم 103: 0198190871
:do {
    /tool user-manager user add customer="admin" username="0198190871" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0198190871";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0198190871";
};

# المستخدم 104: 0116953558
:do {
    /tool user-manager user add customer="admin" username="0116953558" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0116953558";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0116953558";
};

# المستخدم 105: 0179158139
:do {
    /tool user-manager user add customer="admin" username="0179158139" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0179158139";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0179158139";
};

# المستخدم 106: 0188260147
:do {
    /tool user-manager user add customer="admin" username="0188260147" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0188260147";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0188260147";
};

# المستخدم 107: 0167701049
:do {
    /tool user-manager user add customer="admin" username="0167701049" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0167701049";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0167701049";
};

# المستخدم 108: 0147022585
:do {
    /tool user-manager user add customer="admin" username="0147022585" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0147022585";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0147022585";
};

# المستخدم 109: 0131231178
:do {
    /tool user-manager user add customer="admin" username="0131231178" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0131231178";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0131231178";
};

# المستخدم 110: 0129075640
:do {
    /tool user-manager user add customer="admin" username="0129075640" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0129075640";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0129075640";
};

# المستخدم 111: 0143415362
:do {
    /tool user-manager user add customer="admin" username="0143415362" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0143415362";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0143415362";
};

# المستخدم 112: 0177486219
:do {
    /tool user-manager user add customer="admin" username="0177486219" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0177486219";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0177486219";
};

# المستخدم 113: 0162200260
:do {
    /tool user-manager user add customer="admin" username="0162200260" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0162200260";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0162200260";
};

# المستخدم 114: 0135513173
:do {
    /tool user-manager user add customer="admin" username="0135513173" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0135513173";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0135513173";
};

# المستخدم 115: 0146687829
:do {
    /tool user-manager user add customer="admin" username="0146687829" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0146687829";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0146687829";
};

# المستخدم 116: 0124473981
:do {
    /tool user-manager user add customer="admin" username="0124473981" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0124473981";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0124473981";
};

# المستخدم 117: 0105423425
:do {
    /tool user-manager user add customer="admin" username="0105423425" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0105423425";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0105423425";
};

# المستخدم 118: 0187229231
:do {
    /tool user-manager user add customer="admin" username="0187229231" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0187229231";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0187229231";
};

# المستخدم 119: 0159814783
:do {
    /tool user-manager user add customer="admin" username="0159814783" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0159814783";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0159814783";
};

# المستخدم 120: 0129595755
:do {
    /tool user-manager user add customer="admin" username="0129595755" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0129595755";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0129595755";
};

# المستخدم 121: 0102746349
:do {
    /tool user-manager user add customer="admin" username="0102746349" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0102746349";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0102746349";
};

# المستخدم 122: 0183817231
:do {
    /tool user-manager user add customer="admin" username="0183817231" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0183817231";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0183817231";
};

# المستخدم 123: 0100948828
:do {
    /tool user-manager user add customer="admin" username="0100948828" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0100948828";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0100948828";
};

# المستخدم 124: 0105276811
:do {
    /tool user-manager user add customer="admin" username="0105276811" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0105276811";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0105276811";
};

# المستخدم 125: 0110333798
:do {
    /tool user-manager user add customer="admin" username="0110333798" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0110333798";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0110333798";
};

# المستخدم 126: 0176542900
:do {
    /tool user-manager user add customer="admin" username="0176542900" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0176542900";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0176542900";
};

# المستخدم 127: 0195651169
:do {
    /tool user-manager user add customer="admin" username="0195651169" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0195651169";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0195651169";
};

# المستخدم 128: 0104177528
:do {
    /tool user-manager user add customer="admin" username="0104177528" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0104177528";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0104177528";
};

# المستخدم 129: 0176117372
:do {
    /tool user-manager user add customer="admin" username="0176117372" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0176117372";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0176117372";
};

# المستخدم 130: 0155711612
:do {
    /tool user-manager user add customer="admin" username="0155711612" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0155711612";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0155711612";
};

# المستخدم 131: 0101614957
:do {
    /tool user-manager user add customer="admin" username="0101614957" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0101614957";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0101614957";
};

# المستخدم 132: 0157239081
:do {
    /tool user-manager user add customer="admin" username="0157239081" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0157239081";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0157239081";
};

# المستخدم 133: 0104583599
:do {
    /tool user-manager user add customer="admin" username="0104583599" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0104583599";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0104583599";
};

# المستخدم 134: 0184400642
:do {
    /tool user-manager user add customer="admin" username="0184400642" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0184400642";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0184400642";
};

# المستخدم 135: 0191800933
:do {
    /tool user-manager user add customer="admin" username="0191800933" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0191800933";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0191800933";
};

# المستخدم 136: 0147848817
:do {
    /tool user-manager user add customer="admin" username="0147848817" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0147848817";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0147848817";
};

# المستخدم 137: 0113583335
:do {
    /tool user-manager user add customer="admin" username="0113583335" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0113583335";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0113583335";
};

# المستخدم 138: 0127806287
:do {
    /tool user-manager user add customer="admin" username="0127806287" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0127806287";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0127806287";
};

# المستخدم 139: 0108628634
:do {
    /tool user-manager user add customer="admin" username="0108628634" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0108628634";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0108628634";
};

# المستخدم 140: 0196407321
:do {
    /tool user-manager user add customer="admin" username="0196407321" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0196407321";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0196407321";
};

# المستخدم 141: 0168899071
:do {
    /tool user-manager user add customer="admin" username="0168899071" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0168899071";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0168899071";
};

# المستخدم 142: 0149003816
:do {
    /tool user-manager user add customer="admin" username="0149003816" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0149003816";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0149003816";
};

# المستخدم 143: 0138993921
:do {
    /tool user-manager user add customer="admin" username="0138993921" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0138993921";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0138993921";
};

# المستخدم 144: 0110171750
:do {
    /tool user-manager user add customer="admin" username="0110171750" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0110171750";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0110171750";
};

# المستخدم 145: 0136408490
:do {
    /tool user-manager user add customer="admin" username="0136408490" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0136408490";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0136408490";
};

# المستخدم 146: 0146082108
:do {
    /tool user-manager user add customer="admin" username="0146082108" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0146082108";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0146082108";
};

# المستخدم 147: 0187942848
:do {
    /tool user-manager user add customer="admin" username="0187942848" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0187942848";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0187942848";
};

# المستخدم 148: 0199874749
:do {
    /tool user-manager user add customer="admin" username="0199874749" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0199874749";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0199874749";
};

# المستخدم 149: 0148773218
:do {
    /tool user-manager user add customer="admin" username="0148773218" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0148773218";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0148773218";
};

# المستخدم 150: 0125471149
:do {
    /tool user-manager user add customer="admin" username="0125471149" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0125471149";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0125471149";
};

# المستخدم 151: 0185008668
:do {
    /tool user-manager user add customer="admin" username="0185008668" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0185008668";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0185008668";
};

# المستخدم 152: 0101621440
:do {
    /tool user-manager user add customer="admin" username="0101621440" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0101621440";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0101621440";
};

# المستخدم 153: 0151536553
:do {
    /tool user-manager user add customer="admin" username="0151536553" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0151536553";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0151536553";
};

# المستخدم 154: 0141594353
:do {
    /tool user-manager user add customer="admin" username="0141594353" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0141594353";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0141594353";
};

# المستخدم 155: 0162532745
:do {
    /tool user-manager user add customer="admin" username="0162532745" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0162532745";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0162532745";
};

# المستخدم 156: 0148991543
:do {
    /tool user-manager user add customer="admin" username="0148991543" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0148991543";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0148991543";
};

# المستخدم 157: 0116243858
:do {
    /tool user-manager user add customer="admin" username="0116243858" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0116243858";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0116243858";
};

# المستخدم 158: 0195993342
:do {
    /tool user-manager user add customer="admin" username="0195993342" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0195993342";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0195993342";
};

# المستخدم 159: 0170916500
:do {
    /tool user-manager user add customer="admin" username="0170916500" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0170916500";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0170916500";
};

# المستخدم 160: 0178580629
:do {
    /tool user-manager user add customer="admin" username="0178580629" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0178580629";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0178580629";
};

# المستخدم 161: 0132111487
:do {
    /tool user-manager user add customer="admin" username="0132111487" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0132111487";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0132111487";
};

# المستخدم 162: 0115212468
:do {
    /tool user-manager user add customer="admin" username="0115212468" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0115212468";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0115212468";
};

# المستخدم 163: 0125878448
:do {
    /tool user-manager user add customer="admin" username="0125878448" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0125878448";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0125878448";
};

# المستخدم 164: 0188456756
:do {
    /tool user-manager user add customer="admin" username="0188456756" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0188456756";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0188456756";
};

# المستخدم 165: 0110919094
:do {
    /tool user-manager user add customer="admin" username="0110919094" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0110919094";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0110919094";
};

# المستخدم 166: 0125666775
:do {
    /tool user-manager user add customer="admin" username="0125666775" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0125666775";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0125666775";
};

# المستخدم 167: 0182713535
:do {
    /tool user-manager user add customer="admin" username="0182713535" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0182713535";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0182713535";
};

# المستخدم 168: 0151856669
:do {
    /tool user-manager user add customer="admin" username="0151856669" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0151856669";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0151856669";
};

# المستخدم 169: 0198947048
:do {
    /tool user-manager user add customer="admin" username="0198947048" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0198947048";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0198947048";
};

# المستخدم 170: 0145956465
:do {
    /tool user-manager user add customer="admin" username="0145956465" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0145956465";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0145956465";
};

# المستخدم 171: 0192878966
:do {
    /tool user-manager user add customer="admin" username="0192878966" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0192878966";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0192878966";
};

# المستخدم 172: 0126173844
:do {
    /tool user-manager user add customer="admin" username="0126173844" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0126173844";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0126173844";
};

# المستخدم 173: 0123387610
:do {
    /tool user-manager user add customer="admin" username="0123387610" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0123387610";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0123387610";
};

# المستخدم 174: 0180287331
:do {
    /tool user-manager user add customer="admin" username="0180287331" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0180287331";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0180287331";
};

# المستخدم 175: 0119054373
:do {
    /tool user-manager user add customer="admin" username="0119054373" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0119054373";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0119054373";
};

# المستخدم 176: 0146551680
:do {
    /tool user-manager user add customer="admin" username="0146551680" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0146551680";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0146551680";
};

# المستخدم 177: 0123614596
:do {
    /tool user-manager user add customer="admin" username="0123614596" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0123614596";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0123614596";
};

# المستخدم 178: 0145018969
:do {
    /tool user-manager user add customer="admin" username="0145018969" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0145018969";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0145018969";
};

# المستخدم 179: 0188141153
:do {
    /tool user-manager user add customer="admin" username="0188141153" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0188141153";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0188141153";
};

# المستخدم 180: 0183137236
:do {
    /tool user-manager user add customer="admin" username="0183137236" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0183137236";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0183137236";
};

# المستخدم 181: 0158306898
:do {
    /tool user-manager user add customer="admin" username="0158306898" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0158306898";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0158306898";
};

# المستخدم 182: 0113418890
:do {
    /tool user-manager user add customer="admin" username="0113418890" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0113418890";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0113418890";
};

# المستخدم 183: 0118098332
:do {
    /tool user-manager user add customer="admin" username="0118098332" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0118098332";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0118098332";
};

# المستخدم 184: 0105059138
:do {
    /tool user-manager user add customer="admin" username="0105059138" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0105059138";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0105059138";
};

# المستخدم 185: 0196389481
:do {
    /tool user-manager user add customer="admin" username="0196389481" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0196389481";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0196389481";
};

# المستخدم 186: 0162389940
:do {
    /tool user-manager user add customer="admin" username="0162389940" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0162389940";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0162389940";
};

# المستخدم 187: 0158697942
:do {
    /tool user-manager user add customer="admin" username="0158697942" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0158697942";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0158697942";
};

# المستخدم 188: 0156395037
:do {
    /tool user-manager user add customer="admin" username="0156395037" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0156395037";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0156395037";
};

# المستخدم 189: 0106215739
:do {
    /tool user-manager user add customer="admin" username="0106215739" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0106215739";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0106215739";
};

# المستخدم 190: 0173720606
:do {
    /tool user-manager user add customer="admin" username="0173720606" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0173720606";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0173720606";
};

# المستخدم 191: 0125271474
:do {
    /tool user-manager user add customer="admin" username="0125271474" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0125271474";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0125271474";
};

# المستخدم 192: 0109886211
:do {
    /tool user-manager user add customer="admin" username="0109886211" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0109886211";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0109886211";
};

# المستخدم 193: 0105172151
:do {
    /tool user-manager user add customer="admin" username="0105172151" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0105172151";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0105172151";
};

# المستخدم 194: 0172054034
:do {
    /tool user-manager user add customer="admin" username="0172054034" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0172054034";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0172054034";
};

# المستخدم 195: 0134987486
:do {
    /tool user-manager user add customer="admin" username="0134987486" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0134987486";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0134987486";
};

# المستخدم 196: 0170216130
:do {
    /tool user-manager user add customer="admin" username="0170216130" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0170216130";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0170216130";
};

# المستخدم 197: 0108547576
:do {
    /tool user-manager user add customer="admin" username="0108547576" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0108547576";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0108547576";
};

# المستخدم 198: 0138040786
:do {
    /tool user-manager user add customer="admin" username="0138040786" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0138040786";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0138040786";
};

# المستخدم 199: 0185953568
:do {
    /tool user-manager user add customer="admin" username="0185953568" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0185953568";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0185953568";
};

# المستخدم 200: 0103133579
:do {
    /tool user-manager user add customer="admin" username="0103133579" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0103133579";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0103133579";
};


:put "📊 تم الانتهاء من النسخة الاحتياطية";
:put "✅ نجح: $success كرت";
:put "❌ فشل: $errors كرت";
:put "📈 الإجمالي: $total كرت";

:put "🎉 تم استعادة جميع كروت القالب: 10";
:put "💡 النسخة الاحتياطية مكتملة بنجاح!";
