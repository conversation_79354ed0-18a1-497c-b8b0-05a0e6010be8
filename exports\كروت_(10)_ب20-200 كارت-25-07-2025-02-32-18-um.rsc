# MikroTik Script - نسخة احتياطية للكروت
# تم الإنشاء: 2025-07-25 02:32:18
# القالب: 10
# النظام: user_manager
# عدد الكروت: 200
# تم إنشاؤه بواسطة: مولد كروت MikroTik

:put "🚀 بدء تنفيذ النسخة الاحتياطية للكروت";
:put "📋 القالب: 10";
:put "🎯 النظام: user_manager";
:put "📊 عدد الكروت: 200";

:local success 0;
:local errors 0;
:local total 200;

:put "⏳ بدء إضافة الكروت...";

# إضافة مستخدمي User Manager
:put "👤 إضافة 200 مستخدم User Manager...";

# المستخدم 1: 0163963081
:do {
    /tool user-manager user add customer="admin" username="0163963081" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0163963081";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0163963081";
};

# المستخدم 2: 0194239247
:do {
    /tool user-manager user add customer="admin" username="0194239247" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0194239247";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0194239247";
};

# المستخدم 3: 0120249653
:do {
    /tool user-manager user add customer="admin" username="0120249653" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0120249653";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0120249653";
};

# المستخدم 4: 0139942021
:do {
    /tool user-manager user add customer="admin" username="0139942021" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0139942021";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0139942021";
};

# المستخدم 5: 0131109975
:do {
    /tool user-manager user add customer="admin" username="0131109975" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0131109975";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0131109975";
};

# المستخدم 6: 0172119826
:do {
    /tool user-manager user add customer="admin" username="0172119826" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0172119826";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0172119826";
};

# المستخدم 7: 0162233316
:do {
    /tool user-manager user add customer="admin" username="0162233316" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0162233316";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0162233316";
};

# المستخدم 8: 0158024739
:do {
    /tool user-manager user add customer="admin" username="0158024739" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0158024739";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0158024739";
};

# المستخدم 9: 0176875463
:do {
    /tool user-manager user add customer="admin" username="0176875463" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0176875463";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0176875463";
};

# المستخدم 10: 0164531887
:do {
    /tool user-manager user add customer="admin" username="0164531887" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0164531887";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0164531887";
};

# المستخدم 11: 0122271406
:do {
    /tool user-manager user add customer="admin" username="0122271406" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0122271406";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0122271406";
};

# المستخدم 12: 0155543430
:do {
    /tool user-manager user add customer="admin" username="0155543430" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0155543430";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0155543430";
};

# المستخدم 13: 0151485980
:do {
    /tool user-manager user add customer="admin" username="0151485980" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0151485980";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0151485980";
};

# المستخدم 14: 0125973278
:do {
    /tool user-manager user add customer="admin" username="0125973278" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0125973278";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0125973278";
};

# المستخدم 15: 0173284116
:do {
    /tool user-manager user add customer="admin" username="0173284116" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0173284116";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0173284116";
};

# المستخدم 16: 0117492255
:do {
    /tool user-manager user add customer="admin" username="0117492255" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0117492255";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0117492255";
};

# المستخدم 17: 0103452159
:do {
    /tool user-manager user add customer="admin" username="0103452159" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0103452159";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0103452159";
};

# المستخدم 18: 0154437006
:do {
    /tool user-manager user add customer="admin" username="0154437006" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0154437006";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0154437006";
};

# المستخدم 19: 0161423398
:do {
    /tool user-manager user add customer="admin" username="0161423398" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0161423398";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0161423398";
};

# المستخدم 20: 0142350055
:do {
    /tool user-manager user add customer="admin" username="0142350055" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0142350055";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0142350055";
};

# المستخدم 21: 0159789285
:do {
    /tool user-manager user add customer="admin" username="0159789285" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0159789285";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0159789285";
};

# المستخدم 22: 0108632531
:do {
    /tool user-manager user add customer="admin" username="0108632531" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0108632531";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0108632531";
};

# المستخدم 23: 0129801947
:do {
    /tool user-manager user add customer="admin" username="0129801947" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0129801947";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0129801947";
};

# المستخدم 24: 0196516671
:do {
    /tool user-manager user add customer="admin" username="0196516671" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0196516671";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0196516671";
};

# المستخدم 25: 0100711021
:do {
    /tool user-manager user add customer="admin" username="0100711021" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0100711021";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0100711021";
};

# المستخدم 26: 0171487025
:do {
    /tool user-manager user add customer="admin" username="0171487025" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0171487025";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0171487025";
};

# المستخدم 27: 0183864195
:do {
    /tool user-manager user add customer="admin" username="0183864195" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0183864195";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0183864195";
};

# المستخدم 28: 0188605393
:do {
    /tool user-manager user add customer="admin" username="0188605393" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0188605393";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0188605393";
};

# المستخدم 29: 0121981430
:do {
    /tool user-manager user add customer="admin" username="0121981430" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0121981430";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0121981430";
};

# المستخدم 30: 0160129386
:do {
    /tool user-manager user add customer="admin" username="0160129386" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0160129386";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0160129386";
};

# المستخدم 31: 0102554203
:do {
    /tool user-manager user add customer="admin" username="0102554203" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0102554203";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0102554203";
};

# المستخدم 32: 0163520356
:do {
    /tool user-manager user add customer="admin" username="0163520356" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0163520356";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0163520356";
};

# المستخدم 33: 0195800094
:do {
    /tool user-manager user add customer="admin" username="0195800094" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0195800094";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0195800094";
};

# المستخدم 34: 0130859389
:do {
    /tool user-manager user add customer="admin" username="0130859389" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0130859389";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0130859389";
};

# المستخدم 35: 0113171891
:do {
    /tool user-manager user add customer="admin" username="0113171891" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0113171891";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0113171891";
};

# المستخدم 36: 0111825245
:do {
    /tool user-manager user add customer="admin" username="0111825245" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0111825245";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0111825245";
};

# المستخدم 37: 0139222733
:do {
    /tool user-manager user add customer="admin" username="0139222733" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0139222733";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0139222733";
};

# المستخدم 38: 0178035251
:do {
    /tool user-manager user add customer="admin" username="0178035251" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0178035251";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0178035251";
};

# المستخدم 39: 0140734311
:do {
    /tool user-manager user add customer="admin" username="0140734311" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0140734311";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0140734311";
};

# المستخدم 40: 0199401406
:do {
    /tool user-manager user add customer="admin" username="0199401406" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0199401406";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0199401406";
};

# المستخدم 41: 0117966948
:do {
    /tool user-manager user add customer="admin" username="0117966948" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0117966948";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0117966948";
};

# المستخدم 42: 0193413080
:do {
    /tool user-manager user add customer="admin" username="0193413080" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0193413080";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0193413080";
};

# المستخدم 43: 0167743693
:do {
    /tool user-manager user add customer="admin" username="0167743693" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0167743693";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0167743693";
};

# المستخدم 44: 0177683794
:do {
    /tool user-manager user add customer="admin" username="0177683794" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0177683794";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0177683794";
};

# المستخدم 45: 0137362173
:do {
    /tool user-manager user add customer="admin" username="0137362173" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0137362173";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0137362173";
};

# المستخدم 46: 0185947456
:do {
    /tool user-manager user add customer="admin" username="0185947456" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0185947456";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0185947456";
};

# المستخدم 47: 0185212976
:do {
    /tool user-manager user add customer="admin" username="0185212976" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0185212976";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0185212976";
};

# المستخدم 48: 0197775493
:do {
    /tool user-manager user add customer="admin" username="0197775493" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0197775493";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0197775493";
};

# المستخدم 49: 0144921917
:do {
    /tool user-manager user add customer="admin" username="0144921917" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0144921917";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0144921917";
};

# المستخدم 50: 0126853086
:do {
    /tool user-manager user add customer="admin" username="0126853086" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0126853086";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0126853086";
};

# المستخدم 51: 0164467549
:do {
    /tool user-manager user add customer="admin" username="0164467549" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0164467549";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0164467549";
};

# المستخدم 52: 0180371080
:do {
    /tool user-manager user add customer="admin" username="0180371080" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0180371080";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0180371080";
};

# المستخدم 53: 0107679006
:do {
    /tool user-manager user add customer="admin" username="0107679006" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0107679006";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0107679006";
};

# المستخدم 54: 0178889872
:do {
    /tool user-manager user add customer="admin" username="0178889872" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0178889872";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0178889872";
};

# المستخدم 55: 0115738879
:do {
    /tool user-manager user add customer="admin" username="0115738879" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0115738879";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0115738879";
};

# المستخدم 56: 0141879526
:do {
    /tool user-manager user add customer="admin" username="0141879526" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0141879526";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0141879526";
};

# المستخدم 57: 0153652242
:do {
    /tool user-manager user add customer="admin" username="0153652242" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0153652242";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0153652242";
};

# المستخدم 58: 0120372702
:do {
    /tool user-manager user add customer="admin" username="0120372702" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0120372702";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0120372702";
};

# المستخدم 59: 0157317183
:do {
    /tool user-manager user add customer="admin" username="0157317183" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0157317183";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0157317183";
};

# المستخدم 60: 0171428767
:do {
    /tool user-manager user add customer="admin" username="0171428767" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0171428767";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0171428767";
};

# المستخدم 61: 0146041917
:do {
    /tool user-manager user add customer="admin" username="0146041917" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0146041917";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0146041917";
};

# المستخدم 62: 0183783419
:do {
    /tool user-manager user add customer="admin" username="0183783419" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0183783419";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0183783419";
};

# المستخدم 63: 0172023340
:do {
    /tool user-manager user add customer="admin" username="0172023340" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0172023340";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0172023340";
};

# المستخدم 64: 0108643428
:do {
    /tool user-manager user add customer="admin" username="0108643428" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0108643428";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0108643428";
};

# المستخدم 65: 0184304376
:do {
    /tool user-manager user add customer="admin" username="0184304376" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0184304376";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0184304376";
};

# المستخدم 66: 0141069276
:do {
    /tool user-manager user add customer="admin" username="0141069276" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0141069276";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0141069276";
};

# المستخدم 67: 0133449880
:do {
    /tool user-manager user add customer="admin" username="0133449880" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0133449880";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0133449880";
};

# المستخدم 68: 0148597833
:do {
    /tool user-manager user add customer="admin" username="0148597833" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0148597833";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0148597833";
};

# المستخدم 69: 0178097377
:do {
    /tool user-manager user add customer="admin" username="0178097377" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0178097377";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0178097377";
};

# المستخدم 70: 0170918529
:do {
    /tool user-manager user add customer="admin" username="0170918529" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0170918529";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0170918529";
};

# المستخدم 71: 0191837135
:do {
    /tool user-manager user add customer="admin" username="0191837135" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0191837135";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0191837135";
};

# المستخدم 72: 0116170368
:do {
    /tool user-manager user add customer="admin" username="0116170368" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0116170368";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0116170368";
};

# المستخدم 73: 0129531763
:do {
    /tool user-manager user add customer="admin" username="0129531763" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0129531763";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0129531763";
};

# المستخدم 74: 0125247966
:do {
    /tool user-manager user add customer="admin" username="0125247966" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0125247966";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0125247966";
};

# المستخدم 75: 0143545859
:do {
    /tool user-manager user add customer="admin" username="0143545859" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0143545859";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0143545859";
};

# المستخدم 76: 0149612336
:do {
    /tool user-manager user add customer="admin" username="0149612336" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0149612336";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0149612336";
};

# المستخدم 77: 0100874265
:do {
    /tool user-manager user add customer="admin" username="0100874265" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0100874265";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0100874265";
};

# المستخدم 78: 0161605716
:do {
    /tool user-manager user add customer="admin" username="0161605716" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0161605716";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0161605716";
};

# المستخدم 79: 0154989987
:do {
    /tool user-manager user add customer="admin" username="0154989987" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0154989987";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0154989987";
};

# المستخدم 80: 0104168944
:do {
    /tool user-manager user add customer="admin" username="0104168944" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0104168944";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0104168944";
};

# المستخدم 81: 0108679555
:do {
    /tool user-manager user add customer="admin" username="0108679555" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0108679555";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0108679555";
};

# المستخدم 82: 0106806078
:do {
    /tool user-manager user add customer="admin" username="0106806078" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0106806078";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0106806078";
};

# المستخدم 83: 0134379406
:do {
    /tool user-manager user add customer="admin" username="0134379406" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0134379406";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0134379406";
};

# المستخدم 84: 0121897135
:do {
    /tool user-manager user add customer="admin" username="0121897135" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0121897135";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0121897135";
};

# المستخدم 85: 0177181203
:do {
    /tool user-manager user add customer="admin" username="0177181203" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0177181203";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0177181203";
};

# المستخدم 86: 0175918414
:do {
    /tool user-manager user add customer="admin" username="0175918414" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0175918414";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0175918414";
};

# المستخدم 87: 0181193524
:do {
    /tool user-manager user add customer="admin" username="0181193524" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0181193524";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0181193524";
};

# المستخدم 88: 0143248261
:do {
    /tool user-manager user add customer="admin" username="0143248261" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0143248261";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0143248261";
};

# المستخدم 89: 0104579593
:do {
    /tool user-manager user add customer="admin" username="0104579593" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0104579593";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0104579593";
};

# المستخدم 90: 0154546208
:do {
    /tool user-manager user add customer="admin" username="0154546208" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0154546208";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0154546208";
};

# المستخدم 91: 0177005564
:do {
    /tool user-manager user add customer="admin" username="0177005564" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0177005564";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0177005564";
};

# المستخدم 92: 0186428900
:do {
    /tool user-manager user add customer="admin" username="0186428900" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0186428900";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0186428900";
};

# المستخدم 93: 0181380894
:do {
    /tool user-manager user add customer="admin" username="0181380894" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0181380894";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0181380894";
};

# المستخدم 94: 0132535394
:do {
    /tool user-manager user add customer="admin" username="0132535394" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0132535394";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0132535394";
};

# المستخدم 95: 0126677200
:do {
    /tool user-manager user add customer="admin" username="0126677200" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0126677200";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0126677200";
};

# المستخدم 96: 0134363686
:do {
    /tool user-manager user add customer="admin" username="0134363686" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0134363686";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0134363686";
};

# المستخدم 97: 0192973750
:do {
    /tool user-manager user add customer="admin" username="0192973750" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0192973750";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0192973750";
};

# المستخدم 98: 0178249083
:do {
    /tool user-manager user add customer="admin" username="0178249083" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0178249083";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0178249083";
};

# المستخدم 99: 0123528771
:do {
    /tool user-manager user add customer="admin" username="0123528771" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0123528771";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0123528771";
};

# المستخدم 100: 0103437736
:do {
    /tool user-manager user add customer="admin" username="0103437736" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0103437736";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0103437736";
};

# المستخدم 101: 0173669173
:do {
    /tool user-manager user add customer="admin" username="0173669173" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0173669173";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0173669173";
};

# المستخدم 102: 0105375108
:do {
    /tool user-manager user add customer="admin" username="0105375108" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0105375108";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0105375108";
};

# المستخدم 103: 0185920518
:do {
    /tool user-manager user add customer="admin" username="0185920518" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0185920518";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0185920518";
};

# المستخدم 104: 0111073893
:do {
    /tool user-manager user add customer="admin" username="0111073893" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0111073893";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0111073893";
};

# المستخدم 105: 0145174549
:do {
    /tool user-manager user add customer="admin" username="0145174549" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0145174549";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0145174549";
};

# المستخدم 106: 0173409832
:do {
    /tool user-manager user add customer="admin" username="0173409832" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0173409832";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0173409832";
};

# المستخدم 107: 0118862406
:do {
    /tool user-manager user add customer="admin" username="0118862406" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0118862406";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0118862406";
};

# المستخدم 108: 0147844303
:do {
    /tool user-manager user add customer="admin" username="0147844303" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0147844303";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0147844303";
};

# المستخدم 109: 0180659405
:do {
    /tool user-manager user add customer="admin" username="0180659405" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0180659405";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0180659405";
};

# المستخدم 110: 0144074592
:do {
    /tool user-manager user add customer="admin" username="0144074592" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0144074592";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0144074592";
};

# المستخدم 111: 0184449981
:do {
    /tool user-manager user add customer="admin" username="0184449981" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0184449981";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0184449981";
};

# المستخدم 112: 0145567492
:do {
    /tool user-manager user add customer="admin" username="0145567492" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0145567492";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0145567492";
};

# المستخدم 113: 0139586610
:do {
    /tool user-manager user add customer="admin" username="0139586610" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0139586610";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0139586610";
};

# المستخدم 114: 0133284710
:do {
    /tool user-manager user add customer="admin" username="0133284710" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0133284710";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0133284710";
};

# المستخدم 115: 0102043128
:do {
    /tool user-manager user add customer="admin" username="0102043128" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0102043128";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0102043128";
};

# المستخدم 116: 0165265910
:do {
    /tool user-manager user add customer="admin" username="0165265910" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0165265910";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0165265910";
};

# المستخدم 117: 0196109192
:do {
    /tool user-manager user add customer="admin" username="0196109192" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0196109192";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0196109192";
};

# المستخدم 118: 0141662353
:do {
    /tool user-manager user add customer="admin" username="0141662353" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0141662353";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0141662353";
};

# المستخدم 119: 0175014016
:do {
    /tool user-manager user add customer="admin" username="0175014016" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0175014016";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0175014016";
};

# المستخدم 120: 0119144857
:do {
    /tool user-manager user add customer="admin" username="0119144857" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0119144857";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0119144857";
};

# المستخدم 121: 0134713418
:do {
    /tool user-manager user add customer="admin" username="0134713418" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0134713418";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0134713418";
};

# المستخدم 122: 0106727145
:do {
    /tool user-manager user add customer="admin" username="0106727145" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0106727145";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0106727145";
};

# المستخدم 123: 0172004438
:do {
    /tool user-manager user add customer="admin" username="0172004438" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0172004438";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0172004438";
};

# المستخدم 124: 0169611700
:do {
    /tool user-manager user add customer="admin" username="0169611700" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0169611700";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0169611700";
};

# المستخدم 125: 0103437986
:do {
    /tool user-manager user add customer="admin" username="0103437986" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0103437986";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0103437986";
};

# المستخدم 126: 0131869678
:do {
    /tool user-manager user add customer="admin" username="0131869678" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0131869678";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0131869678";
};

# المستخدم 127: 0162102124
:do {
    /tool user-manager user add customer="admin" username="0162102124" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0162102124";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0162102124";
};

# المستخدم 128: 0187547390
:do {
    /tool user-manager user add customer="admin" username="0187547390" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0187547390";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0187547390";
};

# المستخدم 129: 0150649070
:do {
    /tool user-manager user add customer="admin" username="0150649070" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0150649070";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0150649070";
};

# المستخدم 130: 0146326113
:do {
    /tool user-manager user add customer="admin" username="0146326113" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0146326113";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0146326113";
};

# المستخدم 131: 0185298877
:do {
    /tool user-manager user add customer="admin" username="0185298877" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0185298877";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0185298877";
};

# المستخدم 132: 0129508970
:do {
    /tool user-manager user add customer="admin" username="0129508970" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0129508970";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0129508970";
};

# المستخدم 133: 0143706623
:do {
    /tool user-manager user add customer="admin" username="0143706623" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0143706623";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0143706623";
};

# المستخدم 134: 0145308248
:do {
    /tool user-manager user add customer="admin" username="0145308248" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0145308248";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0145308248";
};

# المستخدم 135: 0165311209
:do {
    /tool user-manager user add customer="admin" username="0165311209" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0165311209";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0165311209";
};

# المستخدم 136: 0159376680
:do {
    /tool user-manager user add customer="admin" username="0159376680" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0159376680";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0159376680";
};

# المستخدم 137: 0166632637
:do {
    /tool user-manager user add customer="admin" username="0166632637" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0166632637";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0166632637";
};

# المستخدم 138: 0124458119
:do {
    /tool user-manager user add customer="admin" username="0124458119" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0124458119";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0124458119";
};

# المستخدم 139: 0189158638
:do {
    /tool user-manager user add customer="admin" username="0189158638" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0189158638";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0189158638";
};

# المستخدم 140: 0141953066
:do {
    /tool user-manager user add customer="admin" username="0141953066" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0141953066";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0141953066";
};

# المستخدم 141: 0130472823
:do {
    /tool user-manager user add customer="admin" username="0130472823" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0130472823";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0130472823";
};

# المستخدم 142: 0109259483
:do {
    /tool user-manager user add customer="admin" username="0109259483" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0109259483";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0109259483";
};

# المستخدم 143: 0184043374
:do {
    /tool user-manager user add customer="admin" username="0184043374" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0184043374";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0184043374";
};

# المستخدم 144: 0176800184
:do {
    /tool user-manager user add customer="admin" username="0176800184" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0176800184";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0176800184";
};

# المستخدم 145: 0184395320
:do {
    /tool user-manager user add customer="admin" username="0184395320" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0184395320";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0184395320";
};

# المستخدم 146: 0142533489
:do {
    /tool user-manager user add customer="admin" username="0142533489" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0142533489";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0142533489";
};

# المستخدم 147: 0180041208
:do {
    /tool user-manager user add customer="admin" username="0180041208" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0180041208";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0180041208";
};

# المستخدم 148: 0197027379
:do {
    /tool user-manager user add customer="admin" username="0197027379" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0197027379";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0197027379";
};

# المستخدم 149: 0197497630
:do {
    /tool user-manager user add customer="admin" username="0197497630" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0197497630";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0197497630";
};

# المستخدم 150: 0161052116
:do {
    /tool user-manager user add customer="admin" username="0161052116" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0161052116";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0161052116";
};

# المستخدم 151: 0195424603
:do {
    /tool user-manager user add customer="admin" username="0195424603" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0195424603";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0195424603";
};

# المستخدم 152: 0194455148
:do {
    /tool user-manager user add customer="admin" username="0194455148" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0194455148";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0194455148";
};

# المستخدم 153: 0140589293
:do {
    /tool user-manager user add customer="admin" username="0140589293" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0140589293";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0140589293";
};

# المستخدم 154: 0179813289
:do {
    /tool user-manager user add customer="admin" username="0179813289" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0179813289";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0179813289";
};

# المستخدم 155: 0168559258
:do {
    /tool user-manager user add customer="admin" username="0168559258" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0168559258";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0168559258";
};

# المستخدم 156: 0100254572
:do {
    /tool user-manager user add customer="admin" username="0100254572" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0100254572";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0100254572";
};

# المستخدم 157: 0132554854
:do {
    /tool user-manager user add customer="admin" username="0132554854" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0132554854";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0132554854";
};

# المستخدم 158: 0182471109
:do {
    /tool user-manager user add customer="admin" username="0182471109" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0182471109";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0182471109";
};

# المستخدم 159: 0106094021
:do {
    /tool user-manager user add customer="admin" username="0106094021" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0106094021";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0106094021";
};

# المستخدم 160: 0134087068
:do {
    /tool user-manager user add customer="admin" username="0134087068" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0134087068";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0134087068";
};

# المستخدم 161: 0189798769
:do {
    /tool user-manager user add customer="admin" username="0189798769" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0189798769";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0189798769";
};

# المستخدم 162: 0104029425
:do {
    /tool user-manager user add customer="admin" username="0104029425" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0104029425";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0104029425";
};

# المستخدم 163: 0119669351
:do {
    /tool user-manager user add customer="admin" username="0119669351" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0119669351";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0119669351";
};

# المستخدم 164: 0189616729
:do {
    /tool user-manager user add customer="admin" username="0189616729" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0189616729";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0189616729";
};

# المستخدم 165: 0191856755
:do {
    /tool user-manager user add customer="admin" username="0191856755" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0191856755";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0191856755";
};

# المستخدم 166: 0119394197
:do {
    /tool user-manager user add customer="admin" username="0119394197" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0119394197";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0119394197";
};

# المستخدم 167: 0184457379
:do {
    /tool user-manager user add customer="admin" username="0184457379" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0184457379";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0184457379";
};

# المستخدم 168: 0196832116
:do {
    /tool user-manager user add customer="admin" username="0196832116" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0196832116";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0196832116";
};

# المستخدم 169: 0149913302
:do {
    /tool user-manager user add customer="admin" username="0149913302" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0149913302";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0149913302";
};

# المستخدم 170: 0127008121
:do {
    /tool user-manager user add customer="admin" username="0127008121" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0127008121";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0127008121";
};

# المستخدم 171: 0181855089
:do {
    /tool user-manager user add customer="admin" username="0181855089" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0181855089";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0181855089";
};

# المستخدم 172: 0141751612
:do {
    /tool user-manager user add customer="admin" username="0141751612" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0141751612";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0141751612";
};

# المستخدم 173: 0175358095
:do {
    /tool user-manager user add customer="admin" username="0175358095" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0175358095";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0175358095";
};

# المستخدم 174: 0196463389
:do {
    /tool user-manager user add customer="admin" username="0196463389" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0196463389";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0196463389";
};

# المستخدم 175: 0162050367
:do {
    /tool user-manager user add customer="admin" username="0162050367" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0162050367";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0162050367";
};

# المستخدم 176: 0133788250
:do {
    /tool user-manager user add customer="admin" username="0133788250" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0133788250";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0133788250";
};

# المستخدم 177: 0178681162
:do {
    /tool user-manager user add customer="admin" username="0178681162" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0178681162";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0178681162";
};

# المستخدم 178: 0120695553
:do {
    /tool user-manager user add customer="admin" username="0120695553" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0120695553";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0120695553";
};

# المستخدم 179: 0117696601
:do {
    /tool user-manager user add customer="admin" username="0117696601" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0117696601";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0117696601";
};

# المستخدم 180: 0141270147
:do {
    /tool user-manager user add customer="admin" username="0141270147" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0141270147";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0141270147";
};

# المستخدم 181: 0179276401
:do {
    /tool user-manager user add customer="admin" username="0179276401" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0179276401";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0179276401";
};

# المستخدم 182: 0105036640
:do {
    /tool user-manager user add customer="admin" username="0105036640" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0105036640";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0105036640";
};

# المستخدم 183: 0197340517
:do {
    /tool user-manager user add customer="admin" username="0197340517" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0197340517";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0197340517";
};

# المستخدم 184: 0187574403
:do {
    /tool user-manager user add customer="admin" username="0187574403" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0187574403";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0187574403";
};

# المستخدم 185: 0174831846
:do {
    /tool user-manager user add customer="admin" username="0174831846" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0174831846";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0174831846";
};

# المستخدم 186: 0160921387
:do {
    /tool user-manager user add customer="admin" username="0160921387" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0160921387";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0160921387";
};

# المستخدم 187: 0125647723
:do {
    /tool user-manager user add customer="admin" username="0125647723" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0125647723";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0125647723";
};

# المستخدم 188: 0190345194
:do {
    /tool user-manager user add customer="admin" username="0190345194" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0190345194";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0190345194";
};

# المستخدم 189: 0156729272
:do {
    /tool user-manager user add customer="admin" username="0156729272" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0156729272";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0156729272";
};

# المستخدم 190: 0123445939
:do {
    /tool user-manager user add customer="admin" username="0123445939" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0123445939";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0123445939";
};

# المستخدم 191: 0150791948
:do {
    /tool user-manager user add customer="admin" username="0150791948" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0150791948";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0150791948";
};

# المستخدم 192: 0114401710
:do {
    /tool user-manager user add customer="admin" username="0114401710" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0114401710";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0114401710";
};

# المستخدم 193: 0149052842
:do {
    /tool user-manager user add customer="admin" username="0149052842" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0149052842";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0149052842";
};

# المستخدم 194: 0130594713
:do {
    /tool user-manager user add customer="admin" username="0130594713" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0130594713";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0130594713";
};

# المستخدم 195: 0188136249
:do {
    /tool user-manager user add customer="admin" username="0188136249" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0188136249";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0188136249";
};

# المستخدم 196: 0138713271
:do {
    /tool user-manager user add customer="admin" username="0138713271" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0138713271";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0138713271";
};

# المستخدم 197: 0101143881
:do {
    /tool user-manager user add customer="admin" username="0101143881" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0101143881";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0101143881";
};

# المستخدم 198: 0130903390
:do {
    /tool user-manager user add customer="admin" username="0130903390" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0130903390";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0130903390";
};

# المستخدم 199: 0187140623
:do {
    /tool user-manager user add customer="admin" username="0187140623" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0187140623";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0187140623";
};

# المستخدم 200: 0160874894
:do {
    /tool user-manager user add customer="admin" username="0160874894" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0160874894";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0160874894";
};


:put "📊 تم الانتهاء من النسخة الاحتياطية";
:put "✅ نجح: $success كرت";
:put "❌ فشل: $errors كرت";
:put "📈 الإجمالي: $total كرت";

:put "🎉 تم استعادة جميع كروت القالب: 10";
:put "💡 النسخة الاحتياطية مكتملة بنجاح!";
