# MikroTik Script - نسخة احتياطية للكروت
# تم الإنشاء: 2025-07-25 22:55:10
# القالب: 10
# النظام: user_manager
# عدد الكروت: 200
# تم إنشاؤه بواسطة: مولد كروت MikroTik

:put "🚀 بدء تنفيذ النسخة الاحتياطية للكروت";
:put "📋 القالب: 10";
:put "🎯 النظام: user_manager";
:put "📊 عدد الكروت: 200";

:local success 0;
:local errors 0;
:local total 200;

:put "⏳ بدء إضافة الكروت...";

# إضافة مستخدمي User Manager
:put "👤 إضافة 200 مستخدم User Manager...";

# المستخدم 1: 0112710203
:do {
    /tool user-manager user add customer="admin" username="0112710203" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0112710203";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0112710203";
};

# المستخدم 2: 0194994630
:do {
    /tool user-manager user add customer="admin" username="0194994630" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0194994630";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0194994630";
};

# المستخدم 3: 0185749483
:do {
    /tool user-manager user add customer="admin" username="0185749483" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0185749483";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0185749483";
};

# المستخدم 4: 0165207206
:do {
    /tool user-manager user add customer="admin" username="0165207206" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0165207206";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0165207206";
};

# المستخدم 5: 0148436327
:do {
    /tool user-manager user add customer="admin" username="0148436327" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0148436327";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0148436327";
};

# المستخدم 6: 0152844090
:do {
    /tool user-manager user add customer="admin" username="0152844090" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0152844090";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0152844090";
};

# المستخدم 7: 0109237933
:do {
    /tool user-manager user add customer="admin" username="0109237933" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0109237933";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0109237933";
};

# المستخدم 8: 0110811016
:do {
    /tool user-manager user add customer="admin" username="0110811016" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0110811016";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0110811016";
};

# المستخدم 9: 0120219894
:do {
    /tool user-manager user add customer="admin" username="0120219894" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0120219894";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0120219894";
};

# المستخدم 10: 0187597986
:do {
    /tool user-manager user add customer="admin" username="0187597986" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0187597986";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0187597986";
};

# المستخدم 11: 0105255098
:do {
    /tool user-manager user add customer="admin" username="0105255098" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0105255098";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0105255098";
};

# المستخدم 12: 0172560688
:do {
    /tool user-manager user add customer="admin" username="0172560688" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0172560688";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0172560688";
};

# المستخدم 13: 0131205031
:do {
    /tool user-manager user add customer="admin" username="0131205031" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0131205031";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0131205031";
};

# المستخدم 14: 0199143798
:do {
    /tool user-manager user add customer="admin" username="0199143798" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0199143798";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0199143798";
};

# المستخدم 15: 0110565207
:do {
    /tool user-manager user add customer="admin" username="0110565207" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0110565207";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0110565207";
};

# المستخدم 16: 0163690893
:do {
    /tool user-manager user add customer="admin" username="0163690893" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0163690893";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0163690893";
};

# المستخدم 17: 0130695991
:do {
    /tool user-manager user add customer="admin" username="0130695991" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0130695991";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0130695991";
};

# المستخدم 18: 0138776205
:do {
    /tool user-manager user add customer="admin" username="0138776205" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0138776205";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0138776205";
};

# المستخدم 19: 0191439976
:do {
    /tool user-manager user add customer="admin" username="0191439976" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0191439976";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0191439976";
};

# المستخدم 20: 0167808694
:do {
    /tool user-manager user add customer="admin" username="0167808694" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0167808694";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0167808694";
};

# المستخدم 21: 0163882788
:do {
    /tool user-manager user add customer="admin" username="0163882788" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0163882788";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0163882788";
};

# المستخدم 22: 0186407686
:do {
    /tool user-manager user add customer="admin" username="0186407686" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0186407686";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0186407686";
};

# المستخدم 23: 0133308970
:do {
    /tool user-manager user add customer="admin" username="0133308970" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0133308970";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0133308970";
};

# المستخدم 24: 0159860075
:do {
    /tool user-manager user add customer="admin" username="0159860075" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0159860075";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0159860075";
};

# المستخدم 25: 0163656528
:do {
    /tool user-manager user add customer="admin" username="0163656528" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0163656528";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0163656528";
};

# المستخدم 26: 0195946261
:do {
    /tool user-manager user add customer="admin" username="0195946261" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0195946261";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0195946261";
};

# المستخدم 27: 0172832983
:do {
    /tool user-manager user add customer="admin" username="0172832983" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0172832983";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0172832983";
};

# المستخدم 28: 0170464729
:do {
    /tool user-manager user add customer="admin" username="0170464729" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0170464729";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0170464729";
};

# المستخدم 29: 0174883265
:do {
    /tool user-manager user add customer="admin" username="0174883265" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0174883265";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0174883265";
};

# المستخدم 30: 0196623439
:do {
    /tool user-manager user add customer="admin" username="0196623439" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0196623439";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0196623439";
};

# المستخدم 31: 0135404056
:do {
    /tool user-manager user add customer="admin" username="0135404056" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0135404056";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0135404056";
};

# المستخدم 32: 0149446216
:do {
    /tool user-manager user add customer="admin" username="0149446216" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0149446216";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0149446216";
};

# المستخدم 33: 0112251131
:do {
    /tool user-manager user add customer="admin" username="0112251131" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0112251131";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0112251131";
};

# المستخدم 34: 0125836980
:do {
    /tool user-manager user add customer="admin" username="0125836980" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0125836980";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0125836980";
};

# المستخدم 35: 0162193899
:do {
    /tool user-manager user add customer="admin" username="0162193899" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0162193899";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0162193899";
};

# المستخدم 36: 0105977033
:do {
    /tool user-manager user add customer="admin" username="0105977033" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0105977033";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0105977033";
};

# المستخدم 37: 0180963190
:do {
    /tool user-manager user add customer="admin" username="0180963190" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0180963190";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0180963190";
};

# المستخدم 38: 0133881389
:do {
    /tool user-manager user add customer="admin" username="0133881389" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0133881389";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0133881389";
};

# المستخدم 39: 0175595509
:do {
    /tool user-manager user add customer="admin" username="0175595509" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0175595509";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0175595509";
};

# المستخدم 40: 0121854039
:do {
    /tool user-manager user add customer="admin" username="0121854039" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0121854039";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0121854039";
};

# المستخدم 41: 0144439588
:do {
    /tool user-manager user add customer="admin" username="0144439588" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0144439588";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0144439588";
};

# المستخدم 42: 0139003598
:do {
    /tool user-manager user add customer="admin" username="0139003598" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0139003598";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0139003598";
};

# المستخدم 43: 0119578563
:do {
    /tool user-manager user add customer="admin" username="0119578563" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0119578563";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0119578563";
};

# المستخدم 44: 0111741394
:do {
    /tool user-manager user add customer="admin" username="0111741394" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0111741394";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0111741394";
};

# المستخدم 45: 0125806916
:do {
    /tool user-manager user add customer="admin" username="0125806916" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0125806916";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0125806916";
};

# المستخدم 46: 0189040359
:do {
    /tool user-manager user add customer="admin" username="0189040359" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0189040359";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0189040359";
};

# المستخدم 47: 0133452579
:do {
    /tool user-manager user add customer="admin" username="0133452579" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0133452579";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0133452579";
};

# المستخدم 48: 0146987339
:do {
    /tool user-manager user add customer="admin" username="0146987339" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0146987339";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0146987339";
};

# المستخدم 49: 0125653385
:do {
    /tool user-manager user add customer="admin" username="0125653385" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0125653385";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0125653385";
};

# المستخدم 50: 0127943400
:do {
    /tool user-manager user add customer="admin" username="0127943400" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0127943400";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0127943400";
};

# المستخدم 51: 0146239517
:do {
    /tool user-manager user add customer="admin" username="0146239517" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0146239517";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0146239517";
};

# المستخدم 52: 0110724013
:do {
    /tool user-manager user add customer="admin" username="0110724013" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0110724013";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0110724013";
};

# المستخدم 53: 0106220706
:do {
    /tool user-manager user add customer="admin" username="0106220706" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0106220706";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0106220706";
};

# المستخدم 54: 0172769849
:do {
    /tool user-manager user add customer="admin" username="0172769849" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0172769849";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0172769849";
};

# المستخدم 55: 0159134817
:do {
    /tool user-manager user add customer="admin" username="0159134817" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0159134817";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0159134817";
};

# المستخدم 56: 0124549798
:do {
    /tool user-manager user add customer="admin" username="0124549798" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0124549798";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0124549798";
};

# المستخدم 57: 0178392159
:do {
    /tool user-manager user add customer="admin" username="0178392159" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0178392159";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0178392159";
};

# المستخدم 58: 0117371591
:do {
    /tool user-manager user add customer="admin" username="0117371591" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0117371591";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0117371591";
};

# المستخدم 59: 0131273359
:do {
    /tool user-manager user add customer="admin" username="0131273359" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0131273359";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0131273359";
};

# المستخدم 60: 0148312926
:do {
    /tool user-manager user add customer="admin" username="0148312926" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0148312926";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0148312926";
};

# المستخدم 61: 0103301605
:do {
    /tool user-manager user add customer="admin" username="0103301605" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0103301605";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0103301605";
};

# المستخدم 62: 0138234743
:do {
    /tool user-manager user add customer="admin" username="0138234743" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0138234743";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0138234743";
};

# المستخدم 63: 0172352430
:do {
    /tool user-manager user add customer="admin" username="0172352430" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0172352430";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0172352430";
};

# المستخدم 64: 0145529377
:do {
    /tool user-manager user add customer="admin" username="0145529377" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0145529377";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0145529377";
};

# المستخدم 65: 0156136226
:do {
    /tool user-manager user add customer="admin" username="0156136226" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0156136226";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0156136226";
};

# المستخدم 66: 0191655093
:do {
    /tool user-manager user add customer="admin" username="0191655093" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0191655093";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0191655093";
};

# المستخدم 67: 0170006493
:do {
    /tool user-manager user add customer="admin" username="0170006493" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0170006493";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0170006493";
};

# المستخدم 68: 0181042547
:do {
    /tool user-manager user add customer="admin" username="0181042547" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0181042547";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0181042547";
};

# المستخدم 69: 0138243172
:do {
    /tool user-manager user add customer="admin" username="0138243172" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0138243172";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0138243172";
};

# المستخدم 70: 0193599207
:do {
    /tool user-manager user add customer="admin" username="0193599207" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0193599207";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0193599207";
};

# المستخدم 71: 0126940919
:do {
    /tool user-manager user add customer="admin" username="0126940919" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0126940919";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0126940919";
};

# المستخدم 72: 0119333835
:do {
    /tool user-manager user add customer="admin" username="0119333835" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0119333835";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0119333835";
};

# المستخدم 73: 0116915171
:do {
    /tool user-manager user add customer="admin" username="0116915171" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0116915171";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0116915171";
};

# المستخدم 74: 0188308235
:do {
    /tool user-manager user add customer="admin" username="0188308235" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0188308235";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0188308235";
};

# المستخدم 75: 0146292048
:do {
    /tool user-manager user add customer="admin" username="0146292048" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0146292048";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0146292048";
};

# المستخدم 76: 0197268678
:do {
    /tool user-manager user add customer="admin" username="0197268678" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0197268678";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0197268678";
};

# المستخدم 77: 0103445584
:do {
    /tool user-manager user add customer="admin" username="0103445584" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0103445584";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0103445584";
};

# المستخدم 78: 0116272100
:do {
    /tool user-manager user add customer="admin" username="0116272100" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0116272100";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0116272100";
};

# المستخدم 79: 0142756896
:do {
    /tool user-manager user add customer="admin" username="0142756896" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0142756896";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0142756896";
};

# المستخدم 80: 0101280653
:do {
    /tool user-manager user add customer="admin" username="0101280653" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0101280653";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0101280653";
};

# المستخدم 81: 0106668853
:do {
    /tool user-manager user add customer="admin" username="0106668853" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0106668853";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0106668853";
};

# المستخدم 82: 0178519971
:do {
    /tool user-manager user add customer="admin" username="0178519971" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0178519971";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0178519971";
};

# المستخدم 83: 0110263068
:do {
    /tool user-manager user add customer="admin" username="0110263068" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0110263068";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0110263068";
};

# المستخدم 84: 0159217439
:do {
    /tool user-manager user add customer="admin" username="0159217439" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0159217439";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0159217439";
};

# المستخدم 85: 0119441154
:do {
    /tool user-manager user add customer="admin" username="0119441154" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0119441154";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0119441154";
};

# المستخدم 86: 0149902204
:do {
    /tool user-manager user add customer="admin" username="0149902204" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0149902204";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0149902204";
};

# المستخدم 87: 0161660988
:do {
    /tool user-manager user add customer="admin" username="0161660988" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0161660988";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0161660988";
};

# المستخدم 88: 0144922401
:do {
    /tool user-manager user add customer="admin" username="0144922401" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0144922401";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0144922401";
};

# المستخدم 89: 0139347578
:do {
    /tool user-manager user add customer="admin" username="0139347578" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0139347578";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0139347578";
};

# المستخدم 90: 0182140101
:do {
    /tool user-manager user add customer="admin" username="0182140101" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0182140101";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0182140101";
};

# المستخدم 91: 0119135949
:do {
    /tool user-manager user add customer="admin" username="0119135949" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0119135949";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0119135949";
};

# المستخدم 92: 0135825836
:do {
    /tool user-manager user add customer="admin" username="0135825836" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0135825836";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0135825836";
};

# المستخدم 93: 0155080152
:do {
    /tool user-manager user add customer="admin" username="0155080152" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0155080152";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0155080152";
};

# المستخدم 94: 0150648327
:do {
    /tool user-manager user add customer="admin" username="0150648327" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0150648327";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0150648327";
};

# المستخدم 95: 0151650427
:do {
    /tool user-manager user add customer="admin" username="0151650427" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0151650427";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0151650427";
};

# المستخدم 96: 0184443982
:do {
    /tool user-manager user add customer="admin" username="0184443982" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0184443982";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0184443982";
};

# المستخدم 97: 0127644445
:do {
    /tool user-manager user add customer="admin" username="0127644445" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0127644445";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0127644445";
};

# المستخدم 98: 0155400388
:do {
    /tool user-manager user add customer="admin" username="0155400388" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0155400388";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0155400388";
};

# المستخدم 99: 0135474451
:do {
    /tool user-manager user add customer="admin" username="0135474451" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0135474451";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0135474451";
};

# المستخدم 100: 0144909315
:do {
    /tool user-manager user add customer="admin" username="0144909315" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0144909315";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0144909315";
};

# المستخدم 101: 0154294719
:do {
    /tool user-manager user add customer="admin" username="0154294719" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0154294719";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0154294719";
};

# المستخدم 102: 0187604859
:do {
    /tool user-manager user add customer="admin" username="0187604859" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0187604859";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0187604859";
};

# المستخدم 103: 0179574533
:do {
    /tool user-manager user add customer="admin" username="0179574533" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0179574533";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0179574533";
};

# المستخدم 104: 0111499116
:do {
    /tool user-manager user add customer="admin" username="0111499116" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0111499116";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0111499116";
};

# المستخدم 105: 0160498401
:do {
    /tool user-manager user add customer="admin" username="0160498401" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0160498401";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0160498401";
};

# المستخدم 106: 0137838087
:do {
    /tool user-manager user add customer="admin" username="0137838087" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0137838087";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0137838087";
};

# المستخدم 107: 0173369455
:do {
    /tool user-manager user add customer="admin" username="0173369455" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0173369455";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0173369455";
};

# المستخدم 108: 0122985863
:do {
    /tool user-manager user add customer="admin" username="0122985863" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0122985863";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0122985863";
};

# المستخدم 109: 0157722749
:do {
    /tool user-manager user add customer="admin" username="0157722749" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0157722749";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0157722749";
};

# المستخدم 110: 0175999019
:do {
    /tool user-manager user add customer="admin" username="0175999019" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0175999019";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0175999019";
};

# المستخدم 111: 0197740715
:do {
    /tool user-manager user add customer="admin" username="0197740715" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0197740715";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0197740715";
};

# المستخدم 112: 0137718309
:do {
    /tool user-manager user add customer="admin" username="0137718309" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0137718309";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0137718309";
};

# المستخدم 113: 0109055326
:do {
    /tool user-manager user add customer="admin" username="0109055326" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0109055326";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0109055326";
};

# المستخدم 114: 0195753199
:do {
    /tool user-manager user add customer="admin" username="0195753199" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0195753199";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0195753199";
};

# المستخدم 115: 0194142876
:do {
    /tool user-manager user add customer="admin" username="0194142876" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0194142876";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0194142876";
};

# المستخدم 116: 0114381942
:do {
    /tool user-manager user add customer="admin" username="0114381942" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0114381942";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0114381942";
};

# المستخدم 117: 0100028760
:do {
    /tool user-manager user add customer="admin" username="0100028760" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0100028760";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0100028760";
};

# المستخدم 118: 0128584541
:do {
    /tool user-manager user add customer="admin" username="0128584541" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0128584541";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0128584541";
};

# المستخدم 119: 0186294774
:do {
    /tool user-manager user add customer="admin" username="0186294774" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0186294774";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0186294774";
};

# المستخدم 120: 0122208971
:do {
    /tool user-manager user add customer="admin" username="0122208971" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0122208971";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0122208971";
};

# المستخدم 121: 0190393159
:do {
    /tool user-manager user add customer="admin" username="0190393159" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0190393159";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0190393159";
};

# المستخدم 122: 0111766536
:do {
    /tool user-manager user add customer="admin" username="0111766536" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0111766536";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0111766536";
};

# المستخدم 123: 0181050742
:do {
    /tool user-manager user add customer="admin" username="0181050742" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0181050742";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0181050742";
};

# المستخدم 124: 0170981171
:do {
    /tool user-manager user add customer="admin" username="0170981171" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0170981171";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0170981171";
};

# المستخدم 125: 0177105940
:do {
    /tool user-manager user add customer="admin" username="0177105940" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0177105940";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0177105940";
};

# المستخدم 126: 0176429013
:do {
    /tool user-manager user add customer="admin" username="0176429013" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0176429013";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0176429013";
};

# المستخدم 127: 0122884432
:do {
    /tool user-manager user add customer="admin" username="0122884432" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0122884432";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0122884432";
};

# المستخدم 128: 0125039972
:do {
    /tool user-manager user add customer="admin" username="0125039972" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0125039972";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0125039972";
};

# المستخدم 129: 0126702112
:do {
    /tool user-manager user add customer="admin" username="0126702112" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0126702112";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0126702112";
};

# المستخدم 130: 0182016548
:do {
    /tool user-manager user add customer="admin" username="0182016548" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0182016548";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0182016548";
};

# المستخدم 131: 0199835506
:do {
    /tool user-manager user add customer="admin" username="0199835506" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0199835506";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0199835506";
};

# المستخدم 132: 0162097273
:do {
    /tool user-manager user add customer="admin" username="0162097273" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0162097273";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0162097273";
};

# المستخدم 133: 0172478004
:do {
    /tool user-manager user add customer="admin" username="0172478004" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0172478004";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0172478004";
};

# المستخدم 134: 0111492952
:do {
    /tool user-manager user add customer="admin" username="0111492952" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0111492952";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0111492952";
};

# المستخدم 135: 0117118224
:do {
    /tool user-manager user add customer="admin" username="0117118224" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0117118224";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0117118224";
};

# المستخدم 136: 0129030109
:do {
    /tool user-manager user add customer="admin" username="0129030109" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0129030109";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0129030109";
};

# المستخدم 137: 0191532758
:do {
    /tool user-manager user add customer="admin" username="0191532758" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0191532758";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0191532758";
};

# المستخدم 138: 0101717635
:do {
    /tool user-manager user add customer="admin" username="0101717635" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0101717635";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0101717635";
};

# المستخدم 139: 0150057578
:do {
    /tool user-manager user add customer="admin" username="0150057578" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0150057578";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0150057578";
};

# المستخدم 140: 0142402889
:do {
    /tool user-manager user add customer="admin" username="0142402889" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0142402889";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0142402889";
};

# المستخدم 141: 0194305987
:do {
    /tool user-manager user add customer="admin" username="0194305987" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0194305987";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0194305987";
};

# المستخدم 142: 0127339881
:do {
    /tool user-manager user add customer="admin" username="0127339881" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0127339881";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0127339881";
};

# المستخدم 143: 0174616841
:do {
    /tool user-manager user add customer="admin" username="0174616841" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0174616841";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0174616841";
};

# المستخدم 144: 0128447231
:do {
    /tool user-manager user add customer="admin" username="0128447231" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0128447231";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0128447231";
};

# المستخدم 145: 0136787316
:do {
    /tool user-manager user add customer="admin" username="0136787316" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0136787316";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0136787316";
};

# المستخدم 146: 0122854605
:do {
    /tool user-manager user add customer="admin" username="0122854605" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0122854605";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0122854605";
};

# المستخدم 147: 0171770623
:do {
    /tool user-manager user add customer="admin" username="0171770623" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0171770623";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0171770623";
};

# المستخدم 148: 0190675971
:do {
    /tool user-manager user add customer="admin" username="0190675971" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0190675971";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0190675971";
};

# المستخدم 149: 0169774599
:do {
    /tool user-manager user add customer="admin" username="0169774599" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0169774599";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0169774599";
};

# المستخدم 150: 0177289262
:do {
    /tool user-manager user add customer="admin" username="0177289262" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0177289262";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0177289262";
};

# المستخدم 151: 0129884654
:do {
    /tool user-manager user add customer="admin" username="0129884654" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0129884654";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0129884654";
};

# المستخدم 152: 0143924908
:do {
    /tool user-manager user add customer="admin" username="0143924908" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0143924908";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0143924908";
};

# المستخدم 153: 0162260588
:do {
    /tool user-manager user add customer="admin" username="0162260588" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0162260588";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0162260588";
};

# المستخدم 154: 0188878310
:do {
    /tool user-manager user add customer="admin" username="0188878310" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0188878310";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0188878310";
};

# المستخدم 155: 0126696129
:do {
    /tool user-manager user add customer="admin" username="0126696129" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0126696129";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0126696129";
};

# المستخدم 156: 0101554483
:do {
    /tool user-manager user add customer="admin" username="0101554483" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0101554483";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0101554483";
};

# المستخدم 157: 0182807051
:do {
    /tool user-manager user add customer="admin" username="0182807051" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0182807051";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0182807051";
};

# المستخدم 158: 0161398441
:do {
    /tool user-manager user add customer="admin" username="0161398441" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0161398441";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0161398441";
};

# المستخدم 159: 0121478183
:do {
    /tool user-manager user add customer="admin" username="0121478183" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0121478183";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0121478183";
};

# المستخدم 160: 0101011970
:do {
    /tool user-manager user add customer="admin" username="0101011970" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0101011970";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0101011970";
};

# المستخدم 161: 0100193067
:do {
    /tool user-manager user add customer="admin" username="0100193067" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0100193067";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0100193067";
};

# المستخدم 162: 0113676161
:do {
    /tool user-manager user add customer="admin" username="0113676161" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0113676161";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0113676161";
};

# المستخدم 163: 0166159544
:do {
    /tool user-manager user add customer="admin" username="0166159544" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0166159544";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0166159544";
};

# المستخدم 164: 0168528472
:do {
    /tool user-manager user add customer="admin" username="0168528472" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0168528472";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0168528472";
};

# المستخدم 165: 0114122261
:do {
    /tool user-manager user add customer="admin" username="0114122261" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0114122261";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0114122261";
};

# المستخدم 166: 0196042695
:do {
    /tool user-manager user add customer="admin" username="0196042695" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0196042695";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0196042695";
};

# المستخدم 167: 0136913680
:do {
    /tool user-manager user add customer="admin" username="0136913680" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0136913680";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0136913680";
};

# المستخدم 168: 0105219818
:do {
    /tool user-manager user add customer="admin" username="0105219818" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0105219818";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0105219818";
};

# المستخدم 169: 0116568033
:do {
    /tool user-manager user add customer="admin" username="0116568033" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0116568033";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0116568033";
};

# المستخدم 170: 0191387616
:do {
    /tool user-manager user add customer="admin" username="0191387616" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0191387616";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0191387616";
};

# المستخدم 171: 0101899438
:do {
    /tool user-manager user add customer="admin" username="0101899438" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0101899438";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0101899438";
};

# المستخدم 172: 0130455554
:do {
    /tool user-manager user add customer="admin" username="0130455554" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0130455554";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0130455554";
};

# المستخدم 173: 0126572661
:do {
    /tool user-manager user add customer="admin" username="0126572661" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0126572661";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0126572661";
};

# المستخدم 174: 0197188678
:do {
    /tool user-manager user add customer="admin" username="0197188678" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0197188678";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0197188678";
};

# المستخدم 175: 0131582694
:do {
    /tool user-manager user add customer="admin" username="0131582694" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0131582694";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0131582694";
};

# المستخدم 176: 0111278574
:do {
    /tool user-manager user add customer="admin" username="0111278574" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0111278574";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0111278574";
};

# المستخدم 177: 0178596171
:do {
    /tool user-manager user add customer="admin" username="0178596171" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0178596171";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0178596171";
};

# المستخدم 178: 0199734531
:do {
    /tool user-manager user add customer="admin" username="0199734531" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0199734531";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0199734531";
};

# المستخدم 179: 0140651371
:do {
    /tool user-manager user add customer="admin" username="0140651371" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0140651371";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0140651371";
};

# المستخدم 180: 0156861303
:do {
    /tool user-manager user add customer="admin" username="0156861303" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0156861303";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0156861303";
};

# المستخدم 181: 0134997906
:do {
    /tool user-manager user add customer="admin" username="0134997906" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0134997906";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0134997906";
};

# المستخدم 182: 0167297430
:do {
    /tool user-manager user add customer="admin" username="0167297430" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0167297430";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0167297430";
};

# المستخدم 183: 0153869867
:do {
    /tool user-manager user add customer="admin" username="0153869867" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0153869867";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0153869867";
};

# المستخدم 184: 0193876484
:do {
    /tool user-manager user add customer="admin" username="0193876484" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0193876484";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0193876484";
};

# المستخدم 185: 0189360015
:do {
    /tool user-manager user add customer="admin" username="0189360015" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0189360015";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0189360015";
};

# المستخدم 186: 0102165432
:do {
    /tool user-manager user add customer="admin" username="0102165432" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0102165432";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0102165432";
};

# المستخدم 187: 0141753723
:do {
    /tool user-manager user add customer="admin" username="0141753723" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0141753723";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0141753723";
};

# المستخدم 188: 0188097603
:do {
    /tool user-manager user add customer="admin" username="0188097603" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0188097603";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0188097603";
};

# المستخدم 189: 0159442289
:do {
    /tool user-manager user add customer="admin" username="0159442289" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0159442289";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0159442289";
};

# المستخدم 190: 0110819716
:do {
    /tool user-manager user add customer="admin" username="0110819716" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0110819716";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0110819716";
};

# المستخدم 191: 0130004232
:do {
    /tool user-manager user add customer="admin" username="0130004232" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0130004232";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0130004232";
};

# المستخدم 192: 0145018933
:do {
    /tool user-manager user add customer="admin" username="0145018933" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0145018933";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0145018933";
};

# المستخدم 193: 0112946734
:do {
    /tool user-manager user add customer="admin" username="0112946734" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0112946734";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0112946734";
};

# المستخدم 194: 0175955796
:do {
    /tool user-manager user add customer="admin" username="0175955796" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0175955796";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0175955796";
};

# المستخدم 195: 0106079389
:do {
    /tool user-manager user add customer="admin" username="0106079389" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0106079389";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0106079389";
};

# المستخدم 196: 0133294407
:do {
    /tool user-manager user add customer="admin" username="0133294407" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0133294407";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0133294407";
};

# المستخدم 197: 0195594558
:do {
    /tool user-manager user add customer="admin" username="0195594558" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0195594558";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0195594558";
};

# المستخدم 198: 0152738547
:do {
    /tool user-manager user add customer="admin" username="0152738547" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0152738547";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0152738547";
};

# المستخدم 199: 0103604410
:do {
    /tool user-manager user add customer="admin" username="0103604410" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0103604410";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0103604410";
};

# المستخدم 200: 0108589381
:do {
    /tool user-manager user add customer="admin" username="0108589381" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0108589381";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0108589381";
};


:put "📊 تم الانتهاء من النسخة الاحتياطية";
:put "✅ نجح: $success كرت";
:put "❌ فشل: $errors كرت";
:put "📈 الإجمالي: $total كرت";

:put "🎉 تم استعادة جميع كروت القالب: 10";
:put "💡 النسخة الاحتياطية مكتملة بنجاح!";
