# MikroTik Script - نسخة احتياطية للكروت
# تم الإنشاء: 2025-07-26 01:36:09
# القالب: 10
# النظام: user_manager
# عدد الكروت: 200
# تم إنشاؤه بواسطة: مولد كروت MikroTik

:put "🚀 بدء تنفيذ النسخة الاحتياطية للكروت";
:put "📋 القالب: 10";
:put "🎯 النظام: user_manager";
:put "📊 عدد الكروت: 200";

:local success 0;
:local errors 0;
:local total 200;

:put "⏳ بدء إضافة الكروت...";

# إضافة مستخدمي User Manager
:put "👤 إضافة 200 مستخدم User Manager...";

# المستخدم 1: 0182929515
:do {
    /tool user-manager user add customer="admin" username="0182929515" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0182929515";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0182929515";
};

# المستخدم 2: 0146361542
:do {
    /tool user-manager user add customer="admin" username="0146361542" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0146361542";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0146361542";
};

# المستخدم 3: 0165492317
:do {
    /tool user-manager user add customer="admin" username="0165492317" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0165492317";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0165492317";
};

# المستخدم 4: 0121141352
:do {
    /tool user-manager user add customer="admin" username="0121141352" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0121141352";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0121141352";
};

# المستخدم 5: 0168868577
:do {
    /tool user-manager user add customer="admin" username="0168868577" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0168868577";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0168868577";
};

# المستخدم 6: 0176716541
:do {
    /tool user-manager user add customer="admin" username="0176716541" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0176716541";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0176716541";
};

# المستخدم 7: 0103107015
:do {
    /tool user-manager user add customer="admin" username="0103107015" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0103107015";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0103107015";
};

# المستخدم 8: 0135606697
:do {
    /tool user-manager user add customer="admin" username="0135606697" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0135606697";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0135606697";
};

# المستخدم 9: 0153325709
:do {
    /tool user-manager user add customer="admin" username="0153325709" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0153325709";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0153325709";
};

# المستخدم 10: 0192157567
:do {
    /tool user-manager user add customer="admin" username="0192157567" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0192157567";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0192157567";
};

# المستخدم 11: 0171941196
:do {
    /tool user-manager user add customer="admin" username="0171941196" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0171941196";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0171941196";
};

# المستخدم 12: 0134317255
:do {
    /tool user-manager user add customer="admin" username="0134317255" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0134317255";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0134317255";
};

# المستخدم 13: 0108318000
:do {
    /tool user-manager user add customer="admin" username="0108318000" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0108318000";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0108318000";
};

# المستخدم 14: 0193765373
:do {
    /tool user-manager user add customer="admin" username="0193765373" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0193765373";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0193765373";
};

# المستخدم 15: 0146514439
:do {
    /tool user-manager user add customer="admin" username="0146514439" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0146514439";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0146514439";
};

# المستخدم 16: 0118908319
:do {
    /tool user-manager user add customer="admin" username="0118908319" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0118908319";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0118908319";
};

# المستخدم 17: 0167117213
:do {
    /tool user-manager user add customer="admin" username="0167117213" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0167117213";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0167117213";
};

# المستخدم 18: 0108504337
:do {
    /tool user-manager user add customer="admin" username="0108504337" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0108504337";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0108504337";
};

# المستخدم 19: 0145923807
:do {
    /tool user-manager user add customer="admin" username="0145923807" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0145923807";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0145923807";
};

# المستخدم 20: 0181543942
:do {
    /tool user-manager user add customer="admin" username="0181543942" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0181543942";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0181543942";
};

# المستخدم 21: 0159444362
:do {
    /tool user-manager user add customer="admin" username="0159444362" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0159444362";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0159444362";
};

# المستخدم 22: 0159248661
:do {
    /tool user-manager user add customer="admin" username="0159248661" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0159248661";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0159248661";
};

# المستخدم 23: 0145557682
:do {
    /tool user-manager user add customer="admin" username="0145557682" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0145557682";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0145557682";
};

# المستخدم 24: 0140972479
:do {
    /tool user-manager user add customer="admin" username="0140972479" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0140972479";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0140972479";
};

# المستخدم 25: 0198904049
:do {
    /tool user-manager user add customer="admin" username="0198904049" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0198904049";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0198904049";
};

# المستخدم 26: 0144526706
:do {
    /tool user-manager user add customer="admin" username="0144526706" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0144526706";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0144526706";
};

# المستخدم 27: 0177000400
:do {
    /tool user-manager user add customer="admin" username="0177000400" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0177000400";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0177000400";
};

# المستخدم 28: 0126210521
:do {
    /tool user-manager user add customer="admin" username="0126210521" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0126210521";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0126210521";
};

# المستخدم 29: 0118106245
:do {
    /tool user-manager user add customer="admin" username="0118106245" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0118106245";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0118106245";
};

# المستخدم 30: 0172978505
:do {
    /tool user-manager user add customer="admin" username="0172978505" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0172978505";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0172978505";
};

# المستخدم 31: 0117524018
:do {
    /tool user-manager user add customer="admin" username="0117524018" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0117524018";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0117524018";
};

# المستخدم 32: 0120640817
:do {
    /tool user-manager user add customer="admin" username="0120640817" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0120640817";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0120640817";
};

# المستخدم 33: 0182452510
:do {
    /tool user-manager user add customer="admin" username="0182452510" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0182452510";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0182452510";
};

# المستخدم 34: 0131368375
:do {
    /tool user-manager user add customer="admin" username="0131368375" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0131368375";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0131368375";
};

# المستخدم 35: 0199734945
:do {
    /tool user-manager user add customer="admin" username="0199734945" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0199734945";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0199734945";
};

# المستخدم 36: 0106517650
:do {
    /tool user-manager user add customer="admin" username="0106517650" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0106517650";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0106517650";
};

# المستخدم 37: 0138671131
:do {
    /tool user-manager user add customer="admin" username="0138671131" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0138671131";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0138671131";
};

# المستخدم 38: 0192873315
:do {
    /tool user-manager user add customer="admin" username="0192873315" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0192873315";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0192873315";
};

# المستخدم 39: 0119912046
:do {
    /tool user-manager user add customer="admin" username="0119912046" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0119912046";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0119912046";
};

# المستخدم 40: 0127194070
:do {
    /tool user-manager user add customer="admin" username="0127194070" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0127194070";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0127194070";
};

# المستخدم 41: 0180203486
:do {
    /tool user-manager user add customer="admin" username="0180203486" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0180203486";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0180203486";
};

# المستخدم 42: 0178794253
:do {
    /tool user-manager user add customer="admin" username="0178794253" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0178794253";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0178794253";
};

# المستخدم 43: 0131956379
:do {
    /tool user-manager user add customer="admin" username="0131956379" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0131956379";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0131956379";
};

# المستخدم 44: 0192879591
:do {
    /tool user-manager user add customer="admin" username="0192879591" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0192879591";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0192879591";
};

# المستخدم 45: 0107623518
:do {
    /tool user-manager user add customer="admin" username="0107623518" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0107623518";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0107623518";
};

# المستخدم 46: 0188217666
:do {
    /tool user-manager user add customer="admin" username="0188217666" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0188217666";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0188217666";
};

# المستخدم 47: 0181175775
:do {
    /tool user-manager user add customer="admin" username="0181175775" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0181175775";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0181175775";
};

# المستخدم 48: 0162969749
:do {
    /tool user-manager user add customer="admin" username="0162969749" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0162969749";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0162969749";
};

# المستخدم 49: 0192386405
:do {
    /tool user-manager user add customer="admin" username="0192386405" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0192386405";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0192386405";
};

# المستخدم 50: 0124149230
:do {
    /tool user-manager user add customer="admin" username="0124149230" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0124149230";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0124149230";
};

# المستخدم 51: 0133672844
:do {
    /tool user-manager user add customer="admin" username="0133672844" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0133672844";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0133672844";
};

# المستخدم 52: 0120593981
:do {
    /tool user-manager user add customer="admin" username="0120593981" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0120593981";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0120593981";
};

# المستخدم 53: 0101428939
:do {
    /tool user-manager user add customer="admin" username="0101428939" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0101428939";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0101428939";
};

# المستخدم 54: 0162989786
:do {
    /tool user-manager user add customer="admin" username="0162989786" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0162989786";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0162989786";
};

# المستخدم 55: 0165403402
:do {
    /tool user-manager user add customer="admin" username="0165403402" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0165403402";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0165403402";
};

# المستخدم 56: 0138731742
:do {
    /tool user-manager user add customer="admin" username="0138731742" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0138731742";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0138731742";
};

# المستخدم 57: 0158354323
:do {
    /tool user-manager user add customer="admin" username="0158354323" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0158354323";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0158354323";
};

# المستخدم 58: 0114108277
:do {
    /tool user-manager user add customer="admin" username="0114108277" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0114108277";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0114108277";
};

# المستخدم 59: 0122740686
:do {
    /tool user-manager user add customer="admin" username="0122740686" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0122740686";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0122740686";
};

# المستخدم 60: 0193410697
:do {
    /tool user-manager user add customer="admin" username="0193410697" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0193410697";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0193410697";
};

# المستخدم 61: 0196419280
:do {
    /tool user-manager user add customer="admin" username="0196419280" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0196419280";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0196419280";
};

# المستخدم 62: 0141376900
:do {
    /tool user-manager user add customer="admin" username="0141376900" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0141376900";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0141376900";
};

# المستخدم 63: 0155420404
:do {
    /tool user-manager user add customer="admin" username="0155420404" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0155420404";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0155420404";
};

# المستخدم 64: 0184160670
:do {
    /tool user-manager user add customer="admin" username="0184160670" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0184160670";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0184160670";
};

# المستخدم 65: 0191860864
:do {
    /tool user-manager user add customer="admin" username="0191860864" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0191860864";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0191860864";
};

# المستخدم 66: 0111494897
:do {
    /tool user-manager user add customer="admin" username="0111494897" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0111494897";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0111494897";
};

# المستخدم 67: 0119439175
:do {
    /tool user-manager user add customer="admin" username="0119439175" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0119439175";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0119439175";
};

# المستخدم 68: 0145276283
:do {
    /tool user-manager user add customer="admin" username="0145276283" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0145276283";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0145276283";
};

# المستخدم 69: 0119093182
:do {
    /tool user-manager user add customer="admin" username="0119093182" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0119093182";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0119093182";
};

# المستخدم 70: 0121157067
:do {
    /tool user-manager user add customer="admin" username="0121157067" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0121157067";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0121157067";
};

# المستخدم 71: 0147221488
:do {
    /tool user-manager user add customer="admin" username="0147221488" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0147221488";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0147221488";
};

# المستخدم 72: 0111747098
:do {
    /tool user-manager user add customer="admin" username="0111747098" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0111747098";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0111747098";
};

# المستخدم 73: 0192868688
:do {
    /tool user-manager user add customer="admin" username="0192868688" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0192868688";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0192868688";
};

# المستخدم 74: 0170570517
:do {
    /tool user-manager user add customer="admin" username="0170570517" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0170570517";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0170570517";
};

# المستخدم 75: 0129706900
:do {
    /tool user-manager user add customer="admin" username="0129706900" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0129706900";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0129706900";
};

# المستخدم 76: 0123763634
:do {
    /tool user-manager user add customer="admin" username="0123763634" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0123763634";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0123763634";
};

# المستخدم 77: 0173273587
:do {
    /tool user-manager user add customer="admin" username="0173273587" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0173273587";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0173273587";
};

# المستخدم 78: 0178845934
:do {
    /tool user-manager user add customer="admin" username="0178845934" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0178845934";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0178845934";
};

# المستخدم 79: 0153268736
:do {
    /tool user-manager user add customer="admin" username="0153268736" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0153268736";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0153268736";
};

# المستخدم 80: 0125706795
:do {
    /tool user-manager user add customer="admin" username="0125706795" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0125706795";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0125706795";
};

# المستخدم 81: 0152130931
:do {
    /tool user-manager user add customer="admin" username="0152130931" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0152130931";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0152130931";
};

# المستخدم 82: 0118389486
:do {
    /tool user-manager user add customer="admin" username="0118389486" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0118389486";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0118389486";
};

# المستخدم 83: 0170493576
:do {
    /tool user-manager user add customer="admin" username="0170493576" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0170493576";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0170493576";
};

# المستخدم 84: 0186153031
:do {
    /tool user-manager user add customer="admin" username="0186153031" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0186153031";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0186153031";
};

# المستخدم 85: 0138802058
:do {
    /tool user-manager user add customer="admin" username="0138802058" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0138802058";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0138802058";
};

# المستخدم 86: 0176930129
:do {
    /tool user-manager user add customer="admin" username="0176930129" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0176930129";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0176930129";
};

# المستخدم 87: 0137561700
:do {
    /tool user-manager user add customer="admin" username="0137561700" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0137561700";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0137561700";
};

# المستخدم 88: 0148560691
:do {
    /tool user-manager user add customer="admin" username="0148560691" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0148560691";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0148560691";
};

# المستخدم 89: 0122044024
:do {
    /tool user-manager user add customer="admin" username="0122044024" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0122044024";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0122044024";
};

# المستخدم 90: 0170808759
:do {
    /tool user-manager user add customer="admin" username="0170808759" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0170808759";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0170808759";
};

# المستخدم 91: 0127293557
:do {
    /tool user-manager user add customer="admin" username="0127293557" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0127293557";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0127293557";
};

# المستخدم 92: 0150961777
:do {
    /tool user-manager user add customer="admin" username="0150961777" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0150961777";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0150961777";
};

# المستخدم 93: 0103102348
:do {
    /tool user-manager user add customer="admin" username="0103102348" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0103102348";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0103102348";
};

# المستخدم 94: 0168674529
:do {
    /tool user-manager user add customer="admin" username="0168674529" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0168674529";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0168674529";
};

# المستخدم 95: 0144865402
:do {
    /tool user-manager user add customer="admin" username="0144865402" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0144865402";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0144865402";
};

# المستخدم 96: 0156325181
:do {
    /tool user-manager user add customer="admin" username="0156325181" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0156325181";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0156325181";
};

# المستخدم 97: 0185301426
:do {
    /tool user-manager user add customer="admin" username="0185301426" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0185301426";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0185301426";
};

# المستخدم 98: 0184471129
:do {
    /tool user-manager user add customer="admin" username="0184471129" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0184471129";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0184471129";
};

# المستخدم 99: 0157252609
:do {
    /tool user-manager user add customer="admin" username="0157252609" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0157252609";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0157252609";
};

# المستخدم 100: 0128493599
:do {
    /tool user-manager user add customer="admin" username="0128493599" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0128493599";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0128493599";
};

# المستخدم 101: 0165086982
:do {
    /tool user-manager user add customer="admin" username="0165086982" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0165086982";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0165086982";
};

# المستخدم 102: 0155235044
:do {
    /tool user-manager user add customer="admin" username="0155235044" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0155235044";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0155235044";
};

# المستخدم 103: 0101994820
:do {
    /tool user-manager user add customer="admin" username="0101994820" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0101994820";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0101994820";
};

# المستخدم 104: 0162503343
:do {
    /tool user-manager user add customer="admin" username="0162503343" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0162503343";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0162503343";
};

# المستخدم 105: 0198096103
:do {
    /tool user-manager user add customer="admin" username="0198096103" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0198096103";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0198096103";
};

# المستخدم 106: 0127428099
:do {
    /tool user-manager user add customer="admin" username="0127428099" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0127428099";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0127428099";
};

# المستخدم 107: 0166908886
:do {
    /tool user-manager user add customer="admin" username="0166908886" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0166908886";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0166908886";
};

# المستخدم 108: 0112609864
:do {
    /tool user-manager user add customer="admin" username="0112609864" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0112609864";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0112609864";
};

# المستخدم 109: 0106458261
:do {
    /tool user-manager user add customer="admin" username="0106458261" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0106458261";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0106458261";
};

# المستخدم 110: 0166430150
:do {
    /tool user-manager user add customer="admin" username="0166430150" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0166430150";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0166430150";
};

# المستخدم 111: 0102775096
:do {
    /tool user-manager user add customer="admin" username="0102775096" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0102775096";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0102775096";
};

# المستخدم 112: 0170633099
:do {
    /tool user-manager user add customer="admin" username="0170633099" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0170633099";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0170633099";
};

# المستخدم 113: 0118026258
:do {
    /tool user-manager user add customer="admin" username="0118026258" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0118026258";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0118026258";
};

# المستخدم 114: 0179052530
:do {
    /tool user-manager user add customer="admin" username="0179052530" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0179052530";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0179052530";
};

# المستخدم 115: 0139382296
:do {
    /tool user-manager user add customer="admin" username="0139382296" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0139382296";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0139382296";
};

# المستخدم 116: 0108541185
:do {
    /tool user-manager user add customer="admin" username="0108541185" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0108541185";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0108541185";
};

# المستخدم 117: 0150045968
:do {
    /tool user-manager user add customer="admin" username="0150045968" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0150045968";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0150045968";
};

# المستخدم 118: 0142016771
:do {
    /tool user-manager user add customer="admin" username="0142016771" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0142016771";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0142016771";
};

# المستخدم 119: 0108411040
:do {
    /tool user-manager user add customer="admin" username="0108411040" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0108411040";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0108411040";
};

# المستخدم 120: 0130493430
:do {
    /tool user-manager user add customer="admin" username="0130493430" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0130493430";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0130493430";
};

# المستخدم 121: 0100813927
:do {
    /tool user-manager user add customer="admin" username="0100813927" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0100813927";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0100813927";
};

# المستخدم 122: 0145001554
:do {
    /tool user-manager user add customer="admin" username="0145001554" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0145001554";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0145001554";
};

# المستخدم 123: 0116284912
:do {
    /tool user-manager user add customer="admin" username="0116284912" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0116284912";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0116284912";
};

# المستخدم 124: 0198972156
:do {
    /tool user-manager user add customer="admin" username="0198972156" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0198972156";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0198972156";
};

# المستخدم 125: 0100291516
:do {
    /tool user-manager user add customer="admin" username="0100291516" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0100291516";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0100291516";
};

# المستخدم 126: 0152396221
:do {
    /tool user-manager user add customer="admin" username="0152396221" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0152396221";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0152396221";
};

# المستخدم 127: 0128191832
:do {
    /tool user-manager user add customer="admin" username="0128191832" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0128191832";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0128191832";
};

# المستخدم 128: 0132844607
:do {
    /tool user-manager user add customer="admin" username="0132844607" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0132844607";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0132844607";
};

# المستخدم 129: 0181519873
:do {
    /tool user-manager user add customer="admin" username="0181519873" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0181519873";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0181519873";
};

# المستخدم 130: 0100133480
:do {
    /tool user-manager user add customer="admin" username="0100133480" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0100133480";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0100133480";
};

# المستخدم 131: 0174192570
:do {
    /tool user-manager user add customer="admin" username="0174192570" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0174192570";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0174192570";
};

# المستخدم 132: 0108209957
:do {
    /tool user-manager user add customer="admin" username="0108209957" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0108209957";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0108209957";
};

# المستخدم 133: 0123517443
:do {
    /tool user-manager user add customer="admin" username="0123517443" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0123517443";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0123517443";
};

# المستخدم 134: 0163885483
:do {
    /tool user-manager user add customer="admin" username="0163885483" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0163885483";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0163885483";
};

# المستخدم 135: 0192395159
:do {
    /tool user-manager user add customer="admin" username="0192395159" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0192395159";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0192395159";
};

# المستخدم 136: 0135304054
:do {
    /tool user-manager user add customer="admin" username="0135304054" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0135304054";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0135304054";
};

# المستخدم 137: 0198192768
:do {
    /tool user-manager user add customer="admin" username="0198192768" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0198192768";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0198192768";
};

# المستخدم 138: 0141768049
:do {
    /tool user-manager user add customer="admin" username="0141768049" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0141768049";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0141768049";
};

# المستخدم 139: 0181172519
:do {
    /tool user-manager user add customer="admin" username="0181172519" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0181172519";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0181172519";
};

# المستخدم 140: 0196324855
:do {
    /tool user-manager user add customer="admin" username="0196324855" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0196324855";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0196324855";
};

# المستخدم 141: 0146652173
:do {
    /tool user-manager user add customer="admin" username="0146652173" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0146652173";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0146652173";
};

# المستخدم 142: 0112973609
:do {
    /tool user-manager user add customer="admin" username="0112973609" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0112973609";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0112973609";
};

# المستخدم 143: 0116503244
:do {
    /tool user-manager user add customer="admin" username="0116503244" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0116503244";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0116503244";
};

# المستخدم 144: 0188866562
:do {
    /tool user-manager user add customer="admin" username="0188866562" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0188866562";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0188866562";
};

# المستخدم 145: 0133993086
:do {
    /tool user-manager user add customer="admin" username="0133993086" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0133993086";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0133993086";
};

# المستخدم 146: 0138443182
:do {
    /tool user-manager user add customer="admin" username="0138443182" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0138443182";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0138443182";
};

# المستخدم 147: 0176131942
:do {
    /tool user-manager user add customer="admin" username="0176131942" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0176131942";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0176131942";
};

# المستخدم 148: 0162967363
:do {
    /tool user-manager user add customer="admin" username="0162967363" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0162967363";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0162967363";
};

# المستخدم 149: 0157948654
:do {
    /tool user-manager user add customer="admin" username="0157948654" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0157948654";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0157948654";
};

# المستخدم 150: 0177496687
:do {
    /tool user-manager user add customer="admin" username="0177496687" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0177496687";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0177496687";
};

# المستخدم 151: 0128332902
:do {
    /tool user-manager user add customer="admin" username="0128332902" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0128332902";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0128332902";
};

# المستخدم 152: 0137508251
:do {
    /tool user-manager user add customer="admin" username="0137508251" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0137508251";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0137508251";
};

# المستخدم 153: 0167765157
:do {
    /tool user-manager user add customer="admin" username="0167765157" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0167765157";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0167765157";
};

# المستخدم 154: 0165704176
:do {
    /tool user-manager user add customer="admin" username="0165704176" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0165704176";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0165704176";
};

# المستخدم 155: 0119546136
:do {
    /tool user-manager user add customer="admin" username="0119546136" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0119546136";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0119546136";
};

# المستخدم 156: 0178440926
:do {
    /tool user-manager user add customer="admin" username="0178440926" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0178440926";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0178440926";
};

# المستخدم 157: 0131182200
:do {
    /tool user-manager user add customer="admin" username="0131182200" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0131182200";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0131182200";
};

# المستخدم 158: 0174997405
:do {
    /tool user-manager user add customer="admin" username="0174997405" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0174997405";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0174997405";
};

# المستخدم 159: 0137871716
:do {
    /tool user-manager user add customer="admin" username="0137871716" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0137871716";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0137871716";
};

# المستخدم 160: 0171557896
:do {
    /tool user-manager user add customer="admin" username="0171557896" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0171557896";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0171557896";
};

# المستخدم 161: 0144728463
:do {
    /tool user-manager user add customer="admin" username="0144728463" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0144728463";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0144728463";
};

# المستخدم 162: 0135281712
:do {
    /tool user-manager user add customer="admin" username="0135281712" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0135281712";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0135281712";
};

# المستخدم 163: 0152031084
:do {
    /tool user-manager user add customer="admin" username="0152031084" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0152031084";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0152031084";
};

# المستخدم 164: 0129298622
:do {
    /tool user-manager user add customer="admin" username="0129298622" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0129298622";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0129298622";
};

# المستخدم 165: 0104362414
:do {
    /tool user-manager user add customer="admin" username="0104362414" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0104362414";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0104362414";
};

# المستخدم 166: 0144609439
:do {
    /tool user-manager user add customer="admin" username="0144609439" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0144609439";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0144609439";
};

# المستخدم 167: 0169654883
:do {
    /tool user-manager user add customer="admin" username="0169654883" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0169654883";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0169654883";
};

# المستخدم 168: 0138769913
:do {
    /tool user-manager user add customer="admin" username="0138769913" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0138769913";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0138769913";
};

# المستخدم 169: 0142798463
:do {
    /tool user-manager user add customer="admin" username="0142798463" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0142798463";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0142798463";
};

# المستخدم 170: 0151009155
:do {
    /tool user-manager user add customer="admin" username="0151009155" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0151009155";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0151009155";
};

# المستخدم 171: 0126015633
:do {
    /tool user-manager user add customer="admin" username="0126015633" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0126015633";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0126015633";
};

# المستخدم 172: 0123456063
:do {
    /tool user-manager user add customer="admin" username="0123456063" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0123456063";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0123456063";
};

# المستخدم 173: 0152981497
:do {
    /tool user-manager user add customer="admin" username="0152981497" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0152981497";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0152981497";
};

# المستخدم 174: 0182545061
:do {
    /tool user-manager user add customer="admin" username="0182545061" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0182545061";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0182545061";
};

# المستخدم 175: 0108185342
:do {
    /tool user-manager user add customer="admin" username="0108185342" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0108185342";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0108185342";
};

# المستخدم 176: 0147087486
:do {
    /tool user-manager user add customer="admin" username="0147087486" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0147087486";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0147087486";
};

# المستخدم 177: 0147563524
:do {
    /tool user-manager user add customer="admin" username="0147563524" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0147563524";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0147563524";
};

# المستخدم 178: 0159785971
:do {
    /tool user-manager user add customer="admin" username="0159785971" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0159785971";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0159785971";
};

# المستخدم 179: 0118060650
:do {
    /tool user-manager user add customer="admin" username="0118060650" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0118060650";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0118060650";
};

# المستخدم 180: 0185771989
:do {
    /tool user-manager user add customer="admin" username="0185771989" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0185771989";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0185771989";
};

# المستخدم 181: 0192787997
:do {
    /tool user-manager user add customer="admin" username="0192787997" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0192787997";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0192787997";
};

# المستخدم 182: 0198404193
:do {
    /tool user-manager user add customer="admin" username="0198404193" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0198404193";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0198404193";
};

# المستخدم 183: 0107791115
:do {
    /tool user-manager user add customer="admin" username="0107791115" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0107791115";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0107791115";
};

# المستخدم 184: 0120008728
:do {
    /tool user-manager user add customer="admin" username="0120008728" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0120008728";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0120008728";
};

# المستخدم 185: 0162487479
:do {
    /tool user-manager user add customer="admin" username="0162487479" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0162487479";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0162487479";
};

# المستخدم 186: 0103711088
:do {
    /tool user-manager user add customer="admin" username="0103711088" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0103711088";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0103711088";
};

# المستخدم 187: 0172937732
:do {
    /tool user-manager user add customer="admin" username="0172937732" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0172937732";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0172937732";
};

# المستخدم 188: 0130109250
:do {
    /tool user-manager user add customer="admin" username="0130109250" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0130109250";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0130109250";
};

# المستخدم 189: 0161840760
:do {
    /tool user-manager user add customer="admin" username="0161840760" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0161840760";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0161840760";
};

# المستخدم 190: 0117489850
:do {
    /tool user-manager user add customer="admin" username="0117489850" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0117489850";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0117489850";
};

# المستخدم 191: 0142214593
:do {
    /tool user-manager user add customer="admin" username="0142214593" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0142214593";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0142214593";
};

# المستخدم 192: 0139052789
:do {
    /tool user-manager user add customer="admin" username="0139052789" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0139052789";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0139052789";
};

# المستخدم 193: 0183284780
:do {
    /tool user-manager user add customer="admin" username="0183284780" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0183284780";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0183284780";
};

# المستخدم 194: 0113550866
:do {
    /tool user-manager user add customer="admin" username="0113550866" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0113550866";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0113550866";
};

# المستخدم 195: 0123484481
:do {
    /tool user-manager user add customer="admin" username="0123484481" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0123484481";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0123484481";
};

# المستخدم 196: 0103784176
:do {
    /tool user-manager user add customer="admin" username="0103784176" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0103784176";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0103784176";
};

# المستخدم 197: 0112080513
:do {
    /tool user-manager user add customer="admin" username="0112080513" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0112080513";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0112080513";
};

# المستخدم 198: 0193329887
:do {
    /tool user-manager user add customer="admin" username="0193329887" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0193329887";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0193329887";
};

# المستخدم 199: 0106627997
:do {
    /tool user-manager user add customer="admin" username="0106627997" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0106627997";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0106627997";
};

# المستخدم 200: 0171035463
:do {
    /tool user-manager user add customer="admin" username="0171035463" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0171035463";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0171035463";
};


:put "📊 تم الانتهاء من النسخة الاحتياطية";
:put "✅ نجح: $success كرت";
:put "❌ فشل: $errors كرت";
:put "📈 الإجمالي: $total كرت";

:put "🎉 تم استعادة جميع كروت القالب: 10";
:put "💡 النسخة الاحتياطية مكتملة بنجاح!";
