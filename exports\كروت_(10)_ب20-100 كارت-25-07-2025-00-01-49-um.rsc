# MikroTik Script - نسخة احتياطية للكروت
# تم الإنشاء: 2025-07-25 00:01:50
# القالب: 10
# النظام: user_manager
# عدد الكروت: 100
# تم إنشاؤه بواسطة: مولد كروت MikroTik

:put "🚀 بدء تنفيذ النسخة الاحتياطية للكروت";
:put "📋 القالب: 10";
:put "🎯 النظام: user_manager";
:put "📊 عدد الكروت: 100";

:local success 0;
:local errors 0;
:local total 100;

:put "⏳ بدء إضافة الكروت...";

# إضافة مستخدمي User Manager
:put "👤 إضافة 100 مستخدم User Manager...";

# المستخدم 1: 0145269233
:do {
    /tool user-manager user add customer="admin" username="0145269233" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0145269233";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0145269233";
};

# المستخدم 2: 0105502430
:do {
    /tool user-manager user add customer="admin" username="0105502430" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0105502430";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0105502430";
};

# المستخدم 3: 0132937416
:do {
    /tool user-manager user add customer="admin" username="0132937416" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0132937416";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0132937416";
};

# المستخدم 4: 0114042338
:do {
    /tool user-manager user add customer="admin" username="0114042338" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0114042338";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0114042338";
};

# المستخدم 5: 0154467518
:do {
    /tool user-manager user add customer="admin" username="0154467518" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0154467518";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0154467518";
};

# المستخدم 6: 0191396750
:do {
    /tool user-manager user add customer="admin" username="0191396750" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0191396750";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0191396750";
};

# المستخدم 7: 0160091328
:do {
    /tool user-manager user add customer="admin" username="0160091328" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0160091328";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0160091328";
};

# المستخدم 8: 0116750455
:do {
    /tool user-manager user add customer="admin" username="0116750455" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0116750455";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0116750455";
};

# المستخدم 9: 0116830447
:do {
    /tool user-manager user add customer="admin" username="0116830447" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0116830447";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0116830447";
};

# المستخدم 10: 0116132510
:do {
    /tool user-manager user add customer="admin" username="0116132510" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0116132510";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0116132510";
};

# المستخدم 11: 0168215227
:do {
    /tool user-manager user add customer="admin" username="0168215227" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0168215227";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0168215227";
};

# المستخدم 12: 0110310320
:do {
    /tool user-manager user add customer="admin" username="0110310320" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0110310320";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0110310320";
};

# المستخدم 13: 0147553688
:do {
    /tool user-manager user add customer="admin" username="0147553688" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0147553688";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0147553688";
};

# المستخدم 14: 0167879165
:do {
    /tool user-manager user add customer="admin" username="0167879165" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0167879165";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0167879165";
};

# المستخدم 15: 0125096389
:do {
    /tool user-manager user add customer="admin" username="0125096389" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0125096389";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0125096389";
};

# المستخدم 16: 0104647117
:do {
    /tool user-manager user add customer="admin" username="0104647117" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0104647117";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0104647117";
};

# المستخدم 17: 0117307390
:do {
    /tool user-manager user add customer="admin" username="0117307390" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0117307390";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0117307390";
};

# المستخدم 18: 0169187291
:do {
    /tool user-manager user add customer="admin" username="0169187291" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0169187291";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0169187291";
};

# المستخدم 19: 0153413632
:do {
    /tool user-manager user add customer="admin" username="0153413632" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0153413632";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0153413632";
};

# المستخدم 20: 0159931083
:do {
    /tool user-manager user add customer="admin" username="0159931083" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0159931083";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0159931083";
};

# المستخدم 21: 0124010224
:do {
    /tool user-manager user add customer="admin" username="0124010224" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0124010224";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0124010224";
};

# المستخدم 22: 0162663782
:do {
    /tool user-manager user add customer="admin" username="0162663782" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0162663782";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0162663782";
};

# المستخدم 23: 0182248994
:do {
    /tool user-manager user add customer="admin" username="0182248994" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0182248994";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0182248994";
};

# المستخدم 24: 0185964449
:do {
    /tool user-manager user add customer="admin" username="0185964449" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0185964449";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0185964449";
};

# المستخدم 25: 0144815722
:do {
    /tool user-manager user add customer="admin" username="0144815722" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0144815722";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0144815722";
};

# المستخدم 26: 0109255829
:do {
    /tool user-manager user add customer="admin" username="0109255829" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0109255829";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0109255829";
};

# المستخدم 27: 0107540207
:do {
    /tool user-manager user add customer="admin" username="0107540207" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0107540207";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0107540207";
};

# المستخدم 28: 0172929224
:do {
    /tool user-manager user add customer="admin" username="0172929224" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0172929224";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0172929224";
};

# المستخدم 29: 0124115123
:do {
    /tool user-manager user add customer="admin" username="0124115123" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0124115123";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0124115123";
};

# المستخدم 30: 0137940957
:do {
    /tool user-manager user add customer="admin" username="0137940957" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0137940957";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0137940957";
};

# المستخدم 31: 0131967694
:do {
    /tool user-manager user add customer="admin" username="0131967694" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0131967694";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0131967694";
};

# المستخدم 32: 0178524284
:do {
    /tool user-manager user add customer="admin" username="0178524284" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0178524284";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0178524284";
};

# المستخدم 33: 0147827492
:do {
    /tool user-manager user add customer="admin" username="0147827492" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0147827492";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0147827492";
};

# المستخدم 34: 0160596448
:do {
    /tool user-manager user add customer="admin" username="0160596448" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0160596448";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0160596448";
};

# المستخدم 35: 0138906361
:do {
    /tool user-manager user add customer="admin" username="0138906361" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0138906361";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0138906361";
};

# المستخدم 36: 0133414913
:do {
    /tool user-manager user add customer="admin" username="0133414913" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0133414913";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0133414913";
};

# المستخدم 37: 0198892757
:do {
    /tool user-manager user add customer="admin" username="0198892757" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0198892757";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0198892757";
};

# المستخدم 38: 0192626197
:do {
    /tool user-manager user add customer="admin" username="0192626197" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0192626197";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0192626197";
};

# المستخدم 39: 0165967105
:do {
    /tool user-manager user add customer="admin" username="0165967105" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0165967105";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0165967105";
};

# المستخدم 40: 0121107589
:do {
    /tool user-manager user add customer="admin" username="0121107589" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0121107589";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0121107589";
};

# المستخدم 41: 0144718933
:do {
    /tool user-manager user add customer="admin" username="0144718933" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0144718933";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0144718933";
};

# المستخدم 42: 0187792268
:do {
    /tool user-manager user add customer="admin" username="0187792268" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0187792268";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0187792268";
};

# المستخدم 43: 0126262589
:do {
    /tool user-manager user add customer="admin" username="0126262589" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0126262589";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0126262589";
};

# المستخدم 44: 0144595984
:do {
    /tool user-manager user add customer="admin" username="0144595984" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0144595984";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0144595984";
};

# المستخدم 45: 0189823858
:do {
    /tool user-manager user add customer="admin" username="0189823858" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0189823858";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0189823858";
};

# المستخدم 46: 0163065250
:do {
    /tool user-manager user add customer="admin" username="0163065250" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0163065250";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0163065250";
};

# المستخدم 47: 0189199851
:do {
    /tool user-manager user add customer="admin" username="0189199851" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0189199851";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0189199851";
};

# المستخدم 48: 0111461551
:do {
    /tool user-manager user add customer="admin" username="0111461551" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0111461551";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0111461551";
};

# المستخدم 49: 0153990166
:do {
    /tool user-manager user add customer="admin" username="0153990166" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0153990166";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0153990166";
};

# المستخدم 50: 0176047093
:do {
    /tool user-manager user add customer="admin" username="0176047093" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0176047093";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0176047093";
};

# المستخدم 51: 0145309003
:do {
    /tool user-manager user add customer="admin" username="0145309003" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0145309003";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0145309003";
};

# المستخدم 52: 0173219735
:do {
    /tool user-manager user add customer="admin" username="0173219735" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0173219735";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0173219735";
};

# المستخدم 53: 0180133319
:do {
    /tool user-manager user add customer="admin" username="0180133319" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0180133319";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0180133319";
};

# المستخدم 54: 0132436706
:do {
    /tool user-manager user add customer="admin" username="0132436706" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0132436706";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0132436706";
};

# المستخدم 55: 0100975338
:do {
    /tool user-manager user add customer="admin" username="0100975338" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0100975338";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0100975338";
};

# المستخدم 56: 0117710085
:do {
    /tool user-manager user add customer="admin" username="0117710085" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0117710085";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0117710085";
};

# المستخدم 57: 0177801237
:do {
    /tool user-manager user add customer="admin" username="0177801237" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0177801237";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0177801237";
};

# المستخدم 58: 0190752964
:do {
    /tool user-manager user add customer="admin" username="0190752964" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0190752964";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0190752964";
};

# المستخدم 59: 0178551348
:do {
    /tool user-manager user add customer="admin" username="0178551348" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0178551348";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0178551348";
};

# المستخدم 60: 0115540523
:do {
    /tool user-manager user add customer="admin" username="0115540523" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0115540523";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0115540523";
};

# المستخدم 61: 0190974213
:do {
    /tool user-manager user add customer="admin" username="0190974213" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0190974213";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0190974213";
};

# المستخدم 62: 0160776331
:do {
    /tool user-manager user add customer="admin" username="0160776331" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0160776331";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0160776331";
};

# المستخدم 63: 0108232163
:do {
    /tool user-manager user add customer="admin" username="0108232163" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0108232163";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0108232163";
};

# المستخدم 64: 0131629201
:do {
    /tool user-manager user add customer="admin" username="0131629201" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0131629201";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0131629201";
};

# المستخدم 65: 0102701597
:do {
    /tool user-manager user add customer="admin" username="0102701597" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0102701597";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0102701597";
};

# المستخدم 66: 0191790773
:do {
    /tool user-manager user add customer="admin" username="0191790773" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0191790773";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0191790773";
};

# المستخدم 67: 0118903715
:do {
    /tool user-manager user add customer="admin" username="0118903715" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0118903715";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0118903715";
};

# المستخدم 68: 0138417575
:do {
    /tool user-manager user add customer="admin" username="0138417575" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0138417575";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0138417575";
};

# المستخدم 69: 0177054413
:do {
    /tool user-manager user add customer="admin" username="0177054413" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0177054413";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0177054413";
};

# المستخدم 70: 0120410784
:do {
    /tool user-manager user add customer="admin" username="0120410784" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0120410784";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0120410784";
};

# المستخدم 71: 0180863704
:do {
    /tool user-manager user add customer="admin" username="0180863704" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0180863704";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0180863704";
};

# المستخدم 72: 0161317045
:do {
    /tool user-manager user add customer="admin" username="0161317045" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0161317045";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0161317045";
};

# المستخدم 73: 0177156351
:do {
    /tool user-manager user add customer="admin" username="0177156351" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0177156351";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0177156351";
};

# المستخدم 74: 0194921059
:do {
    /tool user-manager user add customer="admin" username="0194921059" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0194921059";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0194921059";
};

# المستخدم 75: 0131760407
:do {
    /tool user-manager user add customer="admin" username="0131760407" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0131760407";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0131760407";
};

# المستخدم 76: 0117131002
:do {
    /tool user-manager user add customer="admin" username="0117131002" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0117131002";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0117131002";
};

# المستخدم 77: 0158338264
:do {
    /tool user-manager user add customer="admin" username="0158338264" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0158338264";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0158338264";
};

# المستخدم 78: 0185434772
:do {
    /tool user-manager user add customer="admin" username="0185434772" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0185434772";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0185434772";
};

# المستخدم 79: 0122319240
:do {
    /tool user-manager user add customer="admin" username="0122319240" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0122319240";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0122319240";
};

# المستخدم 80: 0104404441
:do {
    /tool user-manager user add customer="admin" username="0104404441" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0104404441";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0104404441";
};

# المستخدم 81: 0120050615
:do {
    /tool user-manager user add customer="admin" username="0120050615" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0120050615";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0120050615";
};

# المستخدم 82: 0108197476
:do {
    /tool user-manager user add customer="admin" username="0108197476" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0108197476";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0108197476";
};

# المستخدم 83: 0168188790
:do {
    /tool user-manager user add customer="admin" username="0168188790" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0168188790";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0168188790";
};

# المستخدم 84: 0126445891
:do {
    /tool user-manager user add customer="admin" username="0126445891" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0126445891";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0126445891";
};

# المستخدم 85: 0198618448
:do {
    /tool user-manager user add customer="admin" username="0198618448" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0198618448";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0198618448";
};

# المستخدم 86: 0173489590
:do {
    /tool user-manager user add customer="admin" username="0173489590" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0173489590";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0173489590";
};

# المستخدم 87: 0135379308
:do {
    /tool user-manager user add customer="admin" username="0135379308" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0135379308";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0135379308";
};

# المستخدم 88: 0143297712
:do {
    /tool user-manager user add customer="admin" username="0143297712" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0143297712";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0143297712";
};

# المستخدم 89: 0115616307
:do {
    /tool user-manager user add customer="admin" username="0115616307" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0115616307";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0115616307";
};

# المستخدم 90: 0102578173
:do {
    /tool user-manager user add customer="admin" username="0102578173" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0102578173";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0102578173";
};

# المستخدم 91: 0116438213
:do {
    /tool user-manager user add customer="admin" username="0116438213" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0116438213";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0116438213";
};

# المستخدم 92: 0111863836
:do {
    /tool user-manager user add customer="admin" username="0111863836" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0111863836";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0111863836";
};

# المستخدم 93: 0131554392
:do {
    /tool user-manager user add customer="admin" username="0131554392" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0131554392";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0131554392";
};

# المستخدم 94: 0121084327
:do {
    /tool user-manager user add customer="admin" username="0121084327" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0121084327";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0121084327";
};

# المستخدم 95: 0110715302
:do {
    /tool user-manager user add customer="admin" username="0110715302" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0110715302";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0110715302";
};

# المستخدم 96: 0184558336
:do {
    /tool user-manager user add customer="admin" username="0184558336" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0184558336";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0184558336";
};

# المستخدم 97: 0186557190
:do {
    /tool user-manager user add customer="admin" username="0186557190" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0186557190";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0186557190";
};

# المستخدم 98: 0165006460
:do {
    /tool user-manager user add customer="admin" username="0165006460" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0165006460";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0165006460";
};

# المستخدم 99: 0123061107
:do {
    /tool user-manager user add customer="admin" username="0123061107" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0123061107";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0123061107";
};

# المستخدم 100: 0152085738
:do {
    /tool user-manager user add customer="admin" username="0152085738" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0152085738";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0152085738";
};


:put "📊 تم الانتهاء من النسخة الاحتياطية";
:put "✅ نجح: $success كرت";
:put "❌ فشل: $errors كرت";
:put "📈 الإجمالي: $total كرت";

:put "🎉 تم استعادة جميع كروت القالب: 10";
:put "💡 النسخة الاحتياطية مكتملة بنجاح!";
