# MikroTik Script - نسخة احتياطية للكروت
# تم الإنشاء: 2025-07-25 04:15:32
# القالب: 10
# النظام: user_manager
# عدد الكروت: 200
# تم إنشاؤه بواسطة: مولد كروت MikroTik

:put "🚀 بدء تنفيذ النسخة الاحتياطية للكروت";
:put "📋 القالب: 10";
:put "🎯 النظام: user_manager";
:put "📊 عدد الكروت: 200";

:local success 0;
:local errors 0;
:local total 200;

:put "⏳ بدء إضافة الكروت...";

# إضافة مستخدمي User Manager
:put "👤 إضافة 200 مستخدم User Manager...";

# المستخدم 1: 0153762373
:do {
    /tool user-manager user add customer="admin" username="0153762373" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0153762373";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0153762373";
};

# المستخدم 2: 0105512411
:do {
    /tool user-manager user add customer="admin" username="0105512411" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0105512411";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0105512411";
};

# المستخدم 3: 0189530787
:do {
    /tool user-manager user add customer="admin" username="0189530787" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0189530787";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0189530787";
};

# المستخدم 4: 0145035551
:do {
    /tool user-manager user add customer="admin" username="0145035551" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0145035551";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0145035551";
};

# المستخدم 5: 0129901888
:do {
    /tool user-manager user add customer="admin" username="0129901888" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0129901888";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0129901888";
};

# المستخدم 6: 0163696046
:do {
    /tool user-manager user add customer="admin" username="0163696046" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0163696046";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0163696046";
};

# المستخدم 7: 0104727096
:do {
    /tool user-manager user add customer="admin" username="0104727096" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0104727096";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0104727096";
};

# المستخدم 8: 0155253039
:do {
    /tool user-manager user add customer="admin" username="0155253039" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0155253039";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0155253039";
};

# المستخدم 9: 0185508705
:do {
    /tool user-manager user add customer="admin" username="0185508705" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0185508705";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0185508705";
};

# المستخدم 10: 0196792405
:do {
    /tool user-manager user add customer="admin" username="0196792405" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0196792405";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0196792405";
};

# المستخدم 11: 0165836588
:do {
    /tool user-manager user add customer="admin" username="0165836588" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0165836588";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0165836588";
};

# المستخدم 12: 0100645480
:do {
    /tool user-manager user add customer="admin" username="0100645480" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0100645480";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0100645480";
};

# المستخدم 13: 0110741261
:do {
    /tool user-manager user add customer="admin" username="0110741261" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0110741261";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0110741261";
};

# المستخدم 14: 0105466223
:do {
    /tool user-manager user add customer="admin" username="0105466223" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0105466223";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0105466223";
};

# المستخدم 15: 0122193397
:do {
    /tool user-manager user add customer="admin" username="0122193397" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0122193397";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0122193397";
};

# المستخدم 16: 0162374907
:do {
    /tool user-manager user add customer="admin" username="0162374907" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0162374907";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0162374907";
};

# المستخدم 17: 0198091834
:do {
    /tool user-manager user add customer="admin" username="0198091834" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0198091834";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0198091834";
};

# المستخدم 18: 0161846941
:do {
    /tool user-manager user add customer="admin" username="0161846941" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0161846941";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0161846941";
};

# المستخدم 19: 0136323230
:do {
    /tool user-manager user add customer="admin" username="0136323230" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0136323230";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0136323230";
};

# المستخدم 20: 0185449170
:do {
    /tool user-manager user add customer="admin" username="0185449170" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0185449170";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0185449170";
};

# المستخدم 21: 0166973098
:do {
    /tool user-manager user add customer="admin" username="0166973098" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0166973098";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0166973098";
};

# المستخدم 22: 0156018943
:do {
    /tool user-manager user add customer="admin" username="0156018943" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0156018943";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0156018943";
};

# المستخدم 23: 0136235966
:do {
    /tool user-manager user add customer="admin" username="0136235966" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0136235966";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0136235966";
};

# المستخدم 24: 0117601076
:do {
    /tool user-manager user add customer="admin" username="0117601076" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0117601076";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0117601076";
};

# المستخدم 25: 0106549115
:do {
    /tool user-manager user add customer="admin" username="0106549115" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0106549115";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0106549115";
};

# المستخدم 26: 0111456977
:do {
    /tool user-manager user add customer="admin" username="0111456977" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0111456977";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0111456977";
};

# المستخدم 27: 0115130373
:do {
    /tool user-manager user add customer="admin" username="0115130373" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0115130373";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0115130373";
};

# المستخدم 28: 0117449552
:do {
    /tool user-manager user add customer="admin" username="0117449552" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0117449552";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0117449552";
};

# المستخدم 29: 0139058952
:do {
    /tool user-manager user add customer="admin" username="0139058952" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0139058952";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0139058952";
};

# المستخدم 30: 0127874125
:do {
    /tool user-manager user add customer="admin" username="0127874125" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0127874125";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0127874125";
};

# المستخدم 31: 0196132213
:do {
    /tool user-manager user add customer="admin" username="0196132213" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0196132213";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0196132213";
};

# المستخدم 32: 0102637205
:do {
    /tool user-manager user add customer="admin" username="0102637205" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0102637205";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0102637205";
};

# المستخدم 33: 0142494951
:do {
    /tool user-manager user add customer="admin" username="0142494951" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0142494951";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0142494951";
};

# المستخدم 34: 0172080463
:do {
    /tool user-manager user add customer="admin" username="0172080463" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0172080463";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0172080463";
};

# المستخدم 35: 0133979038
:do {
    /tool user-manager user add customer="admin" username="0133979038" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0133979038";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0133979038";
};

# المستخدم 36: 0180752894
:do {
    /tool user-manager user add customer="admin" username="0180752894" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0180752894";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0180752894";
};

# المستخدم 37: 0190715903
:do {
    /tool user-manager user add customer="admin" username="0190715903" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0190715903";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0190715903";
};

# المستخدم 38: 0150988735
:do {
    /tool user-manager user add customer="admin" username="0150988735" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0150988735";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0150988735";
};

# المستخدم 39: 0191196884
:do {
    /tool user-manager user add customer="admin" username="0191196884" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0191196884";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0191196884";
};

# المستخدم 40: 0158213239
:do {
    /tool user-manager user add customer="admin" username="0158213239" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0158213239";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0158213239";
};

# المستخدم 41: 0167109676
:do {
    /tool user-manager user add customer="admin" username="0167109676" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0167109676";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0167109676";
};

# المستخدم 42: 0190814966
:do {
    /tool user-manager user add customer="admin" username="0190814966" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0190814966";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0190814966";
};

# المستخدم 43: 0131006449
:do {
    /tool user-manager user add customer="admin" username="0131006449" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0131006449";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0131006449";
};

# المستخدم 44: 0161210884
:do {
    /tool user-manager user add customer="admin" username="0161210884" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0161210884";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0161210884";
};

# المستخدم 45: 0123987654
:do {
    /tool user-manager user add customer="admin" username="0123987654" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0123987654";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0123987654";
};

# المستخدم 46: 0169164007
:do {
    /tool user-manager user add customer="admin" username="0169164007" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0169164007";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0169164007";
};

# المستخدم 47: 0162453235
:do {
    /tool user-manager user add customer="admin" username="0162453235" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0162453235";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0162453235";
};

# المستخدم 48: 0151793427
:do {
    /tool user-manager user add customer="admin" username="0151793427" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0151793427";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0151793427";
};

# المستخدم 49: 0199429502
:do {
    /tool user-manager user add customer="admin" username="0199429502" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0199429502";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0199429502";
};

# المستخدم 50: 0150130279
:do {
    /tool user-manager user add customer="admin" username="0150130279" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0150130279";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0150130279";
};

# المستخدم 51: 0110865011
:do {
    /tool user-manager user add customer="admin" username="0110865011" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0110865011";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0110865011";
};

# المستخدم 52: 0195839366
:do {
    /tool user-manager user add customer="admin" username="0195839366" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0195839366";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0195839366";
};

# المستخدم 53: 0135002408
:do {
    /tool user-manager user add customer="admin" username="0135002408" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0135002408";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0135002408";
};

# المستخدم 54: 0172854688
:do {
    /tool user-manager user add customer="admin" username="0172854688" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0172854688";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0172854688";
};

# المستخدم 55: 0161694802
:do {
    /tool user-manager user add customer="admin" username="0161694802" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0161694802";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0161694802";
};

# المستخدم 56: 0101144691
:do {
    /tool user-manager user add customer="admin" username="0101144691" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0101144691";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0101144691";
};

# المستخدم 57: 0175555950
:do {
    /tool user-manager user add customer="admin" username="0175555950" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0175555950";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0175555950";
};

# المستخدم 58: 0107047238
:do {
    /tool user-manager user add customer="admin" username="0107047238" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0107047238";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0107047238";
};

# المستخدم 59: 0184527625
:do {
    /tool user-manager user add customer="admin" username="0184527625" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0184527625";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0184527625";
};

# المستخدم 60: 0107723944
:do {
    /tool user-manager user add customer="admin" username="0107723944" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0107723944";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0107723944";
};

# المستخدم 61: 0172431272
:do {
    /tool user-manager user add customer="admin" username="0172431272" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0172431272";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0172431272";
};

# المستخدم 62: 0143490472
:do {
    /tool user-manager user add customer="admin" username="0143490472" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0143490472";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0143490472";
};

# المستخدم 63: 0154324019
:do {
    /tool user-manager user add customer="admin" username="0154324019" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0154324019";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0154324019";
};

# المستخدم 64: 0110603420
:do {
    /tool user-manager user add customer="admin" username="0110603420" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0110603420";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0110603420";
};

# المستخدم 65: 0176684006
:do {
    /tool user-manager user add customer="admin" username="0176684006" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0176684006";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0176684006";
};

# المستخدم 66: 0121521320
:do {
    /tool user-manager user add customer="admin" username="0121521320" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0121521320";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0121521320";
};

# المستخدم 67: 0191906771
:do {
    /tool user-manager user add customer="admin" username="0191906771" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0191906771";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0191906771";
};

# المستخدم 68: 0158934312
:do {
    /tool user-manager user add customer="admin" username="0158934312" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0158934312";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0158934312";
};

# المستخدم 69: 0111783463
:do {
    /tool user-manager user add customer="admin" username="0111783463" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0111783463";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0111783463";
};

# المستخدم 70: 0180423233
:do {
    /tool user-manager user add customer="admin" username="0180423233" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0180423233";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0180423233";
};

# المستخدم 71: 0160857352
:do {
    /tool user-manager user add customer="admin" username="0160857352" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0160857352";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0160857352";
};

# المستخدم 72: 0100035436
:do {
    /tool user-manager user add customer="admin" username="0100035436" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0100035436";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0100035436";
};

# المستخدم 73: 0123384968
:do {
    /tool user-manager user add customer="admin" username="0123384968" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0123384968";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0123384968";
};

# المستخدم 74: 0195894840
:do {
    /tool user-manager user add customer="admin" username="0195894840" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0195894840";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0195894840";
};

# المستخدم 75: 0185292092
:do {
    /tool user-manager user add customer="admin" username="0185292092" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0185292092";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0185292092";
};

# المستخدم 76: 0120246870
:do {
    /tool user-manager user add customer="admin" username="0120246870" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0120246870";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0120246870";
};

# المستخدم 77: 0129922382
:do {
    /tool user-manager user add customer="admin" username="0129922382" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0129922382";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0129922382";
};

# المستخدم 78: 0142652350
:do {
    /tool user-manager user add customer="admin" username="0142652350" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0142652350";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0142652350";
};

# المستخدم 79: 0175292630
:do {
    /tool user-manager user add customer="admin" username="0175292630" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0175292630";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0175292630";
};

# المستخدم 80: 0170053563
:do {
    /tool user-manager user add customer="admin" username="0170053563" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0170053563";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0170053563";
};

# المستخدم 81: 0169118289
:do {
    /tool user-manager user add customer="admin" username="0169118289" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0169118289";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0169118289";
};

# المستخدم 82: 0195648263
:do {
    /tool user-manager user add customer="admin" username="0195648263" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0195648263";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0195648263";
};

# المستخدم 83: 0148815284
:do {
    /tool user-manager user add customer="admin" username="0148815284" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0148815284";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0148815284";
};

# المستخدم 84: 0187336130
:do {
    /tool user-manager user add customer="admin" username="0187336130" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0187336130";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0187336130";
};

# المستخدم 85: 0178304492
:do {
    /tool user-manager user add customer="admin" username="0178304492" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0178304492";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0178304492";
};

# المستخدم 86: 0142844883
:do {
    /tool user-manager user add customer="admin" username="0142844883" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0142844883";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0142844883";
};

# المستخدم 87: 0113612378
:do {
    /tool user-manager user add customer="admin" username="0113612378" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0113612378";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0113612378";
};

# المستخدم 88: 0140899044
:do {
    /tool user-manager user add customer="admin" username="0140899044" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0140899044";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0140899044";
};

# المستخدم 89: 0117812277
:do {
    /tool user-manager user add customer="admin" username="0117812277" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0117812277";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0117812277";
};

# المستخدم 90: 0156092416
:do {
    /tool user-manager user add customer="admin" username="0156092416" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0156092416";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0156092416";
};

# المستخدم 91: 0109080825
:do {
    /tool user-manager user add customer="admin" username="0109080825" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0109080825";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0109080825";
};

# المستخدم 92: 0126420322
:do {
    /tool user-manager user add customer="admin" username="0126420322" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0126420322";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0126420322";
};

# المستخدم 93: 0133980866
:do {
    /tool user-manager user add customer="admin" username="0133980866" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0133980866";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0133980866";
};

# المستخدم 94: 0139027825
:do {
    /tool user-manager user add customer="admin" username="0139027825" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0139027825";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0139027825";
};

# المستخدم 95: 0142067823
:do {
    /tool user-manager user add customer="admin" username="0142067823" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0142067823";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0142067823";
};

# المستخدم 96: 0138312891
:do {
    /tool user-manager user add customer="admin" username="0138312891" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0138312891";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0138312891";
};

# المستخدم 97: 0103714852
:do {
    /tool user-manager user add customer="admin" username="0103714852" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0103714852";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0103714852";
};

# المستخدم 98: 0190551385
:do {
    /tool user-manager user add customer="admin" username="0190551385" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0190551385";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0190551385";
};

# المستخدم 99: 0178729921
:do {
    /tool user-manager user add customer="admin" username="0178729921" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0178729921";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0178729921";
};

# المستخدم 100: 0159917752
:do {
    /tool user-manager user add customer="admin" username="0159917752" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0159917752";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0159917752";
};

# المستخدم 101: 0171917639
:do {
    /tool user-manager user add customer="admin" username="0171917639" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0171917639";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0171917639";
};

# المستخدم 102: 0179452683
:do {
    /tool user-manager user add customer="admin" username="0179452683" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0179452683";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0179452683";
};

# المستخدم 103: 0169489543
:do {
    /tool user-manager user add customer="admin" username="0169489543" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0169489543";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0169489543";
};

# المستخدم 104: 0141933272
:do {
    /tool user-manager user add customer="admin" username="0141933272" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0141933272";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0141933272";
};

# المستخدم 105: 0131708820
:do {
    /tool user-manager user add customer="admin" username="0131708820" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0131708820";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0131708820";
};

# المستخدم 106: 0194192137
:do {
    /tool user-manager user add customer="admin" username="0194192137" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0194192137";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0194192137";
};

# المستخدم 107: 0178205338
:do {
    /tool user-manager user add customer="admin" username="0178205338" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0178205338";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0178205338";
};

# المستخدم 108: 0163708562
:do {
    /tool user-manager user add customer="admin" username="0163708562" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0163708562";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0163708562";
};

# المستخدم 109: 0135633272
:do {
    /tool user-manager user add customer="admin" username="0135633272" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0135633272";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0135633272";
};

# المستخدم 110: 0182401774
:do {
    /tool user-manager user add customer="admin" username="0182401774" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0182401774";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0182401774";
};

# المستخدم 111: 0103636778
:do {
    /tool user-manager user add customer="admin" username="0103636778" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0103636778";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0103636778";
};

# المستخدم 112: 0154060208
:do {
    /tool user-manager user add customer="admin" username="0154060208" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0154060208";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0154060208";
};

# المستخدم 113: 0157074935
:do {
    /tool user-manager user add customer="admin" username="0157074935" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0157074935";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0157074935";
};

# المستخدم 114: 0103033301
:do {
    /tool user-manager user add customer="admin" username="0103033301" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0103033301";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0103033301";
};

# المستخدم 115: 0135407579
:do {
    /tool user-manager user add customer="admin" username="0135407579" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0135407579";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0135407579";
};

# المستخدم 116: 0163561863
:do {
    /tool user-manager user add customer="admin" username="0163561863" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0163561863";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0163561863";
};

# المستخدم 117: 0198820709
:do {
    /tool user-manager user add customer="admin" username="0198820709" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0198820709";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0198820709";
};

# المستخدم 118: 0182648432
:do {
    /tool user-manager user add customer="admin" username="0182648432" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0182648432";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0182648432";
};

# المستخدم 119: 0183538258
:do {
    /tool user-manager user add customer="admin" username="0183538258" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0183538258";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0183538258";
};

# المستخدم 120: 0104807556
:do {
    /tool user-manager user add customer="admin" username="0104807556" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0104807556";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0104807556";
};

# المستخدم 121: 0104493173
:do {
    /tool user-manager user add customer="admin" username="0104493173" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0104493173";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0104493173";
};

# المستخدم 122: 0135655188
:do {
    /tool user-manager user add customer="admin" username="0135655188" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0135655188";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0135655188";
};

# المستخدم 123: 0159654031
:do {
    /tool user-manager user add customer="admin" username="0159654031" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0159654031";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0159654031";
};

# المستخدم 124: 0132048578
:do {
    /tool user-manager user add customer="admin" username="0132048578" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0132048578";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0132048578";
};

# المستخدم 125: 0128421404
:do {
    /tool user-manager user add customer="admin" username="0128421404" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0128421404";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0128421404";
};

# المستخدم 126: 0129055665
:do {
    /tool user-manager user add customer="admin" username="0129055665" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0129055665";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0129055665";
};

# المستخدم 127: 0162163949
:do {
    /tool user-manager user add customer="admin" username="0162163949" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0162163949";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0162163949";
};

# المستخدم 128: 0103835073
:do {
    /tool user-manager user add customer="admin" username="0103835073" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0103835073";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0103835073";
};

# المستخدم 129: 0105967081
:do {
    /tool user-manager user add customer="admin" username="0105967081" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0105967081";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0105967081";
};

# المستخدم 130: 0148335192
:do {
    /tool user-manager user add customer="admin" username="0148335192" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0148335192";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0148335192";
};

# المستخدم 131: 0173476275
:do {
    /tool user-manager user add customer="admin" username="0173476275" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0173476275";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0173476275";
};

# المستخدم 132: 0131864358
:do {
    /tool user-manager user add customer="admin" username="0131864358" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0131864358";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0131864358";
};

# المستخدم 133: 0109266925
:do {
    /tool user-manager user add customer="admin" username="0109266925" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0109266925";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0109266925";
};

# المستخدم 134: 0119963305
:do {
    /tool user-manager user add customer="admin" username="0119963305" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0119963305";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0119963305";
};

# المستخدم 135: 0173004694
:do {
    /tool user-manager user add customer="admin" username="0173004694" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0173004694";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0173004694";
};

# المستخدم 136: 0107612360
:do {
    /tool user-manager user add customer="admin" username="0107612360" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0107612360";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0107612360";
};

# المستخدم 137: 0166793161
:do {
    /tool user-manager user add customer="admin" username="0166793161" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0166793161";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0166793161";
};

# المستخدم 138: 0139352150
:do {
    /tool user-manager user add customer="admin" username="0139352150" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0139352150";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0139352150";
};

# المستخدم 139: 0103631084
:do {
    /tool user-manager user add customer="admin" username="0103631084" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0103631084";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0103631084";
};

# المستخدم 140: 0164324881
:do {
    /tool user-manager user add customer="admin" username="0164324881" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0164324881";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0164324881";
};

# المستخدم 141: 0122000169
:do {
    /tool user-manager user add customer="admin" username="0122000169" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0122000169";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0122000169";
};

# المستخدم 142: 0170469934
:do {
    /tool user-manager user add customer="admin" username="0170469934" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0170469934";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0170469934";
};

# المستخدم 143: 0130757677
:do {
    /tool user-manager user add customer="admin" username="0130757677" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0130757677";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0130757677";
};

# المستخدم 144: 0115456528
:do {
    /tool user-manager user add customer="admin" username="0115456528" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0115456528";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0115456528";
};

# المستخدم 145: 0170290426
:do {
    /tool user-manager user add customer="admin" username="0170290426" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0170290426";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0170290426";
};

# المستخدم 146: 0150972128
:do {
    /tool user-manager user add customer="admin" username="0150972128" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0150972128";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0150972128";
};

# المستخدم 147: 0108618028
:do {
    /tool user-manager user add customer="admin" username="0108618028" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0108618028";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0108618028";
};

# المستخدم 148: 0168332244
:do {
    /tool user-manager user add customer="admin" username="0168332244" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0168332244";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0168332244";
};

# المستخدم 149: 0139168153
:do {
    /tool user-manager user add customer="admin" username="0139168153" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0139168153";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0139168153";
};

# المستخدم 150: 0138910284
:do {
    /tool user-manager user add customer="admin" username="0138910284" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0138910284";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0138910284";
};

# المستخدم 151: 0197369441
:do {
    /tool user-manager user add customer="admin" username="0197369441" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0197369441";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0197369441";
};

# المستخدم 152: 0132465857
:do {
    /tool user-manager user add customer="admin" username="0132465857" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0132465857";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0132465857";
};

# المستخدم 153: 0105424576
:do {
    /tool user-manager user add customer="admin" username="0105424576" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0105424576";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0105424576";
};

# المستخدم 154: 0113679633
:do {
    /tool user-manager user add customer="admin" username="0113679633" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0113679633";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0113679633";
};

# المستخدم 155: 0187286576
:do {
    /tool user-manager user add customer="admin" username="0187286576" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0187286576";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0187286576";
};

# المستخدم 156: 0154298699
:do {
    /tool user-manager user add customer="admin" username="0154298699" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0154298699";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0154298699";
};

# المستخدم 157: 0135890054
:do {
    /tool user-manager user add customer="admin" username="0135890054" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0135890054";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0135890054";
};

# المستخدم 158: 0190446546
:do {
    /tool user-manager user add customer="admin" username="0190446546" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0190446546";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0190446546";
};

# المستخدم 159: 0115794834
:do {
    /tool user-manager user add customer="admin" username="0115794834" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0115794834";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0115794834";
};

# المستخدم 160: 0193132052
:do {
    /tool user-manager user add customer="admin" username="0193132052" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0193132052";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0193132052";
};

# المستخدم 161: 0189104935
:do {
    /tool user-manager user add customer="admin" username="0189104935" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0189104935";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0189104935";
};

# المستخدم 162: 0140930741
:do {
    /tool user-manager user add customer="admin" username="0140930741" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0140930741";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0140930741";
};

# المستخدم 163: 0154649820
:do {
    /tool user-manager user add customer="admin" username="0154649820" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0154649820";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0154649820";
};

# المستخدم 164: 0114694348
:do {
    /tool user-manager user add customer="admin" username="0114694348" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0114694348";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0114694348";
};

# المستخدم 165: 0149224757
:do {
    /tool user-manager user add customer="admin" username="0149224757" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0149224757";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0149224757";
};

# المستخدم 166: 0199799390
:do {
    /tool user-manager user add customer="admin" username="0199799390" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0199799390";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0199799390";
};

# المستخدم 167: 0107167132
:do {
    /tool user-manager user add customer="admin" username="0107167132" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0107167132";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0107167132";
};

# المستخدم 168: 0178822486
:do {
    /tool user-manager user add customer="admin" username="0178822486" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0178822486";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0178822486";
};

# المستخدم 169: 0105487492
:do {
    /tool user-manager user add customer="admin" username="0105487492" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0105487492";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0105487492";
};

# المستخدم 170: 0101910065
:do {
    /tool user-manager user add customer="admin" username="0101910065" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0101910065";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0101910065";
};

# المستخدم 171: 0151447225
:do {
    /tool user-manager user add customer="admin" username="0151447225" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0151447225";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0151447225";
};

# المستخدم 172: 0196012453
:do {
    /tool user-manager user add customer="admin" username="0196012453" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0196012453";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0196012453";
};

# المستخدم 173: 0146971070
:do {
    /tool user-manager user add customer="admin" username="0146971070" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0146971070";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0146971070";
};

# المستخدم 174: 0126043620
:do {
    /tool user-manager user add customer="admin" username="0126043620" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0126043620";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0126043620";
};

# المستخدم 175: 0166511369
:do {
    /tool user-manager user add customer="admin" username="0166511369" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0166511369";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0166511369";
};

# المستخدم 176: 0166373546
:do {
    /tool user-manager user add customer="admin" username="0166373546" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0166373546";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0166373546";
};

# المستخدم 177: 0163178315
:do {
    /tool user-manager user add customer="admin" username="0163178315" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0163178315";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0163178315";
};

# المستخدم 178: 0157033151
:do {
    /tool user-manager user add customer="admin" username="0157033151" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0157033151";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0157033151";
};

# المستخدم 179: 0151051084
:do {
    /tool user-manager user add customer="admin" username="0151051084" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0151051084";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0151051084";
};

# المستخدم 180: 0160084497
:do {
    /tool user-manager user add customer="admin" username="0160084497" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0160084497";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0160084497";
};

# المستخدم 181: 0179213220
:do {
    /tool user-manager user add customer="admin" username="0179213220" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0179213220";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0179213220";
};

# المستخدم 182: 0111909983
:do {
    /tool user-manager user add customer="admin" username="0111909983" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0111909983";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0111909983";
};

# المستخدم 183: 0195890282
:do {
    /tool user-manager user add customer="admin" username="0195890282" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0195890282";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0195890282";
};

# المستخدم 184: 0156784243
:do {
    /tool user-manager user add customer="admin" username="0156784243" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0156784243";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0156784243";
};

# المستخدم 185: 0159583306
:do {
    /tool user-manager user add customer="admin" username="0159583306" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0159583306";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0159583306";
};

# المستخدم 186: 0160486244
:do {
    /tool user-manager user add customer="admin" username="0160486244" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0160486244";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0160486244";
};

# المستخدم 187: 0120466822
:do {
    /tool user-manager user add customer="admin" username="0120466822" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0120466822";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0120466822";
};

# المستخدم 188: 0103727782
:do {
    /tool user-manager user add customer="admin" username="0103727782" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0103727782";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0103727782";
};

# المستخدم 189: 0135004461
:do {
    /tool user-manager user add customer="admin" username="0135004461" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0135004461";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0135004461";
};

# المستخدم 190: 0192382311
:do {
    /tool user-manager user add customer="admin" username="0192382311" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0192382311";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0192382311";
};

# المستخدم 191: 0130398875
:do {
    /tool user-manager user add customer="admin" username="0130398875" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0130398875";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0130398875";
};

# المستخدم 192: 0198973267
:do {
    /tool user-manager user add customer="admin" username="0198973267" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0198973267";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0198973267";
};

# المستخدم 193: 0165127481
:do {
    /tool user-manager user add customer="admin" username="0165127481" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0165127481";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0165127481";
};

# المستخدم 194: 0159061376
:do {
    /tool user-manager user add customer="admin" username="0159061376" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0159061376";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0159061376";
};

# المستخدم 195: 0147583085
:do {
    /tool user-manager user add customer="admin" username="0147583085" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0147583085";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0147583085";
};

# المستخدم 196: 0108396087
:do {
    /tool user-manager user add customer="admin" username="0108396087" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0108396087";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0108396087";
};

# المستخدم 197: 0115201667
:do {
    /tool user-manager user add customer="admin" username="0115201667" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0115201667";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0115201667";
};

# المستخدم 198: 0136923088
:do {
    /tool user-manager user add customer="admin" username="0136923088" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0136923088";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0136923088";
};

# المستخدم 199: 0121461362
:do {
    /tool user-manager user add customer="admin" username="0121461362" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0121461362";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0121461362";
};

# المستخدم 200: 0130261254
:do {
    /tool user-manager user add customer="admin" username="0130261254" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0130261254";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0130261254";
};


:put "📊 تم الانتهاء من النسخة الاحتياطية";
:put "✅ نجح: $success كرت";
:put "❌ فشل: $errors كرت";
:put "📈 الإجمالي: $total كرت";

:put "🎉 تم استعادة جميع كروت القالب: 10";
:put "💡 النسخة الاحتياطية مكتملة بنجاح!";
