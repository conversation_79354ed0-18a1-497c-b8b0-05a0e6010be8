# MikroTik Script - نسخة احتياطية للكروت
# تم الإنشاء: 2025-07-25 04:00:17
# القالب: 10
# النظام: user_manager
# عدد الكروت: 200
# تم إنشاؤه بواسطة: مولد كروت MikroTik

:put "🚀 بدء تنفيذ النسخة الاحتياطية للكروت";
:put "📋 القالب: 10";
:put "🎯 النظام: user_manager";
:put "📊 عدد الكروت: 200";

:local success 0;
:local errors 0;
:local total 200;

:put "⏳ بدء إضافة الكروت...";

# إضافة مستخدمي User Manager
:put "👤 إضافة 200 مستخدم User Manager...";

# المستخدم 1: 0176085835
:do {
    /tool user-manager user add customer="admin" username="0176085835" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0176085835";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0176085835";
};

# المستخدم 2: 0159547252
:do {
    /tool user-manager user add customer="admin" username="0159547252" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0159547252";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0159547252";
};

# المستخدم 3: 0168684307
:do {
    /tool user-manager user add customer="admin" username="0168684307" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0168684307";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0168684307";
};

# المستخدم 4: 0137761650
:do {
    /tool user-manager user add customer="admin" username="0137761650" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0137761650";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0137761650";
};

# المستخدم 5: 0180577251
:do {
    /tool user-manager user add customer="admin" username="0180577251" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0180577251";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0180577251";
};

# المستخدم 6: 0105041964
:do {
    /tool user-manager user add customer="admin" username="0105041964" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0105041964";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0105041964";
};

# المستخدم 7: 0114159075
:do {
    /tool user-manager user add customer="admin" username="0114159075" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0114159075";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0114159075";
};

# المستخدم 8: 0157387598
:do {
    /tool user-manager user add customer="admin" username="0157387598" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0157387598";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0157387598";
};

# المستخدم 9: 0127077644
:do {
    /tool user-manager user add customer="admin" username="0127077644" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0127077644";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0127077644";
};

# المستخدم 10: 0114037004
:do {
    /tool user-manager user add customer="admin" username="0114037004" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0114037004";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0114037004";
};

# المستخدم 11: 0126003596
:do {
    /tool user-manager user add customer="admin" username="0126003596" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0126003596";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0126003596";
};

# المستخدم 12: 0192265903
:do {
    /tool user-manager user add customer="admin" username="0192265903" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0192265903";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0192265903";
};

# المستخدم 13: 0135992936
:do {
    /tool user-manager user add customer="admin" username="0135992936" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0135992936";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0135992936";
};

# المستخدم 14: 0171702036
:do {
    /tool user-manager user add customer="admin" username="0171702036" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0171702036";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0171702036";
};

# المستخدم 15: 0125072557
:do {
    /tool user-manager user add customer="admin" username="0125072557" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0125072557";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0125072557";
};

# المستخدم 16: 0132389005
:do {
    /tool user-manager user add customer="admin" username="0132389005" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0132389005";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0132389005";
};

# المستخدم 17: 0184065558
:do {
    /tool user-manager user add customer="admin" username="0184065558" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0184065558";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0184065558";
};

# المستخدم 18: 0172968691
:do {
    /tool user-manager user add customer="admin" username="0172968691" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0172968691";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0172968691";
};

# المستخدم 19: 0113899216
:do {
    /tool user-manager user add customer="admin" username="0113899216" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0113899216";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0113899216";
};

# المستخدم 20: 0174038390
:do {
    /tool user-manager user add customer="admin" username="0174038390" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0174038390";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0174038390";
};

# المستخدم 21: 0152921612
:do {
    /tool user-manager user add customer="admin" username="0152921612" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0152921612";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0152921612";
};

# المستخدم 22: 0182342539
:do {
    /tool user-manager user add customer="admin" username="0182342539" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0182342539";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0182342539";
};

# المستخدم 23: 0185390787
:do {
    /tool user-manager user add customer="admin" username="0185390787" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0185390787";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0185390787";
};

# المستخدم 24: 0103391701
:do {
    /tool user-manager user add customer="admin" username="0103391701" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0103391701";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0103391701";
};

# المستخدم 25: 0190743656
:do {
    /tool user-manager user add customer="admin" username="0190743656" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0190743656";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0190743656";
};

# المستخدم 26: 0168464051
:do {
    /tool user-manager user add customer="admin" username="0168464051" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0168464051";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0168464051";
};

# المستخدم 27: 0168089369
:do {
    /tool user-manager user add customer="admin" username="0168089369" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0168089369";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0168089369";
};

# المستخدم 28: 0105611857
:do {
    /tool user-manager user add customer="admin" username="0105611857" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0105611857";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0105611857";
};

# المستخدم 29: 0117052172
:do {
    /tool user-manager user add customer="admin" username="0117052172" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0117052172";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0117052172";
};

# المستخدم 30: 0170053746
:do {
    /tool user-manager user add customer="admin" username="0170053746" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0170053746";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0170053746";
};

# المستخدم 31: 0185475903
:do {
    /tool user-manager user add customer="admin" username="0185475903" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0185475903";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0185475903";
};

# المستخدم 32: 0120486332
:do {
    /tool user-manager user add customer="admin" username="0120486332" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0120486332";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0120486332";
};

# المستخدم 33: 0199976105
:do {
    /tool user-manager user add customer="admin" username="0199976105" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0199976105";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0199976105";
};

# المستخدم 34: 0114911510
:do {
    /tool user-manager user add customer="admin" username="0114911510" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0114911510";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0114911510";
};

# المستخدم 35: 0170719744
:do {
    /tool user-manager user add customer="admin" username="0170719744" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0170719744";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0170719744";
};

# المستخدم 36: 0121787495
:do {
    /tool user-manager user add customer="admin" username="0121787495" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0121787495";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0121787495";
};

# المستخدم 37: 0145212371
:do {
    /tool user-manager user add customer="admin" username="0145212371" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0145212371";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0145212371";
};

# المستخدم 38: 0110110648
:do {
    /tool user-manager user add customer="admin" username="0110110648" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0110110648";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0110110648";
};

# المستخدم 39: 0181558650
:do {
    /tool user-manager user add customer="admin" username="0181558650" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0181558650";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0181558650";
};

# المستخدم 40: 0126856663
:do {
    /tool user-manager user add customer="admin" username="0126856663" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0126856663";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0126856663";
};

# المستخدم 41: 0111302304
:do {
    /tool user-manager user add customer="admin" username="0111302304" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0111302304";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0111302304";
};

# المستخدم 42: 0158791463
:do {
    /tool user-manager user add customer="admin" username="0158791463" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0158791463";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0158791463";
};

# المستخدم 43: 0131661388
:do {
    /tool user-manager user add customer="admin" username="0131661388" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0131661388";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0131661388";
};

# المستخدم 44: 0190577413
:do {
    /tool user-manager user add customer="admin" username="0190577413" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0190577413";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0190577413";
};

# المستخدم 45: 0103672892
:do {
    /tool user-manager user add customer="admin" username="0103672892" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0103672892";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0103672892";
};

# المستخدم 46: 0171644218
:do {
    /tool user-manager user add customer="admin" username="0171644218" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0171644218";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0171644218";
};

# المستخدم 47: 0145485379
:do {
    /tool user-manager user add customer="admin" username="0145485379" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0145485379";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0145485379";
};

# المستخدم 48: 0196980631
:do {
    /tool user-manager user add customer="admin" username="0196980631" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0196980631";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0196980631";
};

# المستخدم 49: 0122208798
:do {
    /tool user-manager user add customer="admin" username="0122208798" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0122208798";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0122208798";
};

# المستخدم 50: 0100772855
:do {
    /tool user-manager user add customer="admin" username="0100772855" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0100772855";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0100772855";
};

# المستخدم 51: 0128773505
:do {
    /tool user-manager user add customer="admin" username="0128773505" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0128773505";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0128773505";
};

# المستخدم 52: 0182457342
:do {
    /tool user-manager user add customer="admin" username="0182457342" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0182457342";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0182457342";
};

# المستخدم 53: 0104726719
:do {
    /tool user-manager user add customer="admin" username="0104726719" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0104726719";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0104726719";
};

# المستخدم 54: 0182941502
:do {
    /tool user-manager user add customer="admin" username="0182941502" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0182941502";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0182941502";
};

# المستخدم 55: 0101038903
:do {
    /tool user-manager user add customer="admin" username="0101038903" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0101038903";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0101038903";
};

# المستخدم 56: 0153150978
:do {
    /tool user-manager user add customer="admin" username="0153150978" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0153150978";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0153150978";
};

# المستخدم 57: 0120517146
:do {
    /tool user-manager user add customer="admin" username="0120517146" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0120517146";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0120517146";
};

# المستخدم 58: 0117828459
:do {
    /tool user-manager user add customer="admin" username="0117828459" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0117828459";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0117828459";
};

# المستخدم 59: 0137268163
:do {
    /tool user-manager user add customer="admin" username="0137268163" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0137268163";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0137268163";
};

# المستخدم 60: 0195375632
:do {
    /tool user-manager user add customer="admin" username="0195375632" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0195375632";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0195375632";
};

# المستخدم 61: 0182023912
:do {
    /tool user-manager user add customer="admin" username="0182023912" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0182023912";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0182023912";
};

# المستخدم 62: 0118055926
:do {
    /tool user-manager user add customer="admin" username="0118055926" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0118055926";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0118055926";
};

# المستخدم 63: 0150404900
:do {
    /tool user-manager user add customer="admin" username="0150404900" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0150404900";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0150404900";
};

# المستخدم 64: 0123565224
:do {
    /tool user-manager user add customer="admin" username="0123565224" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0123565224";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0123565224";
};

# المستخدم 65: 0133234721
:do {
    /tool user-manager user add customer="admin" username="0133234721" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0133234721";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0133234721";
};

# المستخدم 66: 0169738922
:do {
    /tool user-manager user add customer="admin" username="0169738922" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0169738922";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0169738922";
};

# المستخدم 67: 0149905819
:do {
    /tool user-manager user add customer="admin" username="0149905819" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0149905819";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0149905819";
};

# المستخدم 68: 0150999187
:do {
    /tool user-manager user add customer="admin" username="0150999187" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0150999187";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0150999187";
};

# المستخدم 69: 0159454627
:do {
    /tool user-manager user add customer="admin" username="0159454627" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0159454627";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0159454627";
};

# المستخدم 70: 0171679193
:do {
    /tool user-manager user add customer="admin" username="0171679193" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0171679193";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0171679193";
};

# المستخدم 71: 0101415289
:do {
    /tool user-manager user add customer="admin" username="0101415289" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0101415289";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0101415289";
};

# المستخدم 72: 0167253041
:do {
    /tool user-manager user add customer="admin" username="0167253041" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0167253041";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0167253041";
};

# المستخدم 73: 0152176678
:do {
    /tool user-manager user add customer="admin" username="0152176678" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0152176678";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0152176678";
};

# المستخدم 74: 0161516889
:do {
    /tool user-manager user add customer="admin" username="0161516889" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0161516889";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0161516889";
};

# المستخدم 75: 0148360620
:do {
    /tool user-manager user add customer="admin" username="0148360620" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0148360620";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0148360620";
};

# المستخدم 76: 0141802885
:do {
    /tool user-manager user add customer="admin" username="0141802885" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0141802885";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0141802885";
};

# المستخدم 77: 0110181445
:do {
    /tool user-manager user add customer="admin" username="0110181445" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0110181445";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0110181445";
};

# المستخدم 78: 0146694159
:do {
    /tool user-manager user add customer="admin" username="0146694159" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0146694159";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0146694159";
};

# المستخدم 79: 0180888488
:do {
    /tool user-manager user add customer="admin" username="0180888488" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0180888488";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0180888488";
};

# المستخدم 80: 0105882105
:do {
    /tool user-manager user add customer="admin" username="0105882105" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0105882105";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0105882105";
};

# المستخدم 81: 0135224093
:do {
    /tool user-manager user add customer="admin" username="0135224093" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0135224093";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0135224093";
};

# المستخدم 82: 0117714891
:do {
    /tool user-manager user add customer="admin" username="0117714891" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0117714891";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0117714891";
};

# المستخدم 83: 0152481452
:do {
    /tool user-manager user add customer="admin" username="0152481452" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0152481452";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0152481452";
};

# المستخدم 84: 0153839281
:do {
    /tool user-manager user add customer="admin" username="0153839281" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0153839281";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0153839281";
};

# المستخدم 85: 0170340656
:do {
    /tool user-manager user add customer="admin" username="0170340656" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0170340656";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0170340656";
};

# المستخدم 86: 0190846480
:do {
    /tool user-manager user add customer="admin" username="0190846480" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0190846480";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0190846480";
};

# المستخدم 87: 0130971334
:do {
    /tool user-manager user add customer="admin" username="0130971334" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0130971334";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0130971334";
};

# المستخدم 88: 0137234663
:do {
    /tool user-manager user add customer="admin" username="0137234663" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0137234663";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0137234663";
};

# المستخدم 89: 0147867057
:do {
    /tool user-manager user add customer="admin" username="0147867057" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0147867057";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0147867057";
};

# المستخدم 90: 0117540928
:do {
    /tool user-manager user add customer="admin" username="0117540928" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0117540928";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0117540928";
};

# المستخدم 91: 0161718306
:do {
    /tool user-manager user add customer="admin" username="0161718306" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0161718306";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0161718306";
};

# المستخدم 92: 0112269127
:do {
    /tool user-manager user add customer="admin" username="0112269127" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0112269127";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0112269127";
};

# المستخدم 93: 0108757902
:do {
    /tool user-manager user add customer="admin" username="0108757902" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0108757902";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0108757902";
};

# المستخدم 94: 0161907939
:do {
    /tool user-manager user add customer="admin" username="0161907939" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0161907939";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0161907939";
};

# المستخدم 95: 0198177960
:do {
    /tool user-manager user add customer="admin" username="0198177960" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0198177960";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0198177960";
};

# المستخدم 96: 0169380411
:do {
    /tool user-manager user add customer="admin" username="0169380411" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0169380411";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0169380411";
};

# المستخدم 97: 0104134450
:do {
    /tool user-manager user add customer="admin" username="0104134450" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0104134450";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0104134450";
};

# المستخدم 98: 0188278351
:do {
    /tool user-manager user add customer="admin" username="0188278351" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0188278351";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0188278351";
};

# المستخدم 99: 0112778635
:do {
    /tool user-manager user add customer="admin" username="0112778635" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0112778635";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0112778635";
};

# المستخدم 100: 0134703748
:do {
    /tool user-manager user add customer="admin" username="0134703748" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0134703748";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0134703748";
};

# المستخدم 101: 0162382452
:do {
    /tool user-manager user add customer="admin" username="0162382452" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0162382452";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0162382452";
};

# المستخدم 102: 0185314866
:do {
    /tool user-manager user add customer="admin" username="0185314866" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0185314866";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0185314866";
};

# المستخدم 103: 0133535928
:do {
    /tool user-manager user add customer="admin" username="0133535928" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0133535928";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0133535928";
};

# المستخدم 104: 0187173931
:do {
    /tool user-manager user add customer="admin" username="0187173931" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0187173931";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0187173931";
};

# المستخدم 105: 0172583597
:do {
    /tool user-manager user add customer="admin" username="0172583597" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0172583597";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0172583597";
};

# المستخدم 106: 0110305547
:do {
    /tool user-manager user add customer="admin" username="0110305547" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0110305547";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0110305547";
};

# المستخدم 107: 0190380050
:do {
    /tool user-manager user add customer="admin" username="0190380050" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0190380050";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0190380050";
};

# المستخدم 108: 0104022398
:do {
    /tool user-manager user add customer="admin" username="0104022398" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0104022398";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0104022398";
};

# المستخدم 109: 0167322986
:do {
    /tool user-manager user add customer="admin" username="0167322986" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0167322986";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0167322986";
};

# المستخدم 110: 0118848433
:do {
    /tool user-manager user add customer="admin" username="0118848433" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0118848433";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0118848433";
};

# المستخدم 111: 0178154351
:do {
    /tool user-manager user add customer="admin" username="0178154351" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0178154351";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0178154351";
};

# المستخدم 112: 0119994344
:do {
    /tool user-manager user add customer="admin" username="0119994344" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0119994344";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0119994344";
};

# المستخدم 113: 0167631300
:do {
    /tool user-manager user add customer="admin" username="0167631300" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0167631300";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0167631300";
};

# المستخدم 114: 0153838201
:do {
    /tool user-manager user add customer="admin" username="0153838201" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0153838201";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0153838201";
};

# المستخدم 115: 0145556984
:do {
    /tool user-manager user add customer="admin" username="0145556984" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0145556984";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0145556984";
};

# المستخدم 116: 0154257969
:do {
    /tool user-manager user add customer="admin" username="0154257969" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0154257969";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0154257969";
};

# المستخدم 117: 0154121890
:do {
    /tool user-manager user add customer="admin" username="0154121890" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0154121890";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0154121890";
};

# المستخدم 118: 0134008617
:do {
    /tool user-manager user add customer="admin" username="0134008617" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0134008617";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0134008617";
};

# المستخدم 119: 0128330666
:do {
    /tool user-manager user add customer="admin" username="0128330666" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0128330666";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0128330666";
};

# المستخدم 120: 0185481313
:do {
    /tool user-manager user add customer="admin" username="0185481313" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0185481313";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0185481313";
};

# المستخدم 121: 0111529458
:do {
    /tool user-manager user add customer="admin" username="0111529458" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0111529458";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0111529458";
};

# المستخدم 122: 0153628865
:do {
    /tool user-manager user add customer="admin" username="0153628865" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0153628865";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0153628865";
};

# المستخدم 123: 0188597037
:do {
    /tool user-manager user add customer="admin" username="0188597037" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0188597037";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0188597037";
};

# المستخدم 124: 0137818655
:do {
    /tool user-manager user add customer="admin" username="0137818655" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0137818655";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0137818655";
};

# المستخدم 125: 0102416536
:do {
    /tool user-manager user add customer="admin" username="0102416536" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0102416536";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0102416536";
};

# المستخدم 126: 0130782325
:do {
    /tool user-manager user add customer="admin" username="0130782325" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0130782325";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0130782325";
};

# المستخدم 127: 0102318757
:do {
    /tool user-manager user add customer="admin" username="0102318757" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0102318757";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0102318757";
};

# المستخدم 128: 0198714696
:do {
    /tool user-manager user add customer="admin" username="0198714696" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0198714696";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0198714696";
};

# المستخدم 129: 0191564865
:do {
    /tool user-manager user add customer="admin" username="0191564865" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0191564865";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0191564865";
};

# المستخدم 130: 0108014427
:do {
    /tool user-manager user add customer="admin" username="0108014427" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0108014427";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0108014427";
};

# المستخدم 131: 0145094224
:do {
    /tool user-manager user add customer="admin" username="0145094224" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0145094224";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0145094224";
};

# المستخدم 132: 0190083157
:do {
    /tool user-manager user add customer="admin" username="0190083157" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0190083157";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0190083157";
};

# المستخدم 133: 0140583177
:do {
    /tool user-manager user add customer="admin" username="0140583177" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0140583177";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0140583177";
};

# المستخدم 134: 0183360473
:do {
    /tool user-manager user add customer="admin" username="0183360473" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0183360473";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0183360473";
};

# المستخدم 135: 0172810996
:do {
    /tool user-manager user add customer="admin" username="0172810996" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0172810996";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0172810996";
};

# المستخدم 136: 0166640001
:do {
    /tool user-manager user add customer="admin" username="0166640001" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0166640001";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0166640001";
};

# المستخدم 137: 0183704544
:do {
    /tool user-manager user add customer="admin" username="0183704544" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0183704544";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0183704544";
};

# المستخدم 138: 0153795262
:do {
    /tool user-manager user add customer="admin" username="0153795262" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0153795262";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0153795262";
};

# المستخدم 139: 0182966068
:do {
    /tool user-manager user add customer="admin" username="0182966068" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0182966068";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0182966068";
};

# المستخدم 140: 0118411372
:do {
    /tool user-manager user add customer="admin" username="0118411372" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0118411372";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0118411372";
};

# المستخدم 141: 0192003384
:do {
    /tool user-manager user add customer="admin" username="0192003384" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0192003384";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0192003384";
};

# المستخدم 142: 0102180167
:do {
    /tool user-manager user add customer="admin" username="0102180167" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0102180167";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0102180167";
};

# المستخدم 143: 0138608503
:do {
    /tool user-manager user add customer="admin" username="0138608503" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0138608503";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0138608503";
};

# المستخدم 144: 0131831252
:do {
    /tool user-manager user add customer="admin" username="0131831252" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0131831252";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0131831252";
};

# المستخدم 145: 0169191169
:do {
    /tool user-manager user add customer="admin" username="0169191169" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0169191169";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0169191169";
};

# المستخدم 146: 0198293164
:do {
    /tool user-manager user add customer="admin" username="0198293164" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0198293164";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0198293164";
};

# المستخدم 147: 0117858847
:do {
    /tool user-manager user add customer="admin" username="0117858847" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0117858847";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0117858847";
};

# المستخدم 148: 0130885369
:do {
    /tool user-manager user add customer="admin" username="0130885369" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0130885369";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0130885369";
};

# المستخدم 149: 0170837379
:do {
    /tool user-manager user add customer="admin" username="0170837379" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0170837379";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0170837379";
};

# المستخدم 150: 0143291273
:do {
    /tool user-manager user add customer="admin" username="0143291273" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0143291273";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0143291273";
};

# المستخدم 151: 0152511436
:do {
    /tool user-manager user add customer="admin" username="0152511436" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0152511436";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0152511436";
};

# المستخدم 152: 0119235849
:do {
    /tool user-manager user add customer="admin" username="0119235849" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0119235849";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0119235849";
};

# المستخدم 153: 0123587949
:do {
    /tool user-manager user add customer="admin" username="0123587949" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0123587949";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0123587949";
};

# المستخدم 154: 0196337108
:do {
    /tool user-manager user add customer="admin" username="0196337108" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0196337108";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0196337108";
};

# المستخدم 155: 0165729237
:do {
    /tool user-manager user add customer="admin" username="0165729237" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0165729237";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0165729237";
};

# المستخدم 156: 0157440525
:do {
    /tool user-manager user add customer="admin" username="0157440525" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0157440525";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0157440525";
};

# المستخدم 157: 0196389116
:do {
    /tool user-manager user add customer="admin" username="0196389116" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0196389116";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0196389116";
};

# المستخدم 158: 0139193112
:do {
    /tool user-manager user add customer="admin" username="0139193112" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0139193112";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0139193112";
};

# المستخدم 159: 0125309450
:do {
    /tool user-manager user add customer="admin" username="0125309450" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0125309450";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0125309450";
};

# المستخدم 160: 0140353948
:do {
    /tool user-manager user add customer="admin" username="0140353948" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0140353948";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0140353948";
};

# المستخدم 161: 0179223186
:do {
    /tool user-manager user add customer="admin" username="0179223186" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0179223186";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0179223186";
};

# المستخدم 162: 0182042773
:do {
    /tool user-manager user add customer="admin" username="0182042773" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0182042773";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0182042773";
};

# المستخدم 163: 0169210942
:do {
    /tool user-manager user add customer="admin" username="0169210942" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0169210942";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0169210942";
};

# المستخدم 164: 0170883289
:do {
    /tool user-manager user add customer="admin" username="0170883289" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0170883289";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0170883289";
};

# المستخدم 165: 0133609574
:do {
    /tool user-manager user add customer="admin" username="0133609574" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0133609574";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0133609574";
};

# المستخدم 166: 0145970339
:do {
    /tool user-manager user add customer="admin" username="0145970339" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0145970339";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0145970339";
};

# المستخدم 167: 0122515202
:do {
    /tool user-manager user add customer="admin" username="0122515202" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0122515202";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0122515202";
};

# المستخدم 168: 0114465682
:do {
    /tool user-manager user add customer="admin" username="0114465682" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0114465682";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0114465682";
};

# المستخدم 169: 0181744024
:do {
    /tool user-manager user add customer="admin" username="0181744024" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0181744024";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0181744024";
};

# المستخدم 170: 0182537425
:do {
    /tool user-manager user add customer="admin" username="0182537425" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0182537425";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0182537425";
};

# المستخدم 171: 0140393374
:do {
    /tool user-manager user add customer="admin" username="0140393374" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0140393374";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0140393374";
};

# المستخدم 172: 0194994327
:do {
    /tool user-manager user add customer="admin" username="0194994327" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0194994327";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0194994327";
};

# المستخدم 173: 0130835632
:do {
    /tool user-manager user add customer="admin" username="0130835632" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0130835632";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0130835632";
};

# المستخدم 174: 0103891724
:do {
    /tool user-manager user add customer="admin" username="0103891724" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0103891724";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0103891724";
};

# المستخدم 175: 0167262913
:do {
    /tool user-manager user add customer="admin" username="0167262913" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0167262913";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0167262913";
};

# المستخدم 176: 0184777396
:do {
    /tool user-manager user add customer="admin" username="0184777396" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0184777396";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0184777396";
};

# المستخدم 177: 0139267679
:do {
    /tool user-manager user add customer="admin" username="0139267679" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0139267679";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0139267679";
};

# المستخدم 178: 0170460402
:do {
    /tool user-manager user add customer="admin" username="0170460402" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0170460402";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0170460402";
};

# المستخدم 179: 0194633790
:do {
    /tool user-manager user add customer="admin" username="0194633790" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0194633790";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0194633790";
};

# المستخدم 180: 0160238501
:do {
    /tool user-manager user add customer="admin" username="0160238501" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0160238501";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0160238501";
};

# المستخدم 181: 0128731674
:do {
    /tool user-manager user add customer="admin" username="0128731674" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0128731674";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0128731674";
};

# المستخدم 182: 0121534214
:do {
    /tool user-manager user add customer="admin" username="0121534214" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0121534214";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0121534214";
};

# المستخدم 183: 0158063020
:do {
    /tool user-manager user add customer="admin" username="0158063020" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0158063020";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0158063020";
};

# المستخدم 184: 0100110207
:do {
    /tool user-manager user add customer="admin" username="0100110207" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0100110207";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0100110207";
};

# المستخدم 185: 0132355759
:do {
    /tool user-manager user add customer="admin" username="0132355759" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0132355759";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0132355759";
};

# المستخدم 186: 0127390016
:do {
    /tool user-manager user add customer="admin" username="0127390016" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0127390016";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0127390016";
};

# المستخدم 187: 0113623878
:do {
    /tool user-manager user add customer="admin" username="0113623878" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0113623878";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0113623878";
};

# المستخدم 188: 0196975666
:do {
    /tool user-manager user add customer="admin" username="0196975666" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0196975666";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0196975666";
};

# المستخدم 189: 0123432422
:do {
    /tool user-manager user add customer="admin" username="0123432422" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0123432422";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0123432422";
};

# المستخدم 190: 0148488021
:do {
    /tool user-manager user add customer="admin" username="0148488021" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0148488021";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0148488021";
};

# المستخدم 191: 0145996056
:do {
    /tool user-manager user add customer="admin" username="0145996056" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0145996056";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0145996056";
};

# المستخدم 192: 0151124855
:do {
    /tool user-manager user add customer="admin" username="0151124855" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0151124855";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0151124855";
};

# المستخدم 193: 0128444621
:do {
    /tool user-manager user add customer="admin" username="0128444621" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0128444621";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0128444621";
};

# المستخدم 194: 0128127526
:do {
    /tool user-manager user add customer="admin" username="0128127526" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0128127526";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0128127526";
};

# المستخدم 195: 0114489430
:do {
    /tool user-manager user add customer="admin" username="0114489430" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0114489430";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0114489430";
};

# المستخدم 196: 0146398421
:do {
    /tool user-manager user add customer="admin" username="0146398421" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0146398421";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0146398421";
};

# المستخدم 197: 0182040932
:do {
    /tool user-manager user add customer="admin" username="0182040932" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0182040932";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0182040932";
};

# المستخدم 198: 0109035054
:do {
    /tool user-manager user add customer="admin" username="0109035054" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0109035054";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0109035054";
};

# المستخدم 199: 0100225657
:do {
    /tool user-manager user add customer="admin" username="0100225657" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0100225657";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0100225657";
};

# المستخدم 200: 0166715156
:do {
    /tool user-manager user add customer="admin" username="0166715156" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0166715156";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0166715156";
};


:put "📊 تم الانتهاء من النسخة الاحتياطية";
:put "✅ نجح: $success كرت";
:put "❌ فشل: $errors كرت";
:put "📈 الإجمالي: $total كرت";

:put "🎉 تم استعادة جميع كروت القالب: 10";
:put "💡 النسخة الاحتياطية مكتملة بنجاح!";
