# MikroTik Script - نسخة احتياطية للكروت
# تم الإنشاء: 2025-07-25 03:10:41
# القالب: 10
# النظام: user_manager
# عدد الكروت: 200
# تم إنشاؤه بواسطة: مولد كروت MikroTik

:put "🚀 بدء تنفيذ النسخة الاحتياطية للكروت";
:put "📋 القالب: 10";
:put "🎯 النظام: user_manager";
:put "📊 عدد الكروت: 200";

:local success 0;
:local errors 0;
:local total 200;

:put "⏳ بدء إضافة الكروت...";

# إضافة مستخدمي User Manager
:put "👤 إضافة 200 مستخدم User Manager...";

# المستخدم 1: 0107878174
:do {
    /tool user-manager user add customer="admin" username="0107878174" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0107878174";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0107878174";
};

# المستخدم 2: 0185032910
:do {
    /tool user-manager user add customer="admin" username="0185032910" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0185032910";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0185032910";
};

# المستخدم 3: 0179734631
:do {
    /tool user-manager user add customer="admin" username="0179734631" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0179734631";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0179734631";
};

# المستخدم 4: 0137305483
:do {
    /tool user-manager user add customer="admin" username="0137305483" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0137305483";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0137305483";
};

# المستخدم 5: 0135576688
:do {
    /tool user-manager user add customer="admin" username="0135576688" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0135576688";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0135576688";
};

# المستخدم 6: 0191602291
:do {
    /tool user-manager user add customer="admin" username="0191602291" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0191602291";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0191602291";
};

# المستخدم 7: 0196187573
:do {
    /tool user-manager user add customer="admin" username="0196187573" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0196187573";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0196187573";
};

# المستخدم 8: 0107870450
:do {
    /tool user-manager user add customer="admin" username="0107870450" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0107870450";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0107870450";
};

# المستخدم 9: 0115644708
:do {
    /tool user-manager user add customer="admin" username="0115644708" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0115644708";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0115644708";
};

# المستخدم 10: 0151684922
:do {
    /tool user-manager user add customer="admin" username="0151684922" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0151684922";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0151684922";
};

# المستخدم 11: 0199231630
:do {
    /tool user-manager user add customer="admin" username="0199231630" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0199231630";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0199231630";
};

# المستخدم 12: 0143677655
:do {
    /tool user-manager user add customer="admin" username="0143677655" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0143677655";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0143677655";
};

# المستخدم 13: 0155400299
:do {
    /tool user-manager user add customer="admin" username="0155400299" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0155400299";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0155400299";
};

# المستخدم 14: 0121651444
:do {
    /tool user-manager user add customer="admin" username="0121651444" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0121651444";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0121651444";
};

# المستخدم 15: 0179815433
:do {
    /tool user-manager user add customer="admin" username="0179815433" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0179815433";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0179815433";
};

# المستخدم 16: 0184540001
:do {
    /tool user-manager user add customer="admin" username="0184540001" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0184540001";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0184540001";
};

# المستخدم 17: 0178595459
:do {
    /tool user-manager user add customer="admin" username="0178595459" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0178595459";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0178595459";
};

# المستخدم 18: 0132158254
:do {
    /tool user-manager user add customer="admin" username="0132158254" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0132158254";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0132158254";
};

# المستخدم 19: 0121429361
:do {
    /tool user-manager user add customer="admin" username="0121429361" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0121429361";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0121429361";
};

# المستخدم 20: 0101210652
:do {
    /tool user-manager user add customer="admin" username="0101210652" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0101210652";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0101210652";
};

# المستخدم 21: 0194238389
:do {
    /tool user-manager user add customer="admin" username="0194238389" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0194238389";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0194238389";
};

# المستخدم 22: 0106278497
:do {
    /tool user-manager user add customer="admin" username="0106278497" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0106278497";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0106278497";
};

# المستخدم 23: 0136482766
:do {
    /tool user-manager user add customer="admin" username="0136482766" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0136482766";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0136482766";
};

# المستخدم 24: 0187346108
:do {
    /tool user-manager user add customer="admin" username="0187346108" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0187346108";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0187346108";
};

# المستخدم 25: 0131882836
:do {
    /tool user-manager user add customer="admin" username="0131882836" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0131882836";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0131882836";
};

# المستخدم 26: 0179227560
:do {
    /tool user-manager user add customer="admin" username="0179227560" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0179227560";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0179227560";
};

# المستخدم 27: 0126346402
:do {
    /tool user-manager user add customer="admin" username="0126346402" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0126346402";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0126346402";
};

# المستخدم 28: 0189060946
:do {
    /tool user-manager user add customer="admin" username="0189060946" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0189060946";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0189060946";
};

# المستخدم 29: 0142770209
:do {
    /tool user-manager user add customer="admin" username="0142770209" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0142770209";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0142770209";
};

# المستخدم 30: 0174880751
:do {
    /tool user-manager user add customer="admin" username="0174880751" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0174880751";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0174880751";
};

# المستخدم 31: 0129797256
:do {
    /tool user-manager user add customer="admin" username="0129797256" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0129797256";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0129797256";
};

# المستخدم 32: 0112837324
:do {
    /tool user-manager user add customer="admin" username="0112837324" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0112837324";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0112837324";
};

# المستخدم 33: 0168608618
:do {
    /tool user-manager user add customer="admin" username="0168608618" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0168608618";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0168608618";
};

# المستخدم 34: 0193793429
:do {
    /tool user-manager user add customer="admin" username="0193793429" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0193793429";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0193793429";
};

# المستخدم 35: 0169622897
:do {
    /tool user-manager user add customer="admin" username="0169622897" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0169622897";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0169622897";
};

# المستخدم 36: 0191474317
:do {
    /tool user-manager user add customer="admin" username="0191474317" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0191474317";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0191474317";
};

# المستخدم 37: 0179159073
:do {
    /tool user-manager user add customer="admin" username="0179159073" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0179159073";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0179159073";
};

# المستخدم 38: 0151758831
:do {
    /tool user-manager user add customer="admin" username="0151758831" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0151758831";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0151758831";
};

# المستخدم 39: 0183902313
:do {
    /tool user-manager user add customer="admin" username="0183902313" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0183902313";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0183902313";
};

# المستخدم 40: 0144709680
:do {
    /tool user-manager user add customer="admin" username="0144709680" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0144709680";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0144709680";
};

# المستخدم 41: 0168695093
:do {
    /tool user-manager user add customer="admin" username="0168695093" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0168695093";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0168695093";
};

# المستخدم 42: 0165413521
:do {
    /tool user-manager user add customer="admin" username="0165413521" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0165413521";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0165413521";
};

# المستخدم 43: 0189663256
:do {
    /tool user-manager user add customer="admin" username="0189663256" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0189663256";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0189663256";
};

# المستخدم 44: 0113053803
:do {
    /tool user-manager user add customer="admin" username="0113053803" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0113053803";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0113053803";
};

# المستخدم 45: 0138713127
:do {
    /tool user-manager user add customer="admin" username="0138713127" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0138713127";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0138713127";
};

# المستخدم 46: 0199288802
:do {
    /tool user-manager user add customer="admin" username="0199288802" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0199288802";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0199288802";
};

# المستخدم 47: 0165296712
:do {
    /tool user-manager user add customer="admin" username="0165296712" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0165296712";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0165296712";
};

# المستخدم 48: 0107863450
:do {
    /tool user-manager user add customer="admin" username="0107863450" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0107863450";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0107863450";
};

# المستخدم 49: 0153350606
:do {
    /tool user-manager user add customer="admin" username="0153350606" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0153350606";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0153350606";
};

# المستخدم 50: 0197983683
:do {
    /tool user-manager user add customer="admin" username="0197983683" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0197983683";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0197983683";
};

# المستخدم 51: 0146284798
:do {
    /tool user-manager user add customer="admin" username="0146284798" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0146284798";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0146284798";
};

# المستخدم 52: 0121834273
:do {
    /tool user-manager user add customer="admin" username="0121834273" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0121834273";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0121834273";
};

# المستخدم 53: 0150155924
:do {
    /tool user-manager user add customer="admin" username="0150155924" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0150155924";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0150155924";
};

# المستخدم 54: 0160534026
:do {
    /tool user-manager user add customer="admin" username="0160534026" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0160534026";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0160534026";
};

# المستخدم 55: 0178159854
:do {
    /tool user-manager user add customer="admin" username="0178159854" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0178159854";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0178159854";
};

# المستخدم 56: 0146247016
:do {
    /tool user-manager user add customer="admin" username="0146247016" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0146247016";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0146247016";
};

# المستخدم 57: 0167611923
:do {
    /tool user-manager user add customer="admin" username="0167611923" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0167611923";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0167611923";
};

# المستخدم 58: 0193110559
:do {
    /tool user-manager user add customer="admin" username="0193110559" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0193110559";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0193110559";
};

# المستخدم 59: 0125659480
:do {
    /tool user-manager user add customer="admin" username="0125659480" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0125659480";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0125659480";
};

# المستخدم 60: 0125691413
:do {
    /tool user-manager user add customer="admin" username="0125691413" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0125691413";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0125691413";
};

# المستخدم 61: 0185092770
:do {
    /tool user-manager user add customer="admin" username="0185092770" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0185092770";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0185092770";
};

# المستخدم 62: 0113632167
:do {
    /tool user-manager user add customer="admin" username="0113632167" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0113632167";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0113632167";
};

# المستخدم 63: 0143803513
:do {
    /tool user-manager user add customer="admin" username="0143803513" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0143803513";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0143803513";
};

# المستخدم 64: 0173149530
:do {
    /tool user-manager user add customer="admin" username="0173149530" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0173149530";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0173149530";
};

# المستخدم 65: 0140707610
:do {
    /tool user-manager user add customer="admin" username="0140707610" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0140707610";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0140707610";
};

# المستخدم 66: 0133770628
:do {
    /tool user-manager user add customer="admin" username="0133770628" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0133770628";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0133770628";
};

# المستخدم 67: 0103935802
:do {
    /tool user-manager user add customer="admin" username="0103935802" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0103935802";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0103935802";
};

# المستخدم 68: 0107421825
:do {
    /tool user-manager user add customer="admin" username="0107421825" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0107421825";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0107421825";
};

# المستخدم 69: 0185316285
:do {
    /tool user-manager user add customer="admin" username="0185316285" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0185316285";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0185316285";
};

# المستخدم 70: 0105336807
:do {
    /tool user-manager user add customer="admin" username="0105336807" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0105336807";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0105336807";
};

# المستخدم 71: 0121890205
:do {
    /tool user-manager user add customer="admin" username="0121890205" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0121890205";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0121890205";
};

# المستخدم 72: 0142633220
:do {
    /tool user-manager user add customer="admin" username="0142633220" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0142633220";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0142633220";
};

# المستخدم 73: 0142204549
:do {
    /tool user-manager user add customer="admin" username="0142204549" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0142204549";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0142204549";
};

# المستخدم 74: 0118750711
:do {
    /tool user-manager user add customer="admin" username="0118750711" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0118750711";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0118750711";
};

# المستخدم 75: 0135441997
:do {
    /tool user-manager user add customer="admin" username="0135441997" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0135441997";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0135441997";
};

# المستخدم 76: 0190749130
:do {
    /tool user-manager user add customer="admin" username="0190749130" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0190749130";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0190749130";
};

# المستخدم 77: 0193539895
:do {
    /tool user-manager user add customer="admin" username="0193539895" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0193539895";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0193539895";
};

# المستخدم 78: 0168938152
:do {
    /tool user-manager user add customer="admin" username="0168938152" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0168938152";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0168938152";
};

# المستخدم 79: 0113140866
:do {
    /tool user-manager user add customer="admin" username="0113140866" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0113140866";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0113140866";
};

# المستخدم 80: 0155816116
:do {
    /tool user-manager user add customer="admin" username="0155816116" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0155816116";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0155816116";
};

# المستخدم 81: 0157205251
:do {
    /tool user-manager user add customer="admin" username="0157205251" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0157205251";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0157205251";
};

# المستخدم 82: 0144683283
:do {
    /tool user-manager user add customer="admin" username="0144683283" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0144683283";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0144683283";
};

# المستخدم 83: 0181782449
:do {
    /tool user-manager user add customer="admin" username="0181782449" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0181782449";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0181782449";
};

# المستخدم 84: 0129778416
:do {
    /tool user-manager user add customer="admin" username="0129778416" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0129778416";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0129778416";
};

# المستخدم 85: 0182375473
:do {
    /tool user-manager user add customer="admin" username="0182375473" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0182375473";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0182375473";
};

# المستخدم 86: 0104929755
:do {
    /tool user-manager user add customer="admin" username="0104929755" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0104929755";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0104929755";
};

# المستخدم 87: 0171507238
:do {
    /tool user-manager user add customer="admin" username="0171507238" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0171507238";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0171507238";
};

# المستخدم 88: 0138205608
:do {
    /tool user-manager user add customer="admin" username="0138205608" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0138205608";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0138205608";
};

# المستخدم 89: 0168324858
:do {
    /tool user-manager user add customer="admin" username="0168324858" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0168324858";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0168324858";
};

# المستخدم 90: 0106610381
:do {
    /tool user-manager user add customer="admin" username="0106610381" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0106610381";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0106610381";
};

# المستخدم 91: 0199465723
:do {
    /tool user-manager user add customer="admin" username="0199465723" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0199465723";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0199465723";
};

# المستخدم 92: 0136297216
:do {
    /tool user-manager user add customer="admin" username="0136297216" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0136297216";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0136297216";
};

# المستخدم 93: 0100216586
:do {
    /tool user-manager user add customer="admin" username="0100216586" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0100216586";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0100216586";
};

# المستخدم 94: 0187920234
:do {
    /tool user-manager user add customer="admin" username="0187920234" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0187920234";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0187920234";
};

# المستخدم 95: 0118898301
:do {
    /tool user-manager user add customer="admin" username="0118898301" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0118898301";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0118898301";
};

# المستخدم 96: 0187027657
:do {
    /tool user-manager user add customer="admin" username="0187027657" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0187027657";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0187027657";
};

# المستخدم 97: 0155589365
:do {
    /tool user-manager user add customer="admin" username="0155589365" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0155589365";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0155589365";
};

# المستخدم 98: 0120433498
:do {
    /tool user-manager user add customer="admin" username="0120433498" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0120433498";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0120433498";
};

# المستخدم 99: 0189231869
:do {
    /tool user-manager user add customer="admin" username="0189231869" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0189231869";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0189231869";
};

# المستخدم 100: 0158643516
:do {
    /tool user-manager user add customer="admin" username="0158643516" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0158643516";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0158643516";
};

# المستخدم 101: 0137270483
:do {
    /tool user-manager user add customer="admin" username="0137270483" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0137270483";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0137270483";
};

# المستخدم 102: 0162656676
:do {
    /tool user-manager user add customer="admin" username="0162656676" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0162656676";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0162656676";
};

# المستخدم 103: 0118468748
:do {
    /tool user-manager user add customer="admin" username="0118468748" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0118468748";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0118468748";
};

# المستخدم 104: 0149192375
:do {
    /tool user-manager user add customer="admin" username="0149192375" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0149192375";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0149192375";
};

# المستخدم 105: 0139648519
:do {
    /tool user-manager user add customer="admin" username="0139648519" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0139648519";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0139648519";
};

# المستخدم 106: 0175272748
:do {
    /tool user-manager user add customer="admin" username="0175272748" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0175272748";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0175272748";
};

# المستخدم 107: 0127385516
:do {
    /tool user-manager user add customer="admin" username="0127385516" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0127385516";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0127385516";
};

# المستخدم 108: 0199199943
:do {
    /tool user-manager user add customer="admin" username="0199199943" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0199199943";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0199199943";
};

# المستخدم 109: 0109216826
:do {
    /tool user-manager user add customer="admin" username="0109216826" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0109216826";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0109216826";
};

# المستخدم 110: 0173518847
:do {
    /tool user-manager user add customer="admin" username="0173518847" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0173518847";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0173518847";
};

# المستخدم 111: 0157912207
:do {
    /tool user-manager user add customer="admin" username="0157912207" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0157912207";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0157912207";
};

# المستخدم 112: 0171801023
:do {
    /tool user-manager user add customer="admin" username="0171801023" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0171801023";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0171801023";
};

# المستخدم 113: 0157794728
:do {
    /tool user-manager user add customer="admin" username="0157794728" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0157794728";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0157794728";
};

# المستخدم 114: 0108616804
:do {
    /tool user-manager user add customer="admin" username="0108616804" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0108616804";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0108616804";
};

# المستخدم 115: 0142724188
:do {
    /tool user-manager user add customer="admin" username="0142724188" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0142724188";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0142724188";
};

# المستخدم 116: 0184993669
:do {
    /tool user-manager user add customer="admin" username="0184993669" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0184993669";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0184993669";
};

# المستخدم 117: 0159364587
:do {
    /tool user-manager user add customer="admin" username="0159364587" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0159364587";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0159364587";
};

# المستخدم 118: 0182275270
:do {
    /tool user-manager user add customer="admin" username="0182275270" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0182275270";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0182275270";
};

# المستخدم 119: 0139784336
:do {
    /tool user-manager user add customer="admin" username="0139784336" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0139784336";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0139784336";
};

# المستخدم 120: 0107755756
:do {
    /tool user-manager user add customer="admin" username="0107755756" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0107755756";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0107755756";
};

# المستخدم 121: 0111194994
:do {
    /tool user-manager user add customer="admin" username="0111194994" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0111194994";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0111194994";
};

# المستخدم 122: 0113731663
:do {
    /tool user-manager user add customer="admin" username="0113731663" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0113731663";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0113731663";
};

# المستخدم 123: 0153289272
:do {
    /tool user-manager user add customer="admin" username="0153289272" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0153289272";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0153289272";
};

# المستخدم 124: 0105760494
:do {
    /tool user-manager user add customer="admin" username="0105760494" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0105760494";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0105760494";
};

# المستخدم 125: 0173599527
:do {
    /tool user-manager user add customer="admin" username="0173599527" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0173599527";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0173599527";
};

# المستخدم 126: 0184771382
:do {
    /tool user-manager user add customer="admin" username="0184771382" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0184771382";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0184771382";
};

# المستخدم 127: 0192595810
:do {
    /tool user-manager user add customer="admin" username="0192595810" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0192595810";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0192595810";
};

# المستخدم 128: 0104721051
:do {
    /tool user-manager user add customer="admin" username="0104721051" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0104721051";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0104721051";
};

# المستخدم 129: 0104679428
:do {
    /tool user-manager user add customer="admin" username="0104679428" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0104679428";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0104679428";
};

# المستخدم 130: 0135517395
:do {
    /tool user-manager user add customer="admin" username="0135517395" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0135517395";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0135517395";
};

# المستخدم 131: 0111749228
:do {
    /tool user-manager user add customer="admin" username="0111749228" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0111749228";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0111749228";
};

# المستخدم 132: 0133383477
:do {
    /tool user-manager user add customer="admin" username="0133383477" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0133383477";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0133383477";
};

# المستخدم 133: 0181986146
:do {
    /tool user-manager user add customer="admin" username="0181986146" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0181986146";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0181986146";
};

# المستخدم 134: 0194279164
:do {
    /tool user-manager user add customer="admin" username="0194279164" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0194279164";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0194279164";
};

# المستخدم 135: 0122213899
:do {
    /tool user-manager user add customer="admin" username="0122213899" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0122213899";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0122213899";
};

# المستخدم 136: 0117415064
:do {
    /tool user-manager user add customer="admin" username="0117415064" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0117415064";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0117415064";
};

# المستخدم 137: 0115104731
:do {
    /tool user-manager user add customer="admin" username="0115104731" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0115104731";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0115104731";
};

# المستخدم 138: 0189399879
:do {
    /tool user-manager user add customer="admin" username="0189399879" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0189399879";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0189399879";
};

# المستخدم 139: 0175552385
:do {
    /tool user-manager user add customer="admin" username="0175552385" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0175552385";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0175552385";
};

# المستخدم 140: 0197417706
:do {
    /tool user-manager user add customer="admin" username="0197417706" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0197417706";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0197417706";
};

# المستخدم 141: 0107663558
:do {
    /tool user-manager user add customer="admin" username="0107663558" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0107663558";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0107663558";
};

# المستخدم 142: 0195269022
:do {
    /tool user-manager user add customer="admin" username="0195269022" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0195269022";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0195269022";
};

# المستخدم 143: 0152806230
:do {
    /tool user-manager user add customer="admin" username="0152806230" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0152806230";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0152806230";
};

# المستخدم 144: 0106019995
:do {
    /tool user-manager user add customer="admin" username="0106019995" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0106019995";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0106019995";
};

# المستخدم 145: 0181814441
:do {
    /tool user-manager user add customer="admin" username="0181814441" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0181814441";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0181814441";
};

# المستخدم 146: 0151909057
:do {
    /tool user-manager user add customer="admin" username="0151909057" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0151909057";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0151909057";
};

# المستخدم 147: 0111175380
:do {
    /tool user-manager user add customer="admin" username="0111175380" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0111175380";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0111175380";
};

# المستخدم 148: 0119053937
:do {
    /tool user-manager user add customer="admin" username="0119053937" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0119053937";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0119053937";
};

# المستخدم 149: 0116814070
:do {
    /tool user-manager user add customer="admin" username="0116814070" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0116814070";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0116814070";
};

# المستخدم 150: 0190043764
:do {
    /tool user-manager user add customer="admin" username="0190043764" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0190043764";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0190043764";
};

# المستخدم 151: 0151878064
:do {
    /tool user-manager user add customer="admin" username="0151878064" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0151878064";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0151878064";
};

# المستخدم 152: 0103100384
:do {
    /tool user-manager user add customer="admin" username="0103100384" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0103100384";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0103100384";
};

# المستخدم 153: 0130948369
:do {
    /tool user-manager user add customer="admin" username="0130948369" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0130948369";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0130948369";
};

# المستخدم 154: 0136639977
:do {
    /tool user-manager user add customer="admin" username="0136639977" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0136639977";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0136639977";
};

# المستخدم 155: 0151500991
:do {
    /tool user-manager user add customer="admin" username="0151500991" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0151500991";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0151500991";
};

# المستخدم 156: 0106495281
:do {
    /tool user-manager user add customer="admin" username="0106495281" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0106495281";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0106495281";
};

# المستخدم 157: 0118247091
:do {
    /tool user-manager user add customer="admin" username="0118247091" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0118247091";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0118247091";
};

# المستخدم 158: 0196587526
:do {
    /tool user-manager user add customer="admin" username="0196587526" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0196587526";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0196587526";
};

# المستخدم 159: 0109259875
:do {
    /tool user-manager user add customer="admin" username="0109259875" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0109259875";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0109259875";
};

# المستخدم 160: 0111895697
:do {
    /tool user-manager user add customer="admin" username="0111895697" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0111895697";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0111895697";
};

# المستخدم 161: 0196301847
:do {
    /tool user-manager user add customer="admin" username="0196301847" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0196301847";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0196301847";
};

# المستخدم 162: 0138066045
:do {
    /tool user-manager user add customer="admin" username="0138066045" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0138066045";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0138066045";
};

# المستخدم 163: 0170971705
:do {
    /tool user-manager user add customer="admin" username="0170971705" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0170971705";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0170971705";
};

# المستخدم 164: 0140539301
:do {
    /tool user-manager user add customer="admin" username="0140539301" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0140539301";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0140539301";
};

# المستخدم 165: 0191765913
:do {
    /tool user-manager user add customer="admin" username="0191765913" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0191765913";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0191765913";
};

# المستخدم 166: 0133851991
:do {
    /tool user-manager user add customer="admin" username="0133851991" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0133851991";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0133851991";
};

# المستخدم 167: 0178882021
:do {
    /tool user-manager user add customer="admin" username="0178882021" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0178882021";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0178882021";
};

# المستخدم 168: 0153635683
:do {
    /tool user-manager user add customer="admin" username="0153635683" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0153635683";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0153635683";
};

# المستخدم 169: 0181196575
:do {
    /tool user-manager user add customer="admin" username="0181196575" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0181196575";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0181196575";
};

# المستخدم 170: 0107817208
:do {
    /tool user-manager user add customer="admin" username="0107817208" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0107817208";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0107817208";
};

# المستخدم 171: 0152128508
:do {
    /tool user-manager user add customer="admin" username="0152128508" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0152128508";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0152128508";
};

# المستخدم 172: 0110647467
:do {
    /tool user-manager user add customer="admin" username="0110647467" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0110647467";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0110647467";
};

# المستخدم 173: 0128391497
:do {
    /tool user-manager user add customer="admin" username="0128391497" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0128391497";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0128391497";
};

# المستخدم 174: 0175130521
:do {
    /tool user-manager user add customer="admin" username="0175130521" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0175130521";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0175130521";
};

# المستخدم 175: 0143607209
:do {
    /tool user-manager user add customer="admin" username="0143607209" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0143607209";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0143607209";
};

# المستخدم 176: 0110401647
:do {
    /tool user-manager user add customer="admin" username="0110401647" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0110401647";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0110401647";
};

# المستخدم 177: 0136887953
:do {
    /tool user-manager user add customer="admin" username="0136887953" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0136887953";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0136887953";
};

# المستخدم 178: 0136737899
:do {
    /tool user-manager user add customer="admin" username="0136737899" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0136737899";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0136737899";
};

# المستخدم 179: 0199004517
:do {
    /tool user-manager user add customer="admin" username="0199004517" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0199004517";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0199004517";
};

# المستخدم 180: 0143964232
:do {
    /tool user-manager user add customer="admin" username="0143964232" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0143964232";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0143964232";
};

# المستخدم 181: 0107608988
:do {
    /tool user-manager user add customer="admin" username="0107608988" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0107608988";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0107608988";
};

# المستخدم 182: 0130203588
:do {
    /tool user-manager user add customer="admin" username="0130203588" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0130203588";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0130203588";
};

# المستخدم 183: 0113490679
:do {
    /tool user-manager user add customer="admin" username="0113490679" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0113490679";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0113490679";
};

# المستخدم 184: 0180875154
:do {
    /tool user-manager user add customer="admin" username="0180875154" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0180875154";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0180875154";
};

# المستخدم 185: 0110376668
:do {
    /tool user-manager user add customer="admin" username="0110376668" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0110376668";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0110376668";
};

# المستخدم 186: 0189990174
:do {
    /tool user-manager user add customer="admin" username="0189990174" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0189990174";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0189990174";
};

# المستخدم 187: 0158488315
:do {
    /tool user-manager user add customer="admin" username="0158488315" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0158488315";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0158488315";
};

# المستخدم 188: 0198323246
:do {
    /tool user-manager user add customer="admin" username="0198323246" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0198323246";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0198323246";
};

# المستخدم 189: 0152190408
:do {
    /tool user-manager user add customer="admin" username="0152190408" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0152190408";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0152190408";
};

# المستخدم 190: 0192342663
:do {
    /tool user-manager user add customer="admin" username="0192342663" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0192342663";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0192342663";
};

# المستخدم 191: 0140287339
:do {
    /tool user-manager user add customer="admin" username="0140287339" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0140287339";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0140287339";
};

# المستخدم 192: 0170547236
:do {
    /tool user-manager user add customer="admin" username="0170547236" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0170547236";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0170547236";
};

# المستخدم 193: 0100537447
:do {
    /tool user-manager user add customer="admin" username="0100537447" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0100537447";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0100537447";
};

# المستخدم 194: 0153100605
:do {
    /tool user-manager user add customer="admin" username="0153100605" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0153100605";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0153100605";
};

# المستخدم 195: 0165278253
:do {
    /tool user-manager user add customer="admin" username="0165278253" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0165278253";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0165278253";
};

# المستخدم 196: 0134242250
:do {
    /tool user-manager user add customer="admin" username="0134242250" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0134242250";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0134242250";
};

# المستخدم 197: 0100759008
:do {
    /tool user-manager user add customer="admin" username="0100759008" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0100759008";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0100759008";
};

# المستخدم 198: 0130805365
:do {
    /tool user-manager user add customer="admin" username="0130805365" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0130805365";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0130805365";
};

# المستخدم 199: 0162941652
:do {
    /tool user-manager user add customer="admin" username="0162941652" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0162941652";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0162941652";
};

# المستخدم 200: 0139889807
:do {
    /tool user-manager user add customer="admin" username="0139889807" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0139889807";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0139889807";
};


:put "📊 تم الانتهاء من النسخة الاحتياطية";
:put "✅ نجح: $success كرت";
:put "❌ فشل: $errors كرت";
:put "📈 الإجمالي: $total كرت";

:put "🎉 تم استعادة جميع كروت القالب: 10";
:put "💡 النسخة الاحتياطية مكتملة بنجاح!";
