# MikroTik Script - نسخة احتياطية للكروت
# تم الإنشاء: 2025-07-25 02:55:06
# القالب: 10
# النظام: user_manager
# عدد الكروت: 200
# تم إنشاؤه بواسطة: مولد كروت MikroTik

:put "🚀 بدء تنفيذ النسخة الاحتياطية للكروت";
:put "📋 القالب: 10";
:put "🎯 النظام: user_manager";
:put "📊 عدد الكروت: 200";

:local success 0;
:local errors 0;
:local total 200;

:put "⏳ بدء إضافة الكروت...";

# إضافة مستخدمي User Manager
:put "👤 إضافة 200 مستخدم User Manager...";

# المستخدم 1: 0164060168
:do {
    /tool user-manager user add customer="admin" username="0164060168" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0164060168";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0164060168";
};

# المستخدم 2: 0172820950
:do {
    /tool user-manager user add customer="admin" username="0172820950" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0172820950";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0172820950";
};

# المستخدم 3: 0100187916
:do {
    /tool user-manager user add customer="admin" username="0100187916" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0100187916";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0100187916";
};

# المستخدم 4: 0158773509
:do {
    /tool user-manager user add customer="admin" username="0158773509" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0158773509";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0158773509";
};

# المستخدم 5: 0150301747
:do {
    /tool user-manager user add customer="admin" username="0150301747" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0150301747";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0150301747";
};

# المستخدم 6: 0150216949
:do {
    /tool user-manager user add customer="admin" username="0150216949" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0150216949";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0150216949";
};

# المستخدم 7: 0189864420
:do {
    /tool user-manager user add customer="admin" username="0189864420" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0189864420";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0189864420";
};

# المستخدم 8: 0155072298
:do {
    /tool user-manager user add customer="admin" username="0155072298" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0155072298";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0155072298";
};

# المستخدم 9: 0110998408
:do {
    /tool user-manager user add customer="admin" username="0110998408" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0110998408";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0110998408";
};

# المستخدم 10: 0152298551
:do {
    /tool user-manager user add customer="admin" username="0152298551" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0152298551";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0152298551";
};

# المستخدم 11: 0148961096
:do {
    /tool user-manager user add customer="admin" username="0148961096" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0148961096";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0148961096";
};

# المستخدم 12: 0160984145
:do {
    /tool user-manager user add customer="admin" username="0160984145" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0160984145";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0160984145";
};

# المستخدم 13: 0102991564
:do {
    /tool user-manager user add customer="admin" username="0102991564" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0102991564";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0102991564";
};

# المستخدم 14: 0156581700
:do {
    /tool user-manager user add customer="admin" username="0156581700" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0156581700";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0156581700";
};

# المستخدم 15: 0133080108
:do {
    /tool user-manager user add customer="admin" username="0133080108" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0133080108";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0133080108";
};

# المستخدم 16: 0186241143
:do {
    /tool user-manager user add customer="admin" username="0186241143" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0186241143";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0186241143";
};

# المستخدم 17: 0139848171
:do {
    /tool user-manager user add customer="admin" username="0139848171" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0139848171";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0139848171";
};

# المستخدم 18: 0129035913
:do {
    /tool user-manager user add customer="admin" username="0129035913" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0129035913";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0129035913";
};

# المستخدم 19: 0111894493
:do {
    /tool user-manager user add customer="admin" username="0111894493" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0111894493";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0111894493";
};

# المستخدم 20: 0108472702
:do {
    /tool user-manager user add customer="admin" username="0108472702" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0108472702";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0108472702";
};

# المستخدم 21: 0141261941
:do {
    /tool user-manager user add customer="admin" username="0141261941" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0141261941";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0141261941";
};

# المستخدم 22: 0198770332
:do {
    /tool user-manager user add customer="admin" username="0198770332" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0198770332";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0198770332";
};

# المستخدم 23: 0195694569
:do {
    /tool user-manager user add customer="admin" username="0195694569" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0195694569";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0195694569";
};

# المستخدم 24: 0151482725
:do {
    /tool user-manager user add customer="admin" username="0151482725" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0151482725";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0151482725";
};

# المستخدم 25: 0157795216
:do {
    /tool user-manager user add customer="admin" username="0157795216" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0157795216";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0157795216";
};

# المستخدم 26: 0199537890
:do {
    /tool user-manager user add customer="admin" username="0199537890" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0199537890";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0199537890";
};

# المستخدم 27: 0119937594
:do {
    /tool user-manager user add customer="admin" username="0119937594" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0119937594";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0119937594";
};

# المستخدم 28: 0161947879
:do {
    /tool user-manager user add customer="admin" username="0161947879" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0161947879";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0161947879";
};

# المستخدم 29: 0142904114
:do {
    /tool user-manager user add customer="admin" username="0142904114" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0142904114";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0142904114";
};

# المستخدم 30: 0110769642
:do {
    /tool user-manager user add customer="admin" username="0110769642" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0110769642";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0110769642";
};

# المستخدم 31: 0102729662
:do {
    /tool user-manager user add customer="admin" username="0102729662" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0102729662";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0102729662";
};

# المستخدم 32: 0199818207
:do {
    /tool user-manager user add customer="admin" username="0199818207" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0199818207";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0199818207";
};

# المستخدم 33: 0176756503
:do {
    /tool user-manager user add customer="admin" username="0176756503" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0176756503";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0176756503";
};

# المستخدم 34: 0127381210
:do {
    /tool user-manager user add customer="admin" username="0127381210" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0127381210";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0127381210";
};

# المستخدم 35: 0115318349
:do {
    /tool user-manager user add customer="admin" username="0115318349" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0115318349";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0115318349";
};

# المستخدم 36: 0159561182
:do {
    /tool user-manager user add customer="admin" username="0159561182" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0159561182";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0159561182";
};

# المستخدم 37: 0170732217
:do {
    /tool user-manager user add customer="admin" username="0170732217" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0170732217";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0170732217";
};

# المستخدم 38: 0181220361
:do {
    /tool user-manager user add customer="admin" username="0181220361" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0181220361";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0181220361";
};

# المستخدم 39: 0152093193
:do {
    /tool user-manager user add customer="admin" username="0152093193" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0152093193";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0152093193";
};

# المستخدم 40: 0189077643
:do {
    /tool user-manager user add customer="admin" username="0189077643" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0189077643";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0189077643";
};

# المستخدم 41: 0114974107
:do {
    /tool user-manager user add customer="admin" username="0114974107" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0114974107";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0114974107";
};

# المستخدم 42: 0121909900
:do {
    /tool user-manager user add customer="admin" username="0121909900" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0121909900";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0121909900";
};

# المستخدم 43: 0101378919
:do {
    /tool user-manager user add customer="admin" username="0101378919" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0101378919";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0101378919";
};

# المستخدم 44: 0193141898
:do {
    /tool user-manager user add customer="admin" username="0193141898" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0193141898";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0193141898";
};

# المستخدم 45: 0147527669
:do {
    /tool user-manager user add customer="admin" username="0147527669" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0147527669";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0147527669";
};

# المستخدم 46: 0161698294
:do {
    /tool user-manager user add customer="admin" username="0161698294" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0161698294";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0161698294";
};

# المستخدم 47: 0188538108
:do {
    /tool user-manager user add customer="admin" username="0188538108" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0188538108";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0188538108";
};

# المستخدم 48: 0153564647
:do {
    /tool user-manager user add customer="admin" username="0153564647" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0153564647";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0153564647";
};

# المستخدم 49: 0147808452
:do {
    /tool user-manager user add customer="admin" username="0147808452" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0147808452";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0147808452";
};

# المستخدم 50: 0131077832
:do {
    /tool user-manager user add customer="admin" username="0131077832" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0131077832";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0131077832";
};

# المستخدم 51: 0133312019
:do {
    /tool user-manager user add customer="admin" username="0133312019" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0133312019";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0133312019";
};

# المستخدم 52: 0180875261
:do {
    /tool user-manager user add customer="admin" username="0180875261" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0180875261";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0180875261";
};

# المستخدم 53: 0162287105
:do {
    /tool user-manager user add customer="admin" username="0162287105" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0162287105";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0162287105";
};

# المستخدم 54: 0178124269
:do {
    /tool user-manager user add customer="admin" username="0178124269" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0178124269";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0178124269";
};

# المستخدم 55: 0186943080
:do {
    /tool user-manager user add customer="admin" username="0186943080" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0186943080";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0186943080";
};

# المستخدم 56: 0181625931
:do {
    /tool user-manager user add customer="admin" username="0181625931" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0181625931";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0181625931";
};

# المستخدم 57: 0143348944
:do {
    /tool user-manager user add customer="admin" username="0143348944" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0143348944";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0143348944";
};

# المستخدم 58: 0192431033
:do {
    /tool user-manager user add customer="admin" username="0192431033" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0192431033";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0192431033";
};

# المستخدم 59: 0102870103
:do {
    /tool user-manager user add customer="admin" username="0102870103" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0102870103";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0102870103";
};

# المستخدم 60: 0165687577
:do {
    /tool user-manager user add customer="admin" username="0165687577" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0165687577";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0165687577";
};

# المستخدم 61: 0122330556
:do {
    /tool user-manager user add customer="admin" username="0122330556" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0122330556";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0122330556";
};

# المستخدم 62: 0109897587
:do {
    /tool user-manager user add customer="admin" username="0109897587" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0109897587";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0109897587";
};

# المستخدم 63: 0106941364
:do {
    /tool user-manager user add customer="admin" username="0106941364" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0106941364";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0106941364";
};

# المستخدم 64: 0184367891
:do {
    /tool user-manager user add customer="admin" username="0184367891" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0184367891";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0184367891";
};

# المستخدم 65: 0198846888
:do {
    /tool user-manager user add customer="admin" username="0198846888" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0198846888";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0198846888";
};

# المستخدم 66: 0129464934
:do {
    /tool user-manager user add customer="admin" username="0129464934" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0129464934";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0129464934";
};

# المستخدم 67: 0109719623
:do {
    /tool user-manager user add customer="admin" username="0109719623" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0109719623";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0109719623";
};

# المستخدم 68: 0173911736
:do {
    /tool user-manager user add customer="admin" username="0173911736" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0173911736";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0173911736";
};

# المستخدم 69: 0142353443
:do {
    /tool user-manager user add customer="admin" username="0142353443" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0142353443";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0142353443";
};

# المستخدم 70: 0169574891
:do {
    /tool user-manager user add customer="admin" username="0169574891" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0169574891";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0169574891";
};

# المستخدم 71: 0143087398
:do {
    /tool user-manager user add customer="admin" username="0143087398" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0143087398";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0143087398";
};

# المستخدم 72: 0184893245
:do {
    /tool user-manager user add customer="admin" username="0184893245" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0184893245";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0184893245";
};

# المستخدم 73: 0104742780
:do {
    /tool user-manager user add customer="admin" username="0104742780" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0104742780";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0104742780";
};

# المستخدم 74: 0144703464
:do {
    /tool user-manager user add customer="admin" username="0144703464" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0144703464";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0144703464";
};

# المستخدم 75: 0186304928
:do {
    /tool user-manager user add customer="admin" username="0186304928" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0186304928";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0186304928";
};

# المستخدم 76: 0155934603
:do {
    /tool user-manager user add customer="admin" username="0155934603" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0155934603";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0155934603";
};

# المستخدم 77: 0191065027
:do {
    /tool user-manager user add customer="admin" username="0191065027" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0191065027";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0191065027";
};

# المستخدم 78: 0188371821
:do {
    /tool user-manager user add customer="admin" username="0188371821" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0188371821";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0188371821";
};

# المستخدم 79: 0192473079
:do {
    /tool user-manager user add customer="admin" username="0192473079" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0192473079";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0192473079";
};

# المستخدم 80: 0154707054
:do {
    /tool user-manager user add customer="admin" username="0154707054" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0154707054";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0154707054";
};

# المستخدم 81: 0143878705
:do {
    /tool user-manager user add customer="admin" username="0143878705" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0143878705";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0143878705";
};

# المستخدم 82: 0165270290
:do {
    /tool user-manager user add customer="admin" username="0165270290" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0165270290";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0165270290";
};

# المستخدم 83: 0145344865
:do {
    /tool user-manager user add customer="admin" username="0145344865" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0145344865";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0145344865";
};

# المستخدم 84: 0136783001
:do {
    /tool user-manager user add customer="admin" username="0136783001" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0136783001";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0136783001";
};

# المستخدم 85: 0129151481
:do {
    /tool user-manager user add customer="admin" username="0129151481" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0129151481";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0129151481";
};

# المستخدم 86: 0123127826
:do {
    /tool user-manager user add customer="admin" username="0123127826" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0123127826";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0123127826";
};

# المستخدم 87: 0110920182
:do {
    /tool user-manager user add customer="admin" username="0110920182" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0110920182";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0110920182";
};

# المستخدم 88: 0154785215
:do {
    /tool user-manager user add customer="admin" username="0154785215" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0154785215";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0154785215";
};

# المستخدم 89: 0119578654
:do {
    /tool user-manager user add customer="admin" username="0119578654" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0119578654";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0119578654";
};

# المستخدم 90: 0149711795
:do {
    /tool user-manager user add customer="admin" username="0149711795" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0149711795";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0149711795";
};

# المستخدم 91: 0170420244
:do {
    /tool user-manager user add customer="admin" username="0170420244" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0170420244";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0170420244";
};

# المستخدم 92: 0108672612
:do {
    /tool user-manager user add customer="admin" username="0108672612" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0108672612";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0108672612";
};

# المستخدم 93: 0145745192
:do {
    /tool user-manager user add customer="admin" username="0145745192" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0145745192";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0145745192";
};

# المستخدم 94: 0172667335
:do {
    /tool user-manager user add customer="admin" username="0172667335" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0172667335";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0172667335";
};

# المستخدم 95: 0105798268
:do {
    /tool user-manager user add customer="admin" username="0105798268" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0105798268";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0105798268";
};

# المستخدم 96: 0198915967
:do {
    /tool user-manager user add customer="admin" username="0198915967" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0198915967";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0198915967";
};

# المستخدم 97: 0143952481
:do {
    /tool user-manager user add customer="admin" username="0143952481" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0143952481";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0143952481";
};

# المستخدم 98: 0103809688
:do {
    /tool user-manager user add customer="admin" username="0103809688" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0103809688";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0103809688";
};

# المستخدم 99: 0163897199
:do {
    /tool user-manager user add customer="admin" username="0163897199" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0163897199";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0163897199";
};

# المستخدم 100: 0181990460
:do {
    /tool user-manager user add customer="admin" username="0181990460" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0181990460";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0181990460";
};

# المستخدم 101: 0171433308
:do {
    /tool user-manager user add customer="admin" username="0171433308" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0171433308";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0171433308";
};

# المستخدم 102: 0126591863
:do {
    /tool user-manager user add customer="admin" username="0126591863" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0126591863";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0126591863";
};

# المستخدم 103: 0159742441
:do {
    /tool user-manager user add customer="admin" username="0159742441" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0159742441";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0159742441";
};

# المستخدم 104: 0147816754
:do {
    /tool user-manager user add customer="admin" username="0147816754" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0147816754";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0147816754";
};

# المستخدم 105: 0114297918
:do {
    /tool user-manager user add customer="admin" username="0114297918" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0114297918";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0114297918";
};

# المستخدم 106: 0160727857
:do {
    /tool user-manager user add customer="admin" username="0160727857" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0160727857";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0160727857";
};

# المستخدم 107: 0171143444
:do {
    /tool user-manager user add customer="admin" username="0171143444" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0171143444";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0171143444";
};

# المستخدم 108: 0113306878
:do {
    /tool user-manager user add customer="admin" username="0113306878" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0113306878";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0113306878";
};

# المستخدم 109: 0157205161
:do {
    /tool user-manager user add customer="admin" username="0157205161" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0157205161";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0157205161";
};

# المستخدم 110: 0101381848
:do {
    /tool user-manager user add customer="admin" username="0101381848" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0101381848";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0101381848";
};

# المستخدم 111: 0168374387
:do {
    /tool user-manager user add customer="admin" username="0168374387" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0168374387";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0168374387";
};

# المستخدم 112: 0174101031
:do {
    /tool user-manager user add customer="admin" username="0174101031" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0174101031";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0174101031";
};

# المستخدم 113: 0132060553
:do {
    /tool user-manager user add customer="admin" username="0132060553" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0132060553";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0132060553";
};

# المستخدم 114: 0189275163
:do {
    /tool user-manager user add customer="admin" username="0189275163" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0189275163";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0189275163";
};

# المستخدم 115: 0165516514
:do {
    /tool user-manager user add customer="admin" username="0165516514" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0165516514";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0165516514";
};

# المستخدم 116: 0128314307
:do {
    /tool user-manager user add customer="admin" username="0128314307" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0128314307";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0128314307";
};

# المستخدم 117: 0113764356
:do {
    /tool user-manager user add customer="admin" username="0113764356" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0113764356";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0113764356";
};

# المستخدم 118: 0134383886
:do {
    /tool user-manager user add customer="admin" username="0134383886" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0134383886";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0134383886";
};

# المستخدم 119: 0194163036
:do {
    /tool user-manager user add customer="admin" username="0194163036" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0194163036";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0194163036";
};

# المستخدم 120: 0148307461
:do {
    /tool user-manager user add customer="admin" username="0148307461" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0148307461";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0148307461";
};

# المستخدم 121: 0190377304
:do {
    /tool user-manager user add customer="admin" username="0190377304" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0190377304";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0190377304";
};

# المستخدم 122: 0162837298
:do {
    /tool user-manager user add customer="admin" username="0162837298" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0162837298";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0162837298";
};

# المستخدم 123: 0137112155
:do {
    /tool user-manager user add customer="admin" username="0137112155" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0137112155";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0137112155";
};

# المستخدم 124: 0146664807
:do {
    /tool user-manager user add customer="admin" username="0146664807" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0146664807";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0146664807";
};

# المستخدم 125: 0140391372
:do {
    /tool user-manager user add customer="admin" username="0140391372" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0140391372";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0140391372";
};

# المستخدم 126: 0141318859
:do {
    /tool user-manager user add customer="admin" username="0141318859" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0141318859";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0141318859";
};

# المستخدم 127: 0125461507
:do {
    /tool user-manager user add customer="admin" username="0125461507" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0125461507";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0125461507";
};

# المستخدم 128: 0185073178
:do {
    /tool user-manager user add customer="admin" username="0185073178" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0185073178";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0185073178";
};

# المستخدم 129: 0128945092
:do {
    /tool user-manager user add customer="admin" username="0128945092" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0128945092";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0128945092";
};

# المستخدم 130: 0138374596
:do {
    /tool user-manager user add customer="admin" username="0138374596" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0138374596";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0138374596";
};

# المستخدم 131: 0138895800
:do {
    /tool user-manager user add customer="admin" username="0138895800" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0138895800";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0138895800";
};

# المستخدم 132: 0185607692
:do {
    /tool user-manager user add customer="admin" username="0185607692" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0185607692";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0185607692";
};

# المستخدم 133: 0161205198
:do {
    /tool user-manager user add customer="admin" username="0161205198" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0161205198";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0161205198";
};

# المستخدم 134: 0170610546
:do {
    /tool user-manager user add customer="admin" username="0170610546" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0170610546";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0170610546";
};

# المستخدم 135: 0188182187
:do {
    /tool user-manager user add customer="admin" username="0188182187" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0188182187";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0188182187";
};

# المستخدم 136: 0117895598
:do {
    /tool user-manager user add customer="admin" username="0117895598" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0117895598";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0117895598";
};

# المستخدم 137: 0176632993
:do {
    /tool user-manager user add customer="admin" username="0176632993" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0176632993";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0176632993";
};

# المستخدم 138: 0116092743
:do {
    /tool user-manager user add customer="admin" username="0116092743" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0116092743";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0116092743";
};

# المستخدم 139: 0157784627
:do {
    /tool user-manager user add customer="admin" username="0157784627" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0157784627";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0157784627";
};

# المستخدم 140: 0194013733
:do {
    /tool user-manager user add customer="admin" username="0194013733" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0194013733";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0194013733";
};

# المستخدم 141: 0163508446
:do {
    /tool user-manager user add customer="admin" username="0163508446" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0163508446";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0163508446";
};

# المستخدم 142: 0134268728
:do {
    /tool user-manager user add customer="admin" username="0134268728" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0134268728";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0134268728";
};

# المستخدم 143: 0190020595
:do {
    /tool user-manager user add customer="admin" username="0190020595" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0190020595";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0190020595";
};

# المستخدم 144: 0162421917
:do {
    /tool user-manager user add customer="admin" username="0162421917" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0162421917";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0162421917";
};

# المستخدم 145: 0150101020
:do {
    /tool user-manager user add customer="admin" username="0150101020" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0150101020";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0150101020";
};

# المستخدم 146: 0194044078
:do {
    /tool user-manager user add customer="admin" username="0194044078" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0194044078";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0194044078";
};

# المستخدم 147: 0178929914
:do {
    /tool user-manager user add customer="admin" username="0178929914" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0178929914";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0178929914";
};

# المستخدم 148: 0191571566
:do {
    /tool user-manager user add customer="admin" username="0191571566" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0191571566";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0191571566";
};

# المستخدم 149: 0111059916
:do {
    /tool user-manager user add customer="admin" username="0111059916" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0111059916";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0111059916";
};

# المستخدم 150: 0132638305
:do {
    /tool user-manager user add customer="admin" username="0132638305" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0132638305";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0132638305";
};

# المستخدم 151: 0137093752
:do {
    /tool user-manager user add customer="admin" username="0137093752" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0137093752";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0137093752";
};

# المستخدم 152: 0143953155
:do {
    /tool user-manager user add customer="admin" username="0143953155" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0143953155";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0143953155";
};

# المستخدم 153: 0130795870
:do {
    /tool user-manager user add customer="admin" username="0130795870" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0130795870";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0130795870";
};

# المستخدم 154: 0178083927
:do {
    /tool user-manager user add customer="admin" username="0178083927" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0178083927";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0178083927";
};

# المستخدم 155: 0182999420
:do {
    /tool user-manager user add customer="admin" username="0182999420" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0182999420";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0182999420";
};

# المستخدم 156: 0152636938
:do {
    /tool user-manager user add customer="admin" username="0152636938" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0152636938";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0152636938";
};

# المستخدم 157: 0189138322
:do {
    /tool user-manager user add customer="admin" username="0189138322" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0189138322";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0189138322";
};

# المستخدم 158: 0145960471
:do {
    /tool user-manager user add customer="admin" username="0145960471" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0145960471";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0145960471";
};

# المستخدم 159: 0178682100
:do {
    /tool user-manager user add customer="admin" username="0178682100" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0178682100";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0178682100";
};

# المستخدم 160: 0100833083
:do {
    /tool user-manager user add customer="admin" username="0100833083" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0100833083";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0100833083";
};

# المستخدم 161: 0162414964
:do {
    /tool user-manager user add customer="admin" username="0162414964" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0162414964";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0162414964";
};

# المستخدم 162: 0180051566
:do {
    /tool user-manager user add customer="admin" username="0180051566" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0180051566";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0180051566";
};

# المستخدم 163: 0113514397
:do {
    /tool user-manager user add customer="admin" username="0113514397" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0113514397";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0113514397";
};

# المستخدم 164: 0149961161
:do {
    /tool user-manager user add customer="admin" username="0149961161" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0149961161";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0149961161";
};

# المستخدم 165: 0159583520
:do {
    /tool user-manager user add customer="admin" username="0159583520" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0159583520";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0159583520";
};

# المستخدم 166: 0127420219
:do {
    /tool user-manager user add customer="admin" username="0127420219" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0127420219";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0127420219";
};

# المستخدم 167: 0172251075
:do {
    /tool user-manager user add customer="admin" username="0172251075" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0172251075";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0172251075";
};

# المستخدم 168: 0142508360
:do {
    /tool user-manager user add customer="admin" username="0142508360" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0142508360";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0142508360";
};

# المستخدم 169: 0180276155
:do {
    /tool user-manager user add customer="admin" username="0180276155" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0180276155";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0180276155";
};

# المستخدم 170: 0100884937
:do {
    /tool user-manager user add customer="admin" username="0100884937" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0100884937";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0100884937";
};

# المستخدم 171: 0112446948
:do {
    /tool user-manager user add customer="admin" username="0112446948" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0112446948";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0112446948";
};

# المستخدم 172: 0107080138
:do {
    /tool user-manager user add customer="admin" username="0107080138" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0107080138";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0107080138";
};

# المستخدم 173: 0163939898
:do {
    /tool user-manager user add customer="admin" username="0163939898" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0163939898";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0163939898";
};

# المستخدم 174: 0177013840
:do {
    /tool user-manager user add customer="admin" username="0177013840" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0177013840";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0177013840";
};

# المستخدم 175: 0172337660
:do {
    /tool user-manager user add customer="admin" username="0172337660" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0172337660";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0172337660";
};

# المستخدم 176: 0116090402
:do {
    /tool user-manager user add customer="admin" username="0116090402" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0116090402";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0116090402";
};

# المستخدم 177: 0125462025
:do {
    /tool user-manager user add customer="admin" username="0125462025" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0125462025";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0125462025";
};

# المستخدم 178: 0111522762
:do {
    /tool user-manager user add customer="admin" username="0111522762" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0111522762";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0111522762";
};

# المستخدم 179: 0199714811
:do {
    /tool user-manager user add customer="admin" username="0199714811" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0199714811";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0199714811";
};

# المستخدم 180: 0182244656
:do {
    /tool user-manager user add customer="admin" username="0182244656" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0182244656";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0182244656";
};

# المستخدم 181: 0109767902
:do {
    /tool user-manager user add customer="admin" username="0109767902" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0109767902";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0109767902";
};

# المستخدم 182: 0166940108
:do {
    /tool user-manager user add customer="admin" username="0166940108" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0166940108";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0166940108";
};

# المستخدم 183: 0129396929
:do {
    /tool user-manager user add customer="admin" username="0129396929" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0129396929";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0129396929";
};

# المستخدم 184: 0122465022
:do {
    /tool user-manager user add customer="admin" username="0122465022" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0122465022";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0122465022";
};

# المستخدم 185: 0193324900
:do {
    /tool user-manager user add customer="admin" username="0193324900" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0193324900";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0193324900";
};

# المستخدم 186: 0175520285
:do {
    /tool user-manager user add customer="admin" username="0175520285" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0175520285";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0175520285";
};

# المستخدم 187: 0109356286
:do {
    /tool user-manager user add customer="admin" username="0109356286" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0109356286";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0109356286";
};

# المستخدم 188: 0188777005
:do {
    /tool user-manager user add customer="admin" username="0188777005" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0188777005";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0188777005";
};

# المستخدم 189: 0151529790
:do {
    /tool user-manager user add customer="admin" username="0151529790" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0151529790";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0151529790";
};

# المستخدم 190: 0175081700
:do {
    /tool user-manager user add customer="admin" username="0175081700" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0175081700";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0175081700";
};

# المستخدم 191: 0109351799
:do {
    /tool user-manager user add customer="admin" username="0109351799" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0109351799";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0109351799";
};

# المستخدم 192: 0140786317
:do {
    /tool user-manager user add customer="admin" username="0140786317" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0140786317";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0140786317";
};

# المستخدم 193: 0126747067
:do {
    /tool user-manager user add customer="admin" username="0126747067" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0126747067";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0126747067";
};

# المستخدم 194: 0162635357
:do {
    /tool user-manager user add customer="admin" username="0162635357" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0162635357";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0162635357";
};

# المستخدم 195: 0143863349
:do {
    /tool user-manager user add customer="admin" username="0143863349" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0143863349";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0143863349";
};

# المستخدم 196: 0144947952
:do {
    /tool user-manager user add customer="admin" username="0144947952" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0144947952";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0144947952";
};

# المستخدم 197: 0156075052
:do {
    /tool user-manager user add customer="admin" username="0156075052" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0156075052";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0156075052";
};

# المستخدم 198: 0160598917
:do {
    /tool user-manager user add customer="admin" username="0160598917" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0160598917";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0160598917";
};

# المستخدم 199: 0164991124
:do {
    /tool user-manager user add customer="admin" username="0164991124" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0164991124";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0164991124";
};

# المستخدم 200: 0135582557
:do {
    /tool user-manager user add customer="admin" username="0135582557" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0135582557";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0135582557";
};


:put "📊 تم الانتهاء من النسخة الاحتياطية";
:put "✅ نجح: $success كرت";
:put "❌ فشل: $errors كرت";
:put "📈 الإجمالي: $total كرت";

:put "🎉 تم استعادة جميع كروت القالب: 10";
:put "💡 النسخة الاحتياطية مكتملة بنجاح!";
