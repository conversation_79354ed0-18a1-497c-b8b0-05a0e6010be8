#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار زر عرض عدد الكروت في User Manager
تم إنشاؤه: 2025-07-24
الهدف: التحقق من إضافة زر عرض عدد الكروت في بوت التلجرام
"""

import sys
import os
import re

# إضافة مسار المشروع
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_button_addition_in_user_manager_templates():
    """اختبار إضافة الزر في قائمة User Manager"""
    print("🧪 اختبار إضافة زر عرض عدد الكروت في قائمة User Manager")
    print("=" * 70)
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن دالة send_user_manager_templates
    func_match = re.search(r'def send_user_manager_templates.*?(?=def|\Z)', content, re.DOTALL)
    if not func_match:
        print("❌ لم يتم العثور على دالة send_user_manager_templates")
        return False
    
    func_content = func_match.group(0)
    
    # فحص عناصر الزر المطلوبة
    button_elements = [
        ("إضافة زر عرض عدد الكروت أسفل القوالب مباشرة", "# إضافة زر عرض عدد الكروت أسفل القوالب مباشرة"),
        ("نص الزر", "📊 عرض عدد الكروت"),
        ("callback_data للزر", "show_user_manager_cards_count"),
        ("موضع الزر", "keyboard_buttons.append.*📊 عرض عدد الكروت"),
        ("الزر قبل زر العودة", r"📊 عرض عدد الكروت.*🔙 العودة لاختيار القوالب")
    ]
    
    print("🔍 فحص عناصر الزر:")
    passed_count = 0
    total_count = len(button_elements)
    
    for element_name, pattern in button_elements:
        found = re.search(pattern, func_content, re.DOTALL)
        status = "✅" if found else "❌"
        print(f"   {status} {element_name}")
        if found:
            passed_count += 1
    
    # النتيجة النهائية
    success_rate = (passed_count / total_count) * 100
    print(f"\n📊 النتيجة: {passed_count}/{total_count} ({success_rate:.1f}%)")
    
    if passed_count == total_count:
        print("✅ تم إضافة الزر بنجاح في قائمة User Manager!")
        return True
    else:
        print(f"❌ {total_count - passed_count} عنصر مفقود!")
        return False

def test_callback_handler_addition():
    """اختبار إضافة معالج callback_data"""
    print("\n🧪 اختبار إضافة معالج callback_data")
    print("=" * 70)
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن دالة process_telegram_callback
    func_match = re.search(r'def process_telegram_callback.*?(?=def|\Z)', content, re.DOTALL)
    if not func_match:
        print("❌ لم يتم العثور على دالة process_telegram_callback")
        return False
    
    func_content = func_match.group(0)
    
    # فحص عناصر معالج callback_data
    handler_elements = [
        ("شرط معالجة callback_data", r'elif callback_data == "show_user_manager_cards_count"'),
        ("استدعاء الدالة المعالجة", "self.handle_show_user_manager_cards_count"),
        ("تمرير المعاملات", "bot_token, chat_id"),
        ("تعليق توضيحي", "# معالجة زر \"عرض عدد الكروت\" في User Manager")
    ]
    
    print("🔍 فحص عناصر معالج callback_data:")
    passed_handlers = 0
    total_handlers = len(handler_elements)
    
    for element_name, pattern in handler_elements:
        found = re.search(pattern, func_content)
        status = "✅" if found else "❌"
        print(f"   {status} {element_name}")
        if found:
            passed_handlers += 1
    
    # النتيجة النهائية
    handler_rate = (passed_handlers / total_handlers) * 100
    print(f"\n📊 معالج callback_data: {passed_handlers}/{total_handlers} ({handler_rate:.1f}%)")
    
    if passed_handlers == total_handlers:
        print("✅ تم إضافة معالج callback_data بنجاح!")
        return True
    else:
        print(f"❌ {total_handlers - passed_handlers} عنصر معالج مفقود!")
        return False

def test_handler_function_implementation():
    """اختبار تطبيق دالة المعالجة"""
    print("\n🧪 اختبار تطبيق دالة handle_show_user_manager_cards_count")
    print("=" * 70)
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن دالة handle_show_user_manager_cards_count
    func_match = re.search(r'def handle_show_user_manager_cards_count.*?(?=def|\Z)', content, re.DOTALL)
    if not func_match:
        print("❌ لم يتم العثور على دالة handle_show_user_manager_cards_count")
        return False
    
    func_content = func_match.group(0)
    
    # فحص عناصر الدالة المطلوبة
    function_elements = [
        ("توثيق الدالة", "معالجة طلب عرض عدد الكروت في User Manager"),
        ("فحص مكتبة RouterOS", "if not ROUTEROS_AVAILABLE"),
        ("رسالة انتظار", "⏳.*جاري الاتصال بخادم MikroTik"),
        ("الاتصال بـ MikroTik", "api = self.connect_api()"),
        ("جلب المستخدمين", "/tool/user-manager/user"),
        ("حساب العدد", "users_count = len\\(all_users\\)"),
        ("الحصول على التاريخ والوقت", "datetime.datetime.now()"),
        ("بناء رسالة النتيجة", "📊.*إحصائيات User Manager"),
        ("عرض العدد الحالي", "العدد الحالي للكروت.*users_count"),
        ("معلومات الخادم", "معلومات الخادم"),
        ("معالجة الأخطاء", "except Exception as api_error"),
        ("إغلاق الاتصال", "finally:.*api_connection.*disconnect"),
        ("رسائل سجل", "self.logger.info"),
        ("إرسال النتيجة", "send_telegram_message_direct")
    ]
    
    print("🔍 فحص عناصر دالة المعالجة:")
    passed_functions = 0
    total_functions = len(function_elements)
    
    for element_name, pattern in function_elements:
        found = re.search(pattern, func_content, re.DOTALL)
        status = "✅" if found else "❌"
        print(f"   {status} {element_name}")
        if found:
            passed_functions += 1
    
    # النتيجة النهائية
    function_rate = (passed_functions / total_functions) * 100
    print(f"\n📊 دالة المعالجة: {passed_functions}/{total_functions} ({function_rate:.1f}%)")
    
    if passed_functions == total_functions:
        print("✅ تم تطبيق دالة المعالجة بنجاح!")
        return True
    else:
        print(f"❌ {total_functions - passed_functions} عنصر دالة مفقود!")
        return False

def test_error_handling_and_messages():
    """اختبار معالجة الأخطاء والرسائل"""
    print("\n🧪 اختبار معالجة الأخطاء والرسائل")
    print("=" * 70)
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن دالة handle_show_user_manager_cards_count
    func_match = re.search(r'def handle_show_user_manager_cards_count.*?(?=def|\Z)', content, re.DOTALL)
    if not func_match:
        print("❌ لم يتم العثور على دالة handle_show_user_manager_cards_count")
        return False
    
    func_content = func_match.group(0)
    
    # فحص رسائل الأخطاء والمعالجة
    error_elements = [
        ("خطأ مكتبة RouterOS", "مكتبة RouterOS API غير مثبتة"),
        ("خطأ الاتصال", "فشل في الاتصال بخادم MikroTik"),
        ("الأسباب المحتملة", "الأسباب المحتملة"),
        ("الحلول المقترحة", "الحلول:"),
        ("خطأ جلب البيانات", "خطأ في جلب البيانات من User Manager"),
        ("تفاصيل الخطأ", "تفاصيل الخطأ"),
        ("خطأ عام", "خطأ عام في عرض عدد الكروت"),
        ("رسائل نجاح", "تم الاتصال بنجاح"),
        ("معلومات إضافية", "ملاحظة.*هذا العدد يشمل جميع المستخدمين")
    ]
    
    print("🔍 فحص معالجة الأخطاء والرسائل:")
    passed_errors = 0
    total_errors = len(error_elements)
    
    for element_name, pattern in error_elements:
        found = re.search(pattern, func_content, re.DOTALL)
        status = "✅" if found else "❌"
        print(f"   {status} {element_name}")
        if found:
            passed_errors += 1
    
    # النتيجة النهائية
    error_rate = (passed_errors / total_errors) * 100
    print(f"\n📊 معالجة الأخطاء: {passed_errors}/{total_errors} ({error_rate:.1f}%)")
    
    if passed_errors == total_errors:
        print("✅ معالجة الأخطاء والرسائل مكتملة!")
        return True
    else:
        print(f"❌ {total_errors - passed_errors} عنصر معالجة أخطاء مفقود!")
        return False

def test_integration_and_positioning():
    """اختبار التكامل والموضع"""
    print("\n🧪 اختبار التكامل والموضع")
    print("=" * 70)
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # فحص التكامل والموضع
    integration_elements = [
        ("الزر في المكان الصحيح", "أسفل القوالب مباشرة"),
        ("الزر قبل زر العودة", "قبل أزرار الخيارات الأخرى"),
        ("callback_data فريد", "show_user_manager_cards_count"),
        ("دالة منفصلة للمعالجة", "handle_show_user_manager_cards_count"),
        ("يعمل فقط مع User Manager", "User Manager"),
        ("معالجة شاملة للأخطاء", "معالجة أخطاء شاملة")
    ]
    
    print("🔍 فحص التكامل والموضع:")
    passed_integration = 0
    total_integration = len(integration_elements)
    
    for element_name, pattern in integration_elements:
        found = pattern in content
        status = "✅" if found else "❌"
        print(f"   {status} {element_name}")
        if found:
            passed_integration += 1
    
    # النتيجة النهائية
    integration_rate = (passed_integration / total_integration) * 100
    print(f"\n📊 التكامل والموضع: {passed_integration}/{total_integration} ({integration_rate:.1f}%)")
    
    if passed_integration == total_integration:
        print("✅ التكامل والموضع صحيح!")
        return True
    else:
        print(f"❌ {total_integration - passed_integration} عنصر تكامل يحتاج تحسين!")
        return False

def run_all_tests():
    """تشغيل جميع الاختبارات"""
    print("🚀 بدء اختبار زر عرض عدد الكروت في User Manager")
    print("=" * 80)
    
    tests = [
        ("اختبار إضافة الزر في قائمة User Manager", test_button_addition_in_user_manager_templates),
        ("اختبار إضافة معالج callback_data", test_callback_handler_addition),
        ("اختبار تطبيق دالة المعالجة", test_handler_function_implementation),
        ("اختبار معالجة الأخطاء والرسائل", test_error_handling_and_messages),
        ("اختبار التكامل والموضع", test_integration_and_positioning)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} نجح")
            else:
                print(f"❌ {test_name} فشل")
        except Exception as e:
            print(f"❌ {test_name} فشل بخطأ: {str(e)}")
    
    # النتائج النهائية
    print("\n" + "=" * 80)
    print("📊 النتائج النهائية:")
    print(f"✅ نجح: {passed}/{total}")
    print(f"❌ فشل: {total - passed}/{total}")
    
    if passed == total:
        print("\n🎉 جميع الاختبارات نجحت! زر عرض عدد الكروت تم تطبيقه بنجاح.")
        print("\n📋 الميزات المطبقة:")
        print("• زر جديد في قائمة User Manager: '📊 عرض عدد الكروت'")
        print("• معالج callback_data: 'show_user_manager_cards_count'")
        print("• دالة معالجة منفصلة: handle_show_user_manager_cards_count()")
        print("• اتصال بـ MikroTik وجلب عدد المستخدمين")
        print("• استخدام الأمر: /tool/user-manager/user")
        print("• عرض النتيجة مع تنسيق جميل")
        print("• معالجة أخطاء شاملة مع رسائل واضحة")
        print("• معلومات الخادم والوقت")
        return True
    else:
        print(f"\n❌ {total - passed} اختبار فشل. يحتاج إلى مراجعة.")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
