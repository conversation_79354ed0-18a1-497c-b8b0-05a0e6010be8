# MikroTik Script - نسخة احتياطية للكروت
# تم الإنشاء: 2025-07-25 01:38:46
# القالب: 10
# النظام: user_manager
# عدد الكروت: 200
# تم إنشاؤه بواسطة: مولد كروت MikroTik

:put "🚀 بدء تنفيذ النسخة الاحتياطية للكروت";
:put "📋 القالب: 10";
:put "🎯 النظام: user_manager";
:put "📊 عدد الكروت: 200";

:local success 0;
:local errors 0;
:local total 200;

:put "⏳ بدء إضافة الكروت...";

# إضافة مستخدمي User Manager
:put "👤 إضافة 200 مستخدم User Manager...";

# المستخدم 1: 0113229838
:do {
    /tool user-manager user add customer="admin" username="0113229838" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0113229838";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0113229838";
};

# المستخدم 2: 0153193306
:do {
    /tool user-manager user add customer="admin" username="0153193306" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0153193306";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0153193306";
};

# المستخدم 3: 0168743109
:do {
    /tool user-manager user add customer="admin" username="0168743109" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0168743109";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0168743109";
};

# المستخدم 4: 0115809735
:do {
    /tool user-manager user add customer="admin" username="0115809735" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0115809735";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0115809735";
};

# المستخدم 5: 0157593767
:do {
    /tool user-manager user add customer="admin" username="0157593767" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0157593767";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0157593767";
};

# المستخدم 6: 0155723586
:do {
    /tool user-manager user add customer="admin" username="0155723586" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0155723586";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0155723586";
};

# المستخدم 7: 0155220539
:do {
    /tool user-manager user add customer="admin" username="0155220539" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0155220539";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0155220539";
};

# المستخدم 8: 0134738045
:do {
    /tool user-manager user add customer="admin" username="0134738045" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0134738045";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0134738045";
};

# المستخدم 9: 0135094655
:do {
    /tool user-manager user add customer="admin" username="0135094655" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0135094655";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0135094655";
};

# المستخدم 10: 0188847768
:do {
    /tool user-manager user add customer="admin" username="0188847768" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0188847768";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0188847768";
};

# المستخدم 11: 0162932479
:do {
    /tool user-manager user add customer="admin" username="0162932479" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0162932479";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0162932479";
};

# المستخدم 12: 0149639278
:do {
    /tool user-manager user add customer="admin" username="0149639278" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0149639278";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0149639278";
};

# المستخدم 13: 0129096630
:do {
    /tool user-manager user add customer="admin" username="0129096630" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0129096630";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0129096630";
};

# المستخدم 14: 0132489086
:do {
    /tool user-manager user add customer="admin" username="0132489086" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0132489086";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0132489086";
};

# المستخدم 15: 0163511937
:do {
    /tool user-manager user add customer="admin" username="0163511937" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0163511937";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0163511937";
};

# المستخدم 16: 0188199282
:do {
    /tool user-manager user add customer="admin" username="0188199282" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0188199282";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0188199282";
};

# المستخدم 17: 0116369564
:do {
    /tool user-manager user add customer="admin" username="0116369564" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0116369564";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0116369564";
};

# المستخدم 18: 0122945886
:do {
    /tool user-manager user add customer="admin" username="0122945886" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0122945886";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0122945886";
};

# المستخدم 19: 0110755228
:do {
    /tool user-manager user add customer="admin" username="0110755228" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0110755228";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0110755228";
};

# المستخدم 20: 0144333847
:do {
    /tool user-manager user add customer="admin" username="0144333847" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0144333847";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0144333847";
};

# المستخدم 21: 0186692287
:do {
    /tool user-manager user add customer="admin" username="0186692287" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0186692287";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0186692287";
};

# المستخدم 22: 0115560134
:do {
    /tool user-manager user add customer="admin" username="0115560134" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0115560134";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0115560134";
};

# المستخدم 23: 0178758880
:do {
    /tool user-manager user add customer="admin" username="0178758880" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0178758880";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0178758880";
};

# المستخدم 24: 0181379749
:do {
    /tool user-manager user add customer="admin" username="0181379749" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0181379749";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0181379749";
};

# المستخدم 25: 0181963044
:do {
    /tool user-manager user add customer="admin" username="0181963044" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0181963044";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0181963044";
};

# المستخدم 26: 0170973185
:do {
    /tool user-manager user add customer="admin" username="0170973185" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0170973185";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0170973185";
};

# المستخدم 27: 0196581380
:do {
    /tool user-manager user add customer="admin" username="0196581380" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0196581380";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0196581380";
};

# المستخدم 28: 0198958358
:do {
    /tool user-manager user add customer="admin" username="0198958358" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0198958358";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0198958358";
};

# المستخدم 29: 0198797551
:do {
    /tool user-manager user add customer="admin" username="0198797551" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0198797551";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0198797551";
};

# المستخدم 30: 0155960420
:do {
    /tool user-manager user add customer="admin" username="0155960420" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0155960420";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0155960420";
};

# المستخدم 31: 0105717322
:do {
    /tool user-manager user add customer="admin" username="0105717322" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0105717322";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0105717322";
};

# المستخدم 32: 0162872006
:do {
    /tool user-manager user add customer="admin" username="0162872006" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0162872006";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0162872006";
};

# المستخدم 33: 0195133528
:do {
    /tool user-manager user add customer="admin" username="0195133528" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0195133528";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0195133528";
};

# المستخدم 34: 0137398620
:do {
    /tool user-manager user add customer="admin" username="0137398620" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0137398620";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0137398620";
};

# المستخدم 35: 0199550700
:do {
    /tool user-manager user add customer="admin" username="0199550700" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0199550700";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0199550700";
};

# المستخدم 36: 0154120535
:do {
    /tool user-manager user add customer="admin" username="0154120535" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0154120535";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0154120535";
};

# المستخدم 37: 0169453566
:do {
    /tool user-manager user add customer="admin" username="0169453566" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0169453566";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0169453566";
};

# المستخدم 38: 0155170808
:do {
    /tool user-manager user add customer="admin" username="0155170808" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0155170808";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0155170808";
};

# المستخدم 39: 0190464780
:do {
    /tool user-manager user add customer="admin" username="0190464780" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0190464780";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0190464780";
};

# المستخدم 40: 0149775704
:do {
    /tool user-manager user add customer="admin" username="0149775704" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0149775704";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0149775704";
};

# المستخدم 41: 0112845261
:do {
    /tool user-manager user add customer="admin" username="0112845261" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0112845261";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0112845261";
};

# المستخدم 42: 0145918820
:do {
    /tool user-manager user add customer="admin" username="0145918820" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0145918820";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0145918820";
};

# المستخدم 43: 0127539226
:do {
    /tool user-manager user add customer="admin" username="0127539226" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0127539226";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0127539226";
};

# المستخدم 44: 0178272314
:do {
    /tool user-manager user add customer="admin" username="0178272314" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0178272314";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0178272314";
};

# المستخدم 45: 0146618186
:do {
    /tool user-manager user add customer="admin" username="0146618186" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0146618186";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0146618186";
};

# المستخدم 46: 0143450735
:do {
    /tool user-manager user add customer="admin" username="0143450735" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0143450735";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0143450735";
};

# المستخدم 47: 0181796438
:do {
    /tool user-manager user add customer="admin" username="0181796438" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0181796438";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0181796438";
};

# المستخدم 48: 0199919443
:do {
    /tool user-manager user add customer="admin" username="0199919443" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0199919443";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0199919443";
};

# المستخدم 49: 0173133814
:do {
    /tool user-manager user add customer="admin" username="0173133814" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0173133814";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0173133814";
};

# المستخدم 50: 0133072005
:do {
    /tool user-manager user add customer="admin" username="0133072005" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0133072005";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0133072005";
};

# المستخدم 51: 0116274135
:do {
    /tool user-manager user add customer="admin" username="0116274135" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0116274135";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0116274135";
};

# المستخدم 52: 0178295271
:do {
    /tool user-manager user add customer="admin" username="0178295271" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0178295271";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0178295271";
};

# المستخدم 53: 0188901547
:do {
    /tool user-manager user add customer="admin" username="0188901547" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0188901547";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0188901547";
};

# المستخدم 54: 0116550335
:do {
    /tool user-manager user add customer="admin" username="0116550335" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0116550335";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0116550335";
};

# المستخدم 55: 0174107731
:do {
    /tool user-manager user add customer="admin" username="0174107731" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0174107731";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0174107731";
};

# المستخدم 56: 0136886911
:do {
    /tool user-manager user add customer="admin" username="0136886911" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0136886911";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0136886911";
};

# المستخدم 57: 0128894248
:do {
    /tool user-manager user add customer="admin" username="0128894248" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0128894248";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0128894248";
};

# المستخدم 58: 0105511893
:do {
    /tool user-manager user add customer="admin" username="0105511893" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0105511893";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0105511893";
};

# المستخدم 59: 0162622572
:do {
    /tool user-manager user add customer="admin" username="0162622572" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0162622572";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0162622572";
};

# المستخدم 60: 0183884540
:do {
    /tool user-manager user add customer="admin" username="0183884540" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0183884540";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0183884540";
};

# المستخدم 61: 0174731405
:do {
    /tool user-manager user add customer="admin" username="0174731405" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0174731405";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0174731405";
};

# المستخدم 62: 0179759966
:do {
    /tool user-manager user add customer="admin" username="0179759966" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0179759966";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0179759966";
};

# المستخدم 63: 0189897264
:do {
    /tool user-manager user add customer="admin" username="0189897264" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0189897264";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0189897264";
};

# المستخدم 64: 0142112719
:do {
    /tool user-manager user add customer="admin" username="0142112719" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0142112719";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0142112719";
};

# المستخدم 65: 0177983395
:do {
    /tool user-manager user add customer="admin" username="0177983395" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0177983395";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0177983395";
};

# المستخدم 66: 0139066348
:do {
    /tool user-manager user add customer="admin" username="0139066348" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0139066348";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0139066348";
};

# المستخدم 67: 0153323900
:do {
    /tool user-manager user add customer="admin" username="0153323900" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0153323900";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0153323900";
};

# المستخدم 68: 0124960649
:do {
    /tool user-manager user add customer="admin" username="0124960649" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0124960649";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0124960649";
};

# المستخدم 69: 0113200685
:do {
    /tool user-manager user add customer="admin" username="0113200685" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0113200685";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0113200685";
};

# المستخدم 70: 0104915016
:do {
    /tool user-manager user add customer="admin" username="0104915016" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0104915016";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0104915016";
};

# المستخدم 71: 0118695300
:do {
    /tool user-manager user add customer="admin" username="0118695300" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0118695300";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0118695300";
};

# المستخدم 72: 0141403862
:do {
    /tool user-manager user add customer="admin" username="0141403862" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0141403862";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0141403862";
};

# المستخدم 73: 0176267289
:do {
    /tool user-manager user add customer="admin" username="0176267289" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0176267289";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0176267289";
};

# المستخدم 74: 0134751670
:do {
    /tool user-manager user add customer="admin" username="0134751670" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0134751670";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0134751670";
};

# المستخدم 75: 0131512848
:do {
    /tool user-manager user add customer="admin" username="0131512848" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0131512848";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0131512848";
};

# المستخدم 76: 0116310295
:do {
    /tool user-manager user add customer="admin" username="0116310295" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0116310295";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0116310295";
};

# المستخدم 77: 0198346813
:do {
    /tool user-manager user add customer="admin" username="0198346813" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0198346813";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0198346813";
};

# المستخدم 78: 0125707576
:do {
    /tool user-manager user add customer="admin" username="0125707576" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0125707576";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0125707576";
};

# المستخدم 79: 0123175986
:do {
    /tool user-manager user add customer="admin" username="0123175986" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0123175986";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0123175986";
};

# المستخدم 80: 0153352216
:do {
    /tool user-manager user add customer="admin" username="0153352216" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0153352216";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0153352216";
};

# المستخدم 81: 0130276473
:do {
    /tool user-manager user add customer="admin" username="0130276473" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0130276473";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0130276473";
};

# المستخدم 82: 0169732794
:do {
    /tool user-manager user add customer="admin" username="0169732794" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0169732794";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0169732794";
};

# المستخدم 83: 0191714436
:do {
    /tool user-manager user add customer="admin" username="0191714436" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0191714436";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0191714436";
};

# المستخدم 84: 0140956606
:do {
    /tool user-manager user add customer="admin" username="0140956606" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0140956606";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0140956606";
};

# المستخدم 85: 0123155991
:do {
    /tool user-manager user add customer="admin" username="0123155991" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0123155991";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0123155991";
};

# المستخدم 86: 0144373814
:do {
    /tool user-manager user add customer="admin" username="0144373814" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0144373814";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0144373814";
};

# المستخدم 87: 0150911788
:do {
    /tool user-manager user add customer="admin" username="0150911788" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0150911788";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0150911788";
};

# المستخدم 88: 0112581589
:do {
    /tool user-manager user add customer="admin" username="0112581589" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0112581589";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0112581589";
};

# المستخدم 89: 0119098861
:do {
    /tool user-manager user add customer="admin" username="0119098861" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0119098861";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0119098861";
};

# المستخدم 90: 0168312647
:do {
    /tool user-manager user add customer="admin" username="0168312647" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0168312647";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0168312647";
};

# المستخدم 91: 0121646812
:do {
    /tool user-manager user add customer="admin" username="0121646812" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0121646812";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0121646812";
};

# المستخدم 92: 0190150395
:do {
    /tool user-manager user add customer="admin" username="0190150395" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0190150395";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0190150395";
};

# المستخدم 93: 0181092979
:do {
    /tool user-manager user add customer="admin" username="0181092979" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0181092979";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0181092979";
};

# المستخدم 94: 0172078842
:do {
    /tool user-manager user add customer="admin" username="0172078842" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0172078842";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0172078842";
};

# المستخدم 95: 0158985067
:do {
    /tool user-manager user add customer="admin" username="0158985067" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0158985067";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0158985067";
};

# المستخدم 96: 0198564879
:do {
    /tool user-manager user add customer="admin" username="0198564879" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0198564879";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0198564879";
};

# المستخدم 97: 0109306425
:do {
    /tool user-manager user add customer="admin" username="0109306425" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0109306425";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0109306425";
};

# المستخدم 98: 0184593353
:do {
    /tool user-manager user add customer="admin" username="0184593353" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0184593353";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0184593353";
};

# المستخدم 99: 0147296285
:do {
    /tool user-manager user add customer="admin" username="0147296285" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0147296285";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0147296285";
};

# المستخدم 100: 0185547152
:do {
    /tool user-manager user add customer="admin" username="0185547152" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0185547152";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0185547152";
};

# المستخدم 101: 0124564903
:do {
    /tool user-manager user add customer="admin" username="0124564903" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0124564903";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0124564903";
};

# المستخدم 102: 0107339480
:do {
    /tool user-manager user add customer="admin" username="0107339480" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0107339480";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0107339480";
};

# المستخدم 103: 0158444200
:do {
    /tool user-manager user add customer="admin" username="0158444200" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0158444200";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0158444200";
};

# المستخدم 104: 0174982918
:do {
    /tool user-manager user add customer="admin" username="0174982918" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0174982918";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0174982918";
};

# المستخدم 105: 0137390536
:do {
    /tool user-manager user add customer="admin" username="0137390536" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0137390536";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0137390536";
};

# المستخدم 106: 0127393018
:do {
    /tool user-manager user add customer="admin" username="0127393018" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0127393018";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0127393018";
};

# المستخدم 107: 0173334197
:do {
    /tool user-manager user add customer="admin" username="0173334197" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0173334197";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0173334197";
};

# المستخدم 108: 0133915034
:do {
    /tool user-manager user add customer="admin" username="0133915034" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0133915034";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0133915034";
};

# المستخدم 109: 0128320957
:do {
    /tool user-manager user add customer="admin" username="0128320957" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0128320957";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0128320957";
};

# المستخدم 110: 0134017862
:do {
    /tool user-manager user add customer="admin" username="0134017862" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0134017862";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0134017862";
};

# المستخدم 111: 0119709463
:do {
    /tool user-manager user add customer="admin" username="0119709463" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0119709463";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0119709463";
};

# المستخدم 112: 0192121648
:do {
    /tool user-manager user add customer="admin" username="0192121648" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0192121648";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0192121648";
};

# المستخدم 113: 0156004645
:do {
    /tool user-manager user add customer="admin" username="0156004645" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0156004645";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0156004645";
};

# المستخدم 114: 0167321670
:do {
    /tool user-manager user add customer="admin" username="0167321670" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0167321670";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0167321670";
};

# المستخدم 115: 0198294322
:do {
    /tool user-manager user add customer="admin" username="0198294322" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0198294322";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0198294322";
};

# المستخدم 116: 0191232422
:do {
    /tool user-manager user add customer="admin" username="0191232422" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0191232422";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0191232422";
};

# المستخدم 117: 0118898099
:do {
    /tool user-manager user add customer="admin" username="0118898099" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0118898099";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0118898099";
};

# المستخدم 118: 0121620442
:do {
    /tool user-manager user add customer="admin" username="0121620442" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0121620442";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0121620442";
};

# المستخدم 119: 0184587037
:do {
    /tool user-manager user add customer="admin" username="0184587037" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0184587037";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0184587037";
};

# المستخدم 120: 0179035240
:do {
    /tool user-manager user add customer="admin" username="0179035240" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0179035240";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0179035240";
};

# المستخدم 121: 0137000094
:do {
    /tool user-manager user add customer="admin" username="0137000094" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0137000094";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0137000094";
};

# المستخدم 122: 0138233857
:do {
    /tool user-manager user add customer="admin" username="0138233857" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0138233857";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0138233857";
};

# المستخدم 123: 0196113354
:do {
    /tool user-manager user add customer="admin" username="0196113354" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0196113354";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0196113354";
};

# المستخدم 124: 0173417800
:do {
    /tool user-manager user add customer="admin" username="0173417800" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0173417800";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0173417800";
};

# المستخدم 125: 0112558455
:do {
    /tool user-manager user add customer="admin" username="0112558455" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0112558455";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0112558455";
};

# المستخدم 126: 0115100740
:do {
    /tool user-manager user add customer="admin" username="0115100740" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0115100740";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0115100740";
};

# المستخدم 127: 0173764396
:do {
    /tool user-manager user add customer="admin" username="0173764396" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0173764396";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0173764396";
};

# المستخدم 128: 0196547815
:do {
    /tool user-manager user add customer="admin" username="0196547815" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0196547815";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0196547815";
};

# المستخدم 129: 0197331292
:do {
    /tool user-manager user add customer="admin" username="0197331292" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0197331292";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0197331292";
};

# المستخدم 130: 0156837093
:do {
    /tool user-manager user add customer="admin" username="0156837093" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0156837093";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0156837093";
};

# المستخدم 131: 0160934899
:do {
    /tool user-manager user add customer="admin" username="0160934899" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0160934899";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0160934899";
};

# المستخدم 132: 0184680123
:do {
    /tool user-manager user add customer="admin" username="0184680123" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0184680123";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0184680123";
};

# المستخدم 133: 0135784330
:do {
    /tool user-manager user add customer="admin" username="0135784330" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0135784330";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0135784330";
};

# المستخدم 134: 0163688521
:do {
    /tool user-manager user add customer="admin" username="0163688521" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0163688521";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0163688521";
};

# المستخدم 135: 0132029368
:do {
    /tool user-manager user add customer="admin" username="0132029368" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0132029368";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0132029368";
};

# المستخدم 136: 0185945055
:do {
    /tool user-manager user add customer="admin" username="0185945055" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0185945055";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0185945055";
};

# المستخدم 137: 0165594658
:do {
    /tool user-manager user add customer="admin" username="0165594658" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0165594658";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0165594658";
};

# المستخدم 138: 0171104291
:do {
    /tool user-manager user add customer="admin" username="0171104291" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0171104291";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0171104291";
};

# المستخدم 139: 0107220406
:do {
    /tool user-manager user add customer="admin" username="0107220406" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0107220406";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0107220406";
};

# المستخدم 140: 0132295947
:do {
    /tool user-manager user add customer="admin" username="0132295947" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0132295947";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0132295947";
};

# المستخدم 141: 0148323547
:do {
    /tool user-manager user add customer="admin" username="0148323547" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0148323547";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0148323547";
};

# المستخدم 142: 0107988029
:do {
    /tool user-manager user add customer="admin" username="0107988029" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0107988029";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0107988029";
};

# المستخدم 143: 0134382029
:do {
    /tool user-manager user add customer="admin" username="0134382029" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0134382029";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0134382029";
};

# المستخدم 144: 0117785916
:do {
    /tool user-manager user add customer="admin" username="0117785916" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0117785916";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0117785916";
};

# المستخدم 145: 0170651221
:do {
    /tool user-manager user add customer="admin" username="0170651221" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0170651221";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0170651221";
};

# المستخدم 146: 0171706477
:do {
    /tool user-manager user add customer="admin" username="0171706477" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0171706477";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0171706477";
};

# المستخدم 147: 0168766986
:do {
    /tool user-manager user add customer="admin" username="0168766986" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0168766986";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0168766986";
};

# المستخدم 148: 0162110804
:do {
    /tool user-manager user add customer="admin" username="0162110804" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0162110804";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0162110804";
};

# المستخدم 149: 0193035052
:do {
    /tool user-manager user add customer="admin" username="0193035052" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0193035052";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0193035052";
};

# المستخدم 150: 0102177259
:do {
    /tool user-manager user add customer="admin" username="0102177259" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0102177259";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0102177259";
};

# المستخدم 151: 0157861744
:do {
    /tool user-manager user add customer="admin" username="0157861744" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0157861744";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0157861744";
};

# المستخدم 152: 0178229507
:do {
    /tool user-manager user add customer="admin" username="0178229507" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0178229507";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0178229507";
};

# المستخدم 153: 0169526620
:do {
    /tool user-manager user add customer="admin" username="0169526620" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0169526620";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0169526620";
};

# المستخدم 154: 0191739504
:do {
    /tool user-manager user add customer="admin" username="0191739504" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0191739504";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0191739504";
};

# المستخدم 155: 0180383273
:do {
    /tool user-manager user add customer="admin" username="0180383273" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0180383273";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0180383273";
};

# المستخدم 156: 0141232009
:do {
    /tool user-manager user add customer="admin" username="0141232009" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0141232009";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0141232009";
};

# المستخدم 157: 0138676274
:do {
    /tool user-manager user add customer="admin" username="0138676274" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0138676274";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0138676274";
};

# المستخدم 158: 0137214507
:do {
    /tool user-manager user add customer="admin" username="0137214507" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0137214507";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0137214507";
};

# المستخدم 159: 0189209366
:do {
    /tool user-manager user add customer="admin" username="0189209366" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0189209366";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0189209366";
};

# المستخدم 160: 0175297753
:do {
    /tool user-manager user add customer="admin" username="0175297753" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0175297753";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0175297753";
};

# المستخدم 161: 0179340370
:do {
    /tool user-manager user add customer="admin" username="0179340370" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0179340370";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0179340370";
};

# المستخدم 162: 0152369835
:do {
    /tool user-manager user add customer="admin" username="0152369835" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0152369835";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0152369835";
};

# المستخدم 163: 0176035181
:do {
    /tool user-manager user add customer="admin" username="0176035181" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0176035181";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0176035181";
};

# المستخدم 164: 0187187260
:do {
    /tool user-manager user add customer="admin" username="0187187260" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0187187260";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0187187260";
};

# المستخدم 165: 0198703704
:do {
    /tool user-manager user add customer="admin" username="0198703704" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0198703704";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0198703704";
};

# المستخدم 166: 0141696976
:do {
    /tool user-manager user add customer="admin" username="0141696976" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0141696976";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0141696976";
};

# المستخدم 167: 0111943865
:do {
    /tool user-manager user add customer="admin" username="0111943865" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0111943865";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0111943865";
};

# المستخدم 168: 0192777353
:do {
    /tool user-manager user add customer="admin" username="0192777353" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0192777353";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0192777353";
};

# المستخدم 169: 0100650669
:do {
    /tool user-manager user add customer="admin" username="0100650669" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0100650669";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0100650669";
};

# المستخدم 170: 0123555509
:do {
    /tool user-manager user add customer="admin" username="0123555509" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0123555509";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0123555509";
};

# المستخدم 171: 0106235163
:do {
    /tool user-manager user add customer="admin" username="0106235163" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0106235163";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0106235163";
};

# المستخدم 172: 0153999848
:do {
    /tool user-manager user add customer="admin" username="0153999848" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0153999848";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0153999848";
};

# المستخدم 173: 0179111852
:do {
    /tool user-manager user add customer="admin" username="0179111852" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0179111852";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0179111852";
};

# المستخدم 174: 0167891230
:do {
    /tool user-manager user add customer="admin" username="0167891230" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0167891230";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0167891230";
};

# المستخدم 175: 0120220001
:do {
    /tool user-manager user add customer="admin" username="0120220001" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0120220001";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0120220001";
};

# المستخدم 176: 0102917219
:do {
    /tool user-manager user add customer="admin" username="0102917219" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0102917219";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0102917219";
};

# المستخدم 177: 0178197379
:do {
    /tool user-manager user add customer="admin" username="0178197379" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0178197379";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0178197379";
};

# المستخدم 178: 0126018830
:do {
    /tool user-manager user add customer="admin" username="0126018830" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0126018830";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0126018830";
};

# المستخدم 179: 0171287594
:do {
    /tool user-manager user add customer="admin" username="0171287594" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0171287594";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0171287594";
};

# المستخدم 180: 0167885237
:do {
    /tool user-manager user add customer="admin" username="0167885237" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0167885237";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0167885237";
};

# المستخدم 181: 0135000150
:do {
    /tool user-manager user add customer="admin" username="0135000150" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0135000150";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0135000150";
};

# المستخدم 182: 0133807394
:do {
    /tool user-manager user add customer="admin" username="0133807394" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0133807394";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0133807394";
};

# المستخدم 183: 0144001667
:do {
    /tool user-manager user add customer="admin" username="0144001667" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0144001667";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0144001667";
};

# المستخدم 184: 0105832505
:do {
    /tool user-manager user add customer="admin" username="0105832505" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0105832505";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0105832505";
};

# المستخدم 185: 0182891642
:do {
    /tool user-manager user add customer="admin" username="0182891642" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0182891642";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0182891642";
};

# المستخدم 186: 0165706226
:do {
    /tool user-manager user add customer="admin" username="0165706226" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0165706226";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0165706226";
};

# المستخدم 187: 0170479212
:do {
    /tool user-manager user add customer="admin" username="0170479212" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0170479212";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0170479212";
};

# المستخدم 188: 0155494281
:do {
    /tool user-manager user add customer="admin" username="0155494281" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0155494281";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0155494281";
};

# المستخدم 189: 0193633922
:do {
    /tool user-manager user add customer="admin" username="0193633922" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0193633922";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0193633922";
};

# المستخدم 190: 0174913320
:do {
    /tool user-manager user add customer="admin" username="0174913320" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0174913320";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0174913320";
};

# المستخدم 191: 0176354546
:do {
    /tool user-manager user add customer="admin" username="0176354546" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0176354546";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0176354546";
};

# المستخدم 192: 0152623566
:do {
    /tool user-manager user add customer="admin" username="0152623566" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0152623566";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0152623566";
};

# المستخدم 193: 0188098579
:do {
    /tool user-manager user add customer="admin" username="0188098579" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0188098579";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0188098579";
};

# المستخدم 194: 0138138785
:do {
    /tool user-manager user add customer="admin" username="0138138785" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0138138785";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0138138785";
};

# المستخدم 195: 0197677912
:do {
    /tool user-manager user add customer="admin" username="0197677912" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0197677912";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0197677912";
};

# المستخدم 196: 0129960143
:do {
    /tool user-manager user add customer="admin" username="0129960143" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0129960143";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0129960143";
};

# المستخدم 197: 0102256057
:do {
    /tool user-manager user add customer="admin" username="0102256057" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0102256057";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0102256057";
};

# المستخدم 198: 0172066161
:do {
    /tool user-manager user add customer="admin" username="0172066161" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0172066161";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0172066161";
};

# المستخدم 199: 0118118482
:do {
    /tool user-manager user add customer="admin" username="0118118482" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0118118482";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0118118482";
};

# المستخدم 200: 0121670980
:do {
    /tool user-manager user add customer="admin" username="0121670980" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0121670980";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0121670980";
};


:put "📊 تم الانتهاء من النسخة الاحتياطية";
:put "✅ نجح: $success كرت";
:put "❌ فشل: $errors كرت";
:put "📈 الإجمالي: $total كرت";

:put "🎉 تم استعادة جميع كروت القالب: 10";
:put "💡 النسخة الاحتياطية مكتملة بنجاح!";
