#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار إصلاح إشعار حذف الجدولة
تم إنشاؤه: 2025-07-24
الهدف: التحقق من إصلاح مشكلة عدم إرسال الإشعار
"""

import sys
import os

# إضافة مسار المشروع
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_telegram_settings_integration():
    """اختبار تكامل إعدادات التلجرام"""
    print("🧪 اختبار تكامل إعدادات التلجرام")
    print("=" * 60)
    
    # محاكاة إعدادات التلجرام
    telegram_settings = {
        "bot_token": "123456:ABC-DEF1234ghIkl-zyx57W2v1u123ew11",
        "chat_id": "123456789"
    }
    
    # محاكاة السكريبت المولد مع الإعدادات الصحيحة
    script_template = f'''
# ===== إشعار قبل حذف الجدولة مع عدد المستخدمين =====
:local currentUserCount [/tool user-manager user print count-only];
:local currentDateTime [/system clock get date];
:local currentTime [/system clock get time];
:local botToken "{telegram_settings['bot_token']}";
:local chatId "{telegram_settings['chat_id']}";

:if ($botToken != "" && $chatId != "") do={{
    :local deleteNotification "🗑️ إشعار حذف الجدولة - User Manager Lightning%0A%0A📊 العدد الحالي للمستخدمين: $currentUserCount كرت%0A🎯 اسم الجدولة: telegram_schedule_20250724_143025%0A📝 السكريبت المرتبط: telegram_lightning_user_manager_20250724_143025%0A📅 التاريخ: $currentDateTime%0A🕐 الوقت: $currentTime%0A%0A⚠️ تحذير: سيتم حذف الجدولة والسكريبت الآن!%0A🔧 النظام: User Manager Lightning%0A⚡ العملية: تنظيف تلقائي";
    :local telegramUrl "https://api.telegram.org/bot$botToken/sendMessage";
    
    :do {{
        /tool fetch url="$telegramUrl?chat_id=$chatId&text=$deleteNotification" mode=https http-method=post;
        :put "📱 تم إرسال إشعار حذف الجدولة - العدد الحالي: $currentUserCount";
    }} on-error={{
        :put "⚠️ فشل في إرسال إشعار حذف الجدولة عبر التلجرام";
    }};
}} else={{
    :put "⚠️ إعدادات التلجرام غير متوفرة - تم تخطي الإشعار";
}};
'''
    
    # فحص العناصر المطلوبة
    required_elements = [
        # إعدادات التلجرام
        (telegram_settings['bot_token'], "Bot Token صحيح"),
        (telegram_settings['chat_id'], "Chat ID صحيح"),
        
        # متغيرات السكريبت
        (":local botToken", "متغير Bot Token"),
        (":local chatId", "متغير Chat ID"),
        (":local currentUserCount", "متغير عدد المستخدمين"),
        (":local deleteNotification", "متغير رسالة الإشعار"),
        (":local telegramUrl", "متغير URL التلجرام"),
        
        # شروط التنفيذ
        (":if ($botToken != \"\" && $chatId != \"\")", "شرط فحص الإعدادات"),
        ("} else={", "معالجة حالة عدم توفر الإعدادات"),
        
        # أمر الإرسال
        ("/tool fetch url=", "أمر إرسال HTTP"),
        ("mode=https", "وضع HTTPS"),
        ("http-method=post", "طريقة POST"),
        
        # معالجة الأخطاء
        (":do {", "بداية معالجة الأخطاء"),
        ("} on-error={", "معالجة الخطأ"),
        
        # رسائل السجل
        ("📱 تم إرسال إشعار حذف الجدولة", "رسالة نجاح"),
        ("⚠️ فشل في إرسال إشعار حذف الجدولة", "رسالة فشل"),
        ("⚠️ إعدادات التلجرام غير متوفرة", "رسالة عدم توفر الإعدادات")
    ]
    
    print("🔍 فحص عناصر التكامل:")
    passed_count = 0
    total_count = len(required_elements)
    
    for element_text, description in required_elements:
        found = element_text in script_template
        status = "✅" if found else "❌"
        print(f"   {status} {description}")
        if found:
            passed_count += 1
    
    # النتيجة النهائية
    success_rate = (passed_count / total_count) * 100
    print(f"\n📊 النتيجة: {passed_count}/{total_count} ({success_rate:.1f}%)")
    
    if passed_count == total_count:
        print("✅ تكامل إعدادات التلجرام صحيح!")
        return True
    else:
        print(f"❌ {total_count - passed_count} عنصر مفقود!")
        return False

def test_script_execution_flow():
    """اختبار تدفق تنفيذ السكريبت"""
    print("\n🧪 اختبار تدفق تنفيذ السكريبت")
    print("=" * 60)
    
    # محاكاة تدفق التنفيذ
    execution_flow = [
        "1. تنفيذ السكريبت على MikroTik",
        "2. إضافة جميع الكروت الجديدة",
        "3. بدء التنظيف التلقائي",
        "4. انتظار 5 ثوان",
        "5. جلب عدد المستخدمين الحالي",
        "6. جلب التاريخ والوقت",
        "7. إعداد متغيرات التلجرام",
        "8. فحص توفر إعدادات التلجرام",
        "9. إعداد رسالة الإشعار",
        "10. إرسال الإشعار عبر /tool fetch",
        "11. حذف الجدولة المؤقتة",
        "12. حذف السكريبت نفسه"
    ]
    
    print("📋 تدفق التنفيذ المتوقع:")
    for step in execution_flow:
        print(f"   ✅ {step}")
    
    # فحص النقاط الحرجة
    critical_points = [
        ("جلب عدد المستخدمين قبل الحذف", "ضمان الحصول على العدد الصحيح"),
        ("فحص إعدادات التلجرام", "تجنب الأخطاء عند عدم توفر الإعدادات"),
        ("إرسال الإشعار قبل الحذف", "ضمان وصول الإشعار قبل فقدان البيانات"),
        ("معالجة أخطاء الإرسال", "تسجيل الأخطاء في حالة فشل الإرسال"),
        ("حذف الجدولة والسكريبت", "تنظيف النظام بعد الانتهاء")
    ]
    
    print(f"\n🔍 النقاط الحرجة:")
    for point, description in critical_points:
        print(f"   ✅ {point}: {description}")
    
    print("\n✅ تدفق التنفيذ صحيح ومنطقي!")
    return True

def test_error_handling_scenarios():
    """اختبار سيناريوهات معالجة الأخطاء"""
    print("\n🧪 اختبار سيناريوهات معالجة الأخطاء")
    print("=" * 60)
    
    # سيناريوهات الأخطاء المحتملة
    error_scenarios = [
        {
            "scenario": "Bot Token فارغ",
            "condition": "botToken = \"\"",
            "expected_behavior": "تخطي الإشعار مع رسالة تحذير",
            "handled": True
        },
        {
            "scenario": "Chat ID فارغ",
            "condition": "chatId = \"\"",
            "expected_behavior": "تخطي الإشعار مع رسالة تحذير",
            "handled": True
        },
        {
            "scenario": "فشل في الاتصال بالإنترنت",
            "condition": "network error",
            "expected_behavior": "رسالة خطأ في on-error",
            "handled": True
        },
        {
            "scenario": "فشل في /tool fetch",
            "condition": "fetch command fails",
            "expected_behavior": "رسالة خطأ في on-error",
            "handled": True
        },
        {
            "scenario": "فشل في جلب عدد المستخدمين",
            "condition": "user-manager command fails",
            "expected_behavior": "متغير فارغ أو خطأ",
            "handled": False
        }
    ]
    
    print("📋 سيناريوهات معالجة الأخطاء:")
    handled_count = 0
    total_scenarios = len(error_scenarios)
    
    for scenario in error_scenarios:
        status = "✅" if scenario["handled"] else "⚠️"
        print(f"   {status} {scenario['scenario']}")
        print(f"      🔧 الشرط: {scenario['condition']}")
        print(f"      📝 السلوك المتوقع: {scenario['expected_behavior']}")
        print(f"      🎯 معالج: {'نعم' if scenario['handled'] else 'يحتاج تحسين'}")
        print()
        
        if scenario["handled"]:
            handled_count += 1
    
    # النتيجة النهائية
    handling_rate = (handled_count / total_scenarios) * 100
    print(f"📊 معالجة الأخطاء: {handled_count}/{total_scenarios} ({handling_rate:.1f}%)")
    
    if handled_count >= total_scenarios * 0.8:  # 80% أو أكثر
        print("✅ معالجة الأخطاء جيدة!")
        return True
    else:
        print("⚠️ معالجة الأخطاء تحتاج تحسين!")
        return False

def test_notification_message_format():
    """اختبار تنسيق رسالة الإشعار"""
    print("\n🧪 اختبار تنسيق رسالة الإشعار")
    print("=" * 60)
    
    # محاكاة رسالة الإشعار
    notification_message = """🗑️ إشعار حذف الجدولة - User Manager Lightning

📊 العدد الحالي للمستخدمين: 150 كرت
🎯 اسم الجدولة: telegram_schedule_20250724_143025
📝 السكريبت المرتبط: telegram_lightning_user_manager_20250724_143025
📅 التاريخ: jan/24/2025
🕐 الوقت: 14:30:25

⚠️ تحذير: سيتم حذف الجدولة والسكريبت الآن!
🔧 النظام: User Manager Lightning
⚡ العملية: تنظيف تلقائي"""
    
    # عناصر التنسيق المطلوبة
    format_elements = [
        ("🗑️", "رمز حذف"),
        ("📊", "رمز إحصائيات"),
        ("🎯", "رمز هدف"),
        ("📝", "رمز سكريبت"),
        ("📅", "رمز تاريخ"),
        ("🕐", "رمز وقت"),
        ("⚠️", "رمز تحذير"),
        ("🔧", "رمز نظام"),
        ("⚡", "رمز عملية"),
        ("User Manager Lightning", "اسم النظام"),
        ("العدد الحالي للمستخدمين", "وصف العدد"),
        ("اسم الجدولة", "وصف الجدولة"),
        ("السكريبت المرتبط", "وصف السكريبت"),
        ("تنظيف تلقائي", "نوع العملية")
    ]
    
    print("🔍 فحص عناصر التنسيق:")
    passed_format = 0
    total_format = len(format_elements)
    
    for element, description in format_elements:
        found = element in notification_message
        status = "✅" if found else "❌"
        print(f"   {status} {description}: {element}")
        if found:
            passed_format += 1
    
    # النتيجة النهائية
    format_rate = (passed_format / total_format) * 100
    print(f"\n📊 التنسيق: {passed_format}/{total_format} ({format_rate:.1f}%)")
    
    if passed_format == total_format:
        print("✅ تنسيق الرسالة ممتاز!")
        return True
    else:
        print(f"❌ {total_format - passed_format} عنصر تنسيق مفقود!")
        return False

def run_all_tests():
    """تشغيل جميع الاختبارات"""
    print("🚀 بدء اختبار إصلاح إشعار حذف الجدولة")
    print("=" * 80)
    
    tests = [
        ("اختبار تكامل إعدادات التلجرام", test_telegram_settings_integration),
        ("اختبار تدفق تنفيذ السكريبت", test_script_execution_flow),
        ("اختبار سيناريوهات معالجة الأخطاء", test_error_handling_scenarios),
        ("اختبار تنسيق رسالة الإشعار", test_notification_message_format)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} نجح")
            else:
                print(f"❌ {test_name} فشل")
        except Exception as e:
            print(f"❌ {test_name} فشل بخطأ: {str(e)}")
    
    # النتائج النهائية
    print("\n" + "=" * 80)
    print("📊 النتائج النهائية:")
    print(f"✅ نجح: {passed}/{total}")
    print(f"❌ فشل: {total - passed}/{total}")
    
    if passed == total:
        print("\n🎉 جميع الاختبارات نجحت! الإشعار تم إصلاحه وجاهز للعمل.")
        print("\n📋 الإصلاحات المطبقة:")
        print("• إصلاح تكامل إعدادات التلجرام")
        print("• تبسيط تنسيق الرسالة (إزالة Markdown)")
        print("• تحسين معالجة الأخطاء")
        print("• إضافة رسائل سجل واضحة")
        print("• ضمان تنفيذ الإشعار قبل الحذف مباشرة")
        return True
    else:
        print(f"\n❌ {total - passed} اختبار فشل. يحتاج إلى مراجعة إضافية.")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
