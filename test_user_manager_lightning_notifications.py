#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار ميزة الإشعارات التلقائية في User Manager Lightning
تم إنشاؤه: 2025-07-24
الهدف: اختبار إضافة الإشعارات التلقائية قبل بدء إنشاء الكروت وقبل التنظيف التلقائي
"""

import unittest
import sys
import os
import re
from unittest.mock import Mock, patch, MagicMock

# إضافة مسار المشروع
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

class TestUserManagerLightningNotifications(unittest.TestCase):
    """اختبار ميزة الإشعارات التلقائية في User Manager Lightning"""
    
    def setUp(self):
        """إعداد الاختبار"""
        # إنشاء mock للتطبيق
        self.mock_app = Mock()
        self.mock_app.logger = Mock()
        self.mock_app.system_type = 'user_manager'
        self.mock_app.telegram_bot_token = 'test_bot_token'
        self.mock_app.telegram_chat_id = 'test_chat_id'
        self.mock_app.current_user_info = 'مستخدم الاختبار'
        
        # إعداد mock للحقول المطلوبة
        self.mock_app.version_combo = Mock()
        self.mock_app.version_combo.get.return_value = 'v6'
        self.mock_app.customer_entry = Mock()
        self.mock_app.customer_entry.get.return_value = 'admin'
        self.mock_app.profile_combo = Mock()
        self.mock_app.profile_combo.get.return_value = 'CARD'
        
        # إعداد بيانات الكروت المولدة
        self.mock_app.generated_credentials = [
            {'username': '01234567', 'password': 'pass1'},
            {'username': '01234568', 'password': 'pass2'},
            {'username': '01234569', 'password': 'pass3'}
        ]
        
        # إعداد الحقول الإضافية
        self.mock_app.caller_id_bind_var = Mock()
        self.mock_app.caller_id_bind_var.get.return_value = False
        self.mock_app.user_email = Mock()
        self.mock_app.user_email.get.return_value = ''
        self.mock_app.run_script_before_cleanup_var = Mock()
        self.mock_app.run_script_before_cleanup_var.get.return_value = False
        self.mock_app.script_to_run_entry = Mock()
        self.mock_app.script_to_run_entry.get.return_value = ''

    def test_start_notification_in_script(self):
        """اختبار وجود إشعار بدء إنشاء الكروت في السكريبت"""
        
        # محاكاة دالة generate_user_manager_fast_script
        def mock_generate_script():
            bot_token = getattr(self.mock_app, 'telegram_bot_token', '')
            chat_id = getattr(self.mock_app, 'telegram_chat_id', '')
            current_user = getattr(self.mock_app, 'current_user_info', 'مستخدم غير محدد')
            
            script_lines = []
            
            if bot_token and chat_id:
                script_lines.extend([
                    '# ===== إشعار بدء إنشاء الكروت - User Manager Lightning =====',
                    ':local scriptName "UserCountTelegram";',
                    ':local userCount [/tool user-manager user print count-only];',
                    f':local Token "{bot_token}";',
                    f':local chatId "{chat_id}";',
                    f':local currentUser "{current_user}";',
                    ':local message "🚀 **بدء عملية إنشاء الكروت - User Manager Lightning**\\n\\n📊 **العدد الإجمالي الحالي للكروت:** $userCount\\n👤 **المستخدم:** $currentUser\\n⚡ **النظام:** User Manager (البرق)\\n🕐 **الوقت:** $[/system clock get time]\\n📅 **التاريخ:** $[/system clock get date]\\n\\n⏳ **الحالة:** جاري بدء إنشاء الكروت...";',
                    '',
                    ':local telegramUrl "https://api.telegram.org/bot$Token/sendMessage";',
                    '',
                    ':do {',
                    '    /tool fetch url="$telegramUrl?chat_id=$chatId&text=$message&parse_mode=Markdown" mode=https http-method=post;',
                    '    :put "✅ تم إرسال إشعار بدء إنشاء الكروت: $userCount كرت حالي";',
                    '} on-error={',
                    '    :put "❌ فشل في إرسال إشعار بدء إنشاء الكروت";',
                    '};',
                    '',
                    '# ===== بدء إنشاء الكروت =====',
                    ''
                ])
            
            # إضافة باقي السكريبت
            script_lines.extend([
                ':local usr {',
                '    "01234567"="pass1" ;',
                '    "01234568"="pass2" ;',
                '    "01234569"="pass3"',
                '};',
                '',
                ':put "🚀 بدء الوضع السريع - إضافة المستخدمين...";'
            ])
            
            return '\n'.join(script_lines)
        
        # تنفيذ الاختبار
        script_content = mock_generate_script()
        
        # التحقق من وجود إشعار البداية
        self.assertIn('إشعار بدء إنشاء الكروت - User Manager Lightning', script_content)
        self.assertIn('🚀 **بدء عملية إنشاء الكروت - User Manager Lightning**', script_content)
        self.assertIn('العدد الإجمالي الحالي للكروت', script_content)
        self.assertIn('المستخدم:', script_content)
        self.assertIn('User Manager (البرق)', script_content)
        self.assertIn('جاري بدء إنشاء الكروت', script_content)
        self.assertIn('parse_mode=Markdown', script_content)
        
        print("✅ اختبار إشعار بدء إنشاء الكروت نجح")

    def test_cleanup_notification_in_script(self):
        """اختبار وجود إشعار قبل التنظيف التلقائي في السكريبت"""
        
        # محاكاة أوامر التنظيف مع الإشعار
        def mock_cleanup_commands():
            bot_token = 'test_bot_token'
            chat_id = 'test_chat_id'
            script_name = 'test_script'
            schedule_name = 'test_schedule'
            
            cleanup_commands = f'''

# ===== إشعار قبل التنظيف التلقائي - User Manager Lightning =====
:local finalUserCount [/tool user-manager user print count-only];
:local cleanupToken "{bot_token}";
:local cleanupChatId "{chat_id}";
:local cleanupMessage "🧹 **إشعار قبل التنظيف التلقائي - User Manager Lightning**\\n\\n📊 **العدد الإجمالي للكروت قبل التنظيف:** $finalUserCount\\n⚡ **النظام:** User Manager (البرق)\\n🕐 **الوقت:** $[/system clock get time]\\n📅 **التاريخ:** $[/system clock get date]\\n\\n🧹 **الحالة:** جاري بدء التنظيف التلقائي للسكريبتات...";

:local cleanupTelegramUrl "https://api.telegram.org/bot$cleanupToken/sendMessage";

:do {{
    /tool fetch url="$cleanupTelegramUrl?chat_id=$cleanupChatId&text=$cleanupMessage&parse_mode=Markdown" mode=https http-method=post;
    :put "✅ تم إرسال إشعار قبل التنظيف التلقائي: $finalUserCount كرت";
}} on-error={{
    :put "❌ فشل في إرسال إشعار قبل التنظيف التلقائي";
}};

# ===== التنظيف التلقائي بعد انتهاء البرق =====
:put "🧹 بدء التنظيف التلقائي للبرق...";

# انتظار 5 ثواني للتأكد من اكتمال إضافة جميع الكروت
:delay 5s;

# حذف الجدولة المؤقتة
:do {{
    /system scheduler remove [find name="{schedule_name}"];
    :put "✅ تم حذف الجدولة المؤقتة: {schedule_name}";
}} on-error={{
    :put "⚠️ لم يتم العثور على الجدولة للحذف: {schedule_name}";
}};

# حذف السكريبت نفسه (يجب أن يكون آخر أمر)
:do {{
    /system script remove [find name="{script_name}"];
    :put "✅ تم حذف السكريبت: {script_name}";
}} on-error={{
    :put "⚠️ لم يتم العثور على السكريبت للحذف: {script_name}";
}};

:put "🎉 تم إكمال البرق والتنظيف التلقائي بنجاح!";
'''
            return cleanup_commands
        
        # تنفيذ الاختبار
        cleanup_content = mock_cleanup_commands()
        
        # التحقق من وجود إشعار التنظيف
        self.assertIn('إشعار قبل التنظيف التلقائي - User Manager Lightning', cleanup_content)
        self.assertIn('🧹 **إشعار قبل التنظيف التلقائي - User Manager Lightning**', cleanup_content)
        self.assertIn('العدد الإجمالي للكروت قبل التنظيف', cleanup_content)
        self.assertIn('User Manager (البرق)', cleanup_content)
        self.assertIn('جاري بدء التنظيف التلقائي للسكريبتات', cleanup_content)
        self.assertIn('parse_mode=Markdown', cleanup_content)
        
        print("✅ اختبار إشعار قبل التنظيف التلقائي نجح")

    def test_batch_cleanup_notification(self):
        """اختبار إشعار التنظيف للمجموعات المتعددة"""
        
        # محاكاة إشعار التنظيف للمجموعة
        def mock_batch_cleanup_notification(batch_number):
            bot_token = 'test_bot_token'
            chat_id = 'test_chat_id'
            
            cleanup_commands = f'''

# ===== إشعار قبل التنظيف التلقائي - المجموعة {batch_number} =====
:local batchUserCount [/tool user-manager user print count-only];
:local batchCleanupToken "{bot_token}";
:local batchCleanupChatId "{chat_id}";
:local batchCleanupMessage "🧹 **إشعار قبل التنظيف التلقائي - المجموعة {batch_number}**\\n\\n📊 **العدد الإجمالي للكروت قبل التنظيف:** $batchUserCount\\n⚡ **النظام:** User Manager (البرق)\\n🔢 **المجموعة:** {batch_number}\\n🕐 **الوقت:** $[/system clock get time]\\n📅 **التاريخ:** $[/system clock get date]\\n\\n🧹 **الحالة:** جاري بدء التنظيف التلقائي للسكريبتات...";

:local batchCleanupTelegramUrl "https://api.telegram.org/bot$batchCleanupToken/sendMessage";

:do {{
    /tool fetch url="$batchCleanupTelegramUrl?chat_id=$batchCleanupChatId&text=$batchCleanupMessage&parse_mode=Markdown" mode=https http-method=post;
    :put "✅ تم إرسال إشعار قبل التنظيف التلقائي للمجموعة {batch_number}: $batchUserCount كرت";
}} on-error={{
    :put "❌ فشل في إرسال إشعار قبل التنظيف التلقائي للمجموعة {batch_number}";
}};
'''
            return cleanup_commands
        
        # اختبار المجموعة الأولى
        batch1_content = mock_batch_cleanup_notification(1)
        self.assertIn('إشعار قبل التنظيف التلقائي - المجموعة 1', batch1_content)
        self.assertIn('**المجموعة:** 1', batch1_content)

        # اختبار المجموعة الثانية
        batch2_content = mock_batch_cleanup_notification(2)
        self.assertIn('إشعار قبل التنظيف التلقائي - المجموعة 2', batch2_content)
        self.assertIn('**المجموعة:** 2', batch2_content)
        
        print("✅ اختبار إشعار التنظيف للمجموعات المتعددة نجح")

    def test_user_info_setting(self):
        """اختبار تحديد معلومات المستخدم"""
        
        # محاكاة دالة تحديد معلومات المستخدم
        def mock_set_current_user_info(user_info):
            self.mock_app.current_user_info = user_info
            return True
        
        # اختبار تحديد معلومات المستخدم
        result = mock_set_current_user_info("مستخدم التلجرام بوت")
        self.assertTrue(result)
        self.assertEqual(self.mock_app.current_user_info, "مستخدم التلجرام بوت")
        
        # اختبار تحديد معلومات مستخدم آخر
        result = mock_set_current_user_info("مستخدم الواجهة الرئيسية")
        self.assertTrue(result)
        self.assertEqual(self.mock_app.current_user_info, "مستخدم الواجهة الرئيسية")
        
        print("✅ اختبار تحديد معلومات المستخدم نجح")

    def test_notification_message_format(self):
        """اختبار تنسيق رسائل الإشعارات"""
        
        # اختبار تنسيق رسالة البداية
        start_message = "🚀 **بدء عملية إنشاء الكروت - User Manager Lightning**\\n\\n📊 **العدد الإجمالي الحالي للكروت:** $userCount\\n👤 **المستخدم:** $currentUser\\n⚡ **النظام:** User Manager (البرق)\\n🕐 **الوقت:** $[/system clock get time]\\n📅 **التاريخ:** $[/system clock get date]\\n\\n⏳ **الحالة:** جاري بدء إنشاء الكروت..."
        
        # التحقق من وجود العناصر المطلوبة
        self.assertIn('🚀', start_message)  # أيقونة البداية
        self.assertIn('📊', start_message)  # أيقونة الإحصائيات
        self.assertIn('👤', start_message)  # أيقونة المستخدم
        self.assertIn('⚡', start_message)  # أيقونة البرق
        self.assertIn('🕐', start_message)  # أيقونة الوقت
        self.assertIn('📅', start_message)  # أيقونة التاريخ
        self.assertIn('⏳', start_message)  # أيقونة الحالة
        self.assertIn('**', start_message)  # تنسيق Markdown
        
        # اختبار تنسيق رسالة التنظيف
        cleanup_message = "🧹 **إشعار قبل التنظيف التلقائي - User Manager Lightning**\\n\\n📊 **العدد الإجمالي للكروت قبل التنظيف:** $finalUserCount\\n⚡ **النظام:** User Manager (البرق)\\n🕐 **الوقت:** $[/system clock get time]\\n📅 **التاريخ:** $[/system clock get date]\\n\\n🧹 **الحالة:** جاري بدء التنظيف التلقائي للسكريبتات..."
        
        # التحقق من وجود العناصر المطلوبة
        self.assertIn('🧹', cleanup_message)  # أيقونة التنظيف
        self.assertIn('📊', cleanup_message)  # أيقونة الإحصائيات
        self.assertIn('⚡', cleanup_message)  # أيقونة البرق
        self.assertIn('🕐', cleanup_message)  # أيقونة الوقت
        self.assertIn('📅', cleanup_message)  # أيقونة التاريخ
        self.assertIn('**', cleanup_message)  # تنسيق Markdown
        
        print("✅ اختبار تنسيق رسائل الإشعارات نجح")

def run_tests():
    """تشغيل جميع الاختبارات"""
    print("🧪 بدء اختبار ميزة الإشعارات التلقائية في User Manager Lightning")
    print("=" * 70)
    
    # إنشاء مجموعة الاختبارات
    test_suite = unittest.TestLoader().loadTestsFromTestCase(TestUserManagerLightningNotifications)
    
    # تشغيل الاختبارات
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # عرض النتائج
    print("\n" + "=" * 70)
    if result.wasSuccessful():
        print("🎉 جميع الاختبارات نجحت!")
        print(f"✅ تم تشغيل {result.testsRun} اختبار بنجاح")
    else:
        print("❌ بعض الاختبارات فشلت!")
        print(f"❌ فشل {len(result.failures)} اختبار")
        print(f"⚠️ خطأ في {len(result.errors)} اختبار")
    
    return result.wasSuccessful()

if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1)
