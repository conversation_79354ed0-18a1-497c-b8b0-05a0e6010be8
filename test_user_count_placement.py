#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار وضع كود إحصاء المستخدمين في المكان الصحيح
يختبر أن الكود يوضع:
1. في بداية الاسكربت قبل :local usr {
2. قبل أوامر :delay 5s;
3. قبل أوامر حذف الجدولة
"""

def test_user_count_placement():
    """اختبار وضع كود إحصاء المستخدمين في المكان الصحيح"""
    print("🧪 اختبار وضع كود إحصاء المستخدمين في المكان الصحيح...")
    
    # محاكاة إنشاء اسكربت User Manager مع كود إحصاء المستخدمين
    bot_token = "test_bot_token_123"
    chat_id = "998535391"
    
    # بناء الاسكربت كما هو في الكود الفعلي
    script_lines = []
    
    # إضافة كود إحصاء المستخدمين في البداية
    if bot_token and chat_id:
        script_lines.extend([
            '# ===== إرسال إحصاء المستخدمين عبر Telegram =====',
            ':local scriptName "UserCountTelegram";',
            ':local userCount [/tool user-manager user print count-only];',
            f':local Token "{bot_token}";',
            f':local chatId "{chat_id}";',
            ':local message "Users in Usermanger $userCount";',
            '',
            ':local telegramUrl "https://api.telegram.org/bot$Token/sendMessage";',
            '',
            ':do {',
            '    /tool fetch url="$telegramUrl?chat_id=$chatId&text=$message" mode=https http-method=post;',
            '    :put "✅ تم إرسال إحصاء المستخدمين: $userCount";',
            '} on-error={',
            '    :put "❌ فشل في إرسال إحصاء المستخدمين";',
            '};',
            '',
            '# ===== بدء إنشاء الكروت =====',
            ''
        ])
    
    # إضافة :local usr {
    script_lines.append(':local usr {')
    
    # إضافة بيانات المستخدمين
    script_lines.extend([
        '    "0175668628"="password1" ;',
        '    "0112707552"="password2"'
    ])
    
    script_lines.extend([
        '};',
        '',
        ':put "🚀 بدء الوضع السريع - إضافة المستخدمين...";',
        ':local count 0;',
        ':local total [:len $usr];',
        ':put "📊 العدد الإجمالي: $total مستخدم";',
        '',
        ':foreach u,p in=$usr do={',
        '    :do {',
        '        /tool user-manager user add username=$u password=$p customer="test";',
        '        :set count ($count + 1);',
        '    } on-error={ :put "❌ خطأ في إضافة المستخدم: $u"; };',
        '}',
        '',
        ':put "✅ تم إضافة $count مستخدم بنجاح";'
    ])
    
    # إضافة كود إحصاء المستخدمين قبل الانتهاء
    if bot_token and chat_id:
        script_lines.extend([
            '',
            '# ===== إرسال إحصاء المستخدمين النهائي عبر Telegram =====',
            ':local finalUserCount [/tool user-manager user print count-only];',
            f':local finalToken "{bot_token}";',
            f':local finalChatId "{chat_id}";',
            ':local finalMessage "Users in Usermanger $finalUserCount";',
            '',
            ':local finalTelegramUrl "https://api.telegram.org/bot$finalToken/sendMessage";',
            '',
            ':do {',
            '    /tool fetch url="$finalTelegramUrl?chat_id=$finalChatId&text=$finalMessage" mode=https http-method=post;',
            '    :put "✅ تم إرسال إحصاء المستخدمين النهائي: $finalUserCount";',
            '} on-error={',
            '    :put "❌ فشل في إرسال إحصاء المستخدمين النهائي";',
            '};',
            ''
        ])
    
    script_lines.extend([
        '',
        ':put "🎉 تم الانتهاء من الوضع السريع بنجاح!";'
    ])
    
    # تجميع الاسكربت
    full_script = '\n'.join(script_lines)
    
    # اختبار الترتيب الصحيح
    lines = full_script.split('\n')
    
    # العثور على مؤشرات المواقع المهمة
    user_count_start_index = -1
    local_usr_index = -1
    user_count_end_index = -1
    finish_message_index = -1
    
    for i, line in enumerate(lines):
        if 'إرسال إحصاء المستخدمين عبر Telegram' in line:
            user_count_start_index = i
        elif ':local usr {' in line:
            local_usr_index = i
        elif 'إرسال إحصاء المستخدمين النهائي عبر Telegram' in line:
            user_count_end_index = i
        elif 'تم الانتهاء من الوضع السريع بنجاح' in line:
            finish_message_index = i
    
    # التحقق من الترتيب الصحيح
    assert user_count_start_index != -1, "يجب أن يحتوي الاسكربت على كود إحصاء المستخدمين في البداية"
    assert local_usr_index != -1, "يجب أن يحتوي الاسكربت على :local usr {"
    assert user_count_end_index != -1, "يجب أن يحتوي الاسكربت على كود إحصاء المستخدمين في النهاية"
    assert finish_message_index != -1, "يجب أن يحتوي الاسكربت على رسالة الانتهاء"
    
    # التحقق من الترتيب
    assert user_count_start_index < local_usr_index, "كود إحصاء المستخدمين يجب أن يكون قبل :local usr {"
    assert local_usr_index < user_count_end_index, ":local usr { يجب أن يكون قبل كود إحصاء المستخدمين النهائي"
    assert user_count_end_index < finish_message_index, "كود إحصاء المستخدمين النهائي يجب أن يكون قبل رسالة الانتهاء"
    
    # التحقق من وجود الأوامر المطلوبة
    assert ':local scriptName "UserCountTelegram"' in full_script, "يجب أن يحتوي على اسم السكريبت"
    assert ':local userCount [/tool user-manager user print count-only]' in full_script, "يجب أن يحتوي على أمر عد المستخدمين"
    assert 'Users in Usermanger $userCount' in full_script, "يجب أن يحتوي على نص الرسالة"
    assert '/tool fetch url=' in full_script, "يجب أن يحتوي على أمر إرسال HTTP"
    assert 'mode=https http-method=post' in full_script, "يجب أن يحتوي على معاملات HTTP الصحيحة"
    
    # التحقق من وجود إعدادات Telegram
    assert bot_token in full_script, "يجب أن يحتوي على bot_token"
    assert chat_id in full_script, "يجب أن يحتوي على chat_id"
    
    print("✅ تم وضع كود إحصاء المستخدمين في المكان الصحيح")
    return True

def test_script_structure():
    """اختبار بنية الاسكربت الصحيحة"""
    print("🧪 اختبار بنية الاسكربت...")
    
    # التسلسل المطلوب
    expected_sequence = [
        "إرسال إحصاء المستخدمين عبر Telegram",  # كود إحصاء المستخدمين الأول
        ":local usr {",                              # بداية تعريف المستخدمين
        "0175668628",                                # بيانات المستخدمين
        "بدء الوضع السريع",                        # بداية معالجة المستخدمين
        ":foreach u,p in=$usr do={",                # حلقة إضافة المستخدمين
        "إرسال إحصاء المستخدمين النهائي",          # كود إحصاء المستخدمين الثاني
        "تم الانتهاء من الوضع السريع بنجاح"          # رسالة الانتهاء
    ]
    
    # محاكاة اسكربت مبسط
    script_content = '''
# ===== إرسال إحصاء المستخدمين عبر Telegram =====
:local scriptName "UserCountTelegram";
:local userCount [/tool user-manager user print count-only];

:local usr {
    "0175668628"="password1" ;
    "0112707552"="password2"
};

:put "🚀 بدء الوضع السريع - إضافة المستخدمين...";

:foreach u,p in=$usr do={
    /tool user-manager user add username=$u password=$p;
}

# ===== إرسال إحصاء المستخدمين النهائي عبر Telegram =====
:local finalUserCount [/tool user-manager user print count-only];

:put "🎉 تم الانتهاء من الوضع السريع بنجاح!";
'''
    
    # التحقق من التسلسل
    current_index = 0
    for expected_part in expected_sequence:
        found_index = script_content.find(expected_part, current_index)
        assert found_index != -1, f"لم يتم العثور على: {expected_part}"
        current_index = found_index
    
    print("✅ بنية الاسكربت صحيحة")
    return True

def test_mikrotik_commands():
    """اختبار صحة أوامر MikroTik"""
    print("🧪 اختبار أوامر MikroTik...")
    
    # الأوامر المطلوبة
    required_commands = [
        ":local scriptName \"UserCountTelegram\"",
        ":local userCount [/tool user-manager user print count-only]",
        ":local Token",
        ":local chatId", 
        ":local message \"Users in Usermanger $userCount\"",
        ":local telegramUrl \"https://api.telegram.org/bot$Token/sendMessage\"",
        "/tool fetch url=\"$telegramUrl?chat_id=$chatId&text=$message\" mode=https http-method=post"
    ]
    
    # محاكاة الكود المولد
    bot_token = "test_token"
    chat_id = "test_chat"
    
    generated_code = f'''
:local scriptName "UserCountTelegram";
:local userCount [/tool user-manager user print count-only];
:local Token "{bot_token}";
:local chatId "{chat_id}";
:local message "Users in Usermanger $userCount";

:local telegramUrl "https://api.telegram.org/bot$Token/sendMessage";

:do {{
    /tool fetch url="$telegramUrl?chat_id=$chatId&text=$message" mode=https http-method=post;
    :put "✅ تم إرسال إحصاء المستخدمين: $userCount";
}} on-error={{
    :put "❌ فشل في إرسال إحصاء المستخدمين";
}};
'''
    
    # التحقق من وجود الأوامر
    for command in required_commands:
        if command.startswith(":local Token") or command.startswith(":local chatId"):
            # للمتغيرات التي تحتوي على قيم، نتحقق من الجزء الأساسي
            base_command = command.split('"')[0]
            assert base_command in generated_code, f"الكود يجب أن يحتوي على: {base_command}"
        else:
            assert command in generated_code, f"الكود يجب أن يحتوي على: {command}"
    
    # التحقق من معالجة الأخطاء
    assert ":do {" in generated_code, "يجب أن يحتوي على بنية معالجة الأخطاء"
    assert "} on-error={" in generated_code, "يجب أن يحتوي على معالج الأخطاء"
    
    print("✅ أوامر MikroTik صحيحة")
    return True

def run_all_tests():
    """تشغيل جميع الاختبارات"""
    print("🚀 بدء اختبار وضع كود إحصاء المستخدمين في المكان الصحيح")
    print("=" * 70)
    
    tests = [
        test_user_count_placement,
        test_script_structure,
        test_mikrotik_commands
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ فشل الاختبار {test.__name__}: {str(e)}")
            failed += 1
    
    print("=" * 70)
    
    if failed == 0:
        print(f"🎉 جميع الاختبارات نجحت! ({passed}/{len(tests)})")
        print("✅ تم وضع كود إحصاء المستخدمين في المكان الصحيح")
        print("📍 الكود موضوع:")
        print("   1. في بداية الاسكربت قبل :local usr {")
        print("   2. قبل أوامر :delay 5s;")
        print("   3. قبل رسالة الانتهاء")
        return True
    else:
        print(f"❌ فشل {failed} اختبار من أصل {len(tests)}")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    if success:
        print("\n🎯 الخلاصة: تم وضع كود إحصاء المستخدمين في المكان الصحيح!")
    else:
        print("\n⚠️ الخلاصة: هناك مشاكل في وضع الكود!")
