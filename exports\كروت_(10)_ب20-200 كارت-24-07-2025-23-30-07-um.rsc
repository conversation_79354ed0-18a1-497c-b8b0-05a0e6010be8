# MikroTik Script - نسخة احتياطية للكروت
# تم الإنشاء: 2025-07-24 23:30:07
# القالب: 10
# النظام: user_manager
# عدد الكروت: 200
# تم إنشاؤه بواسطة: مولد كروت MikroTik

:put "🚀 بدء تنفيذ النسخة الاحتياطية للكروت";
:put "📋 القالب: 10";
:put "🎯 النظام: user_manager";
:put "📊 عدد الكروت: 200";

:local success 0;
:local errors 0;
:local total 200;

:put "⏳ بدء إضافة الكروت...";

# إضافة مستخدمي User Manager
:put "👤 إضافة 200 مستخدم User Manager...";

# المستخدم 1: 0143998815
:do {
    /tool user-manager user add customer="admin" username="0143998815" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0143998815";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0143998815";
};

# المستخدم 2: 0147506124
:do {
    /tool user-manager user add customer="admin" username="0147506124" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0147506124";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0147506124";
};

# المستخدم 3: 0147760809
:do {
    /tool user-manager user add customer="admin" username="0147760809" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0147760809";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0147760809";
};

# المستخدم 4: 0139456069
:do {
    /tool user-manager user add customer="admin" username="0139456069" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0139456069";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0139456069";
};

# المستخدم 5: 0117510959
:do {
    /tool user-manager user add customer="admin" username="0117510959" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0117510959";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0117510959";
};

# المستخدم 6: 0148150296
:do {
    /tool user-manager user add customer="admin" username="0148150296" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0148150296";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0148150296";
};

# المستخدم 7: 0114215542
:do {
    /tool user-manager user add customer="admin" username="0114215542" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0114215542";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0114215542";
};

# المستخدم 8: 0107146494
:do {
    /tool user-manager user add customer="admin" username="0107146494" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0107146494";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0107146494";
};

# المستخدم 9: 0102719994
:do {
    /tool user-manager user add customer="admin" username="0102719994" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0102719994";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0102719994";
};

# المستخدم 10: 0191730887
:do {
    /tool user-manager user add customer="admin" username="0191730887" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0191730887";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0191730887";
};

# المستخدم 11: 0100025941
:do {
    /tool user-manager user add customer="admin" username="0100025941" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0100025941";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0100025941";
};

# المستخدم 12: 0159381943
:do {
    /tool user-manager user add customer="admin" username="0159381943" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0159381943";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0159381943";
};

# المستخدم 13: 0152732236
:do {
    /tool user-manager user add customer="admin" username="0152732236" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0152732236";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0152732236";
};

# المستخدم 14: 0182096593
:do {
    /tool user-manager user add customer="admin" username="0182096593" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0182096593";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0182096593";
};

# المستخدم 15: 0154950071
:do {
    /tool user-manager user add customer="admin" username="0154950071" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0154950071";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0154950071";
};

# المستخدم 16: 0162781280
:do {
    /tool user-manager user add customer="admin" username="0162781280" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0162781280";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0162781280";
};

# المستخدم 17: 0192570169
:do {
    /tool user-manager user add customer="admin" username="0192570169" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0192570169";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0192570169";
};

# المستخدم 18: 0153062955
:do {
    /tool user-manager user add customer="admin" username="0153062955" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0153062955";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0153062955";
};

# المستخدم 19: 0126412784
:do {
    /tool user-manager user add customer="admin" username="0126412784" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0126412784";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0126412784";
};

# المستخدم 20: 0119819122
:do {
    /tool user-manager user add customer="admin" username="0119819122" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0119819122";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0119819122";
};

# المستخدم 21: 0132765429
:do {
    /tool user-manager user add customer="admin" username="0132765429" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0132765429";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0132765429";
};

# المستخدم 22: 0156163933
:do {
    /tool user-manager user add customer="admin" username="0156163933" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0156163933";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0156163933";
};

# المستخدم 23: 0167369034
:do {
    /tool user-manager user add customer="admin" username="0167369034" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0167369034";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0167369034";
};

# المستخدم 24: 0153681323
:do {
    /tool user-manager user add customer="admin" username="0153681323" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0153681323";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0153681323";
};

# المستخدم 25: 0190324574
:do {
    /tool user-manager user add customer="admin" username="0190324574" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0190324574";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0190324574";
};

# المستخدم 26: 0178487571
:do {
    /tool user-manager user add customer="admin" username="0178487571" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0178487571";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0178487571";
};

# المستخدم 27: 0122451412
:do {
    /tool user-manager user add customer="admin" username="0122451412" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0122451412";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0122451412";
};

# المستخدم 28: 0121279738
:do {
    /tool user-manager user add customer="admin" username="0121279738" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0121279738";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0121279738";
};

# المستخدم 29: 0144304385
:do {
    /tool user-manager user add customer="admin" username="0144304385" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0144304385";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0144304385";
};

# المستخدم 30: 0147204891
:do {
    /tool user-manager user add customer="admin" username="0147204891" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0147204891";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0147204891";
};

# المستخدم 31: 0102681519
:do {
    /tool user-manager user add customer="admin" username="0102681519" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0102681519";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0102681519";
};

# المستخدم 32: 0142280449
:do {
    /tool user-manager user add customer="admin" username="0142280449" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0142280449";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0142280449";
};

# المستخدم 33: 0180868242
:do {
    /tool user-manager user add customer="admin" username="0180868242" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0180868242";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0180868242";
};

# المستخدم 34: 0156040940
:do {
    /tool user-manager user add customer="admin" username="0156040940" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0156040940";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0156040940";
};

# المستخدم 35: 0113182031
:do {
    /tool user-manager user add customer="admin" username="0113182031" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0113182031";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0113182031";
};

# المستخدم 36: 0113029146
:do {
    /tool user-manager user add customer="admin" username="0113029146" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0113029146";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0113029146";
};

# المستخدم 37: 0143413544
:do {
    /tool user-manager user add customer="admin" username="0143413544" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0143413544";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0143413544";
};

# المستخدم 38: 0197643495
:do {
    /tool user-manager user add customer="admin" username="0197643495" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0197643495";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0197643495";
};

# المستخدم 39: 0146927952
:do {
    /tool user-manager user add customer="admin" username="0146927952" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0146927952";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0146927952";
};

# المستخدم 40: 0126202961
:do {
    /tool user-manager user add customer="admin" username="0126202961" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0126202961";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0126202961";
};

# المستخدم 41: 0144853888
:do {
    /tool user-manager user add customer="admin" username="0144853888" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0144853888";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0144853888";
};

# المستخدم 42: 0109804420
:do {
    /tool user-manager user add customer="admin" username="0109804420" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0109804420";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0109804420";
};

# المستخدم 43: 0157671837
:do {
    /tool user-manager user add customer="admin" username="0157671837" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0157671837";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0157671837";
};

# المستخدم 44: 0184048450
:do {
    /tool user-manager user add customer="admin" username="0184048450" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0184048450";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0184048450";
};

# المستخدم 45: 0168599724
:do {
    /tool user-manager user add customer="admin" username="0168599724" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0168599724";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0168599724";
};

# المستخدم 46: 0194718713
:do {
    /tool user-manager user add customer="admin" username="0194718713" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0194718713";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0194718713";
};

# المستخدم 47: 0141446440
:do {
    /tool user-manager user add customer="admin" username="0141446440" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0141446440";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0141446440";
};

# المستخدم 48: 0174902555
:do {
    /tool user-manager user add customer="admin" username="0174902555" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0174902555";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0174902555";
};

# المستخدم 49: 0161506912
:do {
    /tool user-manager user add customer="admin" username="0161506912" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0161506912";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0161506912";
};

# المستخدم 50: 0140217640
:do {
    /tool user-manager user add customer="admin" username="0140217640" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0140217640";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0140217640";
};

# المستخدم 51: 0102333188
:do {
    /tool user-manager user add customer="admin" username="0102333188" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0102333188";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0102333188";
};

# المستخدم 52: 0143249829
:do {
    /tool user-manager user add customer="admin" username="0143249829" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0143249829";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0143249829";
};

# المستخدم 53: 0128191098
:do {
    /tool user-manager user add customer="admin" username="0128191098" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0128191098";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0128191098";
};

# المستخدم 54: 0148970620
:do {
    /tool user-manager user add customer="admin" username="0148970620" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0148970620";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0148970620";
};

# المستخدم 55: 0151429809
:do {
    /tool user-manager user add customer="admin" username="0151429809" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0151429809";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0151429809";
};

# المستخدم 56: 0114806983
:do {
    /tool user-manager user add customer="admin" username="0114806983" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0114806983";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0114806983";
};

# المستخدم 57: 0193023539
:do {
    /tool user-manager user add customer="admin" username="0193023539" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0193023539";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0193023539";
};

# المستخدم 58: 0178446106
:do {
    /tool user-manager user add customer="admin" username="0178446106" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0178446106";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0178446106";
};

# المستخدم 59: 0180434923
:do {
    /tool user-manager user add customer="admin" username="0180434923" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0180434923";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0180434923";
};

# المستخدم 60: 0151362397
:do {
    /tool user-manager user add customer="admin" username="0151362397" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0151362397";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0151362397";
};

# المستخدم 61: 0178989816
:do {
    /tool user-manager user add customer="admin" username="0178989816" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0178989816";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0178989816";
};

# المستخدم 62: 0112087426
:do {
    /tool user-manager user add customer="admin" username="0112087426" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0112087426";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0112087426";
};

# المستخدم 63: 0110985976
:do {
    /tool user-manager user add customer="admin" username="0110985976" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0110985976";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0110985976";
};

# المستخدم 64: 0152934235
:do {
    /tool user-manager user add customer="admin" username="0152934235" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0152934235";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0152934235";
};

# المستخدم 65: 0187134065
:do {
    /tool user-manager user add customer="admin" username="0187134065" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0187134065";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0187134065";
};

# المستخدم 66: 0177444690
:do {
    /tool user-manager user add customer="admin" username="0177444690" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0177444690";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0177444690";
};

# المستخدم 67: 0184644472
:do {
    /tool user-manager user add customer="admin" username="0184644472" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0184644472";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0184644472";
};

# المستخدم 68: 0158498346
:do {
    /tool user-manager user add customer="admin" username="0158498346" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0158498346";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0158498346";
};

# المستخدم 69: 0154379069
:do {
    /tool user-manager user add customer="admin" username="0154379069" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0154379069";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0154379069";
};

# المستخدم 70: 0111539240
:do {
    /tool user-manager user add customer="admin" username="0111539240" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0111539240";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0111539240";
};

# المستخدم 71: 0147921302
:do {
    /tool user-manager user add customer="admin" username="0147921302" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0147921302";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0147921302";
};

# المستخدم 72: 0114504254
:do {
    /tool user-manager user add customer="admin" username="0114504254" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0114504254";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0114504254";
};

# المستخدم 73: 0137563141
:do {
    /tool user-manager user add customer="admin" username="0137563141" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0137563141";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0137563141";
};

# المستخدم 74: 0150807633
:do {
    /tool user-manager user add customer="admin" username="0150807633" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0150807633";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0150807633";
};

# المستخدم 75: 0106963671
:do {
    /tool user-manager user add customer="admin" username="0106963671" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0106963671";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0106963671";
};

# المستخدم 76: 0154670163
:do {
    /tool user-manager user add customer="admin" username="0154670163" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0154670163";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0154670163";
};

# المستخدم 77: 0150011804
:do {
    /tool user-manager user add customer="admin" username="0150011804" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0150011804";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0150011804";
};

# المستخدم 78: 0174643986
:do {
    /tool user-manager user add customer="admin" username="0174643986" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0174643986";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0174643986";
};

# المستخدم 79: 0193469160
:do {
    /tool user-manager user add customer="admin" username="0193469160" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0193469160";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0193469160";
};

# المستخدم 80: 0166841264
:do {
    /tool user-manager user add customer="admin" username="0166841264" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0166841264";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0166841264";
};

# المستخدم 81: 0127714175
:do {
    /tool user-manager user add customer="admin" username="0127714175" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0127714175";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0127714175";
};

# المستخدم 82: 0142603292
:do {
    /tool user-manager user add customer="admin" username="0142603292" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0142603292";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0142603292";
};

# المستخدم 83: 0117349098
:do {
    /tool user-manager user add customer="admin" username="0117349098" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0117349098";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0117349098";
};

# المستخدم 84: 0152501492
:do {
    /tool user-manager user add customer="admin" username="0152501492" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0152501492";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0152501492";
};

# المستخدم 85: 0197973977
:do {
    /tool user-manager user add customer="admin" username="0197973977" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0197973977";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0197973977";
};

# المستخدم 86: 0189569792
:do {
    /tool user-manager user add customer="admin" username="0189569792" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0189569792";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0189569792";
};

# المستخدم 87: 0139957323
:do {
    /tool user-manager user add customer="admin" username="0139957323" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0139957323";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0139957323";
};

# المستخدم 88: 0152864373
:do {
    /tool user-manager user add customer="admin" username="0152864373" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0152864373";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0152864373";
};

# المستخدم 89: 0127617040
:do {
    /tool user-manager user add customer="admin" username="0127617040" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0127617040";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0127617040";
};

# المستخدم 90: 0119777081
:do {
    /tool user-manager user add customer="admin" username="0119777081" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0119777081";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0119777081";
};

# المستخدم 91: 0125243766
:do {
    /tool user-manager user add customer="admin" username="0125243766" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0125243766";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0125243766";
};

# المستخدم 92: 0176223822
:do {
    /tool user-manager user add customer="admin" username="0176223822" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0176223822";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0176223822";
};

# المستخدم 93: 0121050741
:do {
    /tool user-manager user add customer="admin" username="0121050741" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0121050741";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0121050741";
};

# المستخدم 94: 0171846568
:do {
    /tool user-manager user add customer="admin" username="0171846568" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0171846568";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0171846568";
};

# المستخدم 95: 0193973428
:do {
    /tool user-manager user add customer="admin" username="0193973428" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0193973428";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0193973428";
};

# المستخدم 96: 0169362322
:do {
    /tool user-manager user add customer="admin" username="0169362322" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0169362322";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0169362322";
};

# المستخدم 97: 0151294762
:do {
    /tool user-manager user add customer="admin" username="0151294762" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0151294762";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0151294762";
};

# المستخدم 98: 0156880141
:do {
    /tool user-manager user add customer="admin" username="0156880141" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0156880141";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0156880141";
};

# المستخدم 99: 0166676936
:do {
    /tool user-manager user add customer="admin" username="0166676936" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0166676936";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0166676936";
};

# المستخدم 100: 0134138735
:do {
    /tool user-manager user add customer="admin" username="0134138735" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0134138735";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0134138735";
};

# المستخدم 101: 0198662786
:do {
    /tool user-manager user add customer="admin" username="0198662786" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0198662786";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0198662786";
};

# المستخدم 102: 0157920386
:do {
    /tool user-manager user add customer="admin" username="0157920386" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0157920386";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0157920386";
};

# المستخدم 103: 0164472167
:do {
    /tool user-manager user add customer="admin" username="0164472167" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0164472167";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0164472167";
};

# المستخدم 104: 0189987947
:do {
    /tool user-manager user add customer="admin" username="0189987947" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0189987947";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0189987947";
};

# المستخدم 105: 0128163748
:do {
    /tool user-manager user add customer="admin" username="0128163748" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0128163748";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0128163748";
};

# المستخدم 106: 0126034317
:do {
    /tool user-manager user add customer="admin" username="0126034317" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0126034317";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0126034317";
};

# المستخدم 107: 0136866097
:do {
    /tool user-manager user add customer="admin" username="0136866097" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0136866097";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0136866097";
};

# المستخدم 108: 0180518219
:do {
    /tool user-manager user add customer="admin" username="0180518219" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0180518219";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0180518219";
};

# المستخدم 109: 0111642423
:do {
    /tool user-manager user add customer="admin" username="0111642423" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0111642423";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0111642423";
};

# المستخدم 110: 0142090786
:do {
    /tool user-manager user add customer="admin" username="0142090786" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0142090786";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0142090786";
};

# المستخدم 111: 0160525381
:do {
    /tool user-manager user add customer="admin" username="0160525381" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0160525381";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0160525381";
};

# المستخدم 112: 0153403098
:do {
    /tool user-manager user add customer="admin" username="0153403098" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0153403098";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0153403098";
};

# المستخدم 113: 0137525355
:do {
    /tool user-manager user add customer="admin" username="0137525355" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0137525355";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0137525355";
};

# المستخدم 114: 0100617779
:do {
    /tool user-manager user add customer="admin" username="0100617779" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0100617779";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0100617779";
};

# المستخدم 115: 0196150935
:do {
    /tool user-manager user add customer="admin" username="0196150935" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0196150935";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0196150935";
};

# المستخدم 116: 0171498746
:do {
    /tool user-manager user add customer="admin" username="0171498746" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0171498746";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0171498746";
};

# المستخدم 117: 0153783904
:do {
    /tool user-manager user add customer="admin" username="0153783904" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0153783904";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0153783904";
};

# المستخدم 118: 0121001819
:do {
    /tool user-manager user add customer="admin" username="0121001819" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0121001819";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0121001819";
};

# المستخدم 119: 0139493618
:do {
    /tool user-manager user add customer="admin" username="0139493618" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0139493618";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0139493618";
};

# المستخدم 120: 0145192969
:do {
    /tool user-manager user add customer="admin" username="0145192969" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0145192969";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0145192969";
};

# المستخدم 121: 0148950616
:do {
    /tool user-manager user add customer="admin" username="0148950616" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0148950616";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0148950616";
};

# المستخدم 122: 0152286100
:do {
    /tool user-manager user add customer="admin" username="0152286100" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0152286100";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0152286100";
};

# المستخدم 123: 0138955128
:do {
    /tool user-manager user add customer="admin" username="0138955128" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0138955128";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0138955128";
};

# المستخدم 124: 0159672931
:do {
    /tool user-manager user add customer="admin" username="0159672931" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0159672931";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0159672931";
};

# المستخدم 125: 0106962017
:do {
    /tool user-manager user add customer="admin" username="0106962017" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0106962017";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0106962017";
};

# المستخدم 126: 0168009638
:do {
    /tool user-manager user add customer="admin" username="0168009638" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0168009638";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0168009638";
};

# المستخدم 127: 0140141162
:do {
    /tool user-manager user add customer="admin" username="0140141162" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0140141162";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0140141162";
};

# المستخدم 128: 0159870523
:do {
    /tool user-manager user add customer="admin" username="0159870523" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0159870523";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0159870523";
};

# المستخدم 129: 0101828716
:do {
    /tool user-manager user add customer="admin" username="0101828716" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0101828716";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0101828716";
};

# المستخدم 130: 0137158460
:do {
    /tool user-manager user add customer="admin" username="0137158460" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0137158460";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0137158460";
};

# المستخدم 131: 0132237491
:do {
    /tool user-manager user add customer="admin" username="0132237491" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0132237491";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0132237491";
};

# المستخدم 132: 0193026455
:do {
    /tool user-manager user add customer="admin" username="0193026455" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0193026455";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0193026455";
};

# المستخدم 133: 0155505843
:do {
    /tool user-manager user add customer="admin" username="0155505843" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0155505843";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0155505843";
};

# المستخدم 134: 0171052306
:do {
    /tool user-manager user add customer="admin" username="0171052306" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0171052306";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0171052306";
};

# المستخدم 135: 0101383456
:do {
    /tool user-manager user add customer="admin" username="0101383456" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0101383456";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0101383456";
};

# المستخدم 136: 0190343416
:do {
    /tool user-manager user add customer="admin" username="0190343416" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0190343416";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0190343416";
};

# المستخدم 137: 0158364700
:do {
    /tool user-manager user add customer="admin" username="0158364700" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0158364700";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0158364700";
};

# المستخدم 138: 0112653847
:do {
    /tool user-manager user add customer="admin" username="0112653847" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0112653847";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0112653847";
};

# المستخدم 139: 0184056429
:do {
    /tool user-manager user add customer="admin" username="0184056429" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0184056429";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0184056429";
};

# المستخدم 140: 0183620576
:do {
    /tool user-manager user add customer="admin" username="0183620576" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0183620576";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0183620576";
};

# المستخدم 141: 0119712764
:do {
    /tool user-manager user add customer="admin" username="0119712764" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0119712764";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0119712764";
};

# المستخدم 142: 0101028221
:do {
    /tool user-manager user add customer="admin" username="0101028221" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0101028221";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0101028221";
};

# المستخدم 143: 0145222315
:do {
    /tool user-manager user add customer="admin" username="0145222315" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0145222315";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0145222315";
};

# المستخدم 144: 0155956982
:do {
    /tool user-manager user add customer="admin" username="0155956982" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0155956982";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0155956982";
};

# المستخدم 145: 0110908635
:do {
    /tool user-manager user add customer="admin" username="0110908635" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0110908635";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0110908635";
};

# المستخدم 146: 0138972178
:do {
    /tool user-manager user add customer="admin" username="0138972178" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0138972178";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0138972178";
};

# المستخدم 147: 0183673123
:do {
    /tool user-manager user add customer="admin" username="0183673123" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0183673123";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0183673123";
};

# المستخدم 148: 0120127101
:do {
    /tool user-manager user add customer="admin" username="0120127101" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0120127101";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0120127101";
};

# المستخدم 149: 0174828363
:do {
    /tool user-manager user add customer="admin" username="0174828363" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0174828363";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0174828363";
};

# المستخدم 150: 0104980804
:do {
    /tool user-manager user add customer="admin" username="0104980804" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0104980804";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0104980804";
};

# المستخدم 151: 0145118001
:do {
    /tool user-manager user add customer="admin" username="0145118001" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0145118001";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0145118001";
};

# المستخدم 152: 0134307354
:do {
    /tool user-manager user add customer="admin" username="0134307354" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0134307354";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0134307354";
};

# المستخدم 153: 0137367799
:do {
    /tool user-manager user add customer="admin" username="0137367799" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0137367799";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0137367799";
};

# المستخدم 154: 0102974346
:do {
    /tool user-manager user add customer="admin" username="0102974346" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0102974346";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0102974346";
};

# المستخدم 155: 0152864814
:do {
    /tool user-manager user add customer="admin" username="0152864814" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0152864814";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0152864814";
};

# المستخدم 156: 0116972665
:do {
    /tool user-manager user add customer="admin" username="0116972665" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0116972665";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0116972665";
};

# المستخدم 157: 0154695278
:do {
    /tool user-manager user add customer="admin" username="0154695278" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0154695278";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0154695278";
};

# المستخدم 158: 0187030483
:do {
    /tool user-manager user add customer="admin" username="0187030483" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0187030483";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0187030483";
};

# المستخدم 159: 0154149013
:do {
    /tool user-manager user add customer="admin" username="0154149013" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0154149013";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0154149013";
};

# المستخدم 160: 0135328750
:do {
    /tool user-manager user add customer="admin" username="0135328750" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0135328750";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0135328750";
};

# المستخدم 161: 0120904907
:do {
    /tool user-manager user add customer="admin" username="0120904907" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0120904907";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0120904907";
};

# المستخدم 162: 0195925478
:do {
    /tool user-manager user add customer="admin" username="0195925478" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0195925478";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0195925478";
};

# المستخدم 163: 0167208296
:do {
    /tool user-manager user add customer="admin" username="0167208296" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0167208296";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0167208296";
};

# المستخدم 164: 0120968381
:do {
    /tool user-manager user add customer="admin" username="0120968381" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0120968381";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0120968381";
};

# المستخدم 165: 0138992241
:do {
    /tool user-manager user add customer="admin" username="0138992241" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0138992241";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0138992241";
};

# المستخدم 166: 0135354189
:do {
    /tool user-manager user add customer="admin" username="0135354189" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0135354189";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0135354189";
};

# المستخدم 167: 0160462569
:do {
    /tool user-manager user add customer="admin" username="0160462569" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0160462569";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0160462569";
};

# المستخدم 168: 0163993321
:do {
    /tool user-manager user add customer="admin" username="0163993321" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0163993321";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0163993321";
};

# المستخدم 169: 0165220448
:do {
    /tool user-manager user add customer="admin" username="0165220448" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0165220448";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0165220448";
};

# المستخدم 170: 0180762686
:do {
    /tool user-manager user add customer="admin" username="0180762686" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0180762686";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0180762686";
};

# المستخدم 171: 0141428500
:do {
    /tool user-manager user add customer="admin" username="0141428500" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0141428500";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0141428500";
};

# المستخدم 172: 0179602908
:do {
    /tool user-manager user add customer="admin" username="0179602908" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0179602908";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0179602908";
};

# المستخدم 173: 0183496320
:do {
    /tool user-manager user add customer="admin" username="0183496320" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0183496320";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0183496320";
};

# المستخدم 174: 0115001169
:do {
    /tool user-manager user add customer="admin" username="0115001169" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0115001169";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0115001169";
};

# المستخدم 175: 0199179371
:do {
    /tool user-manager user add customer="admin" username="0199179371" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0199179371";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0199179371";
};

# المستخدم 176: 0137055309
:do {
    /tool user-manager user add customer="admin" username="0137055309" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0137055309";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0137055309";
};

# المستخدم 177: 0116731348
:do {
    /tool user-manager user add customer="admin" username="0116731348" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0116731348";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0116731348";
};

# المستخدم 178: 0181697697
:do {
    /tool user-manager user add customer="admin" username="0181697697" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0181697697";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0181697697";
};

# المستخدم 179: 0110927510
:do {
    /tool user-manager user add customer="admin" username="0110927510" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0110927510";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0110927510";
};

# المستخدم 180: 0145674260
:do {
    /tool user-manager user add customer="admin" username="0145674260" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0145674260";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0145674260";
};

# المستخدم 181: 0112050349
:do {
    /tool user-manager user add customer="admin" username="0112050349" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0112050349";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0112050349";
};

# المستخدم 182: 0119997622
:do {
    /tool user-manager user add customer="admin" username="0119997622" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0119997622";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0119997622";
};

# المستخدم 183: 0161931881
:do {
    /tool user-manager user add customer="admin" username="0161931881" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0161931881";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0161931881";
};

# المستخدم 184: 0104854612
:do {
    /tool user-manager user add customer="admin" username="0104854612" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0104854612";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0104854612";
};

# المستخدم 185: 0143451013
:do {
    /tool user-manager user add customer="admin" username="0143451013" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0143451013";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0143451013";
};

# المستخدم 186: 0163060498
:do {
    /tool user-manager user add customer="admin" username="0163060498" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0163060498";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0163060498";
};

# المستخدم 187: 0113369433
:do {
    /tool user-manager user add customer="admin" username="0113369433" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0113369433";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0113369433";
};

# المستخدم 188: 0181563000
:do {
    /tool user-manager user add customer="admin" username="0181563000" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0181563000";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0181563000";
};

# المستخدم 189: 0180280610
:do {
    /tool user-manager user add customer="admin" username="0180280610" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0180280610";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0180280610";
};

# المستخدم 190: 0175318571
:do {
    /tool user-manager user add customer="admin" username="0175318571" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0175318571";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0175318571";
};

# المستخدم 191: 0178228700
:do {
    /tool user-manager user add customer="admin" username="0178228700" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0178228700";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0178228700";
};

# المستخدم 192: 0108265382
:do {
    /tool user-manager user add customer="admin" username="0108265382" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0108265382";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0108265382";
};

# المستخدم 193: 0164188008
:do {
    /tool user-manager user add customer="admin" username="0164188008" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0164188008";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0164188008";
};

# المستخدم 194: 0105467475
:do {
    /tool user-manager user add customer="admin" username="0105467475" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0105467475";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0105467475";
};

# المستخدم 195: 0117672995
:do {
    /tool user-manager user add customer="admin" username="0117672995" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0117672995";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0117672995";
};

# المستخدم 196: 0190825934
:do {
    /tool user-manager user add customer="admin" username="0190825934" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0190825934";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0190825934";
};

# المستخدم 197: 0113389257
:do {
    /tool user-manager user add customer="admin" username="0113389257" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0113389257";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0113389257";
};

# المستخدم 198: 0141739202
:do {
    /tool user-manager user add customer="admin" username="0141739202" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0141739202";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0141739202";
};

# المستخدم 199: 0170137463
:do {
    /tool user-manager user add customer="admin" username="0170137463" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0170137463";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0170137463";
};

# المستخدم 200: 0135023346
:do {
    /tool user-manager user add customer="admin" username="0135023346" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0135023346";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0135023346";
};


:put "📊 تم الانتهاء من النسخة الاحتياطية";
:put "✅ نجح: $success كرت";
:put "❌ فشل: $errors كرت";
:put "📈 الإجمالي: $total كرت";

:put "🎉 تم استعادة جميع كروت القالب: 10";
:put "💡 النسخة الاحتياطية مكتملة بنجاح!";
