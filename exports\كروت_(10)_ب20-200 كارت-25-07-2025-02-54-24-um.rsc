# MikroTik Script - نسخة احتياطية للكروت
# تم الإنشاء: 2025-07-25 02:54:25
# القالب: 10
# النظام: user_manager
# عدد الكروت: 200
# تم إنشاؤه بواسطة: مولد كروت MikroTik

:put "🚀 بدء تنفيذ النسخة الاحتياطية للكروت";
:put "📋 القالب: 10";
:put "🎯 النظام: user_manager";
:put "📊 عدد الكروت: 200";

:local success 0;
:local errors 0;
:local total 200;

:put "⏳ بدء إضافة الكروت...";

# إضافة مستخدمي User Manager
:put "👤 إضافة 200 مستخدم User Manager...";

# المستخدم 1: 0190462633
:do {
    /tool user-manager user add customer="admin" username="0190462633" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0190462633";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0190462633";
};

# المستخدم 2: 0123820991
:do {
    /tool user-manager user add customer="admin" username="0123820991" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0123820991";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0123820991";
};

# المستخدم 3: 0188077660
:do {
    /tool user-manager user add customer="admin" username="0188077660" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0188077660";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0188077660";
};

# المستخدم 4: 0136878058
:do {
    /tool user-manager user add customer="admin" username="0136878058" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0136878058";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0136878058";
};

# المستخدم 5: 0105661934
:do {
    /tool user-manager user add customer="admin" username="0105661934" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0105661934";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0105661934";
};

# المستخدم 6: 0102098949
:do {
    /tool user-manager user add customer="admin" username="0102098949" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0102098949";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0102098949";
};

# المستخدم 7: 0118384440
:do {
    /tool user-manager user add customer="admin" username="0118384440" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0118384440";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0118384440";
};

# المستخدم 8: 0168987750
:do {
    /tool user-manager user add customer="admin" username="0168987750" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0168987750";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0168987750";
};

# المستخدم 9: 0176527817
:do {
    /tool user-manager user add customer="admin" username="0176527817" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0176527817";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0176527817";
};

# المستخدم 10: 0169772939
:do {
    /tool user-manager user add customer="admin" username="0169772939" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0169772939";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0169772939";
};

# المستخدم 11: 0130693274
:do {
    /tool user-manager user add customer="admin" username="0130693274" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0130693274";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0130693274";
};

# المستخدم 12: 0131505641
:do {
    /tool user-manager user add customer="admin" username="0131505641" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0131505641";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0131505641";
};

# المستخدم 13: 0137365333
:do {
    /tool user-manager user add customer="admin" username="0137365333" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0137365333";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0137365333";
};

# المستخدم 14: 0112434365
:do {
    /tool user-manager user add customer="admin" username="0112434365" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0112434365";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0112434365";
};

# المستخدم 15: 0189381123
:do {
    /tool user-manager user add customer="admin" username="0189381123" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0189381123";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0189381123";
};

# المستخدم 16: 0192409562
:do {
    /tool user-manager user add customer="admin" username="0192409562" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0192409562";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0192409562";
};

# المستخدم 17: 0172759418
:do {
    /tool user-manager user add customer="admin" username="0172759418" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0172759418";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0172759418";
};

# المستخدم 18: 0184144002
:do {
    /tool user-manager user add customer="admin" username="0184144002" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0184144002";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0184144002";
};

# المستخدم 19: 0101881798
:do {
    /tool user-manager user add customer="admin" username="0101881798" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0101881798";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0101881798";
};

# المستخدم 20: 0113363402
:do {
    /tool user-manager user add customer="admin" username="0113363402" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0113363402";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0113363402";
};

# المستخدم 21: 0165961227
:do {
    /tool user-manager user add customer="admin" username="0165961227" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0165961227";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0165961227";
};

# المستخدم 22: 0106209460
:do {
    /tool user-manager user add customer="admin" username="0106209460" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0106209460";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0106209460";
};

# المستخدم 23: 0189867980
:do {
    /tool user-manager user add customer="admin" username="0189867980" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0189867980";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0189867980";
};

# المستخدم 24: 0141859962
:do {
    /tool user-manager user add customer="admin" username="0141859962" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0141859962";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0141859962";
};

# المستخدم 25: 0102418385
:do {
    /tool user-manager user add customer="admin" username="0102418385" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0102418385";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0102418385";
};

# المستخدم 26: 0121185828
:do {
    /tool user-manager user add customer="admin" username="0121185828" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0121185828";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0121185828";
};

# المستخدم 27: 0187155144
:do {
    /tool user-manager user add customer="admin" username="0187155144" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0187155144";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0187155144";
};

# المستخدم 28: 0158184566
:do {
    /tool user-manager user add customer="admin" username="0158184566" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0158184566";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0158184566";
};

# المستخدم 29: 0127824020
:do {
    /tool user-manager user add customer="admin" username="0127824020" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0127824020";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0127824020";
};

# المستخدم 30: 0141973007
:do {
    /tool user-manager user add customer="admin" username="0141973007" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0141973007";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0141973007";
};

# المستخدم 31: 0103867284
:do {
    /tool user-manager user add customer="admin" username="0103867284" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0103867284";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0103867284";
};

# المستخدم 32: 0177095244
:do {
    /tool user-manager user add customer="admin" username="0177095244" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0177095244";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0177095244";
};

# المستخدم 33: 0148042415
:do {
    /tool user-manager user add customer="admin" username="0148042415" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0148042415";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0148042415";
};

# المستخدم 34: 0119744441
:do {
    /tool user-manager user add customer="admin" username="0119744441" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0119744441";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0119744441";
};

# المستخدم 35: 0117710033
:do {
    /tool user-manager user add customer="admin" username="0117710033" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0117710033";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0117710033";
};

# المستخدم 36: 0142153057
:do {
    /tool user-manager user add customer="admin" username="0142153057" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0142153057";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0142153057";
};

# المستخدم 37: 0172722685
:do {
    /tool user-manager user add customer="admin" username="0172722685" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0172722685";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0172722685";
};

# المستخدم 38: 0153143510
:do {
    /tool user-manager user add customer="admin" username="0153143510" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0153143510";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0153143510";
};

# المستخدم 39: 0158518784
:do {
    /tool user-manager user add customer="admin" username="0158518784" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0158518784";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0158518784";
};

# المستخدم 40: 0103670683
:do {
    /tool user-manager user add customer="admin" username="0103670683" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0103670683";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0103670683";
};

# المستخدم 41: 0175048651
:do {
    /tool user-manager user add customer="admin" username="0175048651" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0175048651";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0175048651";
};

# المستخدم 42: 0109482046
:do {
    /tool user-manager user add customer="admin" username="0109482046" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0109482046";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0109482046";
};

# المستخدم 43: 0157319772
:do {
    /tool user-manager user add customer="admin" username="0157319772" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0157319772";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0157319772";
};

# المستخدم 44: 0176949472
:do {
    /tool user-manager user add customer="admin" username="0176949472" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0176949472";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0176949472";
};

# المستخدم 45: 0176189292
:do {
    /tool user-manager user add customer="admin" username="0176189292" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0176189292";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0176189292";
};

# المستخدم 46: 0182739351
:do {
    /tool user-manager user add customer="admin" username="0182739351" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0182739351";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0182739351";
};

# المستخدم 47: 0199708347
:do {
    /tool user-manager user add customer="admin" username="0199708347" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0199708347";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0199708347";
};

# المستخدم 48: 0122793140
:do {
    /tool user-manager user add customer="admin" username="0122793140" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0122793140";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0122793140";
};

# المستخدم 49: 0170218723
:do {
    /tool user-manager user add customer="admin" username="0170218723" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0170218723";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0170218723";
};

# المستخدم 50: 0163254267
:do {
    /tool user-manager user add customer="admin" username="0163254267" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0163254267";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0163254267";
};

# المستخدم 51: 0122929627
:do {
    /tool user-manager user add customer="admin" username="0122929627" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0122929627";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0122929627";
};

# المستخدم 52: 0178247747
:do {
    /tool user-manager user add customer="admin" username="0178247747" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0178247747";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0178247747";
};

# المستخدم 53: 0145488123
:do {
    /tool user-manager user add customer="admin" username="0145488123" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0145488123";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0145488123";
};

# المستخدم 54: 0187860865
:do {
    /tool user-manager user add customer="admin" username="0187860865" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0187860865";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0187860865";
};

# المستخدم 55: 0132762959
:do {
    /tool user-manager user add customer="admin" username="0132762959" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0132762959";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0132762959";
};

# المستخدم 56: 0161021392
:do {
    /tool user-manager user add customer="admin" username="0161021392" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0161021392";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0161021392";
};

# المستخدم 57: 0153331168
:do {
    /tool user-manager user add customer="admin" username="0153331168" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0153331168";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0153331168";
};

# المستخدم 58: 0111878687
:do {
    /tool user-manager user add customer="admin" username="0111878687" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0111878687";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0111878687";
};

# المستخدم 59: 0157839121
:do {
    /tool user-manager user add customer="admin" username="0157839121" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0157839121";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0157839121";
};

# المستخدم 60: 0148695467
:do {
    /tool user-manager user add customer="admin" username="0148695467" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0148695467";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0148695467";
};

# المستخدم 61: 0199054342
:do {
    /tool user-manager user add customer="admin" username="0199054342" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0199054342";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0199054342";
};

# المستخدم 62: 0151570788
:do {
    /tool user-manager user add customer="admin" username="0151570788" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0151570788";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0151570788";
};

# المستخدم 63: 0147842365
:do {
    /tool user-manager user add customer="admin" username="0147842365" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0147842365";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0147842365";
};

# المستخدم 64: 0107797974
:do {
    /tool user-manager user add customer="admin" username="0107797974" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0107797974";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0107797974";
};

# المستخدم 65: 0192996929
:do {
    /tool user-manager user add customer="admin" username="0192996929" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0192996929";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0192996929";
};

# المستخدم 66: 0125488881
:do {
    /tool user-manager user add customer="admin" username="0125488881" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0125488881";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0125488881";
};

# المستخدم 67: 0105475981
:do {
    /tool user-manager user add customer="admin" username="0105475981" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0105475981";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0105475981";
};

# المستخدم 68: 0188536832
:do {
    /tool user-manager user add customer="admin" username="0188536832" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0188536832";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0188536832";
};

# المستخدم 69: 0149229319
:do {
    /tool user-manager user add customer="admin" username="0149229319" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0149229319";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0149229319";
};

# المستخدم 70: 0167931125
:do {
    /tool user-manager user add customer="admin" username="0167931125" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0167931125";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0167931125";
};

# المستخدم 71: 0165347978
:do {
    /tool user-manager user add customer="admin" username="0165347978" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0165347978";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0165347978";
};

# المستخدم 72: 0123787986
:do {
    /tool user-manager user add customer="admin" username="0123787986" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0123787986";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0123787986";
};

# المستخدم 73: 0159315117
:do {
    /tool user-manager user add customer="admin" username="0159315117" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0159315117";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0159315117";
};

# المستخدم 74: 0142687079
:do {
    /tool user-manager user add customer="admin" username="0142687079" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0142687079";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0142687079";
};

# المستخدم 75: 0178369042
:do {
    /tool user-manager user add customer="admin" username="0178369042" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0178369042";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0178369042";
};

# المستخدم 76: 0112787571
:do {
    /tool user-manager user add customer="admin" username="0112787571" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0112787571";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0112787571";
};

# المستخدم 77: 0139788274
:do {
    /tool user-manager user add customer="admin" username="0139788274" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0139788274";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0139788274";
};

# المستخدم 78: 0165968794
:do {
    /tool user-manager user add customer="admin" username="0165968794" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0165968794";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0165968794";
};

# المستخدم 79: 0102018202
:do {
    /tool user-manager user add customer="admin" username="0102018202" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0102018202";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0102018202";
};

# المستخدم 80: 0104767287
:do {
    /tool user-manager user add customer="admin" username="0104767287" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0104767287";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0104767287";
};

# المستخدم 81: 0199145913
:do {
    /tool user-manager user add customer="admin" username="0199145913" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0199145913";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0199145913";
};

# المستخدم 82: 0168212939
:do {
    /tool user-manager user add customer="admin" username="0168212939" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0168212939";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0168212939";
};

# المستخدم 83: 0154711208
:do {
    /tool user-manager user add customer="admin" username="0154711208" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0154711208";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0154711208";
};

# المستخدم 84: 0180026392
:do {
    /tool user-manager user add customer="admin" username="0180026392" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0180026392";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0180026392";
};

# المستخدم 85: 0154364748
:do {
    /tool user-manager user add customer="admin" username="0154364748" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0154364748";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0154364748";
};

# المستخدم 86: 0169389365
:do {
    /tool user-manager user add customer="admin" username="0169389365" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0169389365";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0169389365";
};

# المستخدم 87: 0110823313
:do {
    /tool user-manager user add customer="admin" username="0110823313" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0110823313";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0110823313";
};

# المستخدم 88: 0151023431
:do {
    /tool user-manager user add customer="admin" username="0151023431" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0151023431";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0151023431";
};

# المستخدم 89: 0138336949
:do {
    /tool user-manager user add customer="admin" username="0138336949" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0138336949";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0138336949";
};

# المستخدم 90: 0185371190
:do {
    /tool user-manager user add customer="admin" username="0185371190" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0185371190";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0185371190";
};

# المستخدم 91: 0161661833
:do {
    /tool user-manager user add customer="admin" username="0161661833" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0161661833";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0161661833";
};

# المستخدم 92: 0185005940
:do {
    /tool user-manager user add customer="admin" username="0185005940" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0185005940";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0185005940";
};

# المستخدم 93: 0180363854
:do {
    /tool user-manager user add customer="admin" username="0180363854" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0180363854";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0180363854";
};

# المستخدم 94: 0123972593
:do {
    /tool user-manager user add customer="admin" username="0123972593" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0123972593";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0123972593";
};

# المستخدم 95: 0157999088
:do {
    /tool user-manager user add customer="admin" username="0157999088" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0157999088";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0157999088";
};

# المستخدم 96: 0185160096
:do {
    /tool user-manager user add customer="admin" username="0185160096" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0185160096";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0185160096";
};

# المستخدم 97: 0148119603
:do {
    /tool user-manager user add customer="admin" username="0148119603" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0148119603";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0148119603";
};

# المستخدم 98: 0175965025
:do {
    /tool user-manager user add customer="admin" username="0175965025" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0175965025";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0175965025";
};

# المستخدم 99: 0191321390
:do {
    /tool user-manager user add customer="admin" username="0191321390" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0191321390";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0191321390";
};

# المستخدم 100: 0135740974
:do {
    /tool user-manager user add customer="admin" username="0135740974" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0135740974";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0135740974";
};

# المستخدم 101: 0197521595
:do {
    /tool user-manager user add customer="admin" username="0197521595" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0197521595";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0197521595";
};

# المستخدم 102: 0195508919
:do {
    /tool user-manager user add customer="admin" username="0195508919" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0195508919";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0195508919";
};

# المستخدم 103: 0164045862
:do {
    /tool user-manager user add customer="admin" username="0164045862" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0164045862";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0164045862";
};

# المستخدم 104: 0152915760
:do {
    /tool user-manager user add customer="admin" username="0152915760" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0152915760";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0152915760";
};

# المستخدم 105: 0146407371
:do {
    /tool user-manager user add customer="admin" username="0146407371" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0146407371";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0146407371";
};

# المستخدم 106: 0159575996
:do {
    /tool user-manager user add customer="admin" username="0159575996" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0159575996";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0159575996";
};

# المستخدم 107: 0156104405
:do {
    /tool user-manager user add customer="admin" username="0156104405" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0156104405";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0156104405";
};

# المستخدم 108: 0195992218
:do {
    /tool user-manager user add customer="admin" username="0195992218" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0195992218";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0195992218";
};

# المستخدم 109: 0189879442
:do {
    /tool user-manager user add customer="admin" username="0189879442" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0189879442";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0189879442";
};

# المستخدم 110: 0146428819
:do {
    /tool user-manager user add customer="admin" username="0146428819" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0146428819";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0146428819";
};

# المستخدم 111: 0179825269
:do {
    /tool user-manager user add customer="admin" username="0179825269" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0179825269";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0179825269";
};

# المستخدم 112: 0125098322
:do {
    /tool user-manager user add customer="admin" username="0125098322" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0125098322";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0125098322";
};

# المستخدم 113: 0173327070
:do {
    /tool user-manager user add customer="admin" username="0173327070" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0173327070";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0173327070";
};

# المستخدم 114: 0126183897
:do {
    /tool user-manager user add customer="admin" username="0126183897" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0126183897";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0126183897";
};

# المستخدم 115: 0129649588
:do {
    /tool user-manager user add customer="admin" username="0129649588" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0129649588";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0129649588";
};

# المستخدم 116: 0132552033
:do {
    /tool user-manager user add customer="admin" username="0132552033" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0132552033";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0132552033";
};

# المستخدم 117: 0127612470
:do {
    /tool user-manager user add customer="admin" username="0127612470" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0127612470";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0127612470";
};

# المستخدم 118: 0158831430
:do {
    /tool user-manager user add customer="admin" username="0158831430" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0158831430";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0158831430";
};

# المستخدم 119: 0199376179
:do {
    /tool user-manager user add customer="admin" username="0199376179" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0199376179";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0199376179";
};

# المستخدم 120: 0115059929
:do {
    /tool user-manager user add customer="admin" username="0115059929" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0115059929";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0115059929";
};

# المستخدم 121: 0150726111
:do {
    /tool user-manager user add customer="admin" username="0150726111" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0150726111";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0150726111";
};

# المستخدم 122: 0143831300
:do {
    /tool user-manager user add customer="admin" username="0143831300" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0143831300";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0143831300";
};

# المستخدم 123: 0152178468
:do {
    /tool user-manager user add customer="admin" username="0152178468" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0152178468";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0152178468";
};

# المستخدم 124: 0142302889
:do {
    /tool user-manager user add customer="admin" username="0142302889" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0142302889";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0142302889";
};

# المستخدم 125: 0172521886
:do {
    /tool user-manager user add customer="admin" username="0172521886" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0172521886";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0172521886";
};

# المستخدم 126: 0102206982
:do {
    /tool user-manager user add customer="admin" username="0102206982" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0102206982";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0102206982";
};

# المستخدم 127: 0196059991
:do {
    /tool user-manager user add customer="admin" username="0196059991" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0196059991";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0196059991";
};

# المستخدم 128: 0185134362
:do {
    /tool user-manager user add customer="admin" username="0185134362" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0185134362";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0185134362";
};

# المستخدم 129: 0167737962
:do {
    /tool user-manager user add customer="admin" username="0167737962" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0167737962";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0167737962";
};

# المستخدم 130: 0125524810
:do {
    /tool user-manager user add customer="admin" username="0125524810" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0125524810";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0125524810";
};

# المستخدم 131: 0135951292
:do {
    /tool user-manager user add customer="admin" username="0135951292" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0135951292";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0135951292";
};

# المستخدم 132: 0121058327
:do {
    /tool user-manager user add customer="admin" username="0121058327" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0121058327";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0121058327";
};

# المستخدم 133: 0106258524
:do {
    /tool user-manager user add customer="admin" username="0106258524" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0106258524";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0106258524";
};

# المستخدم 134: 0197947135
:do {
    /tool user-manager user add customer="admin" username="0197947135" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0197947135";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0197947135";
};

# المستخدم 135: 0116455414
:do {
    /tool user-manager user add customer="admin" username="0116455414" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0116455414";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0116455414";
};

# المستخدم 136: 0143685113
:do {
    /tool user-manager user add customer="admin" username="0143685113" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0143685113";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0143685113";
};

# المستخدم 137: 0136416107
:do {
    /tool user-manager user add customer="admin" username="0136416107" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0136416107";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0136416107";
};

# المستخدم 138: 0158500413
:do {
    /tool user-manager user add customer="admin" username="0158500413" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0158500413";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0158500413";
};

# المستخدم 139: 0119258280
:do {
    /tool user-manager user add customer="admin" username="0119258280" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0119258280";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0119258280";
};

# المستخدم 140: 0186799716
:do {
    /tool user-manager user add customer="admin" username="0186799716" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0186799716";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0186799716";
};

# المستخدم 141: 0187867903
:do {
    /tool user-manager user add customer="admin" username="0187867903" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0187867903";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0187867903";
};

# المستخدم 142: 0108952284
:do {
    /tool user-manager user add customer="admin" username="0108952284" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0108952284";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0108952284";
};

# المستخدم 143: 0124966442
:do {
    /tool user-manager user add customer="admin" username="0124966442" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0124966442";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0124966442";
};

# المستخدم 144: 0108613302
:do {
    /tool user-manager user add customer="admin" username="0108613302" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0108613302";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0108613302";
};

# المستخدم 145: 0194176744
:do {
    /tool user-manager user add customer="admin" username="0194176744" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0194176744";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0194176744";
};

# المستخدم 146: 0106823110
:do {
    /tool user-manager user add customer="admin" username="0106823110" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0106823110";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0106823110";
};

# المستخدم 147: 0126164054
:do {
    /tool user-manager user add customer="admin" username="0126164054" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0126164054";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0126164054";
};

# المستخدم 148: 0198453468
:do {
    /tool user-manager user add customer="admin" username="0198453468" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0198453468";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0198453468";
};

# المستخدم 149: 0175091044
:do {
    /tool user-manager user add customer="admin" username="0175091044" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0175091044";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0175091044";
};

# المستخدم 150: 0171160942
:do {
    /tool user-manager user add customer="admin" username="0171160942" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0171160942";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0171160942";
};

# المستخدم 151: 0180277505
:do {
    /tool user-manager user add customer="admin" username="0180277505" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0180277505";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0180277505";
};

# المستخدم 152: 0166624885
:do {
    /tool user-manager user add customer="admin" username="0166624885" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0166624885";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0166624885";
};

# المستخدم 153: 0122921300
:do {
    /tool user-manager user add customer="admin" username="0122921300" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0122921300";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0122921300";
};

# المستخدم 154: 0171921781
:do {
    /tool user-manager user add customer="admin" username="0171921781" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0171921781";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0171921781";
};

# المستخدم 155: 0114930163
:do {
    /tool user-manager user add customer="admin" username="0114930163" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0114930163";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0114930163";
};

# المستخدم 156: 0132165909
:do {
    /tool user-manager user add customer="admin" username="0132165909" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0132165909";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0132165909";
};

# المستخدم 157: 0173156172
:do {
    /tool user-manager user add customer="admin" username="0173156172" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0173156172";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0173156172";
};

# المستخدم 158: 0133006420
:do {
    /tool user-manager user add customer="admin" username="0133006420" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0133006420";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0133006420";
};

# المستخدم 159: 0170439102
:do {
    /tool user-manager user add customer="admin" username="0170439102" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0170439102";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0170439102";
};

# المستخدم 160: 0178669838
:do {
    /tool user-manager user add customer="admin" username="0178669838" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0178669838";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0178669838";
};

# المستخدم 161: 0190810958
:do {
    /tool user-manager user add customer="admin" username="0190810958" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0190810958";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0190810958";
};

# المستخدم 162: 0155216961
:do {
    /tool user-manager user add customer="admin" username="0155216961" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0155216961";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0155216961";
};

# المستخدم 163: 0135918722
:do {
    /tool user-manager user add customer="admin" username="0135918722" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0135918722";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0135918722";
};

# المستخدم 164: 0136080023
:do {
    /tool user-manager user add customer="admin" username="0136080023" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0136080023";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0136080023";
};

# المستخدم 165: 0163322870
:do {
    /tool user-manager user add customer="admin" username="0163322870" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0163322870";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0163322870";
};

# المستخدم 166: 0139010564
:do {
    /tool user-manager user add customer="admin" username="0139010564" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0139010564";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0139010564";
};

# المستخدم 167: 0134037301
:do {
    /tool user-manager user add customer="admin" username="0134037301" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0134037301";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0134037301";
};

# المستخدم 168: 0123173116
:do {
    /tool user-manager user add customer="admin" username="0123173116" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0123173116";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0123173116";
};

# المستخدم 169: 0199692170
:do {
    /tool user-manager user add customer="admin" username="0199692170" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0199692170";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0199692170";
};

# المستخدم 170: 0177692702
:do {
    /tool user-manager user add customer="admin" username="0177692702" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0177692702";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0177692702";
};

# المستخدم 171: 0108948520
:do {
    /tool user-manager user add customer="admin" username="0108948520" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0108948520";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0108948520";
};

# المستخدم 172: 0100580046
:do {
    /tool user-manager user add customer="admin" username="0100580046" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0100580046";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0100580046";
};

# المستخدم 173: 0180386928
:do {
    /tool user-manager user add customer="admin" username="0180386928" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0180386928";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0180386928";
};

# المستخدم 174: 0114648077
:do {
    /tool user-manager user add customer="admin" username="0114648077" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0114648077";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0114648077";
};

# المستخدم 175: 0191290444
:do {
    /tool user-manager user add customer="admin" username="0191290444" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0191290444";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0191290444";
};

# المستخدم 176: 0164651589
:do {
    /tool user-manager user add customer="admin" username="0164651589" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0164651589";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0164651589";
};

# المستخدم 177: 0143347465
:do {
    /tool user-manager user add customer="admin" username="0143347465" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0143347465";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0143347465";
};

# المستخدم 178: 0151493177
:do {
    /tool user-manager user add customer="admin" username="0151493177" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0151493177";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0151493177";
};

# المستخدم 179: 0126143743
:do {
    /tool user-manager user add customer="admin" username="0126143743" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0126143743";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0126143743";
};

# المستخدم 180: 0104109082
:do {
    /tool user-manager user add customer="admin" username="0104109082" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0104109082";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0104109082";
};

# المستخدم 181: 0184105690
:do {
    /tool user-manager user add customer="admin" username="0184105690" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0184105690";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0184105690";
};

# المستخدم 182: 0155975847
:do {
    /tool user-manager user add customer="admin" username="0155975847" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0155975847";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0155975847";
};

# المستخدم 183: 0175791126
:do {
    /tool user-manager user add customer="admin" username="0175791126" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0175791126";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0175791126";
};

# المستخدم 184: 0123379227
:do {
    /tool user-manager user add customer="admin" username="0123379227" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0123379227";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0123379227";
};

# المستخدم 185: 0135610793
:do {
    /tool user-manager user add customer="admin" username="0135610793" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0135610793";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0135610793";
};

# المستخدم 186: 0151061161
:do {
    /tool user-manager user add customer="admin" username="0151061161" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0151061161";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0151061161";
};

# المستخدم 187: 0114435827
:do {
    /tool user-manager user add customer="admin" username="0114435827" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0114435827";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0114435827";
};

# المستخدم 188: 0193039804
:do {
    /tool user-manager user add customer="admin" username="0193039804" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0193039804";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0193039804";
};

# المستخدم 189: 0165241877
:do {
    /tool user-manager user add customer="admin" username="0165241877" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0165241877";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0165241877";
};

# المستخدم 190: 0174524426
:do {
    /tool user-manager user add customer="admin" username="0174524426" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0174524426";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0174524426";
};

# المستخدم 191: 0168702718
:do {
    /tool user-manager user add customer="admin" username="0168702718" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0168702718";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0168702718";
};

# المستخدم 192: 0131756326
:do {
    /tool user-manager user add customer="admin" username="0131756326" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0131756326";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0131756326";
};

# المستخدم 193: 0172317345
:do {
    /tool user-manager user add customer="admin" username="0172317345" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0172317345";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0172317345";
};

# المستخدم 194: 0163684097
:do {
    /tool user-manager user add customer="admin" username="0163684097" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0163684097";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0163684097";
};

# المستخدم 195: 0180222002
:do {
    /tool user-manager user add customer="admin" username="0180222002" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0180222002";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0180222002";
};

# المستخدم 196: 0131573590
:do {
    /tool user-manager user add customer="admin" username="0131573590" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0131573590";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0131573590";
};

# المستخدم 197: 0102802273
:do {
    /tool user-manager user add customer="admin" username="0102802273" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0102802273";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0102802273";
};

# المستخدم 198: 0157423810
:do {
    /tool user-manager user add customer="admin" username="0157423810" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0157423810";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0157423810";
};

# المستخدم 199: 0119513876
:do {
    /tool user-manager user add customer="admin" username="0119513876" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0119513876";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0119513876";
};

# المستخدم 200: 0136825904
:do {
    /tool user-manager user add customer="admin" username="0136825904" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0136825904";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0136825904";
};


:put "📊 تم الانتهاء من النسخة الاحتياطية";
:put "✅ نجح: $success كرت";
:put "❌ فشل: $errors كرت";
:put "📈 الإجمالي: $total كرت";

:put "🎉 تم استعادة جميع كروت القالب: 10";
:put "💡 النسخة الاحتياطية مكتملة بنجاح!";
