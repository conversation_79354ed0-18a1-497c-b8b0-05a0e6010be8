# MikroTik Script - نسخة احتياطية للكروت
# تم الإنشاء: 2025-07-25 02:33:04
# القالب: 10
# النظام: user_manager
# عدد الكروت: 200
# تم إنشاؤه بواسطة: مولد كروت MikroTik

:put "🚀 بدء تنفيذ النسخة الاحتياطية للكروت";
:put "📋 القالب: 10";
:put "🎯 النظام: user_manager";
:put "📊 عدد الكروت: 200";

:local success 0;
:local errors 0;
:local total 200;

:put "⏳ بدء إضافة الكروت...";

# إضافة مستخدمي User Manager
:put "👤 إضافة 200 مستخدم User Manager...";

# المستخدم 1: 0125401852
:do {
    /tool user-manager user add customer="admin" username="0125401852" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0125401852";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0125401852";
};

# المستخدم 2: 0163039209
:do {
    /tool user-manager user add customer="admin" username="0163039209" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0163039209";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0163039209";
};

# المستخدم 3: 0182061830
:do {
    /tool user-manager user add customer="admin" username="0182061830" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0182061830";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0182061830";
};

# المستخدم 4: 0140654192
:do {
    /tool user-manager user add customer="admin" username="0140654192" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0140654192";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0140654192";
};

# المستخدم 5: 0152850702
:do {
    /tool user-manager user add customer="admin" username="0152850702" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0152850702";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0152850702";
};

# المستخدم 6: 0104158250
:do {
    /tool user-manager user add customer="admin" username="0104158250" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0104158250";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0104158250";
};

# المستخدم 7: 0107952953
:do {
    /tool user-manager user add customer="admin" username="0107952953" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0107952953";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0107952953";
};

# المستخدم 8: 0129755916
:do {
    /tool user-manager user add customer="admin" username="0129755916" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0129755916";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0129755916";
};

# المستخدم 9: 0146352027
:do {
    /tool user-manager user add customer="admin" username="0146352027" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0146352027";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0146352027";
};

# المستخدم 10: 0114072486
:do {
    /tool user-manager user add customer="admin" username="0114072486" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0114072486";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0114072486";
};

# المستخدم 11: 0127715154
:do {
    /tool user-manager user add customer="admin" username="0127715154" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0127715154";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0127715154";
};

# المستخدم 12: 0187036736
:do {
    /tool user-manager user add customer="admin" username="0187036736" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0187036736";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0187036736";
};

# المستخدم 13: 0188622362
:do {
    /tool user-manager user add customer="admin" username="0188622362" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0188622362";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0188622362";
};

# المستخدم 14: 0167614472
:do {
    /tool user-manager user add customer="admin" username="0167614472" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0167614472";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0167614472";
};

# المستخدم 15: 0163151733
:do {
    /tool user-manager user add customer="admin" username="0163151733" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0163151733";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0163151733";
};

# المستخدم 16: 0108345405
:do {
    /tool user-manager user add customer="admin" username="0108345405" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0108345405";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0108345405";
};

# المستخدم 17: 0192815274
:do {
    /tool user-manager user add customer="admin" username="0192815274" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0192815274";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0192815274";
};

# المستخدم 18: 0125767487
:do {
    /tool user-manager user add customer="admin" username="0125767487" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0125767487";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0125767487";
};

# المستخدم 19: 0131217152
:do {
    /tool user-manager user add customer="admin" username="0131217152" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0131217152";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0131217152";
};

# المستخدم 20: 0114560188
:do {
    /tool user-manager user add customer="admin" username="0114560188" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0114560188";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0114560188";
};

# المستخدم 21: 0104383667
:do {
    /tool user-manager user add customer="admin" username="0104383667" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0104383667";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0104383667";
};

# المستخدم 22: 0190024785
:do {
    /tool user-manager user add customer="admin" username="0190024785" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0190024785";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0190024785";
};

# المستخدم 23: 0138327643
:do {
    /tool user-manager user add customer="admin" username="0138327643" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0138327643";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0138327643";
};

# المستخدم 24: 0161920499
:do {
    /tool user-manager user add customer="admin" username="0161920499" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0161920499";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0161920499";
};

# المستخدم 25: 0115987303
:do {
    /tool user-manager user add customer="admin" username="0115987303" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0115987303";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0115987303";
};

# المستخدم 26: 0142343371
:do {
    /tool user-manager user add customer="admin" username="0142343371" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0142343371";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0142343371";
};

# المستخدم 27: 0178040299
:do {
    /tool user-manager user add customer="admin" username="0178040299" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0178040299";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0178040299";
};

# المستخدم 28: 0117208794
:do {
    /tool user-manager user add customer="admin" username="0117208794" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0117208794";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0117208794";
};

# المستخدم 29: 0126491123
:do {
    /tool user-manager user add customer="admin" username="0126491123" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0126491123";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0126491123";
};

# المستخدم 30: 0124042506
:do {
    /tool user-manager user add customer="admin" username="0124042506" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0124042506";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0124042506";
};

# المستخدم 31: 0168185918
:do {
    /tool user-manager user add customer="admin" username="0168185918" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0168185918";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0168185918";
};

# المستخدم 32: 0149473112
:do {
    /tool user-manager user add customer="admin" username="0149473112" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0149473112";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0149473112";
};

# المستخدم 33: 0199506561
:do {
    /tool user-manager user add customer="admin" username="0199506561" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0199506561";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0199506561";
};

# المستخدم 34: 0194163244
:do {
    /tool user-manager user add customer="admin" username="0194163244" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0194163244";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0194163244";
};

# المستخدم 35: 0181665716
:do {
    /tool user-manager user add customer="admin" username="0181665716" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0181665716";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0181665716";
};

# المستخدم 36: 0103785030
:do {
    /tool user-manager user add customer="admin" username="0103785030" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0103785030";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0103785030";
};

# المستخدم 37: 0199938023
:do {
    /tool user-manager user add customer="admin" username="0199938023" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0199938023";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0199938023";
};

# المستخدم 38: 0151185678
:do {
    /tool user-manager user add customer="admin" username="0151185678" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0151185678";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0151185678";
};

# المستخدم 39: 0130822754
:do {
    /tool user-manager user add customer="admin" username="0130822754" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0130822754";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0130822754";
};

# المستخدم 40: 0100542435
:do {
    /tool user-manager user add customer="admin" username="0100542435" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0100542435";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0100542435";
};

# المستخدم 41: 0118361310
:do {
    /tool user-manager user add customer="admin" username="0118361310" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0118361310";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0118361310";
};

# المستخدم 42: 0195851447
:do {
    /tool user-manager user add customer="admin" username="0195851447" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0195851447";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0195851447";
};

# المستخدم 43: 0189679264
:do {
    /tool user-manager user add customer="admin" username="0189679264" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0189679264";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0189679264";
};

# المستخدم 44: 0158195164
:do {
    /tool user-manager user add customer="admin" username="0158195164" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0158195164";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0158195164";
};

# المستخدم 45: 0180721260
:do {
    /tool user-manager user add customer="admin" username="0180721260" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0180721260";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0180721260";
};

# المستخدم 46: 0121225095
:do {
    /tool user-manager user add customer="admin" username="0121225095" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0121225095";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0121225095";
};

# المستخدم 47: 0159058730
:do {
    /tool user-manager user add customer="admin" username="0159058730" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0159058730";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0159058730";
};

# المستخدم 48: 0192786674
:do {
    /tool user-manager user add customer="admin" username="0192786674" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0192786674";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0192786674";
};

# المستخدم 49: 0170131429
:do {
    /tool user-manager user add customer="admin" username="0170131429" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0170131429";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0170131429";
};

# المستخدم 50: 0133590568
:do {
    /tool user-manager user add customer="admin" username="0133590568" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0133590568";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0133590568";
};

# المستخدم 51: 0173915182
:do {
    /tool user-manager user add customer="admin" username="0173915182" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0173915182";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0173915182";
};

# المستخدم 52: 0103905191
:do {
    /tool user-manager user add customer="admin" username="0103905191" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0103905191";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0103905191";
};

# المستخدم 53: 0166353111
:do {
    /tool user-manager user add customer="admin" username="0166353111" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0166353111";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0166353111";
};

# المستخدم 54: 0148445087
:do {
    /tool user-manager user add customer="admin" username="0148445087" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0148445087";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0148445087";
};

# المستخدم 55: 0153103549
:do {
    /tool user-manager user add customer="admin" username="0153103549" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0153103549";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0153103549";
};

# المستخدم 56: 0155094372
:do {
    /tool user-manager user add customer="admin" username="0155094372" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0155094372";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0155094372";
};

# المستخدم 57: 0189517485
:do {
    /tool user-manager user add customer="admin" username="0189517485" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0189517485";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0189517485";
};

# المستخدم 58: 0182891142
:do {
    /tool user-manager user add customer="admin" username="0182891142" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0182891142";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0182891142";
};

# المستخدم 59: 0153200529
:do {
    /tool user-manager user add customer="admin" username="0153200529" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0153200529";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0153200529";
};

# المستخدم 60: 0186894991
:do {
    /tool user-manager user add customer="admin" username="0186894991" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0186894991";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0186894991";
};

# المستخدم 61: 0137866764
:do {
    /tool user-manager user add customer="admin" username="0137866764" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0137866764";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0137866764";
};

# المستخدم 62: 0117044482
:do {
    /tool user-manager user add customer="admin" username="0117044482" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0117044482";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0117044482";
};

# المستخدم 63: 0129724911
:do {
    /tool user-manager user add customer="admin" username="0129724911" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0129724911";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0129724911";
};

# المستخدم 64: 0197497139
:do {
    /tool user-manager user add customer="admin" username="0197497139" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0197497139";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0197497139";
};

# المستخدم 65: 0170978973
:do {
    /tool user-manager user add customer="admin" username="0170978973" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0170978973";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0170978973";
};

# المستخدم 66: 0165346406
:do {
    /tool user-manager user add customer="admin" username="0165346406" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0165346406";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0165346406";
};

# المستخدم 67: 0180444607
:do {
    /tool user-manager user add customer="admin" username="0180444607" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0180444607";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0180444607";
};

# المستخدم 68: 0153024438
:do {
    /tool user-manager user add customer="admin" username="0153024438" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0153024438";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0153024438";
};

# المستخدم 69: 0116731230
:do {
    /tool user-manager user add customer="admin" username="0116731230" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0116731230";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0116731230";
};

# المستخدم 70: 0105331041
:do {
    /tool user-manager user add customer="admin" username="0105331041" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0105331041";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0105331041";
};

# المستخدم 71: 0110639948
:do {
    /tool user-manager user add customer="admin" username="0110639948" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0110639948";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0110639948";
};

# المستخدم 72: 0104604723
:do {
    /tool user-manager user add customer="admin" username="0104604723" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0104604723";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0104604723";
};

# المستخدم 73: 0151862887
:do {
    /tool user-manager user add customer="admin" username="0151862887" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0151862887";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0151862887";
};

# المستخدم 74: 0174650971
:do {
    /tool user-manager user add customer="admin" username="0174650971" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0174650971";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0174650971";
};

# المستخدم 75: 0178410321
:do {
    /tool user-manager user add customer="admin" username="0178410321" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0178410321";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0178410321";
};

# المستخدم 76: 0175011226
:do {
    /tool user-manager user add customer="admin" username="0175011226" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0175011226";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0175011226";
};

# المستخدم 77: 0123780230
:do {
    /tool user-manager user add customer="admin" username="0123780230" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0123780230";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0123780230";
};

# المستخدم 78: 0169397445
:do {
    /tool user-manager user add customer="admin" username="0169397445" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0169397445";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0169397445";
};

# المستخدم 79: 0122364236
:do {
    /tool user-manager user add customer="admin" username="0122364236" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0122364236";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0122364236";
};

# المستخدم 80: 0114856599
:do {
    /tool user-manager user add customer="admin" username="0114856599" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0114856599";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0114856599";
};

# المستخدم 81: 0130711753
:do {
    /tool user-manager user add customer="admin" username="0130711753" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0130711753";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0130711753";
};

# المستخدم 82: 0158510175
:do {
    /tool user-manager user add customer="admin" username="0158510175" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0158510175";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0158510175";
};

# المستخدم 83: 0117913032
:do {
    /tool user-manager user add customer="admin" username="0117913032" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0117913032";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0117913032";
};

# المستخدم 84: 0191530210
:do {
    /tool user-manager user add customer="admin" username="0191530210" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0191530210";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0191530210";
};

# المستخدم 85: 0163986354
:do {
    /tool user-manager user add customer="admin" username="0163986354" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0163986354";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0163986354";
};

# المستخدم 86: 0124596815
:do {
    /tool user-manager user add customer="admin" username="0124596815" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0124596815";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0124596815";
};

# المستخدم 87: 0188051095
:do {
    /tool user-manager user add customer="admin" username="0188051095" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0188051095";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0188051095";
};

# المستخدم 88: 0161943778
:do {
    /tool user-manager user add customer="admin" username="0161943778" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0161943778";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0161943778";
};

# المستخدم 89: 0110987355
:do {
    /tool user-manager user add customer="admin" username="0110987355" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0110987355";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0110987355";
};

# المستخدم 90: 0196762368
:do {
    /tool user-manager user add customer="admin" username="0196762368" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0196762368";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0196762368";
};

# المستخدم 91: 0149019646
:do {
    /tool user-manager user add customer="admin" username="0149019646" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0149019646";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0149019646";
};

# المستخدم 92: 0117802278
:do {
    /tool user-manager user add customer="admin" username="0117802278" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0117802278";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0117802278";
};

# المستخدم 93: 0186688266
:do {
    /tool user-manager user add customer="admin" username="0186688266" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0186688266";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0186688266";
};

# المستخدم 94: 0175812122
:do {
    /tool user-manager user add customer="admin" username="0175812122" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0175812122";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0175812122";
};

# المستخدم 95: 0134229111
:do {
    /tool user-manager user add customer="admin" username="0134229111" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0134229111";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0134229111";
};

# المستخدم 96: 0154824500
:do {
    /tool user-manager user add customer="admin" username="0154824500" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0154824500";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0154824500";
};

# المستخدم 97: 0174967890
:do {
    /tool user-manager user add customer="admin" username="0174967890" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0174967890";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0174967890";
};

# المستخدم 98: 0155417495
:do {
    /tool user-manager user add customer="admin" username="0155417495" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0155417495";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0155417495";
};

# المستخدم 99: 0157589212
:do {
    /tool user-manager user add customer="admin" username="0157589212" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0157589212";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0157589212";
};

# المستخدم 100: 0134751818
:do {
    /tool user-manager user add customer="admin" username="0134751818" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0134751818";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0134751818";
};

# المستخدم 101: 0155934024
:do {
    /tool user-manager user add customer="admin" username="0155934024" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0155934024";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0155934024";
};

# المستخدم 102: 0177905364
:do {
    /tool user-manager user add customer="admin" username="0177905364" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0177905364";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0177905364";
};

# المستخدم 103: 0163761528
:do {
    /tool user-manager user add customer="admin" username="0163761528" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0163761528";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0163761528";
};

# المستخدم 104: 0189779029
:do {
    /tool user-manager user add customer="admin" username="0189779029" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0189779029";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0189779029";
};

# المستخدم 105: 0132085961
:do {
    /tool user-manager user add customer="admin" username="0132085961" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0132085961";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0132085961";
};

# المستخدم 106: 0179991188
:do {
    /tool user-manager user add customer="admin" username="0179991188" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0179991188";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0179991188";
};

# المستخدم 107: 0190599460
:do {
    /tool user-manager user add customer="admin" username="0190599460" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0190599460";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0190599460";
};

# المستخدم 108: 0107382759
:do {
    /tool user-manager user add customer="admin" username="0107382759" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0107382759";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0107382759";
};

# المستخدم 109: 0176071836
:do {
    /tool user-manager user add customer="admin" username="0176071836" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0176071836";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0176071836";
};

# المستخدم 110: 0122980716
:do {
    /tool user-manager user add customer="admin" username="0122980716" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0122980716";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0122980716";
};

# المستخدم 111: 0187725188
:do {
    /tool user-manager user add customer="admin" username="0187725188" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0187725188";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0187725188";
};

# المستخدم 112: 0186767397
:do {
    /tool user-manager user add customer="admin" username="0186767397" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0186767397";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0186767397";
};

# المستخدم 113: 0131125898
:do {
    /tool user-manager user add customer="admin" username="0131125898" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0131125898";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0131125898";
};

# المستخدم 114: 0107971786
:do {
    /tool user-manager user add customer="admin" username="0107971786" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0107971786";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0107971786";
};

# المستخدم 115: 0168406633
:do {
    /tool user-manager user add customer="admin" username="0168406633" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0168406633";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0168406633";
};

# المستخدم 116: 0118947245
:do {
    /tool user-manager user add customer="admin" username="0118947245" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0118947245";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0118947245";
};

# المستخدم 117: 0194534919
:do {
    /tool user-manager user add customer="admin" username="0194534919" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0194534919";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0194534919";
};

# المستخدم 118: 0195070362
:do {
    /tool user-manager user add customer="admin" username="0195070362" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0195070362";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0195070362";
};

# المستخدم 119: 0123906976
:do {
    /tool user-manager user add customer="admin" username="0123906976" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0123906976";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0123906976";
};

# المستخدم 120: 0121848700
:do {
    /tool user-manager user add customer="admin" username="0121848700" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0121848700";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0121848700";
};

# المستخدم 121: 0165701087
:do {
    /tool user-manager user add customer="admin" username="0165701087" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0165701087";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0165701087";
};

# المستخدم 122: 0173598520
:do {
    /tool user-manager user add customer="admin" username="0173598520" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0173598520";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0173598520";
};

# المستخدم 123: 0171061344
:do {
    /tool user-manager user add customer="admin" username="0171061344" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0171061344";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0171061344";
};

# المستخدم 124: 0147383583
:do {
    /tool user-manager user add customer="admin" username="0147383583" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0147383583";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0147383583";
};

# المستخدم 125: 0149219137
:do {
    /tool user-manager user add customer="admin" username="0149219137" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0149219137";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0149219137";
};

# المستخدم 126: 0159993707
:do {
    /tool user-manager user add customer="admin" username="0159993707" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0159993707";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0159993707";
};

# المستخدم 127: 0131491168
:do {
    /tool user-manager user add customer="admin" username="0131491168" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0131491168";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0131491168";
};

# المستخدم 128: 0146743653
:do {
    /tool user-manager user add customer="admin" username="0146743653" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0146743653";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0146743653";
};

# المستخدم 129: 0153967855
:do {
    /tool user-manager user add customer="admin" username="0153967855" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0153967855";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0153967855";
};

# المستخدم 130: 0161570272
:do {
    /tool user-manager user add customer="admin" username="0161570272" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0161570272";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0161570272";
};

# المستخدم 131: 0197812566
:do {
    /tool user-manager user add customer="admin" username="0197812566" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0197812566";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0197812566";
};

# المستخدم 132: 0100664459
:do {
    /tool user-manager user add customer="admin" username="0100664459" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0100664459";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0100664459";
};

# المستخدم 133: 0192701913
:do {
    /tool user-manager user add customer="admin" username="0192701913" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0192701913";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0192701913";
};

# المستخدم 134: 0107388865
:do {
    /tool user-manager user add customer="admin" username="0107388865" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0107388865";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0107388865";
};

# المستخدم 135: 0100602416
:do {
    /tool user-manager user add customer="admin" username="0100602416" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0100602416";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0100602416";
};

# المستخدم 136: 0196977225
:do {
    /tool user-manager user add customer="admin" username="0196977225" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0196977225";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0196977225";
};

# المستخدم 137: 0133058866
:do {
    /tool user-manager user add customer="admin" username="0133058866" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0133058866";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0133058866";
};

# المستخدم 138: 0184446353
:do {
    /tool user-manager user add customer="admin" username="0184446353" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0184446353";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0184446353";
};

# المستخدم 139: 0197780253
:do {
    /tool user-manager user add customer="admin" username="0197780253" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0197780253";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0197780253";
};

# المستخدم 140: 0103693601
:do {
    /tool user-manager user add customer="admin" username="0103693601" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0103693601";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0103693601";
};

# المستخدم 141: 0144713719
:do {
    /tool user-manager user add customer="admin" username="0144713719" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0144713719";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0144713719";
};

# المستخدم 142: 0162154133
:do {
    /tool user-manager user add customer="admin" username="0162154133" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0162154133";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0162154133";
};

# المستخدم 143: 0164803708
:do {
    /tool user-manager user add customer="admin" username="0164803708" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0164803708";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0164803708";
};

# المستخدم 144: 0130378387
:do {
    /tool user-manager user add customer="admin" username="0130378387" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0130378387";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0130378387";
};

# المستخدم 145: 0115333469
:do {
    /tool user-manager user add customer="admin" username="0115333469" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0115333469";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0115333469";
};

# المستخدم 146: 0156168542
:do {
    /tool user-manager user add customer="admin" username="0156168542" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0156168542";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0156168542";
};

# المستخدم 147: 0149368791
:do {
    /tool user-manager user add customer="admin" username="0149368791" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0149368791";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0149368791";
};

# المستخدم 148: 0144228465
:do {
    /tool user-manager user add customer="admin" username="0144228465" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0144228465";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0144228465";
};

# المستخدم 149: 0136833760
:do {
    /tool user-manager user add customer="admin" username="0136833760" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0136833760";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0136833760";
};

# المستخدم 150: 0169210087
:do {
    /tool user-manager user add customer="admin" username="0169210087" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0169210087";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0169210087";
};

# المستخدم 151: 0172934143
:do {
    /tool user-manager user add customer="admin" username="0172934143" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0172934143";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0172934143";
};

# المستخدم 152: 0146956871
:do {
    /tool user-manager user add customer="admin" username="0146956871" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0146956871";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0146956871";
};

# المستخدم 153: 0135681001
:do {
    /tool user-manager user add customer="admin" username="0135681001" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0135681001";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0135681001";
};

# المستخدم 154: 0188727146
:do {
    /tool user-manager user add customer="admin" username="0188727146" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0188727146";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0188727146";
};

# المستخدم 155: 0149923989
:do {
    /tool user-manager user add customer="admin" username="0149923989" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0149923989";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0149923989";
};

# المستخدم 156: 0184197299
:do {
    /tool user-manager user add customer="admin" username="0184197299" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0184197299";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0184197299";
};

# المستخدم 157: 0198936353
:do {
    /tool user-manager user add customer="admin" username="0198936353" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0198936353";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0198936353";
};

# المستخدم 158: 0106090053
:do {
    /tool user-manager user add customer="admin" username="0106090053" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0106090053";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0106090053";
};

# المستخدم 159: 0127213463
:do {
    /tool user-manager user add customer="admin" username="0127213463" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0127213463";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0127213463";
};

# المستخدم 160: 0152947804
:do {
    /tool user-manager user add customer="admin" username="0152947804" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0152947804";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0152947804";
};

# المستخدم 161: 0132170239
:do {
    /tool user-manager user add customer="admin" username="0132170239" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0132170239";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0132170239";
};

# المستخدم 162: 0182374783
:do {
    /tool user-manager user add customer="admin" username="0182374783" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0182374783";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0182374783";
};

# المستخدم 163: 0103500688
:do {
    /tool user-manager user add customer="admin" username="0103500688" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0103500688";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0103500688";
};

# المستخدم 164: 0134031620
:do {
    /tool user-manager user add customer="admin" username="0134031620" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0134031620";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0134031620";
};

# المستخدم 165: 0162258725
:do {
    /tool user-manager user add customer="admin" username="0162258725" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0162258725";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0162258725";
};

# المستخدم 166: 0180195759
:do {
    /tool user-manager user add customer="admin" username="0180195759" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0180195759";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0180195759";
};

# المستخدم 167: 0141675258
:do {
    /tool user-manager user add customer="admin" username="0141675258" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0141675258";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0141675258";
};

# المستخدم 168: 0116256048
:do {
    /tool user-manager user add customer="admin" username="0116256048" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0116256048";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0116256048";
};

# المستخدم 169: 0107402969
:do {
    /tool user-manager user add customer="admin" username="0107402969" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0107402969";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0107402969";
};

# المستخدم 170: 0199829315
:do {
    /tool user-manager user add customer="admin" username="0199829315" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0199829315";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0199829315";
};

# المستخدم 171: 0152396833
:do {
    /tool user-manager user add customer="admin" username="0152396833" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0152396833";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0152396833";
};

# المستخدم 172: 0148079907
:do {
    /tool user-manager user add customer="admin" username="0148079907" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0148079907";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0148079907";
};

# المستخدم 173: 0148265689
:do {
    /tool user-manager user add customer="admin" username="0148265689" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0148265689";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0148265689";
};

# المستخدم 174: 0127605397
:do {
    /tool user-manager user add customer="admin" username="0127605397" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0127605397";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0127605397";
};

# المستخدم 175: 0103478843
:do {
    /tool user-manager user add customer="admin" username="0103478843" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0103478843";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0103478843";
};

# المستخدم 176: 0117441123
:do {
    /tool user-manager user add customer="admin" username="0117441123" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0117441123";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0117441123";
};

# المستخدم 177: 0123402892
:do {
    /tool user-manager user add customer="admin" username="0123402892" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0123402892";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0123402892";
};

# المستخدم 178: 0184519855
:do {
    /tool user-manager user add customer="admin" username="0184519855" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0184519855";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0184519855";
};

# المستخدم 179: 0166487461
:do {
    /tool user-manager user add customer="admin" username="0166487461" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0166487461";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0166487461";
};

# المستخدم 180: 0169821495
:do {
    /tool user-manager user add customer="admin" username="0169821495" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0169821495";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0169821495";
};

# المستخدم 181: 0160611248
:do {
    /tool user-manager user add customer="admin" username="0160611248" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0160611248";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0160611248";
};

# المستخدم 182: 0105146493
:do {
    /tool user-manager user add customer="admin" username="0105146493" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0105146493";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0105146493";
};

# المستخدم 183: 0179072265
:do {
    /tool user-manager user add customer="admin" username="0179072265" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0179072265";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0179072265";
};

# المستخدم 184: 0179922070
:do {
    /tool user-manager user add customer="admin" username="0179922070" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0179922070";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0179922070";
};

# المستخدم 185: 0171742401
:do {
    /tool user-manager user add customer="admin" username="0171742401" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0171742401";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0171742401";
};

# المستخدم 186: 0151881024
:do {
    /tool user-manager user add customer="admin" username="0151881024" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0151881024";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0151881024";
};

# المستخدم 187: 0135146788
:do {
    /tool user-manager user add customer="admin" username="0135146788" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0135146788";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0135146788";
};

# المستخدم 188: 0136927762
:do {
    /tool user-manager user add customer="admin" username="0136927762" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0136927762";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0136927762";
};

# المستخدم 189: 0185751699
:do {
    /tool user-manager user add customer="admin" username="0185751699" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0185751699";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0185751699";
};

# المستخدم 190: 0111213492
:do {
    /tool user-manager user add customer="admin" username="0111213492" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0111213492";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0111213492";
};

# المستخدم 191: 0174810554
:do {
    /tool user-manager user add customer="admin" username="0174810554" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0174810554";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0174810554";
};

# المستخدم 192: 0103797118
:do {
    /tool user-manager user add customer="admin" username="0103797118" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0103797118";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0103797118";
};

# المستخدم 193: 0187369964
:do {
    /tool user-manager user add customer="admin" username="0187369964" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0187369964";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0187369964";
};

# المستخدم 194: 0130127180
:do {
    /tool user-manager user add customer="admin" username="0130127180" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0130127180";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0130127180";
};

# المستخدم 195: 0150650345
:do {
    /tool user-manager user add customer="admin" username="0150650345" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0150650345";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0150650345";
};

# المستخدم 196: 0115942978
:do {
    /tool user-manager user add customer="admin" username="0115942978" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0115942978";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0115942978";
};

# المستخدم 197: 0137775087
:do {
    /tool user-manager user add customer="admin" username="0137775087" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0137775087";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0137775087";
};

# المستخدم 198: 0135152595
:do {
    /tool user-manager user add customer="admin" username="0135152595" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0135152595";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0135152595";
};

# المستخدم 199: 0159362398
:do {
    /tool user-manager user add customer="admin" username="0159362398" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0159362398";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0159362398";
};

# المستخدم 200: 0156005432
:do {
    /tool user-manager user add customer="admin" username="0156005432" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0156005432";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0156005432";
};


:put "📊 تم الانتهاء من النسخة الاحتياطية";
:put "✅ نجح: $success كرت";
:put "❌ فشل: $errors كرت";
:put "📈 الإجمالي: $total كرت";

:put "🎉 تم استعادة جميع كروت القالب: 10";
:put "💡 النسخة الاحتياطية مكتملة بنجاح!";
