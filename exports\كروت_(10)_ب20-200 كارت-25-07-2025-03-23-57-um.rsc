# MikroTik Script - نسخة احتياطية للكروت
# تم الإنشاء: 2025-07-25 03:23:57
# القالب: 10
# النظام: user_manager
# عدد الكروت: 200
# تم إنشاؤه بواسطة: مولد كروت MikroTik

:put "🚀 بدء تنفيذ النسخة الاحتياطية للكروت";
:put "📋 القالب: 10";
:put "🎯 النظام: user_manager";
:put "📊 عدد الكروت: 200";

:local success 0;
:local errors 0;
:local total 200;

:put "⏳ بدء إضافة الكروت...";

# إضافة مستخدمي User Manager
:put "👤 إضافة 200 مستخدم User Manager...";

# المستخدم 1: 0162076461
:do {
    /tool user-manager user add customer="admin" username="0162076461" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0162076461";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0162076461";
};

# المستخدم 2: 0153806708
:do {
    /tool user-manager user add customer="admin" username="0153806708" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0153806708";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0153806708";
};

# المستخدم 3: 0114820479
:do {
    /tool user-manager user add customer="admin" username="0114820479" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0114820479";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0114820479";
};

# المستخدم 4: 0115107526
:do {
    /tool user-manager user add customer="admin" username="0115107526" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0115107526";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0115107526";
};

# المستخدم 5: 0131043068
:do {
    /tool user-manager user add customer="admin" username="0131043068" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0131043068";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0131043068";
};

# المستخدم 6: 0151109950
:do {
    /tool user-manager user add customer="admin" username="0151109950" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0151109950";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0151109950";
};

# المستخدم 7: 0124539049
:do {
    /tool user-manager user add customer="admin" username="0124539049" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0124539049";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0124539049";
};

# المستخدم 8: 0193689918
:do {
    /tool user-manager user add customer="admin" username="0193689918" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0193689918";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0193689918";
};

# المستخدم 9: 0117585226
:do {
    /tool user-manager user add customer="admin" username="0117585226" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0117585226";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0117585226";
};

# المستخدم 10: 0144564408
:do {
    /tool user-manager user add customer="admin" username="0144564408" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0144564408";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0144564408";
};

# المستخدم 11: 0185197543
:do {
    /tool user-manager user add customer="admin" username="0185197543" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0185197543";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0185197543";
};

# المستخدم 12: 0132517276
:do {
    /tool user-manager user add customer="admin" username="0132517276" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0132517276";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0132517276";
};

# المستخدم 13: 0162626386
:do {
    /tool user-manager user add customer="admin" username="0162626386" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0162626386";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0162626386";
};

# المستخدم 14: 0109444924
:do {
    /tool user-manager user add customer="admin" username="0109444924" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0109444924";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0109444924";
};

# المستخدم 15: 0120295890
:do {
    /tool user-manager user add customer="admin" username="0120295890" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0120295890";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0120295890";
};

# المستخدم 16: 0194083327
:do {
    /tool user-manager user add customer="admin" username="0194083327" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0194083327";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0194083327";
};

# المستخدم 17: 0121846254
:do {
    /tool user-manager user add customer="admin" username="0121846254" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0121846254";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0121846254";
};

# المستخدم 18: 0122428129
:do {
    /tool user-manager user add customer="admin" username="0122428129" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0122428129";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0122428129";
};

# المستخدم 19: 0191093447
:do {
    /tool user-manager user add customer="admin" username="0191093447" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0191093447";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0191093447";
};

# المستخدم 20: 0171940347
:do {
    /tool user-manager user add customer="admin" username="0171940347" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0171940347";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0171940347";
};

# المستخدم 21: 0140024566
:do {
    /tool user-manager user add customer="admin" username="0140024566" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0140024566";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0140024566";
};

# المستخدم 22: 0176612342
:do {
    /tool user-manager user add customer="admin" username="0176612342" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0176612342";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0176612342";
};

# المستخدم 23: 0109973552
:do {
    /tool user-manager user add customer="admin" username="0109973552" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0109973552";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0109973552";
};

# المستخدم 24: 0177965907
:do {
    /tool user-manager user add customer="admin" username="0177965907" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0177965907";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0177965907";
};

# المستخدم 25: 0134283824
:do {
    /tool user-manager user add customer="admin" username="0134283824" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0134283824";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0134283824";
};

# المستخدم 26: 0158172003
:do {
    /tool user-manager user add customer="admin" username="0158172003" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0158172003";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0158172003";
};

# المستخدم 27: 0122314645
:do {
    /tool user-manager user add customer="admin" username="0122314645" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0122314645";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0122314645";
};

# المستخدم 28: 0103506765
:do {
    /tool user-manager user add customer="admin" username="0103506765" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0103506765";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0103506765";
};

# المستخدم 29: 0140286001
:do {
    /tool user-manager user add customer="admin" username="0140286001" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0140286001";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0140286001";
};

# المستخدم 30: 0164572386
:do {
    /tool user-manager user add customer="admin" username="0164572386" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0164572386";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0164572386";
};

# المستخدم 31: 0152782508
:do {
    /tool user-manager user add customer="admin" username="0152782508" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0152782508";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0152782508";
};

# المستخدم 32: 0111522542
:do {
    /tool user-manager user add customer="admin" username="0111522542" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0111522542";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0111522542";
};

# المستخدم 33: 0153100285
:do {
    /tool user-manager user add customer="admin" username="0153100285" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0153100285";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0153100285";
};

# المستخدم 34: 0190115363
:do {
    /tool user-manager user add customer="admin" username="0190115363" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0190115363";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0190115363";
};

# المستخدم 35: 0145912837
:do {
    /tool user-manager user add customer="admin" username="0145912837" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0145912837";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0145912837";
};

# المستخدم 36: 0152229194
:do {
    /tool user-manager user add customer="admin" username="0152229194" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0152229194";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0152229194";
};

# المستخدم 37: 0199599774
:do {
    /tool user-manager user add customer="admin" username="0199599774" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0199599774";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0199599774";
};

# المستخدم 38: 0185863359
:do {
    /tool user-manager user add customer="admin" username="0185863359" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0185863359";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0185863359";
};

# المستخدم 39: 0176030209
:do {
    /tool user-manager user add customer="admin" username="0176030209" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0176030209";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0176030209";
};

# المستخدم 40: 0172041301
:do {
    /tool user-manager user add customer="admin" username="0172041301" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0172041301";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0172041301";
};

# المستخدم 41: 0183763148
:do {
    /tool user-manager user add customer="admin" username="0183763148" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0183763148";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0183763148";
};

# المستخدم 42: 0139121416
:do {
    /tool user-manager user add customer="admin" username="0139121416" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0139121416";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0139121416";
};

# المستخدم 43: 0103256678
:do {
    /tool user-manager user add customer="admin" username="0103256678" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0103256678";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0103256678";
};

# المستخدم 44: 0191773388
:do {
    /tool user-manager user add customer="admin" username="0191773388" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0191773388";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0191773388";
};

# المستخدم 45: 0197034929
:do {
    /tool user-manager user add customer="admin" username="0197034929" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0197034929";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0197034929";
};

# المستخدم 46: 0184975798
:do {
    /tool user-manager user add customer="admin" username="0184975798" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0184975798";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0184975798";
};

# المستخدم 47: 0170517715
:do {
    /tool user-manager user add customer="admin" username="0170517715" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0170517715";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0170517715";
};

# المستخدم 48: 0137938170
:do {
    /tool user-manager user add customer="admin" username="0137938170" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0137938170";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0137938170";
};

# المستخدم 49: 0151852396
:do {
    /tool user-manager user add customer="admin" username="0151852396" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0151852396";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0151852396";
};

# المستخدم 50: 0137297987
:do {
    /tool user-manager user add customer="admin" username="0137297987" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0137297987";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0137297987";
};

# المستخدم 51: 0125806823
:do {
    /tool user-manager user add customer="admin" username="0125806823" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0125806823";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0125806823";
};

# المستخدم 52: 0156034592
:do {
    /tool user-manager user add customer="admin" username="0156034592" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0156034592";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0156034592";
};

# المستخدم 53: 0108362605
:do {
    /tool user-manager user add customer="admin" username="0108362605" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0108362605";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0108362605";
};

# المستخدم 54: 0145679920
:do {
    /tool user-manager user add customer="admin" username="0145679920" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0145679920";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0145679920";
};

# المستخدم 55: 0166933704
:do {
    /tool user-manager user add customer="admin" username="0166933704" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0166933704";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0166933704";
};

# المستخدم 56: 0181129799
:do {
    /tool user-manager user add customer="admin" username="0181129799" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0181129799";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0181129799";
};

# المستخدم 57: 0135823806
:do {
    /tool user-manager user add customer="admin" username="0135823806" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0135823806";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0135823806";
};

# المستخدم 58: 0107008007
:do {
    /tool user-manager user add customer="admin" username="0107008007" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0107008007";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0107008007";
};

# المستخدم 59: 0124908040
:do {
    /tool user-manager user add customer="admin" username="0124908040" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0124908040";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0124908040";
};

# المستخدم 60: 0175851548
:do {
    /tool user-manager user add customer="admin" username="0175851548" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0175851548";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0175851548";
};

# المستخدم 61: 0140038326
:do {
    /tool user-manager user add customer="admin" username="0140038326" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0140038326";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0140038326";
};

# المستخدم 62: 0189994635
:do {
    /tool user-manager user add customer="admin" username="0189994635" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0189994635";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0189994635";
};

# المستخدم 63: 0183707128
:do {
    /tool user-manager user add customer="admin" username="0183707128" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0183707128";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0183707128";
};

# المستخدم 64: 0109767618
:do {
    /tool user-manager user add customer="admin" username="0109767618" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0109767618";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0109767618";
};

# المستخدم 65: 0182830763
:do {
    /tool user-manager user add customer="admin" username="0182830763" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0182830763";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0182830763";
};

# المستخدم 66: 0142651696
:do {
    /tool user-manager user add customer="admin" username="0142651696" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0142651696";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0142651696";
};

# المستخدم 67: 0128647158
:do {
    /tool user-manager user add customer="admin" username="0128647158" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0128647158";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0128647158";
};

# المستخدم 68: 0197512382
:do {
    /tool user-manager user add customer="admin" username="0197512382" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0197512382";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0197512382";
};

# المستخدم 69: 0178960604
:do {
    /tool user-manager user add customer="admin" username="0178960604" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0178960604";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0178960604";
};

# المستخدم 70: 0188336150
:do {
    /tool user-manager user add customer="admin" username="0188336150" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0188336150";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0188336150";
};

# المستخدم 71: 0127655461
:do {
    /tool user-manager user add customer="admin" username="0127655461" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0127655461";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0127655461";
};

# المستخدم 72: 0186465697
:do {
    /tool user-manager user add customer="admin" username="0186465697" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0186465697";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0186465697";
};

# المستخدم 73: 0128031422
:do {
    /tool user-manager user add customer="admin" username="0128031422" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0128031422";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0128031422";
};

# المستخدم 74: 0136680508
:do {
    /tool user-manager user add customer="admin" username="0136680508" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0136680508";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0136680508";
};

# المستخدم 75: 0184100925
:do {
    /tool user-manager user add customer="admin" username="0184100925" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0184100925";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0184100925";
};

# المستخدم 76: 0155740739
:do {
    /tool user-manager user add customer="admin" username="0155740739" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0155740739";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0155740739";
};

# المستخدم 77: 0172472213
:do {
    /tool user-manager user add customer="admin" username="0172472213" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0172472213";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0172472213";
};

# المستخدم 78: 0130672313
:do {
    /tool user-manager user add customer="admin" username="0130672313" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0130672313";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0130672313";
};

# المستخدم 79: 0181889490
:do {
    /tool user-manager user add customer="admin" username="0181889490" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0181889490";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0181889490";
};

# المستخدم 80: 0108079671
:do {
    /tool user-manager user add customer="admin" username="0108079671" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0108079671";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0108079671";
};

# المستخدم 81: 0123449720
:do {
    /tool user-manager user add customer="admin" username="0123449720" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0123449720";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0123449720";
};

# المستخدم 82: 0118592247
:do {
    /tool user-manager user add customer="admin" username="0118592247" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0118592247";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0118592247";
};

# المستخدم 83: 0185198796
:do {
    /tool user-manager user add customer="admin" username="0185198796" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0185198796";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0185198796";
};

# المستخدم 84: 0112745257
:do {
    /tool user-manager user add customer="admin" username="0112745257" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0112745257";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0112745257";
};

# المستخدم 85: 0186568834
:do {
    /tool user-manager user add customer="admin" username="0186568834" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0186568834";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0186568834";
};

# المستخدم 86: 0158522558
:do {
    /tool user-manager user add customer="admin" username="0158522558" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0158522558";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0158522558";
};

# المستخدم 87: 0169163100
:do {
    /tool user-manager user add customer="admin" username="0169163100" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0169163100";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0169163100";
};

# المستخدم 88: 0137484889
:do {
    /tool user-manager user add customer="admin" username="0137484889" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0137484889";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0137484889";
};

# المستخدم 89: 0184698461
:do {
    /tool user-manager user add customer="admin" username="0184698461" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0184698461";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0184698461";
};

# المستخدم 90: 0127541198
:do {
    /tool user-manager user add customer="admin" username="0127541198" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0127541198";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0127541198";
};

# المستخدم 91: 0159832106
:do {
    /tool user-manager user add customer="admin" username="0159832106" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0159832106";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0159832106";
};

# المستخدم 92: 0120006514
:do {
    /tool user-manager user add customer="admin" username="0120006514" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0120006514";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0120006514";
};

# المستخدم 93: 0157213476
:do {
    /tool user-manager user add customer="admin" username="0157213476" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0157213476";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0157213476";
};

# المستخدم 94: 0141150552
:do {
    /tool user-manager user add customer="admin" username="0141150552" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0141150552";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0141150552";
};

# المستخدم 95: 0141983188
:do {
    /tool user-manager user add customer="admin" username="0141983188" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0141983188";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0141983188";
};

# المستخدم 96: 0136468214
:do {
    /tool user-manager user add customer="admin" username="0136468214" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0136468214";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0136468214";
};

# المستخدم 97: 0164383454
:do {
    /tool user-manager user add customer="admin" username="0164383454" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0164383454";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0164383454";
};

# المستخدم 98: 0131196770
:do {
    /tool user-manager user add customer="admin" username="0131196770" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0131196770";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0131196770";
};

# المستخدم 99: 0154189864
:do {
    /tool user-manager user add customer="admin" username="0154189864" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0154189864";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0154189864";
};

# المستخدم 100: 0196439554
:do {
    /tool user-manager user add customer="admin" username="0196439554" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0196439554";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0196439554";
};

# المستخدم 101: 0125738634
:do {
    /tool user-manager user add customer="admin" username="0125738634" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0125738634";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0125738634";
};

# المستخدم 102: 0104459177
:do {
    /tool user-manager user add customer="admin" username="0104459177" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0104459177";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0104459177";
};

# المستخدم 103: 0178530002
:do {
    /tool user-manager user add customer="admin" username="0178530002" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0178530002";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0178530002";
};

# المستخدم 104: 0155579285
:do {
    /tool user-manager user add customer="admin" username="0155579285" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0155579285";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0155579285";
};

# المستخدم 105: 0147700413
:do {
    /tool user-manager user add customer="admin" username="0147700413" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0147700413";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0147700413";
};

# المستخدم 106: 0154899315
:do {
    /tool user-manager user add customer="admin" username="0154899315" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0154899315";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0154899315";
};

# المستخدم 107: 0147336007
:do {
    /tool user-manager user add customer="admin" username="0147336007" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0147336007";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0147336007";
};

# المستخدم 108: 0133019894
:do {
    /tool user-manager user add customer="admin" username="0133019894" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0133019894";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0133019894";
};

# المستخدم 109: 0183442882
:do {
    /tool user-manager user add customer="admin" username="0183442882" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0183442882";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0183442882";
};

# المستخدم 110: 0179166902
:do {
    /tool user-manager user add customer="admin" username="0179166902" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0179166902";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0179166902";
};

# المستخدم 111: 0143489678
:do {
    /tool user-manager user add customer="admin" username="0143489678" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0143489678";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0143489678";
};

# المستخدم 112: 0100828524
:do {
    /tool user-manager user add customer="admin" username="0100828524" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0100828524";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0100828524";
};

# المستخدم 113: 0134662344
:do {
    /tool user-manager user add customer="admin" username="0134662344" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0134662344";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0134662344";
};

# المستخدم 114: 0131024609
:do {
    /tool user-manager user add customer="admin" username="0131024609" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0131024609";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0131024609";
};

# المستخدم 115: 0127003064
:do {
    /tool user-manager user add customer="admin" username="0127003064" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0127003064";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0127003064";
};

# المستخدم 116: 0124035199
:do {
    /tool user-manager user add customer="admin" username="0124035199" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0124035199";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0124035199";
};

# المستخدم 117: 0194970808
:do {
    /tool user-manager user add customer="admin" username="0194970808" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0194970808";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0194970808";
};

# المستخدم 118: 0158488086
:do {
    /tool user-manager user add customer="admin" username="0158488086" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0158488086";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0158488086";
};

# المستخدم 119: 0183326585
:do {
    /tool user-manager user add customer="admin" username="0183326585" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0183326585";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0183326585";
};

# المستخدم 120: 0171302446
:do {
    /tool user-manager user add customer="admin" username="0171302446" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0171302446";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0171302446";
};

# المستخدم 121: 0163459395
:do {
    /tool user-manager user add customer="admin" username="0163459395" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0163459395";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0163459395";
};

# المستخدم 122: 0128232368
:do {
    /tool user-manager user add customer="admin" username="0128232368" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0128232368";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0128232368";
};

# المستخدم 123: 0138220534
:do {
    /tool user-manager user add customer="admin" username="0138220534" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0138220534";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0138220534";
};

# المستخدم 124: 0111244757
:do {
    /tool user-manager user add customer="admin" username="0111244757" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0111244757";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0111244757";
};

# المستخدم 125: 0166362414
:do {
    /tool user-manager user add customer="admin" username="0166362414" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0166362414";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0166362414";
};

# المستخدم 126: 0114138391
:do {
    /tool user-manager user add customer="admin" username="0114138391" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0114138391";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0114138391";
};

# المستخدم 127: 0134327343
:do {
    /tool user-manager user add customer="admin" username="0134327343" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0134327343";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0134327343";
};

# المستخدم 128: 0156411552
:do {
    /tool user-manager user add customer="admin" username="0156411552" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0156411552";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0156411552";
};

# المستخدم 129: 0112928001
:do {
    /tool user-manager user add customer="admin" username="0112928001" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0112928001";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0112928001";
};

# المستخدم 130: 0187556164
:do {
    /tool user-manager user add customer="admin" username="0187556164" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0187556164";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0187556164";
};

# المستخدم 131: 0170379911
:do {
    /tool user-manager user add customer="admin" username="0170379911" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0170379911";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0170379911";
};

# المستخدم 132: 0159928040
:do {
    /tool user-manager user add customer="admin" username="0159928040" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0159928040";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0159928040";
};

# المستخدم 133: 0185857925
:do {
    /tool user-manager user add customer="admin" username="0185857925" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0185857925";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0185857925";
};

# المستخدم 134: 0196457841
:do {
    /tool user-manager user add customer="admin" username="0196457841" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0196457841";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0196457841";
};

# المستخدم 135: 0181688171
:do {
    /tool user-manager user add customer="admin" username="0181688171" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0181688171";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0181688171";
};

# المستخدم 136: 0164609145
:do {
    /tool user-manager user add customer="admin" username="0164609145" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0164609145";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0164609145";
};

# المستخدم 137: 0120702899
:do {
    /tool user-manager user add customer="admin" username="0120702899" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0120702899";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0120702899";
};

# المستخدم 138: 0113116350
:do {
    /tool user-manager user add customer="admin" username="0113116350" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0113116350";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0113116350";
};

# المستخدم 139: 0185041927
:do {
    /tool user-manager user add customer="admin" username="0185041927" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0185041927";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0185041927";
};

# المستخدم 140: 0145185301
:do {
    /tool user-manager user add customer="admin" username="0145185301" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0145185301";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0145185301";
};

# المستخدم 141: 0142439086
:do {
    /tool user-manager user add customer="admin" username="0142439086" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0142439086";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0142439086";
};

# المستخدم 142: 0118914411
:do {
    /tool user-manager user add customer="admin" username="0118914411" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0118914411";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0118914411";
};

# المستخدم 143: 0174757748
:do {
    /tool user-manager user add customer="admin" username="0174757748" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0174757748";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0174757748";
};

# المستخدم 144: 0178603134
:do {
    /tool user-manager user add customer="admin" username="0178603134" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0178603134";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0178603134";
};

# المستخدم 145: 0178411284
:do {
    /tool user-manager user add customer="admin" username="0178411284" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0178411284";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0178411284";
};

# المستخدم 146: 0114939031
:do {
    /tool user-manager user add customer="admin" username="0114939031" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0114939031";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0114939031";
};

# المستخدم 147: 0170290683
:do {
    /tool user-manager user add customer="admin" username="0170290683" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0170290683";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0170290683";
};

# المستخدم 148: 0134137783
:do {
    /tool user-manager user add customer="admin" username="0134137783" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0134137783";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0134137783";
};

# المستخدم 149: 0136157624
:do {
    /tool user-manager user add customer="admin" username="0136157624" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0136157624";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0136157624";
};

# المستخدم 150: 0123091004
:do {
    /tool user-manager user add customer="admin" username="0123091004" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0123091004";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0123091004";
};

# المستخدم 151: 0191699872
:do {
    /tool user-manager user add customer="admin" username="0191699872" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0191699872";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0191699872";
};

# المستخدم 152: 0174052339
:do {
    /tool user-manager user add customer="admin" username="0174052339" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0174052339";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0174052339";
};

# المستخدم 153: 0128606037
:do {
    /tool user-manager user add customer="admin" username="0128606037" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0128606037";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0128606037";
};

# المستخدم 154: 0117731906
:do {
    /tool user-manager user add customer="admin" username="0117731906" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0117731906";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0117731906";
};

# المستخدم 155: 0146759096
:do {
    /tool user-manager user add customer="admin" username="0146759096" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0146759096";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0146759096";
};

# المستخدم 156: 0138168548
:do {
    /tool user-manager user add customer="admin" username="0138168548" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0138168548";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0138168548";
};

# المستخدم 157: 0188063985
:do {
    /tool user-manager user add customer="admin" username="0188063985" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0188063985";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0188063985";
};

# المستخدم 158: 0130852343
:do {
    /tool user-manager user add customer="admin" username="0130852343" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0130852343";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0130852343";
};

# المستخدم 159: 0119103058
:do {
    /tool user-manager user add customer="admin" username="0119103058" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0119103058";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0119103058";
};

# المستخدم 160: 0178892467
:do {
    /tool user-manager user add customer="admin" username="0178892467" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0178892467";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0178892467";
};

# المستخدم 161: 0104095963
:do {
    /tool user-manager user add customer="admin" username="0104095963" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0104095963";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0104095963";
};

# المستخدم 162: 0150149261
:do {
    /tool user-manager user add customer="admin" username="0150149261" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0150149261";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0150149261";
};

# المستخدم 163: 0150289893
:do {
    /tool user-manager user add customer="admin" username="0150289893" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0150289893";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0150289893";
};

# المستخدم 164: 0144119447
:do {
    /tool user-manager user add customer="admin" username="0144119447" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0144119447";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0144119447";
};

# المستخدم 165: 0141616824
:do {
    /tool user-manager user add customer="admin" username="0141616824" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0141616824";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0141616824";
};

# المستخدم 166: 0117162070
:do {
    /tool user-manager user add customer="admin" username="0117162070" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0117162070";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0117162070";
};

# المستخدم 167: 0157204828
:do {
    /tool user-manager user add customer="admin" username="0157204828" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0157204828";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0157204828";
};

# المستخدم 168: 0168733313
:do {
    /tool user-manager user add customer="admin" username="0168733313" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0168733313";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0168733313";
};

# المستخدم 169: 0185748627
:do {
    /tool user-manager user add customer="admin" username="0185748627" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0185748627";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0185748627";
};

# المستخدم 170: 0148134846
:do {
    /tool user-manager user add customer="admin" username="0148134846" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0148134846";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0148134846";
};

# المستخدم 171: 0130992173
:do {
    /tool user-manager user add customer="admin" username="0130992173" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0130992173";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0130992173";
};

# المستخدم 172: 0112881451
:do {
    /tool user-manager user add customer="admin" username="0112881451" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0112881451";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0112881451";
};

# المستخدم 173: 0197188392
:do {
    /tool user-manager user add customer="admin" username="0197188392" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0197188392";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0197188392";
};

# المستخدم 174: 0130838434
:do {
    /tool user-manager user add customer="admin" username="0130838434" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0130838434";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0130838434";
};

# المستخدم 175: 0196083916
:do {
    /tool user-manager user add customer="admin" username="0196083916" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0196083916";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0196083916";
};

# المستخدم 176: 0198154572
:do {
    /tool user-manager user add customer="admin" username="0198154572" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0198154572";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0198154572";
};

# المستخدم 177: 0183283962
:do {
    /tool user-manager user add customer="admin" username="0183283962" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0183283962";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0183283962";
};

# المستخدم 178: 0149688187
:do {
    /tool user-manager user add customer="admin" username="0149688187" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0149688187";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0149688187";
};

# المستخدم 179: 0198720960
:do {
    /tool user-manager user add customer="admin" username="0198720960" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0198720960";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0198720960";
};

# المستخدم 180: 0178798192
:do {
    /tool user-manager user add customer="admin" username="0178798192" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0178798192";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0178798192";
};

# المستخدم 181: 0139897362
:do {
    /tool user-manager user add customer="admin" username="0139897362" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0139897362";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0139897362";
};

# المستخدم 182: 0123279093
:do {
    /tool user-manager user add customer="admin" username="0123279093" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0123279093";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0123279093";
};

# المستخدم 183: 0141475756
:do {
    /tool user-manager user add customer="admin" username="0141475756" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0141475756";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0141475756";
};

# المستخدم 184: 0134444226
:do {
    /tool user-manager user add customer="admin" username="0134444226" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0134444226";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0134444226";
};

# المستخدم 185: 0191126231
:do {
    /tool user-manager user add customer="admin" username="0191126231" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0191126231";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0191126231";
};

# المستخدم 186: 0101383313
:do {
    /tool user-manager user add customer="admin" username="0101383313" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0101383313";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0101383313";
};

# المستخدم 187: 0180957979
:do {
    /tool user-manager user add customer="admin" username="0180957979" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0180957979";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0180957979";
};

# المستخدم 188: 0189725853
:do {
    /tool user-manager user add customer="admin" username="0189725853" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0189725853";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0189725853";
};

# المستخدم 189: 0115001784
:do {
    /tool user-manager user add customer="admin" username="0115001784" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0115001784";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0115001784";
};

# المستخدم 190: 0110795513
:do {
    /tool user-manager user add customer="admin" username="0110795513" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0110795513";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0110795513";
};

# المستخدم 191: 0170117454
:do {
    /tool user-manager user add customer="admin" username="0170117454" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0170117454";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0170117454";
};

# المستخدم 192: 0196231340
:do {
    /tool user-manager user add customer="admin" username="0196231340" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0196231340";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0196231340";
};

# المستخدم 193: 0120630240
:do {
    /tool user-manager user add customer="admin" username="0120630240" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0120630240";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0120630240";
};

# المستخدم 194: 0140702085
:do {
    /tool user-manager user add customer="admin" username="0140702085" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0140702085";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0140702085";
};

# المستخدم 195: 0161848551
:do {
    /tool user-manager user add customer="admin" username="0161848551" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0161848551";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0161848551";
};

# المستخدم 196: 0141668585
:do {
    /tool user-manager user add customer="admin" username="0141668585" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0141668585";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0141668585";
};

# المستخدم 197: 0183717842
:do {
    /tool user-manager user add customer="admin" username="0183717842" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0183717842";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0183717842";
};

# المستخدم 198: 0164446761
:do {
    /tool user-manager user add customer="admin" username="0164446761" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0164446761";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0164446761";
};

# المستخدم 199: 0106791409
:do {
    /tool user-manager user add customer="admin" username="0106791409" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0106791409";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0106791409";
};

# المستخدم 200: 0149476579
:do {
    /tool user-manager user add customer="admin" username="0149476579" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0149476579";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0149476579";
};


:put "📊 تم الانتهاء من النسخة الاحتياطية";
:put "✅ نجح: $success كرت";
:put "❌ فشل: $errors كرت";
:put "📈 الإجمالي: $total كرت";

:put "🎉 تم استعادة جميع كروت القالب: 10";
:put "💡 النسخة الاحتياطية مكتملة بنجاح!";
