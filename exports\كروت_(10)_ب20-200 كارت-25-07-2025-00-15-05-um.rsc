# MikroTik Script - نسخة احتياطية للكروت
# تم الإنشاء: 2025-07-25 00:15:05
# القالب: 10
# النظام: user_manager
# عدد الكروت: 200
# تم إنشاؤه بواسطة: مولد كروت MikroTik

:put "🚀 بدء تنفيذ النسخة الاحتياطية للكروت";
:put "📋 القالب: 10";
:put "🎯 النظام: user_manager";
:put "📊 عدد الكروت: 200";

:local success 0;
:local errors 0;
:local total 200;

:put "⏳ بدء إضافة الكروت...";

# إضافة مستخدمي User Manager
:put "👤 إضافة 200 مستخدم User Manager...";

# المستخدم 1: 0179421380
:do {
    /tool user-manager user add customer="admin" username="0179421380" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0179421380";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0179421380";
};

# المستخدم 2: 0136037722
:do {
    /tool user-manager user add customer="admin" username="0136037722" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0136037722";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0136037722";
};

# المستخدم 3: 0124931980
:do {
    /tool user-manager user add customer="admin" username="0124931980" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0124931980";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0124931980";
};

# المستخدم 4: 0136799638
:do {
    /tool user-manager user add customer="admin" username="0136799638" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0136799638";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0136799638";
};

# المستخدم 5: 0197382774
:do {
    /tool user-manager user add customer="admin" username="0197382774" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0197382774";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0197382774";
};

# المستخدم 6: 0115062959
:do {
    /tool user-manager user add customer="admin" username="0115062959" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0115062959";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0115062959";
};

# المستخدم 7: 0163255859
:do {
    /tool user-manager user add customer="admin" username="0163255859" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0163255859";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0163255859";
};

# المستخدم 8: 0189607762
:do {
    /tool user-manager user add customer="admin" username="0189607762" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0189607762";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0189607762";
};

# المستخدم 9: 0115388372
:do {
    /tool user-manager user add customer="admin" username="0115388372" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0115388372";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0115388372";
};

# المستخدم 10: 0111758701
:do {
    /tool user-manager user add customer="admin" username="0111758701" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0111758701";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0111758701";
};

# المستخدم 11: 0192868474
:do {
    /tool user-manager user add customer="admin" username="0192868474" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0192868474";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0192868474";
};

# المستخدم 12: 0161582058
:do {
    /tool user-manager user add customer="admin" username="0161582058" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0161582058";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0161582058";
};

# المستخدم 13: 0174297268
:do {
    /tool user-manager user add customer="admin" username="0174297268" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0174297268";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0174297268";
};

# المستخدم 14: 0125844450
:do {
    /tool user-manager user add customer="admin" username="0125844450" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0125844450";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0125844450";
};

# المستخدم 15: 0199276517
:do {
    /tool user-manager user add customer="admin" username="0199276517" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0199276517";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0199276517";
};

# المستخدم 16: 0108198598
:do {
    /tool user-manager user add customer="admin" username="0108198598" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0108198598";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0108198598";
};

# المستخدم 17: 0184270695
:do {
    /tool user-manager user add customer="admin" username="0184270695" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0184270695";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0184270695";
};

# المستخدم 18: 0185458476
:do {
    /tool user-manager user add customer="admin" username="0185458476" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0185458476";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0185458476";
};

# المستخدم 19: 0152936001
:do {
    /tool user-manager user add customer="admin" username="0152936001" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0152936001";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0152936001";
};

# المستخدم 20: 0176967316
:do {
    /tool user-manager user add customer="admin" username="0176967316" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0176967316";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0176967316";
};

# المستخدم 21: 0120249719
:do {
    /tool user-manager user add customer="admin" username="0120249719" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0120249719";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0120249719";
};

# المستخدم 22: 0194676077
:do {
    /tool user-manager user add customer="admin" username="0194676077" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0194676077";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0194676077";
};

# المستخدم 23: 0183590525
:do {
    /tool user-manager user add customer="admin" username="0183590525" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0183590525";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0183590525";
};

# المستخدم 24: 0164351911
:do {
    /tool user-manager user add customer="admin" username="0164351911" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0164351911";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0164351911";
};

# المستخدم 25: 0164501998
:do {
    /tool user-manager user add customer="admin" username="0164501998" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0164501998";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0164501998";
};

# المستخدم 26: 0163751225
:do {
    /tool user-manager user add customer="admin" username="0163751225" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0163751225";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0163751225";
};

# المستخدم 27: 0114268259
:do {
    /tool user-manager user add customer="admin" username="0114268259" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0114268259";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0114268259";
};

# المستخدم 28: 0150455589
:do {
    /tool user-manager user add customer="admin" username="0150455589" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0150455589";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0150455589";
};

# المستخدم 29: 0147884126
:do {
    /tool user-manager user add customer="admin" username="0147884126" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0147884126";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0147884126";
};

# المستخدم 30: 0153996764
:do {
    /tool user-manager user add customer="admin" username="0153996764" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0153996764";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0153996764";
};

# المستخدم 31: 0115934480
:do {
    /tool user-manager user add customer="admin" username="0115934480" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0115934480";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0115934480";
};

# المستخدم 32: 0131293839
:do {
    /tool user-manager user add customer="admin" username="0131293839" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0131293839";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0131293839";
};

# المستخدم 33: 0155836187
:do {
    /tool user-manager user add customer="admin" username="0155836187" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0155836187";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0155836187";
};

# المستخدم 34: 0108080790
:do {
    /tool user-manager user add customer="admin" username="0108080790" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0108080790";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0108080790";
};

# المستخدم 35: 0119999539
:do {
    /tool user-manager user add customer="admin" username="0119999539" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0119999539";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0119999539";
};

# المستخدم 36: 0106061589
:do {
    /tool user-manager user add customer="admin" username="0106061589" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0106061589";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0106061589";
};

# المستخدم 37: 0173279602
:do {
    /tool user-manager user add customer="admin" username="0173279602" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0173279602";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0173279602";
};

# المستخدم 38: 0118216182
:do {
    /tool user-manager user add customer="admin" username="0118216182" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0118216182";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0118216182";
};

# المستخدم 39: 0164284751
:do {
    /tool user-manager user add customer="admin" username="0164284751" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0164284751";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0164284751";
};

# المستخدم 40: 0168266163
:do {
    /tool user-manager user add customer="admin" username="0168266163" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0168266163";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0168266163";
};

# المستخدم 41: 0179366955
:do {
    /tool user-manager user add customer="admin" username="0179366955" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0179366955";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0179366955";
};

# المستخدم 42: 0128097762
:do {
    /tool user-manager user add customer="admin" username="0128097762" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0128097762";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0128097762";
};

# المستخدم 43: 0101742563
:do {
    /tool user-manager user add customer="admin" username="0101742563" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0101742563";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0101742563";
};

# المستخدم 44: 0177547384
:do {
    /tool user-manager user add customer="admin" username="0177547384" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0177547384";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0177547384";
};

# المستخدم 45: 0123070308
:do {
    /tool user-manager user add customer="admin" username="0123070308" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0123070308";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0123070308";
};

# المستخدم 46: 0196157034
:do {
    /tool user-manager user add customer="admin" username="0196157034" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0196157034";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0196157034";
};

# المستخدم 47: 0135307689
:do {
    /tool user-manager user add customer="admin" username="0135307689" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0135307689";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0135307689";
};

# المستخدم 48: 0161733404
:do {
    /tool user-manager user add customer="admin" username="0161733404" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0161733404";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0161733404";
};

# المستخدم 49: 0180127974
:do {
    /tool user-manager user add customer="admin" username="0180127974" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0180127974";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0180127974";
};

# المستخدم 50: 0133390352
:do {
    /tool user-manager user add customer="admin" username="0133390352" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0133390352";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0133390352";
};

# المستخدم 51: 0169953896
:do {
    /tool user-manager user add customer="admin" username="0169953896" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0169953896";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0169953896";
};

# المستخدم 52: 0179083247
:do {
    /tool user-manager user add customer="admin" username="0179083247" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0179083247";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0179083247";
};

# المستخدم 53: 0114118119
:do {
    /tool user-manager user add customer="admin" username="0114118119" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0114118119";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0114118119";
};

# المستخدم 54: 0157219602
:do {
    /tool user-manager user add customer="admin" username="0157219602" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0157219602";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0157219602";
};

# المستخدم 55: 0156980789
:do {
    /tool user-manager user add customer="admin" username="0156980789" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0156980789";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0156980789";
};

# المستخدم 56: 0167657825
:do {
    /tool user-manager user add customer="admin" username="0167657825" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0167657825";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0167657825";
};

# المستخدم 57: 0165754962
:do {
    /tool user-manager user add customer="admin" username="0165754962" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0165754962";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0165754962";
};

# المستخدم 58: 0196367259
:do {
    /tool user-manager user add customer="admin" username="0196367259" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0196367259";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0196367259";
};

# المستخدم 59: 0103849502
:do {
    /tool user-manager user add customer="admin" username="0103849502" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0103849502";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0103849502";
};

# المستخدم 60: 0123117736
:do {
    /tool user-manager user add customer="admin" username="0123117736" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0123117736";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0123117736";
};

# المستخدم 61: 0112889185
:do {
    /tool user-manager user add customer="admin" username="0112889185" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0112889185";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0112889185";
};

# المستخدم 62: 0177957828
:do {
    /tool user-manager user add customer="admin" username="0177957828" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0177957828";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0177957828";
};

# المستخدم 63: 0119324200
:do {
    /tool user-manager user add customer="admin" username="0119324200" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0119324200";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0119324200";
};

# المستخدم 64: 0100833915
:do {
    /tool user-manager user add customer="admin" username="0100833915" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0100833915";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0100833915";
};

# المستخدم 65: 0157168975
:do {
    /tool user-manager user add customer="admin" username="0157168975" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0157168975";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0157168975";
};

# المستخدم 66: 0158792347
:do {
    /tool user-manager user add customer="admin" username="0158792347" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0158792347";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0158792347";
};

# المستخدم 67: 0191631899
:do {
    /tool user-manager user add customer="admin" username="0191631899" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0191631899";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0191631899";
};

# المستخدم 68: 0143170034
:do {
    /tool user-manager user add customer="admin" username="0143170034" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0143170034";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0143170034";
};

# المستخدم 69: 0122870406
:do {
    /tool user-manager user add customer="admin" username="0122870406" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0122870406";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0122870406";
};

# المستخدم 70: 0131699670
:do {
    /tool user-manager user add customer="admin" username="0131699670" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0131699670";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0131699670";
};

# المستخدم 71: 0116880622
:do {
    /tool user-manager user add customer="admin" username="0116880622" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0116880622";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0116880622";
};

# المستخدم 72: 0179893725
:do {
    /tool user-manager user add customer="admin" username="0179893725" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0179893725";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0179893725";
};

# المستخدم 73: 0153462174
:do {
    /tool user-manager user add customer="admin" username="0153462174" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0153462174";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0153462174";
};

# المستخدم 74: 0108209953
:do {
    /tool user-manager user add customer="admin" username="0108209953" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0108209953";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0108209953";
};

# المستخدم 75: 0148775819
:do {
    /tool user-manager user add customer="admin" username="0148775819" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0148775819";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0148775819";
};

# المستخدم 76: 0125372345
:do {
    /tool user-manager user add customer="admin" username="0125372345" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0125372345";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0125372345";
};

# المستخدم 77: 0175512517
:do {
    /tool user-manager user add customer="admin" username="0175512517" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0175512517";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0175512517";
};

# المستخدم 78: 0112767253
:do {
    /tool user-manager user add customer="admin" username="0112767253" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0112767253";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0112767253";
};

# المستخدم 79: 0170211641
:do {
    /tool user-manager user add customer="admin" username="0170211641" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0170211641";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0170211641";
};

# المستخدم 80: 0158495198
:do {
    /tool user-manager user add customer="admin" username="0158495198" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0158495198";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0158495198";
};

# المستخدم 81: 0112337550
:do {
    /tool user-manager user add customer="admin" username="0112337550" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0112337550";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0112337550";
};

# المستخدم 82: 0145136566
:do {
    /tool user-manager user add customer="admin" username="0145136566" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0145136566";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0145136566";
};

# المستخدم 83: 0183283961
:do {
    /tool user-manager user add customer="admin" username="0183283961" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0183283961";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0183283961";
};

# المستخدم 84: 0151157935
:do {
    /tool user-manager user add customer="admin" username="0151157935" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0151157935";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0151157935";
};

# المستخدم 85: 0189743393
:do {
    /tool user-manager user add customer="admin" username="0189743393" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0189743393";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0189743393";
};

# المستخدم 86: 0158999426
:do {
    /tool user-manager user add customer="admin" username="0158999426" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0158999426";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0158999426";
};

# المستخدم 87: 0169068004
:do {
    /tool user-manager user add customer="admin" username="0169068004" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0169068004";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0169068004";
};

# المستخدم 88: 0195207164
:do {
    /tool user-manager user add customer="admin" username="0195207164" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0195207164";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0195207164";
};

# المستخدم 89: 0176516896
:do {
    /tool user-manager user add customer="admin" username="0176516896" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0176516896";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0176516896";
};

# المستخدم 90: 0155303840
:do {
    /tool user-manager user add customer="admin" username="0155303840" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0155303840";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0155303840";
};

# المستخدم 91: 0184469595
:do {
    /tool user-manager user add customer="admin" username="0184469595" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0184469595";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0184469595";
};

# المستخدم 92: 0174677740
:do {
    /tool user-manager user add customer="admin" username="0174677740" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0174677740";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0174677740";
};

# المستخدم 93: 0139324256
:do {
    /tool user-manager user add customer="admin" username="0139324256" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0139324256";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0139324256";
};

# المستخدم 94: 0198023191
:do {
    /tool user-manager user add customer="admin" username="0198023191" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0198023191";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0198023191";
};

# المستخدم 95: 0142192212
:do {
    /tool user-manager user add customer="admin" username="0142192212" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0142192212";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0142192212";
};

# المستخدم 96: 0114526132
:do {
    /tool user-manager user add customer="admin" username="0114526132" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0114526132";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0114526132";
};

# المستخدم 97: 0101868072
:do {
    /tool user-manager user add customer="admin" username="0101868072" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0101868072";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0101868072";
};

# المستخدم 98: 0104706531
:do {
    /tool user-manager user add customer="admin" username="0104706531" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0104706531";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0104706531";
};

# المستخدم 99: 0101809106
:do {
    /tool user-manager user add customer="admin" username="0101809106" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0101809106";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0101809106";
};

# المستخدم 100: 0166084233
:do {
    /tool user-manager user add customer="admin" username="0166084233" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0166084233";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0166084233";
};

# المستخدم 101: 0149975735
:do {
    /tool user-manager user add customer="admin" username="0149975735" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0149975735";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0149975735";
};

# المستخدم 102: 0154514491
:do {
    /tool user-manager user add customer="admin" username="0154514491" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0154514491";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0154514491";
};

# المستخدم 103: 0164790390
:do {
    /tool user-manager user add customer="admin" username="0164790390" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0164790390";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0164790390";
};

# المستخدم 104: 0190719132
:do {
    /tool user-manager user add customer="admin" username="0190719132" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0190719132";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0190719132";
};

# المستخدم 105: 0182631317
:do {
    /tool user-manager user add customer="admin" username="0182631317" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0182631317";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0182631317";
};

# المستخدم 106: 0110482411
:do {
    /tool user-manager user add customer="admin" username="0110482411" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0110482411";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0110482411";
};

# المستخدم 107: 0136598972
:do {
    /tool user-manager user add customer="admin" username="0136598972" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0136598972";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0136598972";
};

# المستخدم 108: 0109610125
:do {
    /tool user-manager user add customer="admin" username="0109610125" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0109610125";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0109610125";
};

# المستخدم 109: 0178927569
:do {
    /tool user-manager user add customer="admin" username="0178927569" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0178927569";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0178927569";
};

# المستخدم 110: 0131561641
:do {
    /tool user-manager user add customer="admin" username="0131561641" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0131561641";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0131561641";
};

# المستخدم 111: 0179262462
:do {
    /tool user-manager user add customer="admin" username="0179262462" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0179262462";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0179262462";
};

# المستخدم 112: 0115681589
:do {
    /tool user-manager user add customer="admin" username="0115681589" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0115681589";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0115681589";
};

# المستخدم 113: 0139737345
:do {
    /tool user-manager user add customer="admin" username="0139737345" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0139737345";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0139737345";
};

# المستخدم 114: 0174042400
:do {
    /tool user-manager user add customer="admin" username="0174042400" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0174042400";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0174042400";
};

# المستخدم 115: 0160747997
:do {
    /tool user-manager user add customer="admin" username="0160747997" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0160747997";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0160747997";
};

# المستخدم 116: 0157132809
:do {
    /tool user-manager user add customer="admin" username="0157132809" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0157132809";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0157132809";
};

# المستخدم 117: 0184095765
:do {
    /tool user-manager user add customer="admin" username="0184095765" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0184095765";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0184095765";
};

# المستخدم 118: 0167685402
:do {
    /tool user-manager user add customer="admin" username="0167685402" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0167685402";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0167685402";
};

# المستخدم 119: 0167545615
:do {
    /tool user-manager user add customer="admin" username="0167545615" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0167545615";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0167545615";
};

# المستخدم 120: 0161521498
:do {
    /tool user-manager user add customer="admin" username="0161521498" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0161521498";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0161521498";
};

# المستخدم 121: 0186810081
:do {
    /tool user-manager user add customer="admin" username="0186810081" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0186810081";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0186810081";
};

# المستخدم 122: 0164796266
:do {
    /tool user-manager user add customer="admin" username="0164796266" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0164796266";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0164796266";
};

# المستخدم 123: 0153588841
:do {
    /tool user-manager user add customer="admin" username="0153588841" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0153588841";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0153588841";
};

# المستخدم 124: 0199180724
:do {
    /tool user-manager user add customer="admin" username="0199180724" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0199180724";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0199180724";
};

# المستخدم 125: 0173825190
:do {
    /tool user-manager user add customer="admin" username="0173825190" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0173825190";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0173825190";
};

# المستخدم 126: 0120180388
:do {
    /tool user-manager user add customer="admin" username="0120180388" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0120180388";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0120180388";
};

# المستخدم 127: 0157520434
:do {
    /tool user-manager user add customer="admin" username="0157520434" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0157520434";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0157520434";
};

# المستخدم 128: 0103205905
:do {
    /tool user-manager user add customer="admin" username="0103205905" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0103205905";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0103205905";
};

# المستخدم 129: 0173884140
:do {
    /tool user-manager user add customer="admin" username="0173884140" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0173884140";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0173884140";
};

# المستخدم 130: 0121246456
:do {
    /tool user-manager user add customer="admin" username="0121246456" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0121246456";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0121246456";
};

# المستخدم 131: 0106737707
:do {
    /tool user-manager user add customer="admin" username="0106737707" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0106737707";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0106737707";
};

# المستخدم 132: 0172077662
:do {
    /tool user-manager user add customer="admin" username="0172077662" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0172077662";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0172077662";
};

# المستخدم 133: 0177350517
:do {
    /tool user-manager user add customer="admin" username="0177350517" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0177350517";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0177350517";
};

# المستخدم 134: 0139763984
:do {
    /tool user-manager user add customer="admin" username="0139763984" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0139763984";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0139763984";
};

# المستخدم 135: 0176190062
:do {
    /tool user-manager user add customer="admin" username="0176190062" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0176190062";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0176190062";
};

# المستخدم 136: 0150437581
:do {
    /tool user-manager user add customer="admin" username="0150437581" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0150437581";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0150437581";
};

# المستخدم 137: 0123476570
:do {
    /tool user-manager user add customer="admin" username="0123476570" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0123476570";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0123476570";
};

# المستخدم 138: 0153759053
:do {
    /tool user-manager user add customer="admin" username="0153759053" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0153759053";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0153759053";
};

# المستخدم 139: 0193605335
:do {
    /tool user-manager user add customer="admin" username="0193605335" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0193605335";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0193605335";
};

# المستخدم 140: 0181723339
:do {
    /tool user-manager user add customer="admin" username="0181723339" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0181723339";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0181723339";
};

# المستخدم 141: 0192036827
:do {
    /tool user-manager user add customer="admin" username="0192036827" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0192036827";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0192036827";
};

# المستخدم 142: 0105734474
:do {
    /tool user-manager user add customer="admin" username="0105734474" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0105734474";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0105734474";
};

# المستخدم 143: 0166784588
:do {
    /tool user-manager user add customer="admin" username="0166784588" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0166784588";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0166784588";
};

# المستخدم 144: 0128098644
:do {
    /tool user-manager user add customer="admin" username="0128098644" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0128098644";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0128098644";
};

# المستخدم 145: 0162730743
:do {
    /tool user-manager user add customer="admin" username="0162730743" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0162730743";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0162730743";
};

# المستخدم 146: 0151480659
:do {
    /tool user-manager user add customer="admin" username="0151480659" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0151480659";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0151480659";
};

# المستخدم 147: 0148278506
:do {
    /tool user-manager user add customer="admin" username="0148278506" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0148278506";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0148278506";
};

# المستخدم 148: 0144825758
:do {
    /tool user-manager user add customer="admin" username="0144825758" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0144825758";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0144825758";
};

# المستخدم 149: 0122044814
:do {
    /tool user-manager user add customer="admin" username="0122044814" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0122044814";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0122044814";
};

# المستخدم 150: 0188966832
:do {
    /tool user-manager user add customer="admin" username="0188966832" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0188966832";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0188966832";
};

# المستخدم 151: 0177727024
:do {
    /tool user-manager user add customer="admin" username="0177727024" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0177727024";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0177727024";
};

# المستخدم 152: 0150777483
:do {
    /tool user-manager user add customer="admin" username="0150777483" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0150777483";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0150777483";
};

# المستخدم 153: 0146568453
:do {
    /tool user-manager user add customer="admin" username="0146568453" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0146568453";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0146568453";
};

# المستخدم 154: 0173175785
:do {
    /tool user-manager user add customer="admin" username="0173175785" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0173175785";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0173175785";
};

# المستخدم 155: 0190003437
:do {
    /tool user-manager user add customer="admin" username="0190003437" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0190003437";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0190003437";
};

# المستخدم 156: 0177612834
:do {
    /tool user-manager user add customer="admin" username="0177612834" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0177612834";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0177612834";
};

# المستخدم 157: 0107486083
:do {
    /tool user-manager user add customer="admin" username="0107486083" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0107486083";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0107486083";
};

# المستخدم 158: 0147286324
:do {
    /tool user-manager user add customer="admin" username="0147286324" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0147286324";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0147286324";
};

# المستخدم 159: 0145578694
:do {
    /tool user-manager user add customer="admin" username="0145578694" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0145578694";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0145578694";
};

# المستخدم 160: 0150431923
:do {
    /tool user-manager user add customer="admin" username="0150431923" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0150431923";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0150431923";
};

# المستخدم 161: 0192983506
:do {
    /tool user-manager user add customer="admin" username="0192983506" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0192983506";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0192983506";
};

# المستخدم 162: 0100312226
:do {
    /tool user-manager user add customer="admin" username="0100312226" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0100312226";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0100312226";
};

# المستخدم 163: 0180200979
:do {
    /tool user-manager user add customer="admin" username="0180200979" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0180200979";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0180200979";
};

# المستخدم 164: 0114495105
:do {
    /tool user-manager user add customer="admin" username="0114495105" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0114495105";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0114495105";
};

# المستخدم 165: 0138902780
:do {
    /tool user-manager user add customer="admin" username="0138902780" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0138902780";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0138902780";
};

# المستخدم 166: 0139630441
:do {
    /tool user-manager user add customer="admin" username="0139630441" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0139630441";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0139630441";
};

# المستخدم 167: 0186055491
:do {
    /tool user-manager user add customer="admin" username="0186055491" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0186055491";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0186055491";
};

# المستخدم 168: 0115495319
:do {
    /tool user-manager user add customer="admin" username="0115495319" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0115495319";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0115495319";
};

# المستخدم 169: 0100954764
:do {
    /tool user-manager user add customer="admin" username="0100954764" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0100954764";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0100954764";
};

# المستخدم 170: 0110580714
:do {
    /tool user-manager user add customer="admin" username="0110580714" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0110580714";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0110580714";
};

# المستخدم 171: 0171397020
:do {
    /tool user-manager user add customer="admin" username="0171397020" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0171397020";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0171397020";
};

# المستخدم 172: 0122965959
:do {
    /tool user-manager user add customer="admin" username="0122965959" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0122965959";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0122965959";
};

# المستخدم 173: 0125964194
:do {
    /tool user-manager user add customer="admin" username="0125964194" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0125964194";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0125964194";
};

# المستخدم 174: 0113997439
:do {
    /tool user-manager user add customer="admin" username="0113997439" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0113997439";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0113997439";
};

# المستخدم 175: 0143970920
:do {
    /tool user-manager user add customer="admin" username="0143970920" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0143970920";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0143970920";
};

# المستخدم 176: 0113844506
:do {
    /tool user-manager user add customer="admin" username="0113844506" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0113844506";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0113844506";
};

# المستخدم 177: 0111284034
:do {
    /tool user-manager user add customer="admin" username="0111284034" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0111284034";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0111284034";
};

# المستخدم 178: 0134367774
:do {
    /tool user-manager user add customer="admin" username="0134367774" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0134367774";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0134367774";
};

# المستخدم 179: 0162778205
:do {
    /tool user-manager user add customer="admin" username="0162778205" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0162778205";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0162778205";
};

# المستخدم 180: 0107347760
:do {
    /tool user-manager user add customer="admin" username="0107347760" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0107347760";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0107347760";
};

# المستخدم 181: 0102108527
:do {
    /tool user-manager user add customer="admin" username="0102108527" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0102108527";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0102108527";
};

# المستخدم 182: 0100049745
:do {
    /tool user-manager user add customer="admin" username="0100049745" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0100049745";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0100049745";
};

# المستخدم 183: 0165472507
:do {
    /tool user-manager user add customer="admin" username="0165472507" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0165472507";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0165472507";
};

# المستخدم 184: 0197914327
:do {
    /tool user-manager user add customer="admin" username="0197914327" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0197914327";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0197914327";
};

# المستخدم 185: 0123343030
:do {
    /tool user-manager user add customer="admin" username="0123343030" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0123343030";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0123343030";
};

# المستخدم 186: 0119380348
:do {
    /tool user-manager user add customer="admin" username="0119380348" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0119380348";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0119380348";
};

# المستخدم 187: 0158354830
:do {
    /tool user-manager user add customer="admin" username="0158354830" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0158354830";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0158354830";
};

# المستخدم 188: 0112505811
:do {
    /tool user-manager user add customer="admin" username="0112505811" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0112505811";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0112505811";
};

# المستخدم 189: 0161937283
:do {
    /tool user-manager user add customer="admin" username="0161937283" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0161937283";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0161937283";
};

# المستخدم 190: 0103757141
:do {
    /tool user-manager user add customer="admin" username="0103757141" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0103757141";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0103757141";
};

# المستخدم 191: 0173372169
:do {
    /tool user-manager user add customer="admin" username="0173372169" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0173372169";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0173372169";
};

# المستخدم 192: 0140511431
:do {
    /tool user-manager user add customer="admin" username="0140511431" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0140511431";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0140511431";
};

# المستخدم 193: 0144236727
:do {
    /tool user-manager user add customer="admin" username="0144236727" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0144236727";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0144236727";
};

# المستخدم 194: 0152042717
:do {
    /tool user-manager user add customer="admin" username="0152042717" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0152042717";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0152042717";
};

# المستخدم 195: 0109756725
:do {
    /tool user-manager user add customer="admin" username="0109756725" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0109756725";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0109756725";
};

# المستخدم 196: 0170055919
:do {
    /tool user-manager user add customer="admin" username="0170055919" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0170055919";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0170055919";
};

# المستخدم 197: 0145223575
:do {
    /tool user-manager user add customer="admin" username="0145223575" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0145223575";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0145223575";
};

# المستخدم 198: 0182068438
:do {
    /tool user-manager user add customer="admin" username="0182068438" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0182068438";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0182068438";
};

# المستخدم 199: 0144996071
:do {
    /tool user-manager user add customer="admin" username="0144996071" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0144996071";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0144996071";
};

# المستخدم 200: 0163412853
:do {
    /tool user-manager user add customer="admin" username="0163412853" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0163412853";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0163412853";
};


:put "📊 تم الانتهاء من النسخة الاحتياطية";
:put "✅ نجح: $success كرت";
:put "❌ فشل: $errors كرت";
:put "📈 الإجمالي: $total كرت";

:put "🎉 تم استعادة جميع كروت القالب: 10";
:put "💡 النسخة الاحتياطية مكتملة بنجاح!";
