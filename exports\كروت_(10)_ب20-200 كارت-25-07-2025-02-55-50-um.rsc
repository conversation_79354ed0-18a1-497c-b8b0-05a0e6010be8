# MikroTik Script - نسخة احتياطية للكروت
# تم الإنشاء: 2025-07-25 02:55:50
# القالب: 10
# النظام: user_manager
# عدد الكروت: 200
# تم إنشاؤه بواسطة: مولد كروت MikroTik

:put "🚀 بدء تنفيذ النسخة الاحتياطية للكروت";
:put "📋 القالب: 10";
:put "🎯 النظام: user_manager";
:put "📊 عدد الكروت: 200";

:local success 0;
:local errors 0;
:local total 200;

:put "⏳ بدء إضافة الكروت...";

# إضافة مستخدمي User Manager
:put "👤 إضافة 200 مستخدم User Manager...";

# المستخدم 1: 0123776864
:do {
    /tool user-manager user add customer="admin" username="0123776864" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0123776864";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0123776864";
};

# المستخدم 2: 0124994551
:do {
    /tool user-manager user add customer="admin" username="0124994551" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0124994551";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0124994551";
};

# المستخدم 3: 0135897603
:do {
    /tool user-manager user add customer="admin" username="0135897603" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0135897603";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0135897603";
};

# المستخدم 4: 0192031511
:do {
    /tool user-manager user add customer="admin" username="0192031511" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0192031511";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0192031511";
};

# المستخدم 5: 0132574150
:do {
    /tool user-manager user add customer="admin" username="0132574150" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0132574150";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0132574150";
};

# المستخدم 6: 0133792247
:do {
    /tool user-manager user add customer="admin" username="0133792247" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0133792247";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0133792247";
};

# المستخدم 7: 0131071989
:do {
    /tool user-manager user add customer="admin" username="0131071989" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0131071989";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0131071989";
};

# المستخدم 8: 0108540934
:do {
    /tool user-manager user add customer="admin" username="0108540934" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0108540934";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0108540934";
};

# المستخدم 9: 0145370885
:do {
    /tool user-manager user add customer="admin" username="0145370885" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0145370885";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0145370885";
};

# المستخدم 10: 0128696240
:do {
    /tool user-manager user add customer="admin" username="0128696240" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0128696240";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0128696240";
};

# المستخدم 11: 0105860969
:do {
    /tool user-manager user add customer="admin" username="0105860969" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0105860969";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0105860969";
};

# المستخدم 12: 0172003946
:do {
    /tool user-manager user add customer="admin" username="0172003946" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0172003946";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0172003946";
};

# المستخدم 13: 0108799621
:do {
    /tool user-manager user add customer="admin" username="0108799621" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0108799621";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0108799621";
};

# المستخدم 14: 0121240158
:do {
    /tool user-manager user add customer="admin" username="0121240158" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0121240158";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0121240158";
};

# المستخدم 15: 0116480905
:do {
    /tool user-manager user add customer="admin" username="0116480905" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0116480905";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0116480905";
};

# المستخدم 16: 0130053621
:do {
    /tool user-manager user add customer="admin" username="0130053621" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0130053621";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0130053621";
};

# المستخدم 17: 0138445880
:do {
    /tool user-manager user add customer="admin" username="0138445880" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0138445880";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0138445880";
};

# المستخدم 18: 0147478699
:do {
    /tool user-manager user add customer="admin" username="0147478699" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0147478699";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0147478699";
};

# المستخدم 19: 0176434085
:do {
    /tool user-manager user add customer="admin" username="0176434085" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0176434085";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0176434085";
};

# المستخدم 20: 0180480933
:do {
    /tool user-manager user add customer="admin" username="0180480933" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0180480933";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0180480933";
};

# المستخدم 21: 0119541642
:do {
    /tool user-manager user add customer="admin" username="0119541642" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0119541642";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0119541642";
};

# المستخدم 22: 0141835731
:do {
    /tool user-manager user add customer="admin" username="0141835731" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0141835731";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0141835731";
};

# المستخدم 23: 0168990456
:do {
    /tool user-manager user add customer="admin" username="0168990456" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0168990456";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0168990456";
};

# المستخدم 24: 0144785438
:do {
    /tool user-manager user add customer="admin" username="0144785438" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0144785438";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0144785438";
};

# المستخدم 25: 0124995750
:do {
    /tool user-manager user add customer="admin" username="0124995750" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0124995750";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0124995750";
};

# المستخدم 26: 0194706418
:do {
    /tool user-manager user add customer="admin" username="0194706418" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0194706418";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0194706418";
};

# المستخدم 27: 0158871590
:do {
    /tool user-manager user add customer="admin" username="0158871590" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0158871590";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0158871590";
};

# المستخدم 28: 0189216235
:do {
    /tool user-manager user add customer="admin" username="0189216235" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0189216235";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0189216235";
};

# المستخدم 29: 0195830331
:do {
    /tool user-manager user add customer="admin" username="0195830331" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0195830331";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0195830331";
};

# المستخدم 30: 0114132496
:do {
    /tool user-manager user add customer="admin" username="0114132496" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0114132496";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0114132496";
};

# المستخدم 31: 0103606449
:do {
    /tool user-manager user add customer="admin" username="0103606449" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0103606449";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0103606449";
};

# المستخدم 32: 0139228708
:do {
    /tool user-manager user add customer="admin" username="0139228708" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0139228708";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0139228708";
};

# المستخدم 33: 0100652724
:do {
    /tool user-manager user add customer="admin" username="0100652724" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0100652724";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0100652724";
};

# المستخدم 34: 0173392866
:do {
    /tool user-manager user add customer="admin" username="0173392866" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0173392866";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0173392866";
};

# المستخدم 35: 0152364259
:do {
    /tool user-manager user add customer="admin" username="0152364259" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0152364259";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0152364259";
};

# المستخدم 36: 0102841184
:do {
    /tool user-manager user add customer="admin" username="0102841184" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0102841184";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0102841184";
};

# المستخدم 37: 0108465020
:do {
    /tool user-manager user add customer="admin" username="0108465020" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0108465020";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0108465020";
};

# المستخدم 38: 0158650803
:do {
    /tool user-manager user add customer="admin" username="0158650803" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0158650803";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0158650803";
};

# المستخدم 39: 0113934547
:do {
    /tool user-manager user add customer="admin" username="0113934547" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0113934547";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0113934547";
};

# المستخدم 40: 0123978705
:do {
    /tool user-manager user add customer="admin" username="0123978705" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0123978705";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0123978705";
};

# المستخدم 41: 0180349406
:do {
    /tool user-manager user add customer="admin" username="0180349406" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0180349406";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0180349406";
};

# المستخدم 42: 0137873081
:do {
    /tool user-manager user add customer="admin" username="0137873081" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0137873081";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0137873081";
};

# المستخدم 43: 0162711033
:do {
    /tool user-manager user add customer="admin" username="0162711033" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0162711033";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0162711033";
};

# المستخدم 44: 0125337076
:do {
    /tool user-manager user add customer="admin" username="0125337076" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0125337076";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0125337076";
};

# المستخدم 45: 0174862328
:do {
    /tool user-manager user add customer="admin" username="0174862328" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0174862328";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0174862328";
};

# المستخدم 46: 0120823201
:do {
    /tool user-manager user add customer="admin" username="0120823201" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0120823201";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0120823201";
};

# المستخدم 47: 0147757966
:do {
    /tool user-manager user add customer="admin" username="0147757966" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0147757966";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0147757966";
};

# المستخدم 48: 0115519582
:do {
    /tool user-manager user add customer="admin" username="0115519582" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0115519582";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0115519582";
};

# المستخدم 49: 0154409365
:do {
    /tool user-manager user add customer="admin" username="0154409365" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0154409365";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0154409365";
};

# المستخدم 50: 0150173298
:do {
    /tool user-manager user add customer="admin" username="0150173298" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0150173298";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0150173298";
};

# المستخدم 51: 0121988879
:do {
    /tool user-manager user add customer="admin" username="0121988879" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0121988879";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0121988879";
};

# المستخدم 52: 0139647228
:do {
    /tool user-manager user add customer="admin" username="0139647228" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0139647228";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0139647228";
};

# المستخدم 53: 0142188051
:do {
    /tool user-manager user add customer="admin" username="0142188051" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0142188051";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0142188051";
};

# المستخدم 54: 0110754653
:do {
    /tool user-manager user add customer="admin" username="0110754653" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0110754653";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0110754653";
};

# المستخدم 55: 0165837914
:do {
    /tool user-manager user add customer="admin" username="0165837914" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0165837914";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0165837914";
};

# المستخدم 56: 0185250295
:do {
    /tool user-manager user add customer="admin" username="0185250295" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0185250295";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0185250295";
};

# المستخدم 57: 0153239132
:do {
    /tool user-manager user add customer="admin" username="0153239132" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0153239132";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0153239132";
};

# المستخدم 58: 0122781740
:do {
    /tool user-manager user add customer="admin" username="0122781740" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0122781740";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0122781740";
};

# المستخدم 59: 0184590847
:do {
    /tool user-manager user add customer="admin" username="0184590847" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0184590847";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0184590847";
};

# المستخدم 60: 0103895761
:do {
    /tool user-manager user add customer="admin" username="0103895761" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0103895761";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0103895761";
};

# المستخدم 61: 0100170758
:do {
    /tool user-manager user add customer="admin" username="0100170758" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0100170758";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0100170758";
};

# المستخدم 62: 0104709213
:do {
    /tool user-manager user add customer="admin" username="0104709213" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0104709213";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0104709213";
};

# المستخدم 63: 0134653220
:do {
    /tool user-manager user add customer="admin" username="0134653220" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0134653220";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0134653220";
};

# المستخدم 64: 0132220078
:do {
    /tool user-manager user add customer="admin" username="0132220078" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0132220078";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0132220078";
};

# المستخدم 65: 0183249399
:do {
    /tool user-manager user add customer="admin" username="0183249399" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0183249399";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0183249399";
};

# المستخدم 66: 0169124169
:do {
    /tool user-manager user add customer="admin" username="0169124169" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0169124169";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0169124169";
};

# المستخدم 67: 0172806554
:do {
    /tool user-manager user add customer="admin" username="0172806554" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0172806554";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0172806554";
};

# المستخدم 68: 0155957591
:do {
    /tool user-manager user add customer="admin" username="0155957591" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0155957591";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0155957591";
};

# المستخدم 69: 0146839451
:do {
    /tool user-manager user add customer="admin" username="0146839451" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0146839451";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0146839451";
};

# المستخدم 70: 0174310257
:do {
    /tool user-manager user add customer="admin" username="0174310257" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0174310257";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0174310257";
};

# المستخدم 71: 0120641072
:do {
    /tool user-manager user add customer="admin" username="0120641072" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0120641072";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0120641072";
};

# المستخدم 72: 0119519467
:do {
    /tool user-manager user add customer="admin" username="0119519467" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0119519467";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0119519467";
};

# المستخدم 73: 0105949627
:do {
    /tool user-manager user add customer="admin" username="0105949627" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0105949627";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0105949627";
};

# المستخدم 74: 0165318268
:do {
    /tool user-manager user add customer="admin" username="0165318268" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0165318268";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0165318268";
};

# المستخدم 75: 0135818240
:do {
    /tool user-manager user add customer="admin" username="0135818240" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0135818240";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0135818240";
};

# المستخدم 76: 0188373730
:do {
    /tool user-manager user add customer="admin" username="0188373730" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0188373730";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0188373730";
};

# المستخدم 77: 0186026794
:do {
    /tool user-manager user add customer="admin" username="0186026794" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0186026794";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0186026794";
};

# المستخدم 78: 0146347284
:do {
    /tool user-manager user add customer="admin" username="0146347284" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0146347284";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0146347284";
};

# المستخدم 79: 0143487582
:do {
    /tool user-manager user add customer="admin" username="0143487582" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0143487582";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0143487582";
};

# المستخدم 80: 0192796600
:do {
    /tool user-manager user add customer="admin" username="0192796600" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0192796600";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0192796600";
};

# المستخدم 81: 0164745271
:do {
    /tool user-manager user add customer="admin" username="0164745271" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0164745271";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0164745271";
};

# المستخدم 82: 0132471913
:do {
    /tool user-manager user add customer="admin" username="0132471913" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0132471913";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0132471913";
};

# المستخدم 83: 0121695208
:do {
    /tool user-manager user add customer="admin" username="0121695208" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0121695208";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0121695208";
};

# المستخدم 84: 0146973892
:do {
    /tool user-manager user add customer="admin" username="0146973892" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0146973892";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0146973892";
};

# المستخدم 85: 0101613756
:do {
    /tool user-manager user add customer="admin" username="0101613756" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0101613756";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0101613756";
};

# المستخدم 86: 0157343548
:do {
    /tool user-manager user add customer="admin" username="0157343548" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0157343548";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0157343548";
};

# المستخدم 87: 0134588525
:do {
    /tool user-manager user add customer="admin" username="0134588525" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0134588525";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0134588525";
};

# المستخدم 88: 0172542723
:do {
    /tool user-manager user add customer="admin" username="0172542723" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0172542723";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0172542723";
};

# المستخدم 89: 0134341280
:do {
    /tool user-manager user add customer="admin" username="0134341280" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0134341280";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0134341280";
};

# المستخدم 90: 0112000067
:do {
    /tool user-manager user add customer="admin" username="0112000067" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0112000067";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0112000067";
};

# المستخدم 91: 0118741909
:do {
    /tool user-manager user add customer="admin" username="0118741909" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0118741909";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0118741909";
};

# المستخدم 92: 0163840777
:do {
    /tool user-manager user add customer="admin" username="0163840777" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0163840777";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0163840777";
};

# المستخدم 93: 0142121045
:do {
    /tool user-manager user add customer="admin" username="0142121045" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0142121045";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0142121045";
};

# المستخدم 94: 0122943417
:do {
    /tool user-manager user add customer="admin" username="0122943417" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0122943417";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0122943417";
};

# المستخدم 95: 0141224349
:do {
    /tool user-manager user add customer="admin" username="0141224349" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0141224349";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0141224349";
};

# المستخدم 96: 0138170210
:do {
    /tool user-manager user add customer="admin" username="0138170210" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0138170210";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0138170210";
};

# المستخدم 97: 0123039476
:do {
    /tool user-manager user add customer="admin" username="0123039476" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0123039476";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0123039476";
};

# المستخدم 98: 0185382420
:do {
    /tool user-manager user add customer="admin" username="0185382420" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0185382420";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0185382420";
};

# المستخدم 99: 0195337368
:do {
    /tool user-manager user add customer="admin" username="0195337368" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0195337368";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0195337368";
};

# المستخدم 100: 0133364642
:do {
    /tool user-manager user add customer="admin" username="0133364642" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0133364642";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0133364642";
};

# المستخدم 101: 0145213693
:do {
    /tool user-manager user add customer="admin" username="0145213693" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0145213693";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0145213693";
};

# المستخدم 102: 0190717646
:do {
    /tool user-manager user add customer="admin" username="0190717646" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0190717646";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0190717646";
};

# المستخدم 103: 0146895808
:do {
    /tool user-manager user add customer="admin" username="0146895808" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0146895808";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0146895808";
};

# المستخدم 104: 0101336045
:do {
    /tool user-manager user add customer="admin" username="0101336045" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0101336045";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0101336045";
};

# المستخدم 105: 0141786717
:do {
    /tool user-manager user add customer="admin" username="0141786717" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0141786717";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0141786717";
};

# المستخدم 106: 0100452297
:do {
    /tool user-manager user add customer="admin" username="0100452297" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0100452297";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0100452297";
};

# المستخدم 107: 0104132022
:do {
    /tool user-manager user add customer="admin" username="0104132022" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0104132022";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0104132022";
};

# المستخدم 108: 0154225256
:do {
    /tool user-manager user add customer="admin" username="0154225256" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0154225256";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0154225256";
};

# المستخدم 109: 0127569937
:do {
    /tool user-manager user add customer="admin" username="0127569937" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0127569937";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0127569937";
};

# المستخدم 110: 0178817811
:do {
    /tool user-manager user add customer="admin" username="0178817811" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0178817811";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0178817811";
};

# المستخدم 111: 0138882152
:do {
    /tool user-manager user add customer="admin" username="0138882152" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0138882152";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0138882152";
};

# المستخدم 112: 0116415006
:do {
    /tool user-manager user add customer="admin" username="0116415006" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0116415006";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0116415006";
};

# المستخدم 113: 0162823777
:do {
    /tool user-manager user add customer="admin" username="0162823777" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0162823777";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0162823777";
};

# المستخدم 114: 0170750705
:do {
    /tool user-manager user add customer="admin" username="0170750705" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0170750705";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0170750705";
};

# المستخدم 115: 0134097555
:do {
    /tool user-manager user add customer="admin" username="0134097555" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0134097555";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0134097555";
};

# المستخدم 116: 0189458863
:do {
    /tool user-manager user add customer="admin" username="0189458863" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0189458863";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0189458863";
};

# المستخدم 117: 0178821032
:do {
    /tool user-manager user add customer="admin" username="0178821032" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0178821032";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0178821032";
};

# المستخدم 118: 0154667726
:do {
    /tool user-manager user add customer="admin" username="0154667726" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0154667726";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0154667726";
};

# المستخدم 119: 0192997129
:do {
    /tool user-manager user add customer="admin" username="0192997129" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0192997129";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0192997129";
};

# المستخدم 120: 0139989703
:do {
    /tool user-manager user add customer="admin" username="0139989703" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0139989703";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0139989703";
};

# المستخدم 121: 0126436595
:do {
    /tool user-manager user add customer="admin" username="0126436595" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0126436595";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0126436595";
};

# المستخدم 122: 0126860153
:do {
    /tool user-manager user add customer="admin" username="0126860153" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0126860153";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0126860153";
};

# المستخدم 123: 0152146504
:do {
    /tool user-manager user add customer="admin" username="0152146504" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0152146504";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0152146504";
};

# المستخدم 124: 0181721617
:do {
    /tool user-manager user add customer="admin" username="0181721617" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0181721617";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0181721617";
};

# المستخدم 125: 0174479295
:do {
    /tool user-manager user add customer="admin" username="0174479295" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0174479295";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0174479295";
};

# المستخدم 126: 0125821261
:do {
    /tool user-manager user add customer="admin" username="0125821261" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0125821261";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0125821261";
};

# المستخدم 127: 0185020101
:do {
    /tool user-manager user add customer="admin" username="0185020101" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0185020101";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0185020101";
};

# المستخدم 128: 0160130477
:do {
    /tool user-manager user add customer="admin" username="0160130477" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0160130477";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0160130477";
};

# المستخدم 129: 0187073271
:do {
    /tool user-manager user add customer="admin" username="0187073271" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0187073271";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0187073271";
};

# المستخدم 130: 0172063312
:do {
    /tool user-manager user add customer="admin" username="0172063312" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0172063312";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0172063312";
};

# المستخدم 131: 0187101466
:do {
    /tool user-manager user add customer="admin" username="0187101466" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0187101466";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0187101466";
};

# المستخدم 132: 0128735555
:do {
    /tool user-manager user add customer="admin" username="0128735555" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0128735555";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0128735555";
};

# المستخدم 133: 0156803162
:do {
    /tool user-manager user add customer="admin" username="0156803162" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0156803162";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0156803162";
};

# المستخدم 134: 0118804614
:do {
    /tool user-manager user add customer="admin" username="0118804614" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0118804614";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0118804614";
};

# المستخدم 135: 0151707089
:do {
    /tool user-manager user add customer="admin" username="0151707089" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0151707089";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0151707089";
};

# المستخدم 136: 0159652881
:do {
    /tool user-manager user add customer="admin" username="0159652881" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0159652881";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0159652881";
};

# المستخدم 137: 0180562520
:do {
    /tool user-manager user add customer="admin" username="0180562520" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0180562520";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0180562520";
};

# المستخدم 138: 0178921297
:do {
    /tool user-manager user add customer="admin" username="0178921297" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0178921297";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0178921297";
};

# المستخدم 139: 0171218730
:do {
    /tool user-manager user add customer="admin" username="0171218730" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0171218730";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0171218730";
};

# المستخدم 140: 0174368883
:do {
    /tool user-manager user add customer="admin" username="0174368883" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0174368883";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0174368883";
};

# المستخدم 141: 0125087948
:do {
    /tool user-manager user add customer="admin" username="0125087948" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0125087948";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0125087948";
};

# المستخدم 142: 0167592619
:do {
    /tool user-manager user add customer="admin" username="0167592619" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0167592619";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0167592619";
};

# المستخدم 143: 0121546101
:do {
    /tool user-manager user add customer="admin" username="0121546101" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0121546101";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0121546101";
};

# المستخدم 144: 0172374285
:do {
    /tool user-manager user add customer="admin" username="0172374285" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0172374285";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0172374285";
};

# المستخدم 145: 0172103975
:do {
    /tool user-manager user add customer="admin" username="0172103975" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0172103975";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0172103975";
};

# المستخدم 146: 0107118414
:do {
    /tool user-manager user add customer="admin" username="0107118414" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0107118414";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0107118414";
};

# المستخدم 147: 0156600632
:do {
    /tool user-manager user add customer="admin" username="0156600632" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0156600632";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0156600632";
};

# المستخدم 148: 0127966102
:do {
    /tool user-manager user add customer="admin" username="0127966102" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0127966102";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0127966102";
};

# المستخدم 149: 0111190076
:do {
    /tool user-manager user add customer="admin" username="0111190076" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0111190076";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0111190076";
};

# المستخدم 150: 0174725513
:do {
    /tool user-manager user add customer="admin" username="0174725513" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0174725513";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0174725513";
};

# المستخدم 151: 0144928533
:do {
    /tool user-manager user add customer="admin" username="0144928533" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0144928533";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0144928533";
};

# المستخدم 152: 0141261121
:do {
    /tool user-manager user add customer="admin" username="0141261121" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0141261121";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0141261121";
};

# المستخدم 153: 0144939956
:do {
    /tool user-manager user add customer="admin" username="0144939956" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0144939956";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0144939956";
};

# المستخدم 154: 0123004507
:do {
    /tool user-manager user add customer="admin" username="0123004507" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0123004507";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0123004507";
};

# المستخدم 155: 0168828681
:do {
    /tool user-manager user add customer="admin" username="0168828681" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0168828681";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0168828681";
};

# المستخدم 156: 0126525303
:do {
    /tool user-manager user add customer="admin" username="0126525303" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0126525303";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0126525303";
};

# المستخدم 157: 0108150161
:do {
    /tool user-manager user add customer="admin" username="0108150161" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0108150161";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0108150161";
};

# المستخدم 158: 0139393113
:do {
    /tool user-manager user add customer="admin" username="0139393113" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0139393113";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0139393113";
};

# المستخدم 159: 0131040412
:do {
    /tool user-manager user add customer="admin" username="0131040412" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0131040412";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0131040412";
};

# المستخدم 160: 0180068307
:do {
    /tool user-manager user add customer="admin" username="0180068307" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0180068307";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0180068307";
};

# المستخدم 161: 0158246153
:do {
    /tool user-manager user add customer="admin" username="0158246153" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0158246153";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0158246153";
};

# المستخدم 162: 0157741676
:do {
    /tool user-manager user add customer="admin" username="0157741676" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0157741676";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0157741676";
};

# المستخدم 163: 0185475255
:do {
    /tool user-manager user add customer="admin" username="0185475255" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0185475255";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0185475255";
};

# المستخدم 164: 0196624735
:do {
    /tool user-manager user add customer="admin" username="0196624735" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0196624735";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0196624735";
};

# المستخدم 165: 0109976942
:do {
    /tool user-manager user add customer="admin" username="0109976942" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0109976942";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0109976942";
};

# المستخدم 166: 0108700172
:do {
    /tool user-manager user add customer="admin" username="0108700172" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0108700172";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0108700172";
};

# المستخدم 167: 0132617619
:do {
    /tool user-manager user add customer="admin" username="0132617619" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0132617619";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0132617619";
};

# المستخدم 168: 0122400507
:do {
    /tool user-manager user add customer="admin" username="0122400507" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0122400507";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0122400507";
};

# المستخدم 169: 0198855912
:do {
    /tool user-manager user add customer="admin" username="0198855912" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0198855912";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0198855912";
};

# المستخدم 170: 0199899909
:do {
    /tool user-manager user add customer="admin" username="0199899909" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0199899909";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0199899909";
};

# المستخدم 171: 0144002436
:do {
    /tool user-manager user add customer="admin" username="0144002436" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0144002436";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0144002436";
};

# المستخدم 172: 0184613770
:do {
    /tool user-manager user add customer="admin" username="0184613770" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0184613770";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0184613770";
};

# المستخدم 173: 0175920186
:do {
    /tool user-manager user add customer="admin" username="0175920186" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0175920186";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0175920186";
};

# المستخدم 174: 0115039118
:do {
    /tool user-manager user add customer="admin" username="0115039118" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0115039118";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0115039118";
};

# المستخدم 175: 0169741411
:do {
    /tool user-manager user add customer="admin" username="0169741411" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0169741411";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0169741411";
};

# المستخدم 176: 0190269377
:do {
    /tool user-manager user add customer="admin" username="0190269377" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0190269377";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0190269377";
};

# المستخدم 177: 0170844772
:do {
    /tool user-manager user add customer="admin" username="0170844772" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0170844772";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0170844772";
};

# المستخدم 178: 0123863537
:do {
    /tool user-manager user add customer="admin" username="0123863537" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0123863537";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0123863537";
};

# المستخدم 179: 0190136000
:do {
    /tool user-manager user add customer="admin" username="0190136000" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0190136000";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0190136000";
};

# المستخدم 180: 0114234675
:do {
    /tool user-manager user add customer="admin" username="0114234675" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0114234675";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0114234675";
};

# المستخدم 181: 0155585600
:do {
    /tool user-manager user add customer="admin" username="0155585600" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0155585600";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0155585600";
};

# المستخدم 182: 0159096303
:do {
    /tool user-manager user add customer="admin" username="0159096303" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0159096303";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0159096303";
};

# المستخدم 183: 0131511240
:do {
    /tool user-manager user add customer="admin" username="0131511240" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0131511240";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0131511240";
};

# المستخدم 184: 0170009573
:do {
    /tool user-manager user add customer="admin" username="0170009573" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0170009573";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0170009573";
};

# المستخدم 185: 0151388736
:do {
    /tool user-manager user add customer="admin" username="0151388736" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0151388736";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0151388736";
};

# المستخدم 186: 0156757957
:do {
    /tool user-manager user add customer="admin" username="0156757957" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0156757957";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0156757957";
};

# المستخدم 187: 0199346663
:do {
    /tool user-manager user add customer="admin" username="0199346663" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0199346663";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0199346663";
};

# المستخدم 188: 0193685452
:do {
    /tool user-manager user add customer="admin" username="0193685452" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0193685452";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0193685452";
};

# المستخدم 189: 0170747435
:do {
    /tool user-manager user add customer="admin" username="0170747435" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0170747435";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0170747435";
};

# المستخدم 190: 0141092765
:do {
    /tool user-manager user add customer="admin" username="0141092765" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0141092765";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0141092765";
};

# المستخدم 191: 0122319217
:do {
    /tool user-manager user add customer="admin" username="0122319217" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0122319217";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0122319217";
};

# المستخدم 192: 0127516319
:do {
    /tool user-manager user add customer="admin" username="0127516319" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0127516319";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0127516319";
};

# المستخدم 193: 0146244605
:do {
    /tool user-manager user add customer="admin" username="0146244605" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0146244605";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0146244605";
};

# المستخدم 194: 0137692035
:do {
    /tool user-manager user add customer="admin" username="0137692035" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0137692035";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0137692035";
};

# المستخدم 195: 0151670952
:do {
    /tool user-manager user add customer="admin" username="0151670952" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0151670952";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0151670952";
};

# المستخدم 196: 0169189164
:do {
    /tool user-manager user add customer="admin" username="0169189164" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0169189164";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0169189164";
};

# المستخدم 197: 0117067239
:do {
    /tool user-manager user add customer="admin" username="0117067239" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0117067239";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0117067239";
};

# المستخدم 198: 0123537646
:do {
    /tool user-manager user add customer="admin" username="0123537646" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0123537646";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0123537646";
};

# المستخدم 199: 0142285416
:do {
    /tool user-manager user add customer="admin" username="0142285416" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0142285416";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0142285416";
};

# المستخدم 200: 0164473430
:do {
    /tool user-manager user add customer="admin" username="0164473430" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0164473430";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0164473430";
};


:put "📊 تم الانتهاء من النسخة الاحتياطية";
:put "✅ نجح: $success كرت";
:put "❌ فشل: $errors كرت";
:put "📈 الإجمالي: $total كرت";

:put "🎉 تم استعادة جميع كروت القالب: 10";
:put "💡 النسخة الاحتياطية مكتملة بنجاح!";
