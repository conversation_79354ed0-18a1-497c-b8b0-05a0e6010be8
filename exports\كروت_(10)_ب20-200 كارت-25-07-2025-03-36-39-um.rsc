# MikroTik Script - نسخة احتياطية للكروت
# تم الإنشاء: 2025-07-25 03:36:39
# القالب: 10
# النظام: user_manager
# عدد الكروت: 200
# تم إنشاؤه بواسطة: مولد كروت MikroTik

:put "🚀 بدء تنفيذ النسخة الاحتياطية للكروت";
:put "📋 القالب: 10";
:put "🎯 النظام: user_manager";
:put "📊 عدد الكروت: 200";

:local success 0;
:local errors 0;
:local total 200;

:put "⏳ بدء إضافة الكروت...";

# إضافة مستخدمي User Manager
:put "👤 إضافة 200 مستخدم User Manager...";

# المستخدم 1: 0123416560
:do {
    /tool user-manager user add customer="admin" username="0123416560" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0123416560";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0123416560";
};

# المستخدم 2: 0122023872
:do {
    /tool user-manager user add customer="admin" username="0122023872" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0122023872";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0122023872";
};

# المستخدم 3: 0164251399
:do {
    /tool user-manager user add customer="admin" username="0164251399" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0164251399";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0164251399";
};

# المستخدم 4: 0181368224
:do {
    /tool user-manager user add customer="admin" username="0181368224" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0181368224";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0181368224";
};

# المستخدم 5: 0158589650
:do {
    /tool user-manager user add customer="admin" username="0158589650" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0158589650";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0158589650";
};

# المستخدم 6: 0188694620
:do {
    /tool user-manager user add customer="admin" username="0188694620" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0188694620";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0188694620";
};

# المستخدم 7: 0138792323
:do {
    /tool user-manager user add customer="admin" username="0138792323" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0138792323";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0138792323";
};

# المستخدم 8: 0180440804
:do {
    /tool user-manager user add customer="admin" username="0180440804" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0180440804";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0180440804";
};

# المستخدم 9: 0197894789
:do {
    /tool user-manager user add customer="admin" username="0197894789" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0197894789";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0197894789";
};

# المستخدم 10: 0151820636
:do {
    /tool user-manager user add customer="admin" username="0151820636" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0151820636";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0151820636";
};

# المستخدم 11: 0131412521
:do {
    /tool user-manager user add customer="admin" username="0131412521" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0131412521";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0131412521";
};

# المستخدم 12: 0137455519
:do {
    /tool user-manager user add customer="admin" username="0137455519" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0137455519";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0137455519";
};

# المستخدم 13: 0169980446
:do {
    /tool user-manager user add customer="admin" username="0169980446" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0169980446";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0169980446";
};

# المستخدم 14: 0174650765
:do {
    /tool user-manager user add customer="admin" username="0174650765" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0174650765";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0174650765";
};

# المستخدم 15: 0182191370
:do {
    /tool user-manager user add customer="admin" username="0182191370" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0182191370";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0182191370";
};

# المستخدم 16: 0152343840
:do {
    /tool user-manager user add customer="admin" username="0152343840" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0152343840";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0152343840";
};

# المستخدم 17: 0160268350
:do {
    /tool user-manager user add customer="admin" username="0160268350" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0160268350";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0160268350";
};

# المستخدم 18: 0143691159
:do {
    /tool user-manager user add customer="admin" username="0143691159" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0143691159";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0143691159";
};

# المستخدم 19: 0145080083
:do {
    /tool user-manager user add customer="admin" username="0145080083" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0145080083";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0145080083";
};

# المستخدم 20: 0168024175
:do {
    /tool user-manager user add customer="admin" username="0168024175" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0168024175";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0168024175";
};

# المستخدم 21: 0138411730
:do {
    /tool user-manager user add customer="admin" username="0138411730" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0138411730";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0138411730";
};

# المستخدم 22: 0138051634
:do {
    /tool user-manager user add customer="admin" username="0138051634" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0138051634";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0138051634";
};

# المستخدم 23: 0149386552
:do {
    /tool user-manager user add customer="admin" username="0149386552" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0149386552";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0149386552";
};

# المستخدم 24: 0196394606
:do {
    /tool user-manager user add customer="admin" username="0196394606" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0196394606";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0196394606";
};

# المستخدم 25: 0112024321
:do {
    /tool user-manager user add customer="admin" username="0112024321" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0112024321";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0112024321";
};

# المستخدم 26: 0152088073
:do {
    /tool user-manager user add customer="admin" username="0152088073" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0152088073";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0152088073";
};

# المستخدم 27: 0169417517
:do {
    /tool user-manager user add customer="admin" username="0169417517" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0169417517";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0169417517";
};

# المستخدم 28: 0156755753
:do {
    /tool user-manager user add customer="admin" username="0156755753" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0156755753";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0156755753";
};

# المستخدم 29: 0197919404
:do {
    /tool user-manager user add customer="admin" username="0197919404" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0197919404";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0197919404";
};

# المستخدم 30: 0196729758
:do {
    /tool user-manager user add customer="admin" username="0196729758" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0196729758";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0196729758";
};

# المستخدم 31: 0138454629
:do {
    /tool user-manager user add customer="admin" username="0138454629" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0138454629";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0138454629";
};

# المستخدم 32: 0139595134
:do {
    /tool user-manager user add customer="admin" username="0139595134" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0139595134";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0139595134";
};

# المستخدم 33: 0167287505
:do {
    /tool user-manager user add customer="admin" username="0167287505" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0167287505";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0167287505";
};

# المستخدم 34: 0132346470
:do {
    /tool user-manager user add customer="admin" username="0132346470" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0132346470";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0132346470";
};

# المستخدم 35: 0147920348
:do {
    /tool user-manager user add customer="admin" username="0147920348" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0147920348";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0147920348";
};

# المستخدم 36: 0127581960
:do {
    /tool user-manager user add customer="admin" username="0127581960" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0127581960";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0127581960";
};

# المستخدم 37: 0169457491
:do {
    /tool user-manager user add customer="admin" username="0169457491" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0169457491";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0169457491";
};

# المستخدم 38: 0126610848
:do {
    /tool user-manager user add customer="admin" username="0126610848" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0126610848";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0126610848";
};

# المستخدم 39: 0198533679
:do {
    /tool user-manager user add customer="admin" username="0198533679" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0198533679";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0198533679";
};

# المستخدم 40: 0124784447
:do {
    /tool user-manager user add customer="admin" username="0124784447" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0124784447";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0124784447";
};

# المستخدم 41: 0163212173
:do {
    /tool user-manager user add customer="admin" username="0163212173" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0163212173";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0163212173";
};

# المستخدم 42: 0108379938
:do {
    /tool user-manager user add customer="admin" username="0108379938" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0108379938";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0108379938";
};

# المستخدم 43: 0166421456
:do {
    /tool user-manager user add customer="admin" username="0166421456" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0166421456";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0166421456";
};

# المستخدم 44: 0147486940
:do {
    /tool user-manager user add customer="admin" username="0147486940" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0147486940";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0147486940";
};

# المستخدم 45: 0189670370
:do {
    /tool user-manager user add customer="admin" username="0189670370" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0189670370";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0189670370";
};

# المستخدم 46: 0188209801
:do {
    /tool user-manager user add customer="admin" username="0188209801" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0188209801";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0188209801";
};

# المستخدم 47: 0165387077
:do {
    /tool user-manager user add customer="admin" username="0165387077" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0165387077";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0165387077";
};

# المستخدم 48: 0127953308
:do {
    /tool user-manager user add customer="admin" username="0127953308" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0127953308";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0127953308";
};

# المستخدم 49: 0103205298
:do {
    /tool user-manager user add customer="admin" username="0103205298" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0103205298";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0103205298";
};

# المستخدم 50: 0178150413
:do {
    /tool user-manager user add customer="admin" username="0178150413" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0178150413";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0178150413";
};

# المستخدم 51: 0126814094
:do {
    /tool user-manager user add customer="admin" username="0126814094" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0126814094";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0126814094";
};

# المستخدم 52: 0130933151
:do {
    /tool user-manager user add customer="admin" username="0130933151" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0130933151";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0130933151";
};

# المستخدم 53: 0151787854
:do {
    /tool user-manager user add customer="admin" username="0151787854" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0151787854";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0151787854";
};

# المستخدم 54: 0186668902
:do {
    /tool user-manager user add customer="admin" username="0186668902" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0186668902";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0186668902";
};

# المستخدم 55: 0147102701
:do {
    /tool user-manager user add customer="admin" username="0147102701" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0147102701";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0147102701";
};

# المستخدم 56: 0153096278
:do {
    /tool user-manager user add customer="admin" username="0153096278" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0153096278";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0153096278";
};

# المستخدم 57: 0126962740
:do {
    /tool user-manager user add customer="admin" username="0126962740" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0126962740";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0126962740";
};

# المستخدم 58: 0113339769
:do {
    /tool user-manager user add customer="admin" username="0113339769" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0113339769";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0113339769";
};

# المستخدم 59: 0168947011
:do {
    /tool user-manager user add customer="admin" username="0168947011" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0168947011";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0168947011";
};

# المستخدم 60: 0128874726
:do {
    /tool user-manager user add customer="admin" username="0128874726" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0128874726";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0128874726";
};

# المستخدم 61: 0137783912
:do {
    /tool user-manager user add customer="admin" username="0137783912" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0137783912";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0137783912";
};

# المستخدم 62: 0146740485
:do {
    /tool user-manager user add customer="admin" username="0146740485" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0146740485";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0146740485";
};

# المستخدم 63: 0161022660
:do {
    /tool user-manager user add customer="admin" username="0161022660" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0161022660";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0161022660";
};

# المستخدم 64: 0167379745
:do {
    /tool user-manager user add customer="admin" username="0167379745" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0167379745";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0167379745";
};

# المستخدم 65: 0143056459
:do {
    /tool user-manager user add customer="admin" username="0143056459" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0143056459";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0143056459";
};

# المستخدم 66: 0134357599
:do {
    /tool user-manager user add customer="admin" username="0134357599" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0134357599";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0134357599";
};

# المستخدم 67: 0194570326
:do {
    /tool user-manager user add customer="admin" username="0194570326" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0194570326";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0194570326";
};

# المستخدم 68: 0135434554
:do {
    /tool user-manager user add customer="admin" username="0135434554" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0135434554";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0135434554";
};

# المستخدم 69: 0185974528
:do {
    /tool user-manager user add customer="admin" username="0185974528" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0185974528";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0185974528";
};

# المستخدم 70: 0112080699
:do {
    /tool user-manager user add customer="admin" username="0112080699" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0112080699";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0112080699";
};

# المستخدم 71: 0182285273
:do {
    /tool user-manager user add customer="admin" username="0182285273" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0182285273";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0182285273";
};

# المستخدم 72: 0104535662
:do {
    /tool user-manager user add customer="admin" username="0104535662" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0104535662";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0104535662";
};

# المستخدم 73: 0199549458
:do {
    /tool user-manager user add customer="admin" username="0199549458" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0199549458";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0199549458";
};

# المستخدم 74: 0199583656
:do {
    /tool user-manager user add customer="admin" username="0199583656" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0199583656";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0199583656";
};

# المستخدم 75: 0115343888
:do {
    /tool user-manager user add customer="admin" username="0115343888" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0115343888";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0115343888";
};

# المستخدم 76: 0171563253
:do {
    /tool user-manager user add customer="admin" username="0171563253" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0171563253";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0171563253";
};

# المستخدم 77: 0128996962
:do {
    /tool user-manager user add customer="admin" username="0128996962" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0128996962";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0128996962";
};

# المستخدم 78: 0154390014
:do {
    /tool user-manager user add customer="admin" username="0154390014" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0154390014";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0154390014";
};

# المستخدم 79: 0180504085
:do {
    /tool user-manager user add customer="admin" username="0180504085" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0180504085";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0180504085";
};

# المستخدم 80: 0171528932
:do {
    /tool user-manager user add customer="admin" username="0171528932" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0171528932";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0171528932";
};

# المستخدم 81: 0120566663
:do {
    /tool user-manager user add customer="admin" username="0120566663" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0120566663";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0120566663";
};

# المستخدم 82: 0109015141
:do {
    /tool user-manager user add customer="admin" username="0109015141" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0109015141";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0109015141";
};

# المستخدم 83: 0153263261
:do {
    /tool user-manager user add customer="admin" username="0153263261" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0153263261";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0153263261";
};

# المستخدم 84: 0153292319
:do {
    /tool user-manager user add customer="admin" username="0153292319" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0153292319";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0153292319";
};

# المستخدم 85: 0198251597
:do {
    /tool user-manager user add customer="admin" username="0198251597" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0198251597";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0198251597";
};

# المستخدم 86: 0171449889
:do {
    /tool user-manager user add customer="admin" username="0171449889" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0171449889";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0171449889";
};

# المستخدم 87: 0152487385
:do {
    /tool user-manager user add customer="admin" username="0152487385" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0152487385";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0152487385";
};

# المستخدم 88: 0176059428
:do {
    /tool user-manager user add customer="admin" username="0176059428" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0176059428";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0176059428";
};

# المستخدم 89: 0160523881
:do {
    /tool user-manager user add customer="admin" username="0160523881" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0160523881";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0160523881";
};

# المستخدم 90: 0141358050
:do {
    /tool user-manager user add customer="admin" username="0141358050" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0141358050";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0141358050";
};

# المستخدم 91: 0151151658
:do {
    /tool user-manager user add customer="admin" username="0151151658" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0151151658";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0151151658";
};

# المستخدم 92: 0199919741
:do {
    /tool user-manager user add customer="admin" username="0199919741" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0199919741";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0199919741";
};

# المستخدم 93: 0128354479
:do {
    /tool user-manager user add customer="admin" username="0128354479" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0128354479";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0128354479";
};

# المستخدم 94: 0178505367
:do {
    /tool user-manager user add customer="admin" username="0178505367" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0178505367";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0178505367";
};

# المستخدم 95: 0133881690
:do {
    /tool user-manager user add customer="admin" username="0133881690" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0133881690";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0133881690";
};

# المستخدم 96: 0192871447
:do {
    /tool user-manager user add customer="admin" username="0192871447" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0192871447";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0192871447";
};

# المستخدم 97: 0152763449
:do {
    /tool user-manager user add customer="admin" username="0152763449" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0152763449";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0152763449";
};

# المستخدم 98: 0160277997
:do {
    /tool user-manager user add customer="admin" username="0160277997" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0160277997";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0160277997";
};

# المستخدم 99: 0182931367
:do {
    /tool user-manager user add customer="admin" username="0182931367" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0182931367";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0182931367";
};

# المستخدم 100: 0176038288
:do {
    /tool user-manager user add customer="admin" username="0176038288" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0176038288";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0176038288";
};

# المستخدم 101: 0120388781
:do {
    /tool user-manager user add customer="admin" username="0120388781" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0120388781";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0120388781";
};

# المستخدم 102: 0169052243
:do {
    /tool user-manager user add customer="admin" username="0169052243" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0169052243";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0169052243";
};

# المستخدم 103: 0152475071
:do {
    /tool user-manager user add customer="admin" username="0152475071" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0152475071";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0152475071";
};

# المستخدم 104: 0197897590
:do {
    /tool user-manager user add customer="admin" username="0197897590" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0197897590";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0197897590";
};

# المستخدم 105: 0137540192
:do {
    /tool user-manager user add customer="admin" username="0137540192" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0137540192";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0137540192";
};

# المستخدم 106: 0162019287
:do {
    /tool user-manager user add customer="admin" username="0162019287" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0162019287";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0162019287";
};

# المستخدم 107: 0127838839
:do {
    /tool user-manager user add customer="admin" username="0127838839" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0127838839";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0127838839";
};

# المستخدم 108: 0158480036
:do {
    /tool user-manager user add customer="admin" username="0158480036" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0158480036";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0158480036";
};

# المستخدم 109: 0153491549
:do {
    /tool user-manager user add customer="admin" username="0153491549" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0153491549";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0153491549";
};

# المستخدم 110: 0104483594
:do {
    /tool user-manager user add customer="admin" username="0104483594" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0104483594";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0104483594";
};

# المستخدم 111: 0119753980
:do {
    /tool user-manager user add customer="admin" username="0119753980" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0119753980";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0119753980";
};

# المستخدم 112: 0153439711
:do {
    /tool user-manager user add customer="admin" username="0153439711" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0153439711";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0153439711";
};

# المستخدم 113: 0182932222
:do {
    /tool user-manager user add customer="admin" username="0182932222" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0182932222";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0182932222";
};

# المستخدم 114: 0189934155
:do {
    /tool user-manager user add customer="admin" username="0189934155" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0189934155";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0189934155";
};

# المستخدم 115: 0196926019
:do {
    /tool user-manager user add customer="admin" username="0196926019" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0196926019";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0196926019";
};

# المستخدم 116: 0181554787
:do {
    /tool user-manager user add customer="admin" username="0181554787" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0181554787";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0181554787";
};

# المستخدم 117: 0161801283
:do {
    /tool user-manager user add customer="admin" username="0161801283" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0161801283";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0161801283";
};

# المستخدم 118: 0164514346
:do {
    /tool user-manager user add customer="admin" username="0164514346" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0164514346";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0164514346";
};

# المستخدم 119: 0125748683
:do {
    /tool user-manager user add customer="admin" username="0125748683" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0125748683";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0125748683";
};

# المستخدم 120: 0120535299
:do {
    /tool user-manager user add customer="admin" username="0120535299" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0120535299";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0120535299";
};

# المستخدم 121: 0191264472
:do {
    /tool user-manager user add customer="admin" username="0191264472" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0191264472";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0191264472";
};

# المستخدم 122: 0169448658
:do {
    /tool user-manager user add customer="admin" username="0169448658" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0169448658";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0169448658";
};

# المستخدم 123: 0115787088
:do {
    /tool user-manager user add customer="admin" username="0115787088" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0115787088";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0115787088";
};

# المستخدم 124: 0142092644
:do {
    /tool user-manager user add customer="admin" username="0142092644" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0142092644";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0142092644";
};

# المستخدم 125: 0115724353
:do {
    /tool user-manager user add customer="admin" username="0115724353" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0115724353";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0115724353";
};

# المستخدم 126: 0141009828
:do {
    /tool user-manager user add customer="admin" username="0141009828" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0141009828";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0141009828";
};

# المستخدم 127: 0111486532
:do {
    /tool user-manager user add customer="admin" username="0111486532" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0111486532";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0111486532";
};

# المستخدم 128: 0199713684
:do {
    /tool user-manager user add customer="admin" username="0199713684" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0199713684";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0199713684";
};

# المستخدم 129: 0137455462
:do {
    /tool user-manager user add customer="admin" username="0137455462" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0137455462";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0137455462";
};

# المستخدم 130: 0146399986
:do {
    /tool user-manager user add customer="admin" username="0146399986" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0146399986";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0146399986";
};

# المستخدم 131: 0122090066
:do {
    /tool user-manager user add customer="admin" username="0122090066" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0122090066";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0122090066";
};

# المستخدم 132: 0177790685
:do {
    /tool user-manager user add customer="admin" username="0177790685" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0177790685";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0177790685";
};

# المستخدم 133: 0165796050
:do {
    /tool user-manager user add customer="admin" username="0165796050" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0165796050";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0165796050";
};

# المستخدم 134: 0176998180
:do {
    /tool user-manager user add customer="admin" username="0176998180" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0176998180";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0176998180";
};

# المستخدم 135: 0161642913
:do {
    /tool user-manager user add customer="admin" username="0161642913" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0161642913";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0161642913";
};

# المستخدم 136: 0185529647
:do {
    /tool user-manager user add customer="admin" username="0185529647" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0185529647";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0185529647";
};

# المستخدم 137: 0156935352
:do {
    /tool user-manager user add customer="admin" username="0156935352" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0156935352";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0156935352";
};

# المستخدم 138: 0141706816
:do {
    /tool user-manager user add customer="admin" username="0141706816" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0141706816";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0141706816";
};

# المستخدم 139: 0113262761
:do {
    /tool user-manager user add customer="admin" username="0113262761" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0113262761";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0113262761";
};

# المستخدم 140: 0182010138
:do {
    /tool user-manager user add customer="admin" username="0182010138" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0182010138";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0182010138";
};

# المستخدم 141: 0150571551
:do {
    /tool user-manager user add customer="admin" username="0150571551" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0150571551";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0150571551";
};

# المستخدم 142: 0162604222
:do {
    /tool user-manager user add customer="admin" username="0162604222" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0162604222";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0162604222";
};

# المستخدم 143: 0106639828
:do {
    /tool user-manager user add customer="admin" username="0106639828" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0106639828";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0106639828";
};

# المستخدم 144: 0199095839
:do {
    /tool user-manager user add customer="admin" username="0199095839" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0199095839";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0199095839";
};

# المستخدم 145: 0197346375
:do {
    /tool user-manager user add customer="admin" username="0197346375" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0197346375";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0197346375";
};

# المستخدم 146: 0101628388
:do {
    /tool user-manager user add customer="admin" username="0101628388" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0101628388";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0101628388";
};

# المستخدم 147: 0171525652
:do {
    /tool user-manager user add customer="admin" username="0171525652" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0171525652";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0171525652";
};

# المستخدم 148: 0127501136
:do {
    /tool user-manager user add customer="admin" username="0127501136" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0127501136";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0127501136";
};

# المستخدم 149: 0176878420
:do {
    /tool user-manager user add customer="admin" username="0176878420" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0176878420";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0176878420";
};

# المستخدم 150: 0112210760
:do {
    /tool user-manager user add customer="admin" username="0112210760" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0112210760";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0112210760";
};

# المستخدم 151: 0195936967
:do {
    /tool user-manager user add customer="admin" username="0195936967" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0195936967";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0195936967";
};

# المستخدم 152: 0180994639
:do {
    /tool user-manager user add customer="admin" username="0180994639" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0180994639";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0180994639";
};

# المستخدم 153: 0175729223
:do {
    /tool user-manager user add customer="admin" username="0175729223" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0175729223";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0175729223";
};

# المستخدم 154: 0187612790
:do {
    /tool user-manager user add customer="admin" username="0187612790" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0187612790";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0187612790";
};

# المستخدم 155: 0136022198
:do {
    /tool user-manager user add customer="admin" username="0136022198" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0136022198";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0136022198";
};

# المستخدم 156: 0184849143
:do {
    /tool user-manager user add customer="admin" username="0184849143" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0184849143";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0184849143";
};

# المستخدم 157: 0196347751
:do {
    /tool user-manager user add customer="admin" username="0196347751" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0196347751";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0196347751";
};

# المستخدم 158: 0134799153
:do {
    /tool user-manager user add customer="admin" username="0134799153" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0134799153";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0134799153";
};

# المستخدم 159: 0179907409
:do {
    /tool user-manager user add customer="admin" username="0179907409" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0179907409";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0179907409";
};

# المستخدم 160: 0199421753
:do {
    /tool user-manager user add customer="admin" username="0199421753" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0199421753";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0199421753";
};

# المستخدم 161: 0164169064
:do {
    /tool user-manager user add customer="admin" username="0164169064" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0164169064";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0164169064";
};

# المستخدم 162: 0105443289
:do {
    /tool user-manager user add customer="admin" username="0105443289" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0105443289";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0105443289";
};

# المستخدم 163: 0104570223
:do {
    /tool user-manager user add customer="admin" username="0104570223" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0104570223";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0104570223";
};

# المستخدم 164: 0117476963
:do {
    /tool user-manager user add customer="admin" username="0117476963" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0117476963";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0117476963";
};

# المستخدم 165: 0183823867
:do {
    /tool user-manager user add customer="admin" username="0183823867" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0183823867";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0183823867";
};

# المستخدم 166: 0152821558
:do {
    /tool user-manager user add customer="admin" username="0152821558" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0152821558";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0152821558";
};

# المستخدم 167: 0156510650
:do {
    /tool user-manager user add customer="admin" username="0156510650" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0156510650";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0156510650";
};

# المستخدم 168: 0114107368
:do {
    /tool user-manager user add customer="admin" username="0114107368" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0114107368";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0114107368";
};

# المستخدم 169: 0102582260
:do {
    /tool user-manager user add customer="admin" username="0102582260" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0102582260";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0102582260";
};

# المستخدم 170: 0143916928
:do {
    /tool user-manager user add customer="admin" username="0143916928" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0143916928";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0143916928";
};

# المستخدم 171: 0102476804
:do {
    /tool user-manager user add customer="admin" username="0102476804" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0102476804";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0102476804";
};

# المستخدم 172: 0157078717
:do {
    /tool user-manager user add customer="admin" username="0157078717" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0157078717";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0157078717";
};

# المستخدم 173: 0142655650
:do {
    /tool user-manager user add customer="admin" username="0142655650" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0142655650";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0142655650";
};

# المستخدم 174: 0152632768
:do {
    /tool user-manager user add customer="admin" username="0152632768" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0152632768";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0152632768";
};

# المستخدم 175: 0170518455
:do {
    /tool user-manager user add customer="admin" username="0170518455" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0170518455";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0170518455";
};

# المستخدم 176: 0163789240
:do {
    /tool user-manager user add customer="admin" username="0163789240" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0163789240";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0163789240";
};

# المستخدم 177: 0142978204
:do {
    /tool user-manager user add customer="admin" username="0142978204" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0142978204";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0142978204";
};

# المستخدم 178: 0133992449
:do {
    /tool user-manager user add customer="admin" username="0133992449" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0133992449";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0133992449";
};

# المستخدم 179: 0130638813
:do {
    /tool user-manager user add customer="admin" username="0130638813" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0130638813";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0130638813";
};

# المستخدم 180: 0123250819
:do {
    /tool user-manager user add customer="admin" username="0123250819" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0123250819";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0123250819";
};

# المستخدم 181: 0107077045
:do {
    /tool user-manager user add customer="admin" username="0107077045" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0107077045";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0107077045";
};

# المستخدم 182: 0105716574
:do {
    /tool user-manager user add customer="admin" username="0105716574" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0105716574";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0105716574";
};

# المستخدم 183: 0102595875
:do {
    /tool user-manager user add customer="admin" username="0102595875" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0102595875";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0102595875";
};

# المستخدم 184: 0176656589
:do {
    /tool user-manager user add customer="admin" username="0176656589" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0176656589";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0176656589";
};

# المستخدم 185: 0173316686
:do {
    /tool user-manager user add customer="admin" username="0173316686" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0173316686";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0173316686";
};

# المستخدم 186: 0179063479
:do {
    /tool user-manager user add customer="admin" username="0179063479" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0179063479";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0179063479";
};

# المستخدم 187: 0162326514
:do {
    /tool user-manager user add customer="admin" username="0162326514" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0162326514";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0162326514";
};

# المستخدم 188: 0182786834
:do {
    /tool user-manager user add customer="admin" username="0182786834" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0182786834";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0182786834";
};

# المستخدم 189: 0198611822
:do {
    /tool user-manager user add customer="admin" username="0198611822" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0198611822";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0198611822";
};

# المستخدم 190: 0131999079
:do {
    /tool user-manager user add customer="admin" username="0131999079" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0131999079";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0131999079";
};

# المستخدم 191: 0107429466
:do {
    /tool user-manager user add customer="admin" username="0107429466" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0107429466";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0107429466";
};

# المستخدم 192: 0160882078
:do {
    /tool user-manager user add customer="admin" username="0160882078" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0160882078";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0160882078";
};

# المستخدم 193: 0188815986
:do {
    /tool user-manager user add customer="admin" username="0188815986" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0188815986";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0188815986";
};

# المستخدم 194: 0130746841
:do {
    /tool user-manager user add customer="admin" username="0130746841" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0130746841";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0130746841";
};

# المستخدم 195: 0116692742
:do {
    /tool user-manager user add customer="admin" username="0116692742" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0116692742";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0116692742";
};

# المستخدم 196: 0101338872
:do {
    /tool user-manager user add customer="admin" username="0101338872" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0101338872";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0101338872";
};

# المستخدم 197: 0116224454
:do {
    /tool user-manager user add customer="admin" username="0116224454" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0116224454";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0116224454";
};

# المستخدم 198: 0188229715
:do {
    /tool user-manager user add customer="admin" username="0188229715" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0188229715";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0188229715";
};

# المستخدم 199: 0117933501
:do {
    /tool user-manager user add customer="admin" username="0117933501" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0117933501";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0117933501";
};

# المستخدم 200: 0103169058
:do {
    /tool user-manager user add customer="admin" username="0103169058" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0103169058";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0103169058";
};


:put "📊 تم الانتهاء من النسخة الاحتياطية";
:put "✅ نجح: $success كرت";
:put "❌ فشل: $errors كرت";
:put "📈 الإجمالي: $total كرت";

:put "🎉 تم استعادة جميع كروت القالب: 10";
:put "💡 النسخة الاحتياطية مكتملة بنجاح!";
