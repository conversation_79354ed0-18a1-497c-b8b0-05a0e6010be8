# MikroTik Script - نسخة احتياطية للكروت
# تم الإنشاء: 2025-07-25 00:14:35
# القالب: 10
# النظام: user_manager
# عدد الكروت: 200
# تم إنشاؤه بواسطة: مولد كروت MikroTik

:put "🚀 بدء تنفيذ النسخة الاحتياطية للكروت";
:put "📋 القالب: 10";
:put "🎯 النظام: user_manager";
:put "📊 عدد الكروت: 200";

:local success 0;
:local errors 0;
:local total 200;

:put "⏳ بدء إضافة الكروت...";

# إضافة مستخدمي User Manager
:put "👤 إضافة 200 مستخدم User Manager...";

# المستخدم 1: 0155029277
:do {
    /tool user-manager user add customer="admin" username="0155029277" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0155029277";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0155029277";
};

# المستخدم 2: 0145126483
:do {
    /tool user-manager user add customer="admin" username="0145126483" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0145126483";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0145126483";
};

# المستخدم 3: 0184258023
:do {
    /tool user-manager user add customer="admin" username="0184258023" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0184258023";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0184258023";
};

# المستخدم 4: 0121079451
:do {
    /tool user-manager user add customer="admin" username="0121079451" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0121079451";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0121079451";
};

# المستخدم 5: 0108367632
:do {
    /tool user-manager user add customer="admin" username="0108367632" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0108367632";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0108367632";
};

# المستخدم 6: 0161589573
:do {
    /tool user-manager user add customer="admin" username="0161589573" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0161589573";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0161589573";
};

# المستخدم 7: 0103376484
:do {
    /tool user-manager user add customer="admin" username="0103376484" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0103376484";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0103376484";
};

# المستخدم 8: 0141489802
:do {
    /tool user-manager user add customer="admin" username="0141489802" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0141489802";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0141489802";
};

# المستخدم 9: 0154646889
:do {
    /tool user-manager user add customer="admin" username="0154646889" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0154646889";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0154646889";
};

# المستخدم 10: 0142396870
:do {
    /tool user-manager user add customer="admin" username="0142396870" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0142396870";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0142396870";
};

# المستخدم 11: 0141648392
:do {
    /tool user-manager user add customer="admin" username="0141648392" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0141648392";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0141648392";
};

# المستخدم 12: 0114487123
:do {
    /tool user-manager user add customer="admin" username="0114487123" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0114487123";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0114487123";
};

# المستخدم 13: 0112469064
:do {
    /tool user-manager user add customer="admin" username="0112469064" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0112469064";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0112469064";
};

# المستخدم 14: 0198906650
:do {
    /tool user-manager user add customer="admin" username="0198906650" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0198906650";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0198906650";
};

# المستخدم 15: 0138698123
:do {
    /tool user-manager user add customer="admin" username="0138698123" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0138698123";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0138698123";
};

# المستخدم 16: 0139036181
:do {
    /tool user-manager user add customer="admin" username="0139036181" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0139036181";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0139036181";
};

# المستخدم 17: 0152800289
:do {
    /tool user-manager user add customer="admin" username="0152800289" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0152800289";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0152800289";
};

# المستخدم 18: 0149490962
:do {
    /tool user-manager user add customer="admin" username="0149490962" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0149490962";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0149490962";
};

# المستخدم 19: 0129438868
:do {
    /tool user-manager user add customer="admin" username="0129438868" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0129438868";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0129438868";
};

# المستخدم 20: 0126946881
:do {
    /tool user-manager user add customer="admin" username="0126946881" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0126946881";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0126946881";
};

# المستخدم 21: 0104008220
:do {
    /tool user-manager user add customer="admin" username="0104008220" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0104008220";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0104008220";
};

# المستخدم 22: 0119985688
:do {
    /tool user-manager user add customer="admin" username="0119985688" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0119985688";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0119985688";
};

# المستخدم 23: 0183767033
:do {
    /tool user-manager user add customer="admin" username="0183767033" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0183767033";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0183767033";
};

# المستخدم 24: 0127841317
:do {
    /tool user-manager user add customer="admin" username="0127841317" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0127841317";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0127841317";
};

# المستخدم 25: 0179577675
:do {
    /tool user-manager user add customer="admin" username="0179577675" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0179577675";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0179577675";
};

# المستخدم 26: 0122739435
:do {
    /tool user-manager user add customer="admin" username="0122739435" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0122739435";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0122739435";
};

# المستخدم 27: 0180154484
:do {
    /tool user-manager user add customer="admin" username="0180154484" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0180154484";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0180154484";
};

# المستخدم 28: 0119172719
:do {
    /tool user-manager user add customer="admin" username="0119172719" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0119172719";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0119172719";
};

# المستخدم 29: 0117670525
:do {
    /tool user-manager user add customer="admin" username="0117670525" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0117670525";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0117670525";
};

# المستخدم 30: 0101195210
:do {
    /tool user-manager user add customer="admin" username="0101195210" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0101195210";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0101195210";
};

# المستخدم 31: 0191251804
:do {
    /tool user-manager user add customer="admin" username="0191251804" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0191251804";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0191251804";
};

# المستخدم 32: 0171436754
:do {
    /tool user-manager user add customer="admin" username="0171436754" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0171436754";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0171436754";
};

# المستخدم 33: 0183706373
:do {
    /tool user-manager user add customer="admin" username="0183706373" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0183706373";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0183706373";
};

# المستخدم 34: 0190360379
:do {
    /tool user-manager user add customer="admin" username="0190360379" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0190360379";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0190360379";
};

# المستخدم 35: 0118271841
:do {
    /tool user-manager user add customer="admin" username="0118271841" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0118271841";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0118271841";
};

# المستخدم 36: 0142772877
:do {
    /tool user-manager user add customer="admin" username="0142772877" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0142772877";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0142772877";
};

# المستخدم 37: 0115316508
:do {
    /tool user-manager user add customer="admin" username="0115316508" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0115316508";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0115316508";
};

# المستخدم 38: 0158213658
:do {
    /tool user-manager user add customer="admin" username="0158213658" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0158213658";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0158213658";
};

# المستخدم 39: 0197595784
:do {
    /tool user-manager user add customer="admin" username="0197595784" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0197595784";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0197595784";
};

# المستخدم 40: 0185725063
:do {
    /tool user-manager user add customer="admin" username="0185725063" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0185725063";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0185725063";
};

# المستخدم 41: 0177398732
:do {
    /tool user-manager user add customer="admin" username="0177398732" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0177398732";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0177398732";
};

# المستخدم 42: 0180537277
:do {
    /tool user-manager user add customer="admin" username="0180537277" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0180537277";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0180537277";
};

# المستخدم 43: 0159427782
:do {
    /tool user-manager user add customer="admin" username="0159427782" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0159427782";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0159427782";
};

# المستخدم 44: 0144235082
:do {
    /tool user-manager user add customer="admin" username="0144235082" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0144235082";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0144235082";
};

# المستخدم 45: 0121973308
:do {
    /tool user-manager user add customer="admin" username="0121973308" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0121973308";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0121973308";
};

# المستخدم 46: 0133509870
:do {
    /tool user-manager user add customer="admin" username="0133509870" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0133509870";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0133509870";
};

# المستخدم 47: 0130991731
:do {
    /tool user-manager user add customer="admin" username="0130991731" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0130991731";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0130991731";
};

# المستخدم 48: 0175622008
:do {
    /tool user-manager user add customer="admin" username="0175622008" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0175622008";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0175622008";
};

# المستخدم 49: 0181881740
:do {
    /tool user-manager user add customer="admin" username="0181881740" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0181881740";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0181881740";
};

# المستخدم 50: 0169705156
:do {
    /tool user-manager user add customer="admin" username="0169705156" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0169705156";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0169705156";
};

# المستخدم 51: 0182316946
:do {
    /tool user-manager user add customer="admin" username="0182316946" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0182316946";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0182316946";
};

# المستخدم 52: 0157444626
:do {
    /tool user-manager user add customer="admin" username="0157444626" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0157444626";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0157444626";
};

# المستخدم 53: 0174111188
:do {
    /tool user-manager user add customer="admin" username="0174111188" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0174111188";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0174111188";
};

# المستخدم 54: 0155847313
:do {
    /tool user-manager user add customer="admin" username="0155847313" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0155847313";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0155847313";
};

# المستخدم 55: 0198452120
:do {
    /tool user-manager user add customer="admin" username="0198452120" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0198452120";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0198452120";
};

# المستخدم 56: 0154135105
:do {
    /tool user-manager user add customer="admin" username="0154135105" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0154135105";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0154135105";
};

# المستخدم 57: 0114404307
:do {
    /tool user-manager user add customer="admin" username="0114404307" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0114404307";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0114404307";
};

# المستخدم 58: 0111195201
:do {
    /tool user-manager user add customer="admin" username="0111195201" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0111195201";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0111195201";
};

# المستخدم 59: 0111165055
:do {
    /tool user-manager user add customer="admin" username="0111165055" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0111165055";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0111165055";
};

# المستخدم 60: 0163203094
:do {
    /tool user-manager user add customer="admin" username="0163203094" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0163203094";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0163203094";
};

# المستخدم 61: 0187638868
:do {
    /tool user-manager user add customer="admin" username="0187638868" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0187638868";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0187638868";
};

# المستخدم 62: 0114082773
:do {
    /tool user-manager user add customer="admin" username="0114082773" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0114082773";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0114082773";
};

# المستخدم 63: 0122933934
:do {
    /tool user-manager user add customer="admin" username="0122933934" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0122933934";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0122933934";
};

# المستخدم 64: 0126602212
:do {
    /tool user-manager user add customer="admin" username="0126602212" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0126602212";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0126602212";
};

# المستخدم 65: 0142936420
:do {
    /tool user-manager user add customer="admin" username="0142936420" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0142936420";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0142936420";
};

# المستخدم 66: 0124000360
:do {
    /tool user-manager user add customer="admin" username="0124000360" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0124000360";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0124000360";
};

# المستخدم 67: 0189909447
:do {
    /tool user-manager user add customer="admin" username="0189909447" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0189909447";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0189909447";
};

# المستخدم 68: 0105500753
:do {
    /tool user-manager user add customer="admin" username="0105500753" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0105500753";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0105500753";
};

# المستخدم 69: 0156198235
:do {
    /tool user-manager user add customer="admin" username="0156198235" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0156198235";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0156198235";
};

# المستخدم 70: 0174913835
:do {
    /tool user-manager user add customer="admin" username="0174913835" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0174913835";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0174913835";
};

# المستخدم 71: 0156758436
:do {
    /tool user-manager user add customer="admin" username="0156758436" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0156758436";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0156758436";
};

# المستخدم 72: 0132033226
:do {
    /tool user-manager user add customer="admin" username="0132033226" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0132033226";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0132033226";
};

# المستخدم 73: 0155133449
:do {
    /tool user-manager user add customer="admin" username="0155133449" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0155133449";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0155133449";
};

# المستخدم 74: 0137092580
:do {
    /tool user-manager user add customer="admin" username="0137092580" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0137092580";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0137092580";
};

# المستخدم 75: 0176502077
:do {
    /tool user-manager user add customer="admin" username="0176502077" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0176502077";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0176502077";
};

# المستخدم 76: 0169424907
:do {
    /tool user-manager user add customer="admin" username="0169424907" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0169424907";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0169424907";
};

# المستخدم 77: 0151685177
:do {
    /tool user-manager user add customer="admin" username="0151685177" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0151685177";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0151685177";
};

# المستخدم 78: 0138093354
:do {
    /tool user-manager user add customer="admin" username="0138093354" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0138093354";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0138093354";
};

# المستخدم 79: 0131056323
:do {
    /tool user-manager user add customer="admin" username="0131056323" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0131056323";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0131056323";
};

# المستخدم 80: 0175609793
:do {
    /tool user-manager user add customer="admin" username="0175609793" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0175609793";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0175609793";
};

# المستخدم 81: 0183751428
:do {
    /tool user-manager user add customer="admin" username="0183751428" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0183751428";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0183751428";
};

# المستخدم 82: 0104800223
:do {
    /tool user-manager user add customer="admin" username="0104800223" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0104800223";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0104800223";
};

# المستخدم 83: 0107545473
:do {
    /tool user-manager user add customer="admin" username="0107545473" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0107545473";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0107545473";
};

# المستخدم 84: 0138912131
:do {
    /tool user-manager user add customer="admin" username="0138912131" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0138912131";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0138912131";
};

# المستخدم 85: 0126283506
:do {
    /tool user-manager user add customer="admin" username="0126283506" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0126283506";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0126283506";
};

# المستخدم 86: 0157930536
:do {
    /tool user-manager user add customer="admin" username="0157930536" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0157930536";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0157930536";
};

# المستخدم 87: 0148157217
:do {
    /tool user-manager user add customer="admin" username="0148157217" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0148157217";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0148157217";
};

# المستخدم 88: 0138804901
:do {
    /tool user-manager user add customer="admin" username="0138804901" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0138804901";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0138804901";
};

# المستخدم 89: 0141392227
:do {
    /tool user-manager user add customer="admin" username="0141392227" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0141392227";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0141392227";
};

# المستخدم 90: 0188326140
:do {
    /tool user-manager user add customer="admin" username="0188326140" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0188326140";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0188326140";
};

# المستخدم 91: 0101210427
:do {
    /tool user-manager user add customer="admin" username="0101210427" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0101210427";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0101210427";
};

# المستخدم 92: 0179173947
:do {
    /tool user-manager user add customer="admin" username="0179173947" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0179173947";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0179173947";
};

# المستخدم 93: 0174851239
:do {
    /tool user-manager user add customer="admin" username="0174851239" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0174851239";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0174851239";
};

# المستخدم 94: 0149962000
:do {
    /tool user-manager user add customer="admin" username="0149962000" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0149962000";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0149962000";
};

# المستخدم 95: 0184337853
:do {
    /tool user-manager user add customer="admin" username="0184337853" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0184337853";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0184337853";
};

# المستخدم 96: 0162905354
:do {
    /tool user-manager user add customer="admin" username="0162905354" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0162905354";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0162905354";
};

# المستخدم 97: 0138719093
:do {
    /tool user-manager user add customer="admin" username="0138719093" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0138719093";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0138719093";
};

# المستخدم 98: 0194671714
:do {
    /tool user-manager user add customer="admin" username="0194671714" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0194671714";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0194671714";
};

# المستخدم 99: 0151927124
:do {
    /tool user-manager user add customer="admin" username="0151927124" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0151927124";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0151927124";
};

# المستخدم 100: 0191114095
:do {
    /tool user-manager user add customer="admin" username="0191114095" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0191114095";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0191114095";
};

# المستخدم 101: 0172993998
:do {
    /tool user-manager user add customer="admin" username="0172993998" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0172993998";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0172993998";
};

# المستخدم 102: 0100266499
:do {
    /tool user-manager user add customer="admin" username="0100266499" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0100266499";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0100266499";
};

# المستخدم 103: 0172146918
:do {
    /tool user-manager user add customer="admin" username="0172146918" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0172146918";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0172146918";
};

# المستخدم 104: 0123972977
:do {
    /tool user-manager user add customer="admin" username="0123972977" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0123972977";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0123972977";
};

# المستخدم 105: 0197779637
:do {
    /tool user-manager user add customer="admin" username="0197779637" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0197779637";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0197779637";
};

# المستخدم 106: 0129102576
:do {
    /tool user-manager user add customer="admin" username="0129102576" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0129102576";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0129102576";
};

# المستخدم 107: 0112046637
:do {
    /tool user-manager user add customer="admin" username="0112046637" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0112046637";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0112046637";
};

# المستخدم 108: 0187900346
:do {
    /tool user-manager user add customer="admin" username="0187900346" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0187900346";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0187900346";
};

# المستخدم 109: 0135716426
:do {
    /tool user-manager user add customer="admin" username="0135716426" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0135716426";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0135716426";
};

# المستخدم 110: 0135509916
:do {
    /tool user-manager user add customer="admin" username="0135509916" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0135509916";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0135509916";
};

# المستخدم 111: 0138057248
:do {
    /tool user-manager user add customer="admin" username="0138057248" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0138057248";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0138057248";
};

# المستخدم 112: 0178592309
:do {
    /tool user-manager user add customer="admin" username="0178592309" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0178592309";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0178592309";
};

# المستخدم 113: 0179834937
:do {
    /tool user-manager user add customer="admin" username="0179834937" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0179834937";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0179834937";
};

# المستخدم 114: 0156965582
:do {
    /tool user-manager user add customer="admin" username="0156965582" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0156965582";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0156965582";
};

# المستخدم 115: 0120810037
:do {
    /tool user-manager user add customer="admin" username="0120810037" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0120810037";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0120810037";
};

# المستخدم 116: 0188931856
:do {
    /tool user-manager user add customer="admin" username="0188931856" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0188931856";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0188931856";
};

# المستخدم 117: 0135046763
:do {
    /tool user-manager user add customer="admin" username="0135046763" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0135046763";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0135046763";
};

# المستخدم 118: 0160751669
:do {
    /tool user-manager user add customer="admin" username="0160751669" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0160751669";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0160751669";
};

# المستخدم 119: 0104081652
:do {
    /tool user-manager user add customer="admin" username="0104081652" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0104081652";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0104081652";
};

# المستخدم 120: 0113349199
:do {
    /tool user-manager user add customer="admin" username="0113349199" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0113349199";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0113349199";
};

# المستخدم 121: 0164788410
:do {
    /tool user-manager user add customer="admin" username="0164788410" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0164788410";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0164788410";
};

# المستخدم 122: 0158780109
:do {
    /tool user-manager user add customer="admin" username="0158780109" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0158780109";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0158780109";
};

# المستخدم 123: 0145842643
:do {
    /tool user-manager user add customer="admin" username="0145842643" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0145842643";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0145842643";
};

# المستخدم 124: 0103272878
:do {
    /tool user-manager user add customer="admin" username="0103272878" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0103272878";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0103272878";
};

# المستخدم 125: 0101991804
:do {
    /tool user-manager user add customer="admin" username="0101991804" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0101991804";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0101991804";
};

# المستخدم 126: 0125474680
:do {
    /tool user-manager user add customer="admin" username="0125474680" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0125474680";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0125474680";
};

# المستخدم 127: 0176976887
:do {
    /tool user-manager user add customer="admin" username="0176976887" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0176976887";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0176976887";
};

# المستخدم 128: 0144971559
:do {
    /tool user-manager user add customer="admin" username="0144971559" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0144971559";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0144971559";
};

# المستخدم 129: 0114557237
:do {
    /tool user-manager user add customer="admin" username="0114557237" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0114557237";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0114557237";
};

# المستخدم 130: 0135880755
:do {
    /tool user-manager user add customer="admin" username="0135880755" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0135880755";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0135880755";
};

# المستخدم 131: 0101268486
:do {
    /tool user-manager user add customer="admin" username="0101268486" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0101268486";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0101268486";
};

# المستخدم 132: 0119808559
:do {
    /tool user-manager user add customer="admin" username="0119808559" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0119808559";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0119808559";
};

# المستخدم 133: 0119828274
:do {
    /tool user-manager user add customer="admin" username="0119828274" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0119828274";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0119828274";
};

# المستخدم 134: 0186148911
:do {
    /tool user-manager user add customer="admin" username="0186148911" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0186148911";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0186148911";
};

# المستخدم 135: 0131980622
:do {
    /tool user-manager user add customer="admin" username="0131980622" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0131980622";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0131980622";
};

# المستخدم 136: 0155743237
:do {
    /tool user-manager user add customer="admin" username="0155743237" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0155743237";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0155743237";
};

# المستخدم 137: 0172889581
:do {
    /tool user-manager user add customer="admin" username="0172889581" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0172889581";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0172889581";
};

# المستخدم 138: 0118144828
:do {
    /tool user-manager user add customer="admin" username="0118144828" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0118144828";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0118144828";
};

# المستخدم 139: 0160167827
:do {
    /tool user-manager user add customer="admin" username="0160167827" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0160167827";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0160167827";
};

# المستخدم 140: 0149623520
:do {
    /tool user-manager user add customer="admin" username="0149623520" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0149623520";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0149623520";
};

# المستخدم 141: 0178531616
:do {
    /tool user-manager user add customer="admin" username="0178531616" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0178531616";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0178531616";
};

# المستخدم 142: 0132540409
:do {
    /tool user-manager user add customer="admin" username="0132540409" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0132540409";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0132540409";
};

# المستخدم 143: 0140579377
:do {
    /tool user-manager user add customer="admin" username="0140579377" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0140579377";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0140579377";
};

# المستخدم 144: 0130994440
:do {
    /tool user-manager user add customer="admin" username="0130994440" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0130994440";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0130994440";
};

# المستخدم 145: 0159839088
:do {
    /tool user-manager user add customer="admin" username="0159839088" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0159839088";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0159839088";
};

# المستخدم 146: 0131898032
:do {
    /tool user-manager user add customer="admin" username="0131898032" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0131898032";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0131898032";
};

# المستخدم 147: 0134541984
:do {
    /tool user-manager user add customer="admin" username="0134541984" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0134541984";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0134541984";
};

# المستخدم 148: 0125088107
:do {
    /tool user-manager user add customer="admin" username="0125088107" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0125088107";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0125088107";
};

# المستخدم 149: 0148498047
:do {
    /tool user-manager user add customer="admin" username="0148498047" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0148498047";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0148498047";
};

# المستخدم 150: 0168982570
:do {
    /tool user-manager user add customer="admin" username="0168982570" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0168982570";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0168982570";
};

# المستخدم 151: 0115558437
:do {
    /tool user-manager user add customer="admin" username="0115558437" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0115558437";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0115558437";
};

# المستخدم 152: 0140550918
:do {
    /tool user-manager user add customer="admin" username="0140550918" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0140550918";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0140550918";
};

# المستخدم 153: 0192393502
:do {
    /tool user-manager user add customer="admin" username="0192393502" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0192393502";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0192393502";
};

# المستخدم 154: 0151386498
:do {
    /tool user-manager user add customer="admin" username="0151386498" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0151386498";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0151386498";
};

# المستخدم 155: 0161554525
:do {
    /tool user-manager user add customer="admin" username="0161554525" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0161554525";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0161554525";
};

# المستخدم 156: 0131022870
:do {
    /tool user-manager user add customer="admin" username="0131022870" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0131022870";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0131022870";
};

# المستخدم 157: 0101091862
:do {
    /tool user-manager user add customer="admin" username="0101091862" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0101091862";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0101091862";
};

# المستخدم 158: 0187443910
:do {
    /tool user-manager user add customer="admin" username="0187443910" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0187443910";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0187443910";
};

# المستخدم 159: 0117383129
:do {
    /tool user-manager user add customer="admin" username="0117383129" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0117383129";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0117383129";
};

# المستخدم 160: 0132255276
:do {
    /tool user-manager user add customer="admin" username="0132255276" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0132255276";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0132255276";
};

# المستخدم 161: 0192537448
:do {
    /tool user-manager user add customer="admin" username="0192537448" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0192537448";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0192537448";
};

# المستخدم 162: 0108952271
:do {
    /tool user-manager user add customer="admin" username="0108952271" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0108952271";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0108952271";
};

# المستخدم 163: 0159613083
:do {
    /tool user-manager user add customer="admin" username="0159613083" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0159613083";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0159613083";
};

# المستخدم 164: 0143277927
:do {
    /tool user-manager user add customer="admin" username="0143277927" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0143277927";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0143277927";
};

# المستخدم 165: 0158233426
:do {
    /tool user-manager user add customer="admin" username="0158233426" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0158233426";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0158233426";
};

# المستخدم 166: 0186705958
:do {
    /tool user-manager user add customer="admin" username="0186705958" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0186705958";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0186705958";
};

# المستخدم 167: 0176341893
:do {
    /tool user-manager user add customer="admin" username="0176341893" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0176341893";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0176341893";
};

# المستخدم 168: 0173670253
:do {
    /tool user-manager user add customer="admin" username="0173670253" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0173670253";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0173670253";
};

# المستخدم 169: 0132420069
:do {
    /tool user-manager user add customer="admin" username="0132420069" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0132420069";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0132420069";
};

# المستخدم 170: 0102247402
:do {
    /tool user-manager user add customer="admin" username="0102247402" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0102247402";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0102247402";
};

# المستخدم 171: 0161300535
:do {
    /tool user-manager user add customer="admin" username="0161300535" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0161300535";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0161300535";
};

# المستخدم 172: 0169983984
:do {
    /tool user-manager user add customer="admin" username="0169983984" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0169983984";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0169983984";
};

# المستخدم 173: 0162432270
:do {
    /tool user-manager user add customer="admin" username="0162432270" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0162432270";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0162432270";
};

# المستخدم 174: 0158411146
:do {
    /tool user-manager user add customer="admin" username="0158411146" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0158411146";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0158411146";
};

# المستخدم 175: 0186851234
:do {
    /tool user-manager user add customer="admin" username="0186851234" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0186851234";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0186851234";
};

# المستخدم 176: 0199112921
:do {
    /tool user-manager user add customer="admin" username="0199112921" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0199112921";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0199112921";
};

# المستخدم 177: 0110530368
:do {
    /tool user-manager user add customer="admin" username="0110530368" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0110530368";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0110530368";
};

# المستخدم 178: 0135183694
:do {
    /tool user-manager user add customer="admin" username="0135183694" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0135183694";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0135183694";
};

# المستخدم 179: 0143506000
:do {
    /tool user-manager user add customer="admin" username="0143506000" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0143506000";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0143506000";
};

# المستخدم 180: 0122647619
:do {
    /tool user-manager user add customer="admin" username="0122647619" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0122647619";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0122647619";
};

# المستخدم 181: 0165296671
:do {
    /tool user-manager user add customer="admin" username="0165296671" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0165296671";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0165296671";
};

# المستخدم 182: 0194101862
:do {
    /tool user-manager user add customer="admin" username="0194101862" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0194101862";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0194101862";
};

# المستخدم 183: 0146638461
:do {
    /tool user-manager user add customer="admin" username="0146638461" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0146638461";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0146638461";
};

# المستخدم 184: 0138726502
:do {
    /tool user-manager user add customer="admin" username="0138726502" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0138726502";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0138726502";
};

# المستخدم 185: 0182859823
:do {
    /tool user-manager user add customer="admin" username="0182859823" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0182859823";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0182859823";
};

# المستخدم 186: 0106963315
:do {
    /tool user-manager user add customer="admin" username="0106963315" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0106963315";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0106963315";
};

# المستخدم 187: 0121148449
:do {
    /tool user-manager user add customer="admin" username="0121148449" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0121148449";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0121148449";
};

# المستخدم 188: 0131869853
:do {
    /tool user-manager user add customer="admin" username="0131869853" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0131869853";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0131869853";
};

# المستخدم 189: 0189259798
:do {
    /tool user-manager user add customer="admin" username="0189259798" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0189259798";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0189259798";
};

# المستخدم 190: 0196724595
:do {
    /tool user-manager user add customer="admin" username="0196724595" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0196724595";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0196724595";
};

# المستخدم 191: 0103886174
:do {
    /tool user-manager user add customer="admin" username="0103886174" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0103886174";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0103886174";
};

# المستخدم 192: 0131898034
:do {
    /tool user-manager user add customer="admin" username="0131898034" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0131898034";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0131898034";
};

# المستخدم 193: 0158416636
:do {
    /tool user-manager user add customer="admin" username="0158416636" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0158416636";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0158416636";
};

# المستخدم 194: 0177745263
:do {
    /tool user-manager user add customer="admin" username="0177745263" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0177745263";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0177745263";
};

# المستخدم 195: 0149559655
:do {
    /tool user-manager user add customer="admin" username="0149559655" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0149559655";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0149559655";
};

# المستخدم 196: 0155930658
:do {
    /tool user-manager user add customer="admin" username="0155930658" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0155930658";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0155930658";
};

# المستخدم 197: 0192336970
:do {
    /tool user-manager user add customer="admin" username="0192336970" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0192336970";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0192336970";
};

# المستخدم 198: 0128850687
:do {
    /tool user-manager user add customer="admin" username="0128850687" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0128850687";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0128850687";
};

# المستخدم 199: 0142894654
:do {
    /tool user-manager user add customer="admin" username="0142894654" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0142894654";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0142894654";
};

# المستخدم 200: 0194115469
:do {
    /tool user-manager user add customer="admin" username="0194115469" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0194115469";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0194115469";
};


:put "📊 تم الانتهاء من النسخة الاحتياطية";
:put "✅ نجح: $success كرت";
:put "❌ فشل: $errors كرت";
:put "📈 الإجمالي: $total كرت";

:put "🎉 تم استعادة جميع كروت القالب: 10";
:put "💡 النسخة الاحتياطية مكتملة بنجاح!";
