# MikroTik Script - نسخة احتياطية للكروت
# تم الإنشاء: 2025-07-24 19:29:48
# القالب: 10
# النظام: user_manager
# عدد الكروت: 200
# تم إنشاؤه بواسطة: مولد كروت MikroTik

:put "🚀 بدء تنفيذ النسخة الاحتياطية للكروت";
:put "📋 القالب: 10";
:put "🎯 النظام: user_manager";
:put "📊 عدد الكروت: 200";

:local success 0;
:local errors 0;
:local total 200;

:put "⏳ بدء إضافة الكروت...";

# إضافة مستخدمي User Manager
:put "👤 إضافة 200 مستخدم User Manager...";

# المستخدم 1: 0128144163
:do {
    /tool user-manager user add customer="admin" username="0128144163" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0128144163";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0128144163";
};

# المستخدم 2: 0145888697
:do {
    /tool user-manager user add customer="admin" username="0145888697" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0145888697";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0145888697";
};

# المستخدم 3: 0142104784
:do {
    /tool user-manager user add customer="admin" username="0142104784" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0142104784";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0142104784";
};

# المستخدم 4: 0144945080
:do {
    /tool user-manager user add customer="admin" username="0144945080" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0144945080";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0144945080";
};

# المستخدم 5: 0120094950
:do {
    /tool user-manager user add customer="admin" username="0120094950" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0120094950";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0120094950";
};

# المستخدم 6: 0154727795
:do {
    /tool user-manager user add customer="admin" username="0154727795" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0154727795";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0154727795";
};

# المستخدم 7: 0159756165
:do {
    /tool user-manager user add customer="admin" username="0159756165" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0159756165";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0159756165";
};

# المستخدم 8: 0105959764
:do {
    /tool user-manager user add customer="admin" username="0105959764" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0105959764";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0105959764";
};

# المستخدم 9: 0167031938
:do {
    /tool user-manager user add customer="admin" username="0167031938" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0167031938";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0167031938";
};

# المستخدم 10: 0130044968
:do {
    /tool user-manager user add customer="admin" username="0130044968" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0130044968";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0130044968";
};

# المستخدم 11: 0109270532
:do {
    /tool user-manager user add customer="admin" username="0109270532" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0109270532";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0109270532";
};

# المستخدم 12: 0101479525
:do {
    /tool user-manager user add customer="admin" username="0101479525" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0101479525";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0101479525";
};

# المستخدم 13: 0183960880
:do {
    /tool user-manager user add customer="admin" username="0183960880" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0183960880";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0183960880";
};

# المستخدم 14: 0126598216
:do {
    /tool user-manager user add customer="admin" username="0126598216" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0126598216";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0126598216";
};

# المستخدم 15: 0115931909
:do {
    /tool user-manager user add customer="admin" username="0115931909" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0115931909";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0115931909";
};

# المستخدم 16: 0144156908
:do {
    /tool user-manager user add customer="admin" username="0144156908" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0144156908";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0144156908";
};

# المستخدم 17: 0137008894
:do {
    /tool user-manager user add customer="admin" username="0137008894" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0137008894";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0137008894";
};

# المستخدم 18: 0112435999
:do {
    /tool user-manager user add customer="admin" username="0112435999" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0112435999";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0112435999";
};

# المستخدم 19: 0139652891
:do {
    /tool user-manager user add customer="admin" username="0139652891" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0139652891";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0139652891";
};

# المستخدم 20: 0106414677
:do {
    /tool user-manager user add customer="admin" username="0106414677" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0106414677";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0106414677";
};

# المستخدم 21: 0141845676
:do {
    /tool user-manager user add customer="admin" username="0141845676" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0141845676";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0141845676";
};

# المستخدم 22: 0115572226
:do {
    /tool user-manager user add customer="admin" username="0115572226" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0115572226";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0115572226";
};

# المستخدم 23: 0132383531
:do {
    /tool user-manager user add customer="admin" username="0132383531" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0132383531";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0132383531";
};

# المستخدم 24: 0191663557
:do {
    /tool user-manager user add customer="admin" username="0191663557" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0191663557";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0191663557";
};

# المستخدم 25: 0107435856
:do {
    /tool user-manager user add customer="admin" username="0107435856" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0107435856";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0107435856";
};

# المستخدم 26: 0181562650
:do {
    /tool user-manager user add customer="admin" username="0181562650" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0181562650";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0181562650";
};

# المستخدم 27: 0111612976
:do {
    /tool user-manager user add customer="admin" username="0111612976" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0111612976";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0111612976";
};

# المستخدم 28: 0166152480
:do {
    /tool user-manager user add customer="admin" username="0166152480" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0166152480";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0166152480";
};

# المستخدم 29: 0118929457
:do {
    /tool user-manager user add customer="admin" username="0118929457" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0118929457";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0118929457";
};

# المستخدم 30: 0110819219
:do {
    /tool user-manager user add customer="admin" username="0110819219" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0110819219";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0110819219";
};

# المستخدم 31: 0192124110
:do {
    /tool user-manager user add customer="admin" username="0192124110" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0192124110";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0192124110";
};

# المستخدم 32: 0135593285
:do {
    /tool user-manager user add customer="admin" username="0135593285" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0135593285";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0135593285";
};

# المستخدم 33: 0187710867
:do {
    /tool user-manager user add customer="admin" username="0187710867" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0187710867";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0187710867";
};

# المستخدم 34: 0185478333
:do {
    /tool user-manager user add customer="admin" username="0185478333" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0185478333";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0185478333";
};

# المستخدم 35: 0177029381
:do {
    /tool user-manager user add customer="admin" username="0177029381" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0177029381";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0177029381";
};

# المستخدم 36: 0106990821
:do {
    /tool user-manager user add customer="admin" username="0106990821" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0106990821";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0106990821";
};

# المستخدم 37: 0136529837
:do {
    /tool user-manager user add customer="admin" username="0136529837" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0136529837";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0136529837";
};

# المستخدم 38: 0109531187
:do {
    /tool user-manager user add customer="admin" username="0109531187" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0109531187";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0109531187";
};

# المستخدم 39: 0163939152
:do {
    /tool user-manager user add customer="admin" username="0163939152" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0163939152";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0163939152";
};

# المستخدم 40: 0131913607
:do {
    /tool user-manager user add customer="admin" username="0131913607" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0131913607";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0131913607";
};

# المستخدم 41: 0184145262
:do {
    /tool user-manager user add customer="admin" username="0184145262" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0184145262";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0184145262";
};

# المستخدم 42: 0184688161
:do {
    /tool user-manager user add customer="admin" username="0184688161" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0184688161";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0184688161";
};

# المستخدم 43: 0119764117
:do {
    /tool user-manager user add customer="admin" username="0119764117" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0119764117";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0119764117";
};

# المستخدم 44: 0110018047
:do {
    /tool user-manager user add customer="admin" username="0110018047" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0110018047";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0110018047";
};

# المستخدم 45: 0133780770
:do {
    /tool user-manager user add customer="admin" username="0133780770" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0133780770";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0133780770";
};

# المستخدم 46: 0179543339
:do {
    /tool user-manager user add customer="admin" username="0179543339" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0179543339";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0179543339";
};

# المستخدم 47: 0143110070
:do {
    /tool user-manager user add customer="admin" username="0143110070" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0143110070";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0143110070";
};

# المستخدم 48: 0166816428
:do {
    /tool user-manager user add customer="admin" username="0166816428" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0166816428";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0166816428";
};

# المستخدم 49: 0196456464
:do {
    /tool user-manager user add customer="admin" username="0196456464" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0196456464";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0196456464";
};

# المستخدم 50: 0130295177
:do {
    /tool user-manager user add customer="admin" username="0130295177" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0130295177";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0130295177";
};

# المستخدم 51: 0101443564
:do {
    /tool user-manager user add customer="admin" username="0101443564" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0101443564";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0101443564";
};

# المستخدم 52: 0114956467
:do {
    /tool user-manager user add customer="admin" username="0114956467" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0114956467";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0114956467";
};

# المستخدم 53: 0137954200
:do {
    /tool user-manager user add customer="admin" username="0137954200" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0137954200";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0137954200";
};

# المستخدم 54: 0124984249
:do {
    /tool user-manager user add customer="admin" username="0124984249" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0124984249";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0124984249";
};

# المستخدم 55: 0139360326
:do {
    /tool user-manager user add customer="admin" username="0139360326" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0139360326";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0139360326";
};

# المستخدم 56: 0139000330
:do {
    /tool user-manager user add customer="admin" username="0139000330" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0139000330";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0139000330";
};

# المستخدم 57: 0173538269
:do {
    /tool user-manager user add customer="admin" username="0173538269" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0173538269";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0173538269";
};

# المستخدم 58: 0163338838
:do {
    /tool user-manager user add customer="admin" username="0163338838" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0163338838";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0163338838";
};

# المستخدم 59: 0183709840
:do {
    /tool user-manager user add customer="admin" username="0183709840" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0183709840";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0183709840";
};

# المستخدم 60: 0136845785
:do {
    /tool user-manager user add customer="admin" username="0136845785" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0136845785";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0136845785";
};

# المستخدم 61: 0178336483
:do {
    /tool user-manager user add customer="admin" username="0178336483" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0178336483";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0178336483";
};

# المستخدم 62: 0177006256
:do {
    /tool user-manager user add customer="admin" username="0177006256" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0177006256";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0177006256";
};

# المستخدم 63: 0142808405
:do {
    /tool user-manager user add customer="admin" username="0142808405" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0142808405";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0142808405";
};

# المستخدم 64: 0148183989
:do {
    /tool user-manager user add customer="admin" username="0148183989" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0148183989";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0148183989";
};

# المستخدم 65: 0170657880
:do {
    /tool user-manager user add customer="admin" username="0170657880" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0170657880";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0170657880";
};

# المستخدم 66: 0179357645
:do {
    /tool user-manager user add customer="admin" username="0179357645" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0179357645";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0179357645";
};

# المستخدم 67: 0115809659
:do {
    /tool user-manager user add customer="admin" username="0115809659" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0115809659";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0115809659";
};

# المستخدم 68: 0100629670
:do {
    /tool user-manager user add customer="admin" username="0100629670" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0100629670";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0100629670";
};

# المستخدم 69: 0152137438
:do {
    /tool user-manager user add customer="admin" username="0152137438" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0152137438";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0152137438";
};

# المستخدم 70: 0140237391
:do {
    /tool user-manager user add customer="admin" username="0140237391" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0140237391";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0140237391";
};

# المستخدم 71: 0159244960
:do {
    /tool user-manager user add customer="admin" username="0159244960" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0159244960";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0159244960";
};

# المستخدم 72: 0177059313
:do {
    /tool user-manager user add customer="admin" username="0177059313" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0177059313";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0177059313";
};

# المستخدم 73: 0149396397
:do {
    /tool user-manager user add customer="admin" username="0149396397" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0149396397";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0149396397";
};

# المستخدم 74: 0189045420
:do {
    /tool user-manager user add customer="admin" username="0189045420" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0189045420";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0189045420";
};

# المستخدم 75: 0181073460
:do {
    /tool user-manager user add customer="admin" username="0181073460" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0181073460";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0181073460";
};

# المستخدم 76: 0126770219
:do {
    /tool user-manager user add customer="admin" username="0126770219" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0126770219";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0126770219";
};

# المستخدم 77: 0146802406
:do {
    /tool user-manager user add customer="admin" username="0146802406" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0146802406";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0146802406";
};

# المستخدم 78: 0135470404
:do {
    /tool user-manager user add customer="admin" username="0135470404" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0135470404";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0135470404";
};

# المستخدم 79: 0199653239
:do {
    /tool user-manager user add customer="admin" username="0199653239" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0199653239";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0199653239";
};

# المستخدم 80: 0120644834
:do {
    /tool user-manager user add customer="admin" username="0120644834" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0120644834";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0120644834";
};

# المستخدم 81: 0136872692
:do {
    /tool user-manager user add customer="admin" username="0136872692" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0136872692";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0136872692";
};

# المستخدم 82: 0161024218
:do {
    /tool user-manager user add customer="admin" username="0161024218" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0161024218";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0161024218";
};

# المستخدم 83: 0167700945
:do {
    /tool user-manager user add customer="admin" username="0167700945" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0167700945";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0167700945";
};

# المستخدم 84: 0199684739
:do {
    /tool user-manager user add customer="admin" username="0199684739" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0199684739";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0199684739";
};

# المستخدم 85: 0144576703
:do {
    /tool user-manager user add customer="admin" username="0144576703" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0144576703";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0144576703";
};

# المستخدم 86: 0144321391
:do {
    /tool user-manager user add customer="admin" username="0144321391" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0144321391";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0144321391";
};

# المستخدم 87: 0138906934
:do {
    /tool user-manager user add customer="admin" username="0138906934" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0138906934";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0138906934";
};

# المستخدم 88: 0158992995
:do {
    /tool user-manager user add customer="admin" username="0158992995" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0158992995";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0158992995";
};

# المستخدم 89: 0125310902
:do {
    /tool user-manager user add customer="admin" username="0125310902" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0125310902";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0125310902";
};

# المستخدم 90: 0133890602
:do {
    /tool user-manager user add customer="admin" username="0133890602" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0133890602";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0133890602";
};

# المستخدم 91: 0121055584
:do {
    /tool user-manager user add customer="admin" username="0121055584" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0121055584";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0121055584";
};

# المستخدم 92: 0188157321
:do {
    /tool user-manager user add customer="admin" username="0188157321" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0188157321";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0188157321";
};

# المستخدم 93: 0121851968
:do {
    /tool user-manager user add customer="admin" username="0121851968" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0121851968";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0121851968";
};

# المستخدم 94: 0118930453
:do {
    /tool user-manager user add customer="admin" username="0118930453" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0118930453";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0118930453";
};

# المستخدم 95: 0128127640
:do {
    /tool user-manager user add customer="admin" username="0128127640" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0128127640";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0128127640";
};

# المستخدم 96: 0113936432
:do {
    /tool user-manager user add customer="admin" username="0113936432" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0113936432";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0113936432";
};

# المستخدم 97: 0177422690
:do {
    /tool user-manager user add customer="admin" username="0177422690" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0177422690";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0177422690";
};

# المستخدم 98: 0108516455
:do {
    /tool user-manager user add customer="admin" username="0108516455" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0108516455";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0108516455";
};

# المستخدم 99: 0133317457
:do {
    /tool user-manager user add customer="admin" username="0133317457" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0133317457";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0133317457";
};

# المستخدم 100: 0164649755
:do {
    /tool user-manager user add customer="admin" username="0164649755" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0164649755";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0164649755";
};

# المستخدم 101: 0195562124
:do {
    /tool user-manager user add customer="admin" username="0195562124" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0195562124";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0195562124";
};

# المستخدم 102: 0163922541
:do {
    /tool user-manager user add customer="admin" username="0163922541" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0163922541";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0163922541";
};

# المستخدم 103: 0169589603
:do {
    /tool user-manager user add customer="admin" username="0169589603" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0169589603";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0169589603";
};

# المستخدم 104: 0143728098
:do {
    /tool user-manager user add customer="admin" username="0143728098" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0143728098";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0143728098";
};

# المستخدم 105: 0142769235
:do {
    /tool user-manager user add customer="admin" username="0142769235" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0142769235";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0142769235";
};

# المستخدم 106: 0135713023
:do {
    /tool user-manager user add customer="admin" username="0135713023" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0135713023";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0135713023";
};

# المستخدم 107: 0103413379
:do {
    /tool user-manager user add customer="admin" username="0103413379" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0103413379";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0103413379";
};

# المستخدم 108: 0101664614
:do {
    /tool user-manager user add customer="admin" username="0101664614" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0101664614";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0101664614";
};

# المستخدم 109: 0115602105
:do {
    /tool user-manager user add customer="admin" username="0115602105" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0115602105";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0115602105";
};

# المستخدم 110: 0197970710
:do {
    /tool user-manager user add customer="admin" username="0197970710" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0197970710";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0197970710";
};

# المستخدم 111: 0144388075
:do {
    /tool user-manager user add customer="admin" username="0144388075" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0144388075";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0144388075";
};

# المستخدم 112: 0162253712
:do {
    /tool user-manager user add customer="admin" username="0162253712" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0162253712";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0162253712";
};

# المستخدم 113: 0123496616
:do {
    /tool user-manager user add customer="admin" username="0123496616" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0123496616";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0123496616";
};

# المستخدم 114: 0150836084
:do {
    /tool user-manager user add customer="admin" username="0150836084" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0150836084";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0150836084";
};

# المستخدم 115: 0181205710
:do {
    /tool user-manager user add customer="admin" username="0181205710" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0181205710";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0181205710";
};

# المستخدم 116: 0153737056
:do {
    /tool user-manager user add customer="admin" username="0153737056" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0153737056";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0153737056";
};

# المستخدم 117: 0196634267
:do {
    /tool user-manager user add customer="admin" username="0196634267" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0196634267";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0196634267";
};

# المستخدم 118: 0119305782
:do {
    /tool user-manager user add customer="admin" username="0119305782" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0119305782";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0119305782";
};

# المستخدم 119: 0161410807
:do {
    /tool user-manager user add customer="admin" username="0161410807" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0161410807";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0161410807";
};

# المستخدم 120: 0171759391
:do {
    /tool user-manager user add customer="admin" username="0171759391" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0171759391";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0171759391";
};

# المستخدم 121: 0198331979
:do {
    /tool user-manager user add customer="admin" username="0198331979" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0198331979";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0198331979";
};

# المستخدم 122: 0105181674
:do {
    /tool user-manager user add customer="admin" username="0105181674" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0105181674";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0105181674";
};

# المستخدم 123: 0105213382
:do {
    /tool user-manager user add customer="admin" username="0105213382" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0105213382";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0105213382";
};

# المستخدم 124: 0152646075
:do {
    /tool user-manager user add customer="admin" username="0152646075" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0152646075";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0152646075";
};

# المستخدم 125: 0109868830
:do {
    /tool user-manager user add customer="admin" username="0109868830" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0109868830";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0109868830";
};

# المستخدم 126: 0176092075
:do {
    /tool user-manager user add customer="admin" username="0176092075" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0176092075";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0176092075";
};

# المستخدم 127: 0135303504
:do {
    /tool user-manager user add customer="admin" username="0135303504" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0135303504";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0135303504";
};

# المستخدم 128: 0173076299
:do {
    /tool user-manager user add customer="admin" username="0173076299" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0173076299";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0173076299";
};

# المستخدم 129: 0167755195
:do {
    /tool user-manager user add customer="admin" username="0167755195" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0167755195";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0167755195";
};

# المستخدم 130: 0157689765
:do {
    /tool user-manager user add customer="admin" username="0157689765" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0157689765";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0157689765";
};

# المستخدم 131: 0144414531
:do {
    /tool user-manager user add customer="admin" username="0144414531" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0144414531";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0144414531";
};

# المستخدم 132: 0178680745
:do {
    /tool user-manager user add customer="admin" username="0178680745" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0178680745";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0178680745";
};

# المستخدم 133: 0145255060
:do {
    /tool user-manager user add customer="admin" username="0145255060" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0145255060";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0145255060";
};

# المستخدم 134: 0190291381
:do {
    /tool user-manager user add customer="admin" username="0190291381" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0190291381";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0190291381";
};

# المستخدم 135: 0175742211
:do {
    /tool user-manager user add customer="admin" username="0175742211" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0175742211";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0175742211";
};

# المستخدم 136: 0151293278
:do {
    /tool user-manager user add customer="admin" username="0151293278" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0151293278";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0151293278";
};

# المستخدم 137: 0194166971
:do {
    /tool user-manager user add customer="admin" username="0194166971" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0194166971";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0194166971";
};

# المستخدم 138: 0199764494
:do {
    /tool user-manager user add customer="admin" username="0199764494" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0199764494";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0199764494";
};

# المستخدم 139: 0120738678
:do {
    /tool user-manager user add customer="admin" username="0120738678" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0120738678";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0120738678";
};

# المستخدم 140: 0168289369
:do {
    /tool user-manager user add customer="admin" username="0168289369" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0168289369";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0168289369";
};

# المستخدم 141: 0143318699
:do {
    /tool user-manager user add customer="admin" username="0143318699" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0143318699";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0143318699";
};

# المستخدم 142: 0179435295
:do {
    /tool user-manager user add customer="admin" username="0179435295" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0179435295";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0179435295";
};

# المستخدم 143: 0146804425
:do {
    /tool user-manager user add customer="admin" username="0146804425" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0146804425";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0146804425";
};

# المستخدم 144: 0155461306
:do {
    /tool user-manager user add customer="admin" username="0155461306" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0155461306";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0155461306";
};

# المستخدم 145: 0179961870
:do {
    /tool user-manager user add customer="admin" username="0179961870" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0179961870";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0179961870";
};

# المستخدم 146: 0132732083
:do {
    /tool user-manager user add customer="admin" username="0132732083" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0132732083";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0132732083";
};

# المستخدم 147: 0146290553
:do {
    /tool user-manager user add customer="admin" username="0146290553" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0146290553";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0146290553";
};

# المستخدم 148: 0161427235
:do {
    /tool user-manager user add customer="admin" username="0161427235" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0161427235";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0161427235";
};

# المستخدم 149: 0198270381
:do {
    /tool user-manager user add customer="admin" username="0198270381" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0198270381";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0198270381";
};

# المستخدم 150: 0165886269
:do {
    /tool user-manager user add customer="admin" username="0165886269" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0165886269";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0165886269";
};

# المستخدم 151: 0168556455
:do {
    /tool user-manager user add customer="admin" username="0168556455" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0168556455";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0168556455";
};

# المستخدم 152: 0144550141
:do {
    /tool user-manager user add customer="admin" username="0144550141" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0144550141";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0144550141";
};

# المستخدم 153: 0100106030
:do {
    /tool user-manager user add customer="admin" username="0100106030" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0100106030";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0100106030";
};

# المستخدم 154: 0104356584
:do {
    /tool user-manager user add customer="admin" username="0104356584" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0104356584";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0104356584";
};

# المستخدم 155: 0118138047
:do {
    /tool user-manager user add customer="admin" username="0118138047" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0118138047";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0118138047";
};

# المستخدم 156: 0178022333
:do {
    /tool user-manager user add customer="admin" username="0178022333" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0178022333";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0178022333";
};

# المستخدم 157: 0105588510
:do {
    /tool user-manager user add customer="admin" username="0105588510" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0105588510";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0105588510";
};

# المستخدم 158: 0137342718
:do {
    /tool user-manager user add customer="admin" username="0137342718" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0137342718";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0137342718";
};

# المستخدم 159: 0102886301
:do {
    /tool user-manager user add customer="admin" username="0102886301" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0102886301";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0102886301";
};

# المستخدم 160: 0153958470
:do {
    /tool user-manager user add customer="admin" username="0153958470" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0153958470";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0153958470";
};

# المستخدم 161: 0199820331
:do {
    /tool user-manager user add customer="admin" username="0199820331" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0199820331";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0199820331";
};

# المستخدم 162: 0176597788
:do {
    /tool user-manager user add customer="admin" username="0176597788" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0176597788";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0176597788";
};

# المستخدم 163: 0180403080
:do {
    /tool user-manager user add customer="admin" username="0180403080" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0180403080";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0180403080";
};

# المستخدم 164: 0177692144
:do {
    /tool user-manager user add customer="admin" username="0177692144" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0177692144";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0177692144";
};

# المستخدم 165: 0148890611
:do {
    /tool user-manager user add customer="admin" username="0148890611" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0148890611";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0148890611";
};

# المستخدم 166: 0185990781
:do {
    /tool user-manager user add customer="admin" username="0185990781" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0185990781";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0185990781";
};

# المستخدم 167: 0170343386
:do {
    /tool user-manager user add customer="admin" username="0170343386" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0170343386";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0170343386";
};

# المستخدم 168: 0115396805
:do {
    /tool user-manager user add customer="admin" username="0115396805" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0115396805";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0115396805";
};

# المستخدم 169: 0186798298
:do {
    /tool user-manager user add customer="admin" username="0186798298" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0186798298";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0186798298";
};

# المستخدم 170: 0157383156
:do {
    /tool user-manager user add customer="admin" username="0157383156" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0157383156";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0157383156";
};

# المستخدم 171: 0185104935
:do {
    /tool user-manager user add customer="admin" username="0185104935" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0185104935";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0185104935";
};

# المستخدم 172: 0182977015
:do {
    /tool user-manager user add customer="admin" username="0182977015" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0182977015";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0182977015";
};

# المستخدم 173: 0126591461
:do {
    /tool user-manager user add customer="admin" username="0126591461" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0126591461";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0126591461";
};

# المستخدم 174: 0181579972
:do {
    /tool user-manager user add customer="admin" username="0181579972" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0181579972";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0181579972";
};

# المستخدم 175: 0134660798
:do {
    /tool user-manager user add customer="admin" username="0134660798" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0134660798";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0134660798";
};

# المستخدم 176: 0108476496
:do {
    /tool user-manager user add customer="admin" username="0108476496" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0108476496";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0108476496";
};

# المستخدم 177: 0103298307
:do {
    /tool user-manager user add customer="admin" username="0103298307" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0103298307";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0103298307";
};

# المستخدم 178: 0126384452
:do {
    /tool user-manager user add customer="admin" username="0126384452" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0126384452";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0126384452";
};

# المستخدم 179: 0188111800
:do {
    /tool user-manager user add customer="admin" username="0188111800" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0188111800";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0188111800";
};

# المستخدم 180: 0167829286
:do {
    /tool user-manager user add customer="admin" username="0167829286" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0167829286";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0167829286";
};

# المستخدم 181: 0174292622
:do {
    /tool user-manager user add customer="admin" username="0174292622" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0174292622";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0174292622";
};

# المستخدم 182: 0143900017
:do {
    /tool user-manager user add customer="admin" username="0143900017" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0143900017";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0143900017";
};

# المستخدم 183: 0127429841
:do {
    /tool user-manager user add customer="admin" username="0127429841" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0127429841";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0127429841";
};

# المستخدم 184: 0112797816
:do {
    /tool user-manager user add customer="admin" username="0112797816" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0112797816";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0112797816";
};

# المستخدم 185: 0156572966
:do {
    /tool user-manager user add customer="admin" username="0156572966" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0156572966";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0156572966";
};

# المستخدم 186: 0186824976
:do {
    /tool user-manager user add customer="admin" username="0186824976" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0186824976";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0186824976";
};

# المستخدم 187: 0130286122
:do {
    /tool user-manager user add customer="admin" username="0130286122" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0130286122";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0130286122";
};

# المستخدم 188: 0107983920
:do {
    /tool user-manager user add customer="admin" username="0107983920" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0107983920";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0107983920";
};

# المستخدم 189: 0112058102
:do {
    /tool user-manager user add customer="admin" username="0112058102" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0112058102";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0112058102";
};

# المستخدم 190: 0194620340
:do {
    /tool user-manager user add customer="admin" username="0194620340" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0194620340";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0194620340";
};

# المستخدم 191: 0177505763
:do {
    /tool user-manager user add customer="admin" username="0177505763" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0177505763";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0177505763";
};

# المستخدم 192: 0198694904
:do {
    /tool user-manager user add customer="admin" username="0198694904" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0198694904";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0198694904";
};

# المستخدم 193: 0139036643
:do {
    /tool user-manager user add customer="admin" username="0139036643" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0139036643";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0139036643";
};

# المستخدم 194: 0117282382
:do {
    /tool user-manager user add customer="admin" username="0117282382" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0117282382";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0117282382";
};

# المستخدم 195: 0199526685
:do {
    /tool user-manager user add customer="admin" username="0199526685" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0199526685";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0199526685";
};

# المستخدم 196: 0157973760
:do {
    /tool user-manager user add customer="admin" username="0157973760" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0157973760";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0157973760";
};

# المستخدم 197: 0145718459
:do {
    /tool user-manager user add customer="admin" username="0145718459" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0145718459";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0145718459";
};

# المستخدم 198: 0116501122
:do {
    /tool user-manager user add customer="admin" username="0116501122" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0116501122";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0116501122";
};

# المستخدم 199: 0101555335
:do {
    /tool user-manager user add customer="admin" username="0101555335" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0101555335";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0101555335";
};

# المستخدم 200: 0186281503
:do {
    /tool user-manager user add customer="admin" username="0186281503" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0186281503";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0186281503";
};


:put "📊 تم الانتهاء من النسخة الاحتياطية";
:put "✅ نجح: $success كرت";
:put "❌ فشل: $errors كرت";
:put "📈 الإجمالي: $total كرت";

:put "🎉 تم استعادة جميع كروت القالب: 10";
:put "💡 النسخة الاحتياطية مكتملة بنجاح!";
