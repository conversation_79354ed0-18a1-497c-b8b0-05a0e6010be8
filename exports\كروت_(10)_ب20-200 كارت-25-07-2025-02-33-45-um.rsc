# MikroTik Script - نسخة احتياطية للكروت
# تم الإنشاء: 2025-07-25 02:33:46
# القالب: 10
# النظام: user_manager
# عدد الكروت: 200
# تم إنشاؤه بواسطة: مولد كروت MikroTik

:put "🚀 بدء تنفيذ النسخة الاحتياطية للكروت";
:put "📋 القالب: 10";
:put "🎯 النظام: user_manager";
:put "📊 عدد الكروت: 200";

:local success 0;
:local errors 0;
:local total 200;

:put "⏳ بدء إضافة الكروت...";

# إضافة مستخدمي User Manager
:put "👤 إضافة 200 مستخدم User Manager...";

# المستخدم 1: 0144487404
:do {
    /tool user-manager user add customer="admin" username="0144487404" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0144487404";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0144487404";
};

# المستخدم 2: 0131738388
:do {
    /tool user-manager user add customer="admin" username="0131738388" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0131738388";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0131738388";
};

# المستخدم 3: 0105400761
:do {
    /tool user-manager user add customer="admin" username="0105400761" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0105400761";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0105400761";
};

# المستخدم 4: 0126760180
:do {
    /tool user-manager user add customer="admin" username="0126760180" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0126760180";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0126760180";
};

# المستخدم 5: 0105011412
:do {
    /tool user-manager user add customer="admin" username="0105011412" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0105011412";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0105011412";
};

# المستخدم 6: 0142888780
:do {
    /tool user-manager user add customer="admin" username="0142888780" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0142888780";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0142888780";
};

# المستخدم 7: 0149548636
:do {
    /tool user-manager user add customer="admin" username="0149548636" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0149548636";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0149548636";
};

# المستخدم 8: 0144488999
:do {
    /tool user-manager user add customer="admin" username="0144488999" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0144488999";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0144488999";
};

# المستخدم 9: 0176149363
:do {
    /tool user-manager user add customer="admin" username="0176149363" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0176149363";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0176149363";
};

# المستخدم 10: 0130499076
:do {
    /tool user-manager user add customer="admin" username="0130499076" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0130499076";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0130499076";
};

# المستخدم 11: 0154989171
:do {
    /tool user-manager user add customer="admin" username="0154989171" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0154989171";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0154989171";
};

# المستخدم 12: 0170093336
:do {
    /tool user-manager user add customer="admin" username="0170093336" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0170093336";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0170093336";
};

# المستخدم 13: 0123940196
:do {
    /tool user-manager user add customer="admin" username="0123940196" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0123940196";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0123940196";
};

# المستخدم 14: 0178016275
:do {
    /tool user-manager user add customer="admin" username="0178016275" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0178016275";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0178016275";
};

# المستخدم 15: 0128827481
:do {
    /tool user-manager user add customer="admin" username="0128827481" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0128827481";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0128827481";
};

# المستخدم 16: 0118550884
:do {
    /tool user-manager user add customer="admin" username="0118550884" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0118550884";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0118550884";
};

# المستخدم 17: 0129076382
:do {
    /tool user-manager user add customer="admin" username="0129076382" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0129076382";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0129076382";
};

# المستخدم 18: 0102195215
:do {
    /tool user-manager user add customer="admin" username="0102195215" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0102195215";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0102195215";
};

# المستخدم 19: 0164975962
:do {
    /tool user-manager user add customer="admin" username="0164975962" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0164975962";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0164975962";
};

# المستخدم 20: 0180225761
:do {
    /tool user-manager user add customer="admin" username="0180225761" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0180225761";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0180225761";
};

# المستخدم 21: 0124048197
:do {
    /tool user-manager user add customer="admin" username="0124048197" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0124048197";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0124048197";
};

# المستخدم 22: 0188523257
:do {
    /tool user-manager user add customer="admin" username="0188523257" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0188523257";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0188523257";
};

# المستخدم 23: 0153209710
:do {
    /tool user-manager user add customer="admin" username="0153209710" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0153209710";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0153209710";
};

# المستخدم 24: 0165810816
:do {
    /tool user-manager user add customer="admin" username="0165810816" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0165810816";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0165810816";
};

# المستخدم 25: 0113121249
:do {
    /tool user-manager user add customer="admin" username="0113121249" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0113121249";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0113121249";
};

# المستخدم 26: 0152114418
:do {
    /tool user-manager user add customer="admin" username="0152114418" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0152114418";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0152114418";
};

# المستخدم 27: 0108841187
:do {
    /tool user-manager user add customer="admin" username="0108841187" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0108841187";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0108841187";
};

# المستخدم 28: 0121852023
:do {
    /tool user-manager user add customer="admin" username="0121852023" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0121852023";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0121852023";
};

# المستخدم 29: 0196402256
:do {
    /tool user-manager user add customer="admin" username="0196402256" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0196402256";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0196402256";
};

# المستخدم 30: 0128669750
:do {
    /tool user-manager user add customer="admin" username="0128669750" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0128669750";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0128669750";
};

# المستخدم 31: 0106621359
:do {
    /tool user-manager user add customer="admin" username="0106621359" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0106621359";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0106621359";
};

# المستخدم 32: 0150477865
:do {
    /tool user-manager user add customer="admin" username="0150477865" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0150477865";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0150477865";
};

# المستخدم 33: 0152718852
:do {
    /tool user-manager user add customer="admin" username="0152718852" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0152718852";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0152718852";
};

# المستخدم 34: 0199381156
:do {
    /tool user-manager user add customer="admin" username="0199381156" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0199381156";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0199381156";
};

# المستخدم 35: 0141847877
:do {
    /tool user-manager user add customer="admin" username="0141847877" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0141847877";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0141847877";
};

# المستخدم 36: 0137770778
:do {
    /tool user-manager user add customer="admin" username="0137770778" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0137770778";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0137770778";
};

# المستخدم 37: 0179926997
:do {
    /tool user-manager user add customer="admin" username="0179926997" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0179926997";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0179926997";
};

# المستخدم 38: 0162494006
:do {
    /tool user-manager user add customer="admin" username="0162494006" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0162494006";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0162494006";
};

# المستخدم 39: 0167446817
:do {
    /tool user-manager user add customer="admin" username="0167446817" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0167446817";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0167446817";
};

# المستخدم 40: 0168426301
:do {
    /tool user-manager user add customer="admin" username="0168426301" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0168426301";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0168426301";
};

# المستخدم 41: 0101806340
:do {
    /tool user-manager user add customer="admin" username="0101806340" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0101806340";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0101806340";
};

# المستخدم 42: 0129631608
:do {
    /tool user-manager user add customer="admin" username="0129631608" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0129631608";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0129631608";
};

# المستخدم 43: 0132240485
:do {
    /tool user-manager user add customer="admin" username="0132240485" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0132240485";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0132240485";
};

# المستخدم 44: 0115221411
:do {
    /tool user-manager user add customer="admin" username="0115221411" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0115221411";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0115221411";
};

# المستخدم 45: 0175126583
:do {
    /tool user-manager user add customer="admin" username="0175126583" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0175126583";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0175126583";
};

# المستخدم 46: 0176120852
:do {
    /tool user-manager user add customer="admin" username="0176120852" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0176120852";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0176120852";
};

# المستخدم 47: 0140239604
:do {
    /tool user-manager user add customer="admin" username="0140239604" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0140239604";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0140239604";
};

# المستخدم 48: 0169571462
:do {
    /tool user-manager user add customer="admin" username="0169571462" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0169571462";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0169571462";
};

# المستخدم 49: 0123683211
:do {
    /tool user-manager user add customer="admin" username="0123683211" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0123683211";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0123683211";
};

# المستخدم 50: 0136484383
:do {
    /tool user-manager user add customer="admin" username="0136484383" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0136484383";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0136484383";
};

# المستخدم 51: 0161991982
:do {
    /tool user-manager user add customer="admin" username="0161991982" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0161991982";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0161991982";
};

# المستخدم 52: 0104320785
:do {
    /tool user-manager user add customer="admin" username="0104320785" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0104320785";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0104320785";
};

# المستخدم 53: 0175219705
:do {
    /tool user-manager user add customer="admin" username="0175219705" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0175219705";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0175219705";
};

# المستخدم 54: 0107211246
:do {
    /tool user-manager user add customer="admin" username="0107211246" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0107211246";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0107211246";
};

# المستخدم 55: 0133665577
:do {
    /tool user-manager user add customer="admin" username="0133665577" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0133665577";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0133665577";
};

# المستخدم 56: 0124593782
:do {
    /tool user-manager user add customer="admin" username="0124593782" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0124593782";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0124593782";
};

# المستخدم 57: 0126024618
:do {
    /tool user-manager user add customer="admin" username="0126024618" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0126024618";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0126024618";
};

# المستخدم 58: 0136970348
:do {
    /tool user-manager user add customer="admin" username="0136970348" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0136970348";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0136970348";
};

# المستخدم 59: 0100214145
:do {
    /tool user-manager user add customer="admin" username="0100214145" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0100214145";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0100214145";
};

# المستخدم 60: 0107814300
:do {
    /tool user-manager user add customer="admin" username="0107814300" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0107814300";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0107814300";
};

# المستخدم 61: 0109239794
:do {
    /tool user-manager user add customer="admin" username="0109239794" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0109239794";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0109239794";
};

# المستخدم 62: 0180145756
:do {
    /tool user-manager user add customer="admin" username="0180145756" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0180145756";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0180145756";
};

# المستخدم 63: 0142989131
:do {
    /tool user-manager user add customer="admin" username="0142989131" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0142989131";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0142989131";
};

# المستخدم 64: 0106093263
:do {
    /tool user-manager user add customer="admin" username="0106093263" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0106093263";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0106093263";
};

# المستخدم 65: 0100951569
:do {
    /tool user-manager user add customer="admin" username="0100951569" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0100951569";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0100951569";
};

# المستخدم 66: 0156767755
:do {
    /tool user-manager user add customer="admin" username="0156767755" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0156767755";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0156767755";
};

# المستخدم 67: 0188354732
:do {
    /tool user-manager user add customer="admin" username="0188354732" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0188354732";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0188354732";
};

# المستخدم 68: 0108663701
:do {
    /tool user-manager user add customer="admin" username="0108663701" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0108663701";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0108663701";
};

# المستخدم 69: 0117838337
:do {
    /tool user-manager user add customer="admin" username="0117838337" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0117838337";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0117838337";
};

# المستخدم 70: 0192610025
:do {
    /tool user-manager user add customer="admin" username="0192610025" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0192610025";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0192610025";
};

# المستخدم 71: 0127262649
:do {
    /tool user-manager user add customer="admin" username="0127262649" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0127262649";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0127262649";
};

# المستخدم 72: 0159576332
:do {
    /tool user-manager user add customer="admin" username="0159576332" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0159576332";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0159576332";
};

# المستخدم 73: 0188162447
:do {
    /tool user-manager user add customer="admin" username="0188162447" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0188162447";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0188162447";
};

# المستخدم 74: 0170445887
:do {
    /tool user-manager user add customer="admin" username="0170445887" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0170445887";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0170445887";
};

# المستخدم 75: 0196705915
:do {
    /tool user-manager user add customer="admin" username="0196705915" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0196705915";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0196705915";
};

# المستخدم 76: 0140439433
:do {
    /tool user-manager user add customer="admin" username="0140439433" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0140439433";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0140439433";
};

# المستخدم 77: 0197649417
:do {
    /tool user-manager user add customer="admin" username="0197649417" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0197649417";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0197649417";
};

# المستخدم 78: 0173313286
:do {
    /tool user-manager user add customer="admin" username="0173313286" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0173313286";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0173313286";
};

# المستخدم 79: 0142322849
:do {
    /tool user-manager user add customer="admin" username="0142322849" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0142322849";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0142322849";
};

# المستخدم 80: 0127028849
:do {
    /tool user-manager user add customer="admin" username="0127028849" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0127028849";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0127028849";
};

# المستخدم 81: 0175265214
:do {
    /tool user-manager user add customer="admin" username="0175265214" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0175265214";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0175265214";
};

# المستخدم 82: 0122322269
:do {
    /tool user-manager user add customer="admin" username="0122322269" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0122322269";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0122322269";
};

# المستخدم 83: 0157764981
:do {
    /tool user-manager user add customer="admin" username="0157764981" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0157764981";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0157764981";
};

# المستخدم 84: 0160878246
:do {
    /tool user-manager user add customer="admin" username="0160878246" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0160878246";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0160878246";
};

# المستخدم 85: 0192072173
:do {
    /tool user-manager user add customer="admin" username="0192072173" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0192072173";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0192072173";
};

# المستخدم 86: 0192292433
:do {
    /tool user-manager user add customer="admin" username="0192292433" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0192292433";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0192292433";
};

# المستخدم 87: 0155979802
:do {
    /tool user-manager user add customer="admin" username="0155979802" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0155979802";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0155979802";
};

# المستخدم 88: 0149190975
:do {
    /tool user-manager user add customer="admin" username="0149190975" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0149190975";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0149190975";
};

# المستخدم 89: 0100824810
:do {
    /tool user-manager user add customer="admin" username="0100824810" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0100824810";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0100824810";
};

# المستخدم 90: 0181547086
:do {
    /tool user-manager user add customer="admin" username="0181547086" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0181547086";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0181547086";
};

# المستخدم 91: 0167645903
:do {
    /tool user-manager user add customer="admin" username="0167645903" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0167645903";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0167645903";
};

# المستخدم 92: 0176623260
:do {
    /tool user-manager user add customer="admin" username="0176623260" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0176623260";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0176623260";
};

# المستخدم 93: 0187417437
:do {
    /tool user-manager user add customer="admin" username="0187417437" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0187417437";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0187417437";
};

# المستخدم 94: 0100058900
:do {
    /tool user-manager user add customer="admin" username="0100058900" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0100058900";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0100058900";
};

# المستخدم 95: 0161596739
:do {
    /tool user-manager user add customer="admin" username="0161596739" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0161596739";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0161596739";
};

# المستخدم 96: 0176282317
:do {
    /tool user-manager user add customer="admin" username="0176282317" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0176282317";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0176282317";
};

# المستخدم 97: 0128560691
:do {
    /tool user-manager user add customer="admin" username="0128560691" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0128560691";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0128560691";
};

# المستخدم 98: 0158139785
:do {
    /tool user-manager user add customer="admin" username="0158139785" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0158139785";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0158139785";
};

# المستخدم 99: 0158339445
:do {
    /tool user-manager user add customer="admin" username="0158339445" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0158339445";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0158339445";
};

# المستخدم 100: 0147279871
:do {
    /tool user-manager user add customer="admin" username="0147279871" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0147279871";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0147279871";
};

# المستخدم 101: 0113147294
:do {
    /tool user-manager user add customer="admin" username="0113147294" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0113147294";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0113147294";
};

# المستخدم 102: 0152481113
:do {
    /tool user-manager user add customer="admin" username="0152481113" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0152481113";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0152481113";
};

# المستخدم 103: 0160748117
:do {
    /tool user-manager user add customer="admin" username="0160748117" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0160748117";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0160748117";
};

# المستخدم 104: 0115906941
:do {
    /tool user-manager user add customer="admin" username="0115906941" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0115906941";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0115906941";
};

# المستخدم 105: 0153343403
:do {
    /tool user-manager user add customer="admin" username="0153343403" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0153343403";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0153343403";
};

# المستخدم 106: 0183677207
:do {
    /tool user-manager user add customer="admin" username="0183677207" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0183677207";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0183677207";
};

# المستخدم 107: 0151822411
:do {
    /tool user-manager user add customer="admin" username="0151822411" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0151822411";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0151822411";
};

# المستخدم 108: 0140745254
:do {
    /tool user-manager user add customer="admin" username="0140745254" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0140745254";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0140745254";
};

# المستخدم 109: 0155240272
:do {
    /tool user-manager user add customer="admin" username="0155240272" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0155240272";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0155240272";
};

# المستخدم 110: 0184347835
:do {
    /tool user-manager user add customer="admin" username="0184347835" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0184347835";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0184347835";
};

# المستخدم 111: 0189158646
:do {
    /tool user-manager user add customer="admin" username="0189158646" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0189158646";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0189158646";
};

# المستخدم 112: 0158368166
:do {
    /tool user-manager user add customer="admin" username="0158368166" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0158368166";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0158368166";
};

# المستخدم 113: 0115097548
:do {
    /tool user-manager user add customer="admin" username="0115097548" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0115097548";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0115097548";
};

# المستخدم 114: 0139344726
:do {
    /tool user-manager user add customer="admin" username="0139344726" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0139344726";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0139344726";
};

# المستخدم 115: 0171975076
:do {
    /tool user-manager user add customer="admin" username="0171975076" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0171975076";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0171975076";
};

# المستخدم 116: 0161803838
:do {
    /tool user-manager user add customer="admin" username="0161803838" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0161803838";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0161803838";
};

# المستخدم 117: 0139743041
:do {
    /tool user-manager user add customer="admin" username="0139743041" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0139743041";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0139743041";
};

# المستخدم 118: 0138592048
:do {
    /tool user-manager user add customer="admin" username="0138592048" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0138592048";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0138592048";
};

# المستخدم 119: 0171842052
:do {
    /tool user-manager user add customer="admin" username="0171842052" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0171842052";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0171842052";
};

# المستخدم 120: 0106324534
:do {
    /tool user-manager user add customer="admin" username="0106324534" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0106324534";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0106324534";
};

# المستخدم 121: 0136246623
:do {
    /tool user-manager user add customer="admin" username="0136246623" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0136246623";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0136246623";
};

# المستخدم 122: 0109448585
:do {
    /tool user-manager user add customer="admin" username="0109448585" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0109448585";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0109448585";
};

# المستخدم 123: 0100000524
:do {
    /tool user-manager user add customer="admin" username="0100000524" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0100000524";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0100000524";
};

# المستخدم 124: 0177353713
:do {
    /tool user-manager user add customer="admin" username="0177353713" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0177353713";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0177353713";
};

# المستخدم 125: 0111380299
:do {
    /tool user-manager user add customer="admin" username="0111380299" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0111380299";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0111380299";
};

# المستخدم 126: 0128613594
:do {
    /tool user-manager user add customer="admin" username="0128613594" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0128613594";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0128613594";
};

# المستخدم 127: 0173647744
:do {
    /tool user-manager user add customer="admin" username="0173647744" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0173647744";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0173647744";
};

# المستخدم 128: 0126958339
:do {
    /tool user-manager user add customer="admin" username="0126958339" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0126958339";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0126958339";
};

# المستخدم 129: 0147469675
:do {
    /tool user-manager user add customer="admin" username="0147469675" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0147469675";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0147469675";
};

# المستخدم 130: 0129050982
:do {
    /tool user-manager user add customer="admin" username="0129050982" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0129050982";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0129050982";
};

# المستخدم 131: 0175737573
:do {
    /tool user-manager user add customer="admin" username="0175737573" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0175737573";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0175737573";
};

# المستخدم 132: 0183862055
:do {
    /tool user-manager user add customer="admin" username="0183862055" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0183862055";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0183862055";
};

# المستخدم 133: 0150093307
:do {
    /tool user-manager user add customer="admin" username="0150093307" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0150093307";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0150093307";
};

# المستخدم 134: 0113562178
:do {
    /tool user-manager user add customer="admin" username="0113562178" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0113562178";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0113562178";
};

# المستخدم 135: 0186060741
:do {
    /tool user-manager user add customer="admin" username="0186060741" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0186060741";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0186060741";
};

# المستخدم 136: 0163900303
:do {
    /tool user-manager user add customer="admin" username="0163900303" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0163900303";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0163900303";
};

# المستخدم 137: 0173244969
:do {
    /tool user-manager user add customer="admin" username="0173244969" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0173244969";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0173244969";
};

# المستخدم 138: 0178279539
:do {
    /tool user-manager user add customer="admin" username="0178279539" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0178279539";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0178279539";
};

# المستخدم 139: 0142378761
:do {
    /tool user-manager user add customer="admin" username="0142378761" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0142378761";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0142378761";
};

# المستخدم 140: 0139418494
:do {
    /tool user-manager user add customer="admin" username="0139418494" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0139418494";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0139418494";
};

# المستخدم 141: 0143382362
:do {
    /tool user-manager user add customer="admin" username="0143382362" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0143382362";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0143382362";
};

# المستخدم 142: 0141259655
:do {
    /tool user-manager user add customer="admin" username="0141259655" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0141259655";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0141259655";
};

# المستخدم 143: 0126174082
:do {
    /tool user-manager user add customer="admin" username="0126174082" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0126174082";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0126174082";
};

# المستخدم 144: 0196474658
:do {
    /tool user-manager user add customer="admin" username="0196474658" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0196474658";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0196474658";
};

# المستخدم 145: 0179137876
:do {
    /tool user-manager user add customer="admin" username="0179137876" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0179137876";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0179137876";
};

# المستخدم 146: 0195131903
:do {
    /tool user-manager user add customer="admin" username="0195131903" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0195131903";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0195131903";
};

# المستخدم 147: 0163983822
:do {
    /tool user-manager user add customer="admin" username="0163983822" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0163983822";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0163983822";
};

# المستخدم 148: 0157169088
:do {
    /tool user-manager user add customer="admin" username="0157169088" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0157169088";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0157169088";
};

# المستخدم 149: 0105989835
:do {
    /tool user-manager user add customer="admin" username="0105989835" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0105989835";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0105989835";
};

# المستخدم 150: 0152476651
:do {
    /tool user-manager user add customer="admin" username="0152476651" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0152476651";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0152476651";
};

# المستخدم 151: 0155101512
:do {
    /tool user-manager user add customer="admin" username="0155101512" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0155101512";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0155101512";
};

# المستخدم 152: 0179096722
:do {
    /tool user-manager user add customer="admin" username="0179096722" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0179096722";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0179096722";
};

# المستخدم 153: 0199296764
:do {
    /tool user-manager user add customer="admin" username="0199296764" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0199296764";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0199296764";
};

# المستخدم 154: 0160422131
:do {
    /tool user-manager user add customer="admin" username="0160422131" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0160422131";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0160422131";
};

# المستخدم 155: 0146498783
:do {
    /tool user-manager user add customer="admin" username="0146498783" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0146498783";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0146498783";
};

# المستخدم 156: 0134133509
:do {
    /tool user-manager user add customer="admin" username="0134133509" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0134133509";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0134133509";
};

# المستخدم 157: 0109906748
:do {
    /tool user-manager user add customer="admin" username="0109906748" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0109906748";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0109906748";
};

# المستخدم 158: 0101939221
:do {
    /tool user-manager user add customer="admin" username="0101939221" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0101939221";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0101939221";
};

# المستخدم 159: 0150506145
:do {
    /tool user-manager user add customer="admin" username="0150506145" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0150506145";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0150506145";
};

# المستخدم 160: 0139912330
:do {
    /tool user-manager user add customer="admin" username="0139912330" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0139912330";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0139912330";
};

# المستخدم 161: 0132794143
:do {
    /tool user-manager user add customer="admin" username="0132794143" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0132794143";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0132794143";
};

# المستخدم 162: 0180620777
:do {
    /tool user-manager user add customer="admin" username="0180620777" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0180620777";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0180620777";
};

# المستخدم 163: 0190870250
:do {
    /tool user-manager user add customer="admin" username="0190870250" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0190870250";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0190870250";
};

# المستخدم 164: 0113693114
:do {
    /tool user-manager user add customer="admin" username="0113693114" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0113693114";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0113693114";
};

# المستخدم 165: 0130642591
:do {
    /tool user-manager user add customer="admin" username="0130642591" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0130642591";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0130642591";
};

# المستخدم 166: 0188452866
:do {
    /tool user-manager user add customer="admin" username="0188452866" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0188452866";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0188452866";
};

# المستخدم 167: 0115452805
:do {
    /tool user-manager user add customer="admin" username="0115452805" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0115452805";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0115452805";
};

# المستخدم 168: 0180196725
:do {
    /tool user-manager user add customer="admin" username="0180196725" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0180196725";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0180196725";
};

# المستخدم 169: 0194452976
:do {
    /tool user-manager user add customer="admin" username="0194452976" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0194452976";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0194452976";
};

# المستخدم 170: 0179777848
:do {
    /tool user-manager user add customer="admin" username="0179777848" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0179777848";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0179777848";
};

# المستخدم 171: 0183120143
:do {
    /tool user-manager user add customer="admin" username="0183120143" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0183120143";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0183120143";
};

# المستخدم 172: 0169075931
:do {
    /tool user-manager user add customer="admin" username="0169075931" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0169075931";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0169075931";
};

# المستخدم 173: 0178701211
:do {
    /tool user-manager user add customer="admin" username="0178701211" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0178701211";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0178701211";
};

# المستخدم 174: 0114998132
:do {
    /tool user-manager user add customer="admin" username="0114998132" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0114998132";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0114998132";
};

# المستخدم 175: 0164437756
:do {
    /tool user-manager user add customer="admin" username="0164437756" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0164437756";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0164437756";
};

# المستخدم 176: 0128238753
:do {
    /tool user-manager user add customer="admin" username="0128238753" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0128238753";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0128238753";
};

# المستخدم 177: 0126760963
:do {
    /tool user-manager user add customer="admin" username="0126760963" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0126760963";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0126760963";
};

# المستخدم 178: 0151419254
:do {
    /tool user-manager user add customer="admin" username="0151419254" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0151419254";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0151419254";
};

# المستخدم 179: 0130428313
:do {
    /tool user-manager user add customer="admin" username="0130428313" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0130428313";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0130428313";
};

# المستخدم 180: 0147590786
:do {
    /tool user-manager user add customer="admin" username="0147590786" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0147590786";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0147590786";
};

# المستخدم 181: 0141236795
:do {
    /tool user-manager user add customer="admin" username="0141236795" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0141236795";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0141236795";
};

# المستخدم 182: 0128642717
:do {
    /tool user-manager user add customer="admin" username="0128642717" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0128642717";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0128642717";
};

# المستخدم 183: 0129509087
:do {
    /tool user-manager user add customer="admin" username="0129509087" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0129509087";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0129509087";
};

# المستخدم 184: 0155095611
:do {
    /tool user-manager user add customer="admin" username="0155095611" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0155095611";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0155095611";
};

# المستخدم 185: 0187884424
:do {
    /tool user-manager user add customer="admin" username="0187884424" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0187884424";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0187884424";
};

# المستخدم 186: 0161511259
:do {
    /tool user-manager user add customer="admin" username="0161511259" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0161511259";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0161511259";
};

# المستخدم 187: 0192929459
:do {
    /tool user-manager user add customer="admin" username="0192929459" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0192929459";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0192929459";
};

# المستخدم 188: 0113480950
:do {
    /tool user-manager user add customer="admin" username="0113480950" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0113480950";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0113480950";
};

# المستخدم 189: 0146936799
:do {
    /tool user-manager user add customer="admin" username="0146936799" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0146936799";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0146936799";
};

# المستخدم 190: 0178170960
:do {
    /tool user-manager user add customer="admin" username="0178170960" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0178170960";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0178170960";
};

# المستخدم 191: 0122389833
:do {
    /tool user-manager user add customer="admin" username="0122389833" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0122389833";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0122389833";
};

# المستخدم 192: 0108996386
:do {
    /tool user-manager user add customer="admin" username="0108996386" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0108996386";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0108996386";
};

# المستخدم 193: 0157453319
:do {
    /tool user-manager user add customer="admin" username="0157453319" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0157453319";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0157453319";
};

# المستخدم 194: 0189790852
:do {
    /tool user-manager user add customer="admin" username="0189790852" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0189790852";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0189790852";
};

# المستخدم 195: 0175334320
:do {
    /tool user-manager user add customer="admin" username="0175334320" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0175334320";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0175334320";
};

# المستخدم 196: 0122721196
:do {
    /tool user-manager user add customer="admin" username="0122721196" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0122721196";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0122721196";
};

# المستخدم 197: 0144431814
:do {
    /tool user-manager user add customer="admin" username="0144431814" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0144431814";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0144431814";
};

# المستخدم 198: 0136937176
:do {
    /tool user-manager user add customer="admin" username="0136937176" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0136937176";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0136937176";
};

# المستخدم 199: 0121184000
:do {
    /tool user-manager user add customer="admin" username="0121184000" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0121184000";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0121184000";
};

# المستخدم 200: 0158975609
:do {
    /tool user-manager user add customer="admin" username="0158975609" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0158975609";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0158975609";
};


:put "📊 تم الانتهاء من النسخة الاحتياطية";
:put "✅ نجح: $success كرت";
:put "❌ فشل: $errors كرت";
:put "📈 الإجمالي: $total كرت";

:put "🎉 تم استعادة جميع كروت القالب: 10";
:put "💡 النسخة الاحتياطية مكتملة بنجاح!";
