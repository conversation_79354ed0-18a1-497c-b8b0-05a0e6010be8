# MikroTik Script - نسخة احتياطية للكروت
# تم الإنشاء: 2025-07-24 19:46:26
# القالب: 10
# النظام: user_manager
# عدد الكروت: 200
# تم إنشاؤه بواسطة: مولد كروت MikroTik

:put "🚀 بدء تنفيذ النسخة الاحتياطية للكروت";
:put "📋 القالب: 10";
:put "🎯 النظام: user_manager";
:put "📊 عدد الكروت: 200";

:local success 0;
:local errors 0;
:local total 200;

:put "⏳ بدء إضافة الكروت...";

# إضافة مستخدمي User Manager
:put "👤 إضافة 200 مستخدم User Manager...";

# المستخدم 1: 0112117467
:do {
    /tool user-manager user add customer="admin" username="0112117467" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0112117467";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0112117467";
};

# المستخدم 2: 0121501259
:do {
    /tool user-manager user add customer="admin" username="0121501259" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0121501259";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0121501259";
};

# المستخدم 3: 0157597497
:do {
    /tool user-manager user add customer="admin" username="0157597497" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0157597497";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0157597497";
};

# المستخدم 4: 0152770317
:do {
    /tool user-manager user add customer="admin" username="0152770317" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0152770317";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0152770317";
};

# المستخدم 5: 0186673933
:do {
    /tool user-manager user add customer="admin" username="0186673933" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0186673933";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0186673933";
};

# المستخدم 6: 0183163250
:do {
    /tool user-manager user add customer="admin" username="0183163250" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0183163250";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0183163250";
};

# المستخدم 7: 0147302730
:do {
    /tool user-manager user add customer="admin" username="0147302730" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0147302730";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0147302730";
};

# المستخدم 8: 0143579678
:do {
    /tool user-manager user add customer="admin" username="0143579678" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0143579678";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0143579678";
};

# المستخدم 9: 0168690932
:do {
    /tool user-manager user add customer="admin" username="0168690932" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0168690932";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0168690932";
};

# المستخدم 10: 0125143214
:do {
    /tool user-manager user add customer="admin" username="0125143214" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0125143214";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0125143214";
};

# المستخدم 11: 0171402470
:do {
    /tool user-manager user add customer="admin" username="0171402470" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0171402470";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0171402470";
};

# المستخدم 12: 0126558753
:do {
    /tool user-manager user add customer="admin" username="0126558753" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0126558753";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0126558753";
};

# المستخدم 13: 0179047552
:do {
    /tool user-manager user add customer="admin" username="0179047552" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0179047552";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0179047552";
};

# المستخدم 14: 0125132027
:do {
    /tool user-manager user add customer="admin" username="0125132027" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0125132027";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0125132027";
};

# المستخدم 15: 0121479229
:do {
    /tool user-manager user add customer="admin" username="0121479229" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0121479229";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0121479229";
};

# المستخدم 16: 0193044930
:do {
    /tool user-manager user add customer="admin" username="0193044930" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0193044930";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0193044930";
};

# المستخدم 17: 0167015866
:do {
    /tool user-manager user add customer="admin" username="0167015866" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0167015866";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0167015866";
};

# المستخدم 18: 0116504276
:do {
    /tool user-manager user add customer="admin" username="0116504276" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0116504276";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0116504276";
};

# المستخدم 19: 0152791006
:do {
    /tool user-manager user add customer="admin" username="0152791006" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0152791006";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0152791006";
};

# المستخدم 20: 0159477432
:do {
    /tool user-manager user add customer="admin" username="0159477432" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0159477432";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0159477432";
};

# المستخدم 21: 0123033098
:do {
    /tool user-manager user add customer="admin" username="0123033098" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0123033098";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0123033098";
};

# المستخدم 22: 0136583660
:do {
    /tool user-manager user add customer="admin" username="0136583660" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0136583660";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0136583660";
};

# المستخدم 23: 0117010782
:do {
    /tool user-manager user add customer="admin" username="0117010782" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0117010782";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0117010782";
};

# المستخدم 24: 0154515306
:do {
    /tool user-manager user add customer="admin" username="0154515306" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0154515306";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0154515306";
};

# المستخدم 25: 0184837958
:do {
    /tool user-manager user add customer="admin" username="0184837958" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0184837958";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0184837958";
};

# المستخدم 26: 0126914820
:do {
    /tool user-manager user add customer="admin" username="0126914820" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0126914820";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0126914820";
};

# المستخدم 27: 0150453135
:do {
    /tool user-manager user add customer="admin" username="0150453135" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0150453135";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0150453135";
};

# المستخدم 28: 0189959109
:do {
    /tool user-manager user add customer="admin" username="0189959109" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0189959109";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0189959109";
};

# المستخدم 29: 0133340783
:do {
    /tool user-manager user add customer="admin" username="0133340783" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0133340783";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0133340783";
};

# المستخدم 30: 0141506946
:do {
    /tool user-manager user add customer="admin" username="0141506946" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0141506946";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0141506946";
};

# المستخدم 31: 0153883106
:do {
    /tool user-manager user add customer="admin" username="0153883106" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0153883106";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0153883106";
};

# المستخدم 32: 0159058771
:do {
    /tool user-manager user add customer="admin" username="0159058771" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0159058771";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0159058771";
};

# المستخدم 33: 0131163519
:do {
    /tool user-manager user add customer="admin" username="0131163519" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0131163519";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0131163519";
};

# المستخدم 34: 0176835083
:do {
    /tool user-manager user add customer="admin" username="0176835083" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0176835083";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0176835083";
};

# المستخدم 35: 0165113420
:do {
    /tool user-manager user add customer="admin" username="0165113420" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0165113420";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0165113420";
};

# المستخدم 36: 0126769963
:do {
    /tool user-manager user add customer="admin" username="0126769963" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0126769963";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0126769963";
};

# المستخدم 37: 0150510332
:do {
    /tool user-manager user add customer="admin" username="0150510332" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0150510332";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0150510332";
};

# المستخدم 38: 0183783033
:do {
    /tool user-manager user add customer="admin" username="0183783033" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0183783033";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0183783033";
};

# المستخدم 39: 0126013578
:do {
    /tool user-manager user add customer="admin" username="0126013578" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0126013578";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0126013578";
};

# المستخدم 40: 0111028943
:do {
    /tool user-manager user add customer="admin" username="0111028943" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0111028943";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0111028943";
};

# المستخدم 41: 0118224300
:do {
    /tool user-manager user add customer="admin" username="0118224300" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0118224300";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0118224300";
};

# المستخدم 42: 0149662903
:do {
    /tool user-manager user add customer="admin" username="0149662903" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0149662903";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0149662903";
};

# المستخدم 43: 0186579547
:do {
    /tool user-manager user add customer="admin" username="0186579547" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0186579547";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0186579547";
};

# المستخدم 44: 0138542388
:do {
    /tool user-manager user add customer="admin" username="0138542388" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0138542388";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0138542388";
};

# المستخدم 45: 0132408629
:do {
    /tool user-manager user add customer="admin" username="0132408629" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0132408629";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0132408629";
};

# المستخدم 46: 0150922295
:do {
    /tool user-manager user add customer="admin" username="0150922295" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0150922295";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0150922295";
};

# المستخدم 47: 0101523488
:do {
    /tool user-manager user add customer="admin" username="0101523488" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0101523488";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0101523488";
};

# المستخدم 48: 0126444004
:do {
    /tool user-manager user add customer="admin" username="0126444004" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0126444004";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0126444004";
};

# المستخدم 49: 0149500428
:do {
    /tool user-manager user add customer="admin" username="0149500428" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0149500428";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0149500428";
};

# المستخدم 50: 0123622613
:do {
    /tool user-manager user add customer="admin" username="0123622613" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0123622613";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0123622613";
};

# المستخدم 51: 0163607927
:do {
    /tool user-manager user add customer="admin" username="0163607927" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0163607927";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0163607927";
};

# المستخدم 52: 0167160113
:do {
    /tool user-manager user add customer="admin" username="0167160113" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0167160113";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0167160113";
};

# المستخدم 53: 0170579563
:do {
    /tool user-manager user add customer="admin" username="0170579563" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0170579563";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0170579563";
};

# المستخدم 54: 0104321955
:do {
    /tool user-manager user add customer="admin" username="0104321955" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0104321955";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0104321955";
};

# المستخدم 55: 0119269195
:do {
    /tool user-manager user add customer="admin" username="0119269195" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0119269195";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0119269195";
};

# المستخدم 56: 0109307901
:do {
    /tool user-manager user add customer="admin" username="0109307901" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0109307901";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0109307901";
};

# المستخدم 57: 0190377760
:do {
    /tool user-manager user add customer="admin" username="0190377760" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0190377760";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0190377760";
};

# المستخدم 58: 0110007304
:do {
    /tool user-manager user add customer="admin" username="0110007304" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0110007304";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0110007304";
};

# المستخدم 59: 0165101671
:do {
    /tool user-manager user add customer="admin" username="0165101671" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0165101671";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0165101671";
};

# المستخدم 60: 0151446298
:do {
    /tool user-manager user add customer="admin" username="0151446298" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0151446298";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0151446298";
};

# المستخدم 61: 0195687557
:do {
    /tool user-manager user add customer="admin" username="0195687557" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0195687557";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0195687557";
};

# المستخدم 62: 0199879326
:do {
    /tool user-manager user add customer="admin" username="0199879326" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0199879326";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0199879326";
};

# المستخدم 63: 0129062585
:do {
    /tool user-manager user add customer="admin" username="0129062585" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0129062585";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0129062585";
};

# المستخدم 64: 0112932726
:do {
    /tool user-manager user add customer="admin" username="0112932726" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0112932726";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0112932726";
};

# المستخدم 65: 0188946938
:do {
    /tool user-manager user add customer="admin" username="0188946938" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0188946938";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0188946938";
};

# المستخدم 66: 0172456489
:do {
    /tool user-manager user add customer="admin" username="0172456489" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0172456489";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0172456489";
};

# المستخدم 67: 0169910242
:do {
    /tool user-manager user add customer="admin" username="0169910242" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0169910242";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0169910242";
};

# المستخدم 68: 0191566414
:do {
    /tool user-manager user add customer="admin" username="0191566414" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0191566414";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0191566414";
};

# المستخدم 69: 0125094955
:do {
    /tool user-manager user add customer="admin" username="0125094955" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0125094955";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0125094955";
};

# المستخدم 70: 0104717832
:do {
    /tool user-manager user add customer="admin" username="0104717832" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0104717832";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0104717832";
};

# المستخدم 71: 0107410016
:do {
    /tool user-manager user add customer="admin" username="0107410016" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0107410016";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0107410016";
};

# المستخدم 72: 0151262094
:do {
    /tool user-manager user add customer="admin" username="0151262094" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0151262094";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0151262094";
};

# المستخدم 73: 0174557095
:do {
    /tool user-manager user add customer="admin" username="0174557095" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0174557095";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0174557095";
};

# المستخدم 74: 0133097656
:do {
    /tool user-manager user add customer="admin" username="0133097656" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0133097656";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0133097656";
};

# المستخدم 75: 0114346398
:do {
    /tool user-manager user add customer="admin" username="0114346398" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0114346398";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0114346398";
};

# المستخدم 76: 0121983303
:do {
    /tool user-manager user add customer="admin" username="0121983303" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0121983303";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0121983303";
};

# المستخدم 77: 0172057022
:do {
    /tool user-manager user add customer="admin" username="0172057022" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0172057022";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0172057022";
};

# المستخدم 78: 0156345165
:do {
    /tool user-manager user add customer="admin" username="0156345165" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0156345165";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0156345165";
};

# المستخدم 79: 0183407887
:do {
    /tool user-manager user add customer="admin" username="0183407887" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0183407887";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0183407887";
};

# المستخدم 80: 0153980226
:do {
    /tool user-manager user add customer="admin" username="0153980226" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0153980226";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0153980226";
};

# المستخدم 81: 0191976955
:do {
    /tool user-manager user add customer="admin" username="0191976955" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0191976955";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0191976955";
};

# المستخدم 82: 0173107167
:do {
    /tool user-manager user add customer="admin" username="0173107167" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0173107167";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0173107167";
};

# المستخدم 83: 0153079828
:do {
    /tool user-manager user add customer="admin" username="0153079828" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0153079828";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0153079828";
};

# المستخدم 84: 0135603516
:do {
    /tool user-manager user add customer="admin" username="0135603516" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0135603516";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0135603516";
};

# المستخدم 85: 0182908696
:do {
    /tool user-manager user add customer="admin" username="0182908696" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0182908696";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0182908696";
};

# المستخدم 86: 0112447224
:do {
    /tool user-manager user add customer="admin" username="0112447224" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0112447224";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0112447224";
};

# المستخدم 87: 0120344127
:do {
    /tool user-manager user add customer="admin" username="0120344127" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0120344127";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0120344127";
};

# المستخدم 88: 0138818166
:do {
    /tool user-manager user add customer="admin" username="0138818166" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0138818166";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0138818166";
};

# المستخدم 89: 0162356330
:do {
    /tool user-manager user add customer="admin" username="0162356330" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0162356330";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0162356330";
};

# المستخدم 90: 0170995328
:do {
    /tool user-manager user add customer="admin" username="0170995328" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0170995328";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0170995328";
};

# المستخدم 91: 0114367825
:do {
    /tool user-manager user add customer="admin" username="0114367825" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0114367825";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0114367825";
};

# المستخدم 92: 0134772493
:do {
    /tool user-manager user add customer="admin" username="0134772493" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0134772493";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0134772493";
};

# المستخدم 93: 0104637579
:do {
    /tool user-manager user add customer="admin" username="0104637579" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0104637579";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0104637579";
};

# المستخدم 94: 0103109081
:do {
    /tool user-manager user add customer="admin" username="0103109081" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0103109081";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0103109081";
};

# المستخدم 95: 0198988813
:do {
    /tool user-manager user add customer="admin" username="0198988813" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0198988813";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0198988813";
};

# المستخدم 96: 0184842986
:do {
    /tool user-manager user add customer="admin" username="0184842986" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0184842986";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0184842986";
};

# المستخدم 97: 0157242200
:do {
    /tool user-manager user add customer="admin" username="0157242200" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0157242200";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0157242200";
};

# المستخدم 98: 0180393484
:do {
    /tool user-manager user add customer="admin" username="0180393484" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0180393484";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0180393484";
};

# المستخدم 99: 0105017431
:do {
    /tool user-manager user add customer="admin" username="0105017431" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0105017431";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0105017431";
};

# المستخدم 100: 0105270653
:do {
    /tool user-manager user add customer="admin" username="0105270653" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0105270653";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0105270653";
};

# المستخدم 101: 0175668628
:do {
    /tool user-manager user add customer="admin" username="0175668628" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0175668628";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0175668628";
};

# المستخدم 102: 0112707552
:do {
    /tool user-manager user add customer="admin" username="0112707552" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0112707552";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0112707552";
};

# المستخدم 103: 0148193458
:do {
    /tool user-manager user add customer="admin" username="0148193458" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0148193458";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0148193458";
};

# المستخدم 104: 0124594386
:do {
    /tool user-manager user add customer="admin" username="0124594386" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0124594386";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0124594386";
};

# المستخدم 105: 0171606031
:do {
    /tool user-manager user add customer="admin" username="0171606031" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0171606031";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0171606031";
};

# المستخدم 106: 0104526659
:do {
    /tool user-manager user add customer="admin" username="0104526659" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0104526659";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0104526659";
};

# المستخدم 107: 0172879649
:do {
    /tool user-manager user add customer="admin" username="0172879649" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0172879649";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0172879649";
};

# المستخدم 108: 0175900972
:do {
    /tool user-manager user add customer="admin" username="0175900972" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0175900972";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0175900972";
};

# المستخدم 109: 0169360442
:do {
    /tool user-manager user add customer="admin" username="0169360442" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0169360442";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0169360442";
};

# المستخدم 110: 0101044433
:do {
    /tool user-manager user add customer="admin" username="0101044433" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0101044433";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0101044433";
};

# المستخدم 111: 0140357246
:do {
    /tool user-manager user add customer="admin" username="0140357246" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0140357246";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0140357246";
};

# المستخدم 112: 0154791443
:do {
    /tool user-manager user add customer="admin" username="0154791443" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0154791443";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0154791443";
};

# المستخدم 113: 0130531319
:do {
    /tool user-manager user add customer="admin" username="0130531319" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0130531319";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0130531319";
};

# المستخدم 114: 0197580566
:do {
    /tool user-manager user add customer="admin" username="0197580566" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0197580566";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0197580566";
};

# المستخدم 115: 0145182847
:do {
    /tool user-manager user add customer="admin" username="0145182847" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0145182847";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0145182847";
};

# المستخدم 116: 0141921415
:do {
    /tool user-manager user add customer="admin" username="0141921415" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0141921415";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0141921415";
};

# المستخدم 117: 0153592252
:do {
    /tool user-manager user add customer="admin" username="0153592252" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0153592252";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0153592252";
};

# المستخدم 118: 0114274492
:do {
    /tool user-manager user add customer="admin" username="0114274492" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0114274492";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0114274492";
};

# المستخدم 119: 0164117983
:do {
    /tool user-manager user add customer="admin" username="0164117983" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0164117983";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0164117983";
};

# المستخدم 120: 0156447754
:do {
    /tool user-manager user add customer="admin" username="0156447754" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0156447754";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0156447754";
};

# المستخدم 121: 0111177537
:do {
    /tool user-manager user add customer="admin" username="0111177537" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0111177537";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0111177537";
};

# المستخدم 122: 0130753141
:do {
    /tool user-manager user add customer="admin" username="0130753141" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0130753141";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0130753141";
};

# المستخدم 123: 0158867419
:do {
    /tool user-manager user add customer="admin" username="0158867419" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0158867419";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0158867419";
};

# المستخدم 124: 0102954620
:do {
    /tool user-manager user add customer="admin" username="0102954620" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0102954620";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0102954620";
};

# المستخدم 125: 0179522574
:do {
    /tool user-manager user add customer="admin" username="0179522574" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0179522574";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0179522574";
};

# المستخدم 126: 0162860502
:do {
    /tool user-manager user add customer="admin" username="0162860502" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0162860502";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0162860502";
};

# المستخدم 127: 0104936485
:do {
    /tool user-manager user add customer="admin" username="0104936485" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0104936485";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0104936485";
};

# المستخدم 128: 0145756875
:do {
    /tool user-manager user add customer="admin" username="0145756875" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0145756875";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0145756875";
};

# المستخدم 129: 0150311648
:do {
    /tool user-manager user add customer="admin" username="0150311648" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0150311648";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0150311648";
};

# المستخدم 130: 0136803664
:do {
    /tool user-manager user add customer="admin" username="0136803664" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0136803664";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0136803664";
};

# المستخدم 131: 0169628775
:do {
    /tool user-manager user add customer="admin" username="0169628775" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0169628775";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0169628775";
};

# المستخدم 132: 0193379521
:do {
    /tool user-manager user add customer="admin" username="0193379521" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0193379521";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0193379521";
};

# المستخدم 133: 0121157220
:do {
    /tool user-manager user add customer="admin" username="0121157220" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0121157220";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0121157220";
};

# المستخدم 134: 0144165573
:do {
    /tool user-manager user add customer="admin" username="0144165573" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0144165573";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0144165573";
};

# المستخدم 135: 0188577912
:do {
    /tool user-manager user add customer="admin" username="0188577912" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0188577912";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0188577912";
};

# المستخدم 136: 0183857469
:do {
    /tool user-manager user add customer="admin" username="0183857469" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0183857469";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0183857469";
};

# المستخدم 137: 0156410521
:do {
    /tool user-manager user add customer="admin" username="0156410521" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0156410521";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0156410521";
};

# المستخدم 138: 0171691659
:do {
    /tool user-manager user add customer="admin" username="0171691659" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0171691659";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0171691659";
};

# المستخدم 139: 0168913397
:do {
    /tool user-manager user add customer="admin" username="0168913397" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0168913397";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0168913397";
};

# المستخدم 140: 0167794064
:do {
    /tool user-manager user add customer="admin" username="0167794064" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0167794064";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0167794064";
};

# المستخدم 141: 0182190225
:do {
    /tool user-manager user add customer="admin" username="0182190225" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0182190225";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0182190225";
};

# المستخدم 142: 0197932057
:do {
    /tool user-manager user add customer="admin" username="0197932057" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0197932057";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0197932057";
};

# المستخدم 143: 0166896469
:do {
    /tool user-manager user add customer="admin" username="0166896469" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0166896469";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0166896469";
};

# المستخدم 144: 0125767531
:do {
    /tool user-manager user add customer="admin" username="0125767531" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0125767531";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0125767531";
};

# المستخدم 145: 0180511290
:do {
    /tool user-manager user add customer="admin" username="0180511290" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0180511290";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0180511290";
};

# المستخدم 146: 0194518646
:do {
    /tool user-manager user add customer="admin" username="0194518646" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0194518646";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0194518646";
};

# المستخدم 147: 0171683435
:do {
    /tool user-manager user add customer="admin" username="0171683435" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0171683435";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0171683435";
};

# المستخدم 148: 0157378277
:do {
    /tool user-manager user add customer="admin" username="0157378277" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0157378277";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0157378277";
};

# المستخدم 149: 0199253745
:do {
    /tool user-manager user add customer="admin" username="0199253745" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0199253745";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0199253745";
};

# المستخدم 150: 0152370266
:do {
    /tool user-manager user add customer="admin" username="0152370266" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0152370266";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0152370266";
};

# المستخدم 151: 0134503005
:do {
    /tool user-manager user add customer="admin" username="0134503005" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0134503005";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0134503005";
};

# المستخدم 152: 0128911948
:do {
    /tool user-manager user add customer="admin" username="0128911948" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0128911948";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0128911948";
};

# المستخدم 153: 0146606815
:do {
    /tool user-manager user add customer="admin" username="0146606815" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0146606815";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0146606815";
};

# المستخدم 154: 0109888654
:do {
    /tool user-manager user add customer="admin" username="0109888654" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0109888654";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0109888654";
};

# المستخدم 155: 0149421975
:do {
    /tool user-manager user add customer="admin" username="0149421975" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0149421975";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0149421975";
};

# المستخدم 156: 0104005561
:do {
    /tool user-manager user add customer="admin" username="0104005561" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0104005561";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0104005561";
};

# المستخدم 157: 0126193764
:do {
    /tool user-manager user add customer="admin" username="0126193764" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0126193764";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0126193764";
};

# المستخدم 158: 0136117179
:do {
    /tool user-manager user add customer="admin" username="0136117179" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0136117179";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0136117179";
};

# المستخدم 159: 0159978974
:do {
    /tool user-manager user add customer="admin" username="0159978974" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0159978974";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0159978974";
};

# المستخدم 160: 0123603707
:do {
    /tool user-manager user add customer="admin" username="0123603707" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0123603707";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0123603707";
};

# المستخدم 161: 0180946487
:do {
    /tool user-manager user add customer="admin" username="0180946487" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0180946487";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0180946487";
};

# المستخدم 162: 0176762253
:do {
    /tool user-manager user add customer="admin" username="0176762253" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0176762253";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0176762253";
};

# المستخدم 163: 0122542000
:do {
    /tool user-manager user add customer="admin" username="0122542000" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0122542000";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0122542000";
};

# المستخدم 164: 0142479869
:do {
    /tool user-manager user add customer="admin" username="0142479869" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0142479869";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0142479869";
};

# المستخدم 165: 0130086547
:do {
    /tool user-manager user add customer="admin" username="0130086547" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0130086547";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0130086547";
};

# المستخدم 166: 0159223770
:do {
    /tool user-manager user add customer="admin" username="0159223770" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0159223770";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0159223770";
};

# المستخدم 167: 0176139005
:do {
    /tool user-manager user add customer="admin" username="0176139005" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0176139005";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0176139005";
};

# المستخدم 168: 0123848977
:do {
    /tool user-manager user add customer="admin" username="0123848977" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0123848977";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0123848977";
};

# المستخدم 169: 0138588431
:do {
    /tool user-manager user add customer="admin" username="0138588431" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0138588431";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0138588431";
};

# المستخدم 170: 0153262174
:do {
    /tool user-manager user add customer="admin" username="0153262174" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0153262174";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0153262174";
};

# المستخدم 171: 0118844619
:do {
    /tool user-manager user add customer="admin" username="0118844619" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0118844619";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0118844619";
};

# المستخدم 172: 0179455092
:do {
    /tool user-manager user add customer="admin" username="0179455092" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0179455092";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0179455092";
};

# المستخدم 173: 0152562100
:do {
    /tool user-manager user add customer="admin" username="0152562100" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0152562100";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0152562100";
};

# المستخدم 174: 0159482236
:do {
    /tool user-manager user add customer="admin" username="0159482236" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0159482236";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0159482236";
};

# المستخدم 175: 0182459882
:do {
    /tool user-manager user add customer="admin" username="0182459882" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0182459882";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0182459882";
};

# المستخدم 176: 0178022450
:do {
    /tool user-manager user add customer="admin" username="0178022450" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0178022450";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0178022450";
};

# المستخدم 177: 0112924233
:do {
    /tool user-manager user add customer="admin" username="0112924233" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0112924233";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0112924233";
};

# المستخدم 178: 0151685619
:do {
    /tool user-manager user add customer="admin" username="0151685619" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0151685619";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0151685619";
};

# المستخدم 179: 0123464761
:do {
    /tool user-manager user add customer="admin" username="0123464761" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0123464761";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0123464761";
};

# المستخدم 180: 0117616222
:do {
    /tool user-manager user add customer="admin" username="0117616222" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0117616222";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0117616222";
};

# المستخدم 181: 0111225764
:do {
    /tool user-manager user add customer="admin" username="0111225764" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0111225764";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0111225764";
};

# المستخدم 182: 0137349998
:do {
    /tool user-manager user add customer="admin" username="0137349998" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0137349998";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0137349998";
};

# المستخدم 183: 0195351667
:do {
    /tool user-manager user add customer="admin" username="0195351667" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0195351667";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0195351667";
};

# المستخدم 184: 0194636877
:do {
    /tool user-manager user add customer="admin" username="0194636877" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0194636877";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0194636877";
};

# المستخدم 185: 0148836636
:do {
    /tool user-manager user add customer="admin" username="0148836636" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0148836636";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0148836636";
};

# المستخدم 186: 0132694382
:do {
    /tool user-manager user add customer="admin" username="0132694382" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0132694382";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0132694382";
};

# المستخدم 187: 0169345746
:do {
    /tool user-manager user add customer="admin" username="0169345746" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0169345746";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0169345746";
};

# المستخدم 188: 0153150128
:do {
    /tool user-manager user add customer="admin" username="0153150128" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0153150128";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0153150128";
};

# المستخدم 189: 0173406950
:do {
    /tool user-manager user add customer="admin" username="0173406950" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0173406950";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0173406950";
};

# المستخدم 190: 0100029530
:do {
    /tool user-manager user add customer="admin" username="0100029530" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0100029530";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0100029530";
};

# المستخدم 191: 0152396561
:do {
    /tool user-manager user add customer="admin" username="0152396561" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0152396561";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0152396561";
};

# المستخدم 192: 0187492473
:do {
    /tool user-manager user add customer="admin" username="0187492473" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0187492473";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0187492473";
};

# المستخدم 193: 0194141165
:do {
    /tool user-manager user add customer="admin" username="0194141165" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0194141165";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0194141165";
};

# المستخدم 194: 0107513962
:do {
    /tool user-manager user add customer="admin" username="0107513962" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0107513962";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0107513962";
};

# المستخدم 195: 0152212774
:do {
    /tool user-manager user add customer="admin" username="0152212774" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0152212774";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0152212774";
};

# المستخدم 196: 0147162060
:do {
    /tool user-manager user add customer="admin" username="0147162060" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0147162060";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0147162060";
};

# المستخدم 197: 0170601277
:do {
    /tool user-manager user add customer="admin" username="0170601277" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0170601277";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0170601277";
};

# المستخدم 198: 0145725974
:do {
    /tool user-manager user add customer="admin" username="0145725974" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0145725974";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0145725974";
};

# المستخدم 199: 0189095357
:do {
    /tool user-manager user add customer="admin" username="0189095357" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0189095357";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0189095357";
};

# المستخدم 200: 0106166900
:do {
    /tool user-manager user add customer="admin" username="0106166900" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0106166900";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0106166900";
};


:put "📊 تم الانتهاء من النسخة الاحتياطية";
:put "✅ نجح: $success كرت";
:put "❌ فشل: $errors كرت";
:put "📈 الإجمالي: $total كرت";

:put "🎉 تم استعادة جميع كروت القالب: 10";
:put "💡 النسخة الاحتياطية مكتملة بنجاح!";
