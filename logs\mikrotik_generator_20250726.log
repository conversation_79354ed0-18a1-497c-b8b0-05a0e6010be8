2025-07-26 00:12:11,926 - INFO - تم بدء تشغيل التطبيق
2025-07-26 00:12:11,970 - INFO - تم إنشاء المجلدات الأساسية
2025-07-26 00:12:12,036 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-26 00:12:12,069 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-26 00:12:13,328 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-26 00:12:30,652 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-26 00:12:30,652 - INFO - ت<PERSON> <PERSON>عداد التطبيق بنجاح
2025-07-26 00:12:33,136 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-26 00:12:35,387 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-26 00:12:35,387 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-26 00:12:35,712 - INFO - معالجة callback: select_system_um
2025-07-26 00:12:35,950 - ERROR - خطأ في الرد على callback query: HTTP Error 400: Bad Request
2025-07-26 00:12:36,253 - INFO - تم إرسال 2 قالب مفلتر لنظام User Manager
2025-07-26 00:12:38,012 - INFO - معالجة callback: independent_template_um_10
2025-07-26 00:12:38,395 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-26 00:12:38,458 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-26 00:12:38,732 - INFO - 🔄 تبديل النظام تلقائياً من None إلى user_manager
2025-07-26 00:12:38,732 - INFO - 🔄 طلب تبديل النظام من التلجرام: None → user_manager
2025-07-26 00:12:38,761 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-26 00:12:38,780 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-26 00:12:39,085 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-26 00:12:40,075 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-26 00:12:40,187 - WARNING - تحذير: فشل في حفظ الإعدادات: 'MikroTikCardGenerator' object has no attribute 'version_combo'
2025-07-26 00:12:40,190 - INFO - تم تعيين النظام الجديد: user_manager
2025-07-26 00:12:40,191 - INFO - 🔄 إعادة بناء الواجهة للنظام: user_manager
2025-07-26 00:12:40,197 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-26 00:12:40,199 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-26 00:12:44,080 - INFO - تم إعداد تبويب عرض الكروت لـ User Manager بنجاح
2025-07-26 00:12:44,646 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-26 00:12:44,647 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-26 00:12:45,078 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام user_manager
2025-07-26 00:12:45,078 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-26 00:12:46,029 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager
2025-07-26 00:12:46,104 - INFO - تم تسجيل العملية: تبديل النظام من التلجرام - تم تبديل النظام من None إلى user_manager
2025-07-26 00:12:46,105 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-26 00:12:46,106 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-26 00:12:46,106 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-26 00:12:47,444 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-26 00:12:47,445 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-26 00:12:47,501 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-26 00:12:47,508 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-26 00:12:47,513 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-26 00:12:47,518 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-26 00:12:47,521 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-26 00:12:47,522 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager - القالب: 10
2025-07-26 00:12:47,782 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-26 00:12:50,734 - INFO - معالجة callback: independent_create_um_10_lightning
2025-07-26 00:12:51,096 - INFO - 🔍 بدء اختبار جلب العدد الحالي للكروت قبل إرسال الإشعار
2025-07-26 00:12:51,096 - INFO - 🧪 بدء اختبار الاتصال وجلب عدد User Manager
2025-07-26 00:12:51,097 - INFO - محاولة الاتصال بـ 6.6.6.100:8728 (عادي)
2025-07-26 00:12:51,171 - INFO - نجح الاتصال مع 6.6.6.100
2025-07-26 00:12:51,176 - INFO - ✅ نجح الاتصال - اختبار جلب العدد...
2025-07-26 00:12:51,215 - INFO - 🏷️ اسم النظام: MikroTik
2025-07-26 00:12:51,259 - INFO - 📋 إصدار RouterOS: 6.43.12 (stable)
2025-07-26 00:12:51,259 - INFO - 📊 اختبار جلب مستخدمي User Manager...
2025-07-26 00:13:00,521 - INFO - ✅ تم جلب 4659 مستخدم من User Manager
2025-07-26 00:13:00,522 - INFO - 👥 عرض أول 5 مستخدمين:
2025-07-26 00:13:00,523 - INFO -   1. غير محدد (البروفايل: غير محدد)
2025-07-26 00:13:00,523 - INFO -   2. غير محدد (البروفايل: غير محدد)
2025-07-26 00:13:00,523 - INFO -   3. غير محدد (البروفايل: غير محدد)
2025-07-26 00:13:00,523 - INFO -   4. غير محدد (البروفايل: غير محدد)
2025-07-26 00:13:00,524 - INFO -   5. غير محدد (البروفايل: غير محدد)
2025-07-26 00:13:00,524 - ERROR - ❌ خطأ في API: 'RouterOsApi' object has no attribute 'disconnect'
2025-07-26 00:13:00,528 - INFO - 📊 نتيجة الاختبار: خطأ في API: 'RouterOsApi' object has no attribute 'disconnect'
2025-07-26 00:13:00,528 - INFO - 🔔 بدء إرسال إشعار اختيار القالب: 10 (User Manager)
2025-07-26 00:13:00,529 - INFO - 📊 جلب العدد الحالي من User Manager...
2025-07-26 00:13:00,530 - INFO - 🔍 بدء جلب العدد الحالي للكروت في User Manager
2025-07-26 00:13:00,530 - INFO - ✅ مكتبة routeros_api متوفرة
2025-07-26 00:13:00,531 - INFO - 🔗 محاولة الاتصال بـ 6.6.6.100 باستخدام المستخدم: 11
2025-07-26 00:13:00,534 - INFO - استخدام الاتصال الحالي
2025-07-26 00:13:00,535 - INFO - ✅ تم الاتصال بـ MikroTik بنجاح
2025-07-26 00:13:00,537 - INFO - 📊 جلب قائمة المستخدمين من User Manager...
2025-07-26 00:13:09,785 - INFO - ✅ تم جلب 4659 مستخدم من User Manager بنجاح
2025-07-26 00:13:09,785 - INFO - 👤 مستخدم 1: غير محدد
2025-07-26 00:13:09,785 - INFO - 👤 مستخدم 2: غير محدد
2025-07-26 00:13:09,786 - INFO - 👤 مستخدم 3: غير محدد
2025-07-26 00:13:09,790 - INFO - 📈 العدد المجلوب من User Manager: 4659
2025-07-26 00:13:10,132 - INFO - ✅ تم إرسال إشعار اختيار القالب '10' - العدد الحالي: 4659
2025-07-26 00:27:01,319 - ERROR - خطأ في معالجة تحديثات التلجرام: HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot5161769536:AAHFeXIB-kCvIfo_NwfR7dwiWOUgXJF-p-Y/getUpdates?offset=858841610&timeout=10 (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000194CFE08550>: Failed to establish a new connection: [WinError 10051] A socket operation was attempted to an unreachable network'))
2025-07-26 00:40:40,676 - INFO - تم بدء تشغيل التطبيق
2025-07-26 00:40:41,289 - INFO - تم إنشاء المجلدات الأساسية
2025-07-26 00:40:41,296 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-26 00:40:41,467 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-26 00:40:42,077 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-26 00:41:03,530 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-26 00:41:03,531 - INFO - تم إعداد التطبيق بنجاح
2025-07-26 00:41:05,720 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-26 00:41:06,668 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-26 00:41:06,669 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-26 00:41:09,682 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-26 00:41:09,752 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-26 00:41:10,087 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-26 00:41:10,087 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-26 00:42:55,566 - INFO - معالجة callback: select_system_um
2025-07-26 00:42:56,127 - INFO - تم إرسال 2 قالب مفلتر لنظام User Manager
2025-07-26 00:42:59,422 - INFO - معالجة callback: independent_template_um_10
2025-07-26 00:42:59,900 - INFO - 🔄 تبديل النظام تلقائياً من None إلى user_manager
2025-07-26 00:42:59,901 - INFO - 🔄 طلب تبديل النظام من التلجرام: None → user_manager
2025-07-26 00:43:00,155 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-26 00:43:00,299 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-26 00:43:00,400 - WARNING - تحذير: فشل في حفظ الإعدادات: 'MikroTikCardGenerator' object has no attribute 'version_combo'
2025-07-26 00:43:00,400 - INFO - تم تعيين النظام الجديد: user_manager
2025-07-26 00:43:00,401 - INFO - 🔄 إعادة بناء الواجهة للنظام: user_manager
2025-07-26 00:43:00,402 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-26 00:43:00,409 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-26 00:43:01,490 - INFO - تم إعداد تبويب عرض الكروت لـ User Manager بنجاح
2025-07-26 00:43:01,754 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-26 00:43:01,755 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-26 00:43:02,169 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام user_manager
2025-07-26 00:43:02,171 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-26 00:43:02,422 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager
2025-07-26 00:43:02,470 - INFO - تم تسجيل العملية: تبديل النظام من التلجرام - تم تبديل النظام من None إلى user_manager
2025-07-26 00:43:02,483 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-26 00:43:02,489 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-26 00:43:02,503 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-26 00:43:03,034 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-26 00:43:03,035 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-26 00:43:03,063 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-26 00:43:03,064 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-26 00:43:03,066 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-26 00:43:03,072 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-26 00:43:03,086 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-26 00:43:03,087 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager - القالب: 10
2025-07-26 00:43:03,406 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-26 00:43:13,316 - INFO - معالجة callback: independent_create_um_10_lightning
2025-07-26 00:43:13,633 - INFO - 🔍 بدء اختبار جلب العدد الحالي للكروت قبل إرسال الإشعار
2025-07-26 00:43:13,634 - INFO - 🧪 بدء اختبار الاتصال وجلب عدد User Manager
2025-07-26 00:43:13,634 - INFO - محاولة الاتصال بـ 6.6.6.100:8728 (عادي)
2025-07-26 00:43:13,705 - INFO - نجح الاتصال مع 6.6.6.100
2025-07-26 00:43:13,706 - INFO - ✅ نجح الاتصال - اختبار جلب العدد...
2025-07-26 00:43:13,740 - INFO - 🏷️ اسم النظام: MikroTik
2025-07-26 00:43:13,784 - INFO - 📋 إصدار RouterOS: 6.43.12 (stable)
2025-07-26 00:43:13,785 - INFO - 📊 اختبار جلب مستخدمي User Manager...
2025-07-26 00:43:23,025 - INFO - ✅ تم جلب 4659 مستخدم من User Manager
2025-07-26 00:43:23,026 - INFO - 👥 عرض أول 5 مستخدمين:
2025-07-26 00:43:23,027 - INFO -   1. غير محدد (البروفايل: غير محدد)
2025-07-26 00:43:23,027 - INFO -   2. غير محدد (البروفايل: غير محدد)
2025-07-26 00:43:23,027 - INFO -   3. غير محدد (البروفايل: غير محدد)
2025-07-26 00:43:23,027 - INFO -   4. غير محدد (البروفايل: غير محدد)
2025-07-26 00:43:23,028 - INFO -   5. غير محدد (البروفايل: غير محدد)
2025-07-26 00:43:23,028 - ERROR - ❌ خطأ في API: 'RouterOsApi' object has no attribute 'disconnect'
2025-07-26 00:43:23,032 - INFO - 📊 نتيجة الاختبار: خطأ في API: 'RouterOsApi' object has no attribute 'disconnect'
2025-07-26 00:43:23,040 - INFO - 🔔 بدء إرسال إشعار اختيار القالب: 10 (User Manager)
2025-07-26 00:43:23,040 - INFO - 📊 جلب العدد الحالي من User Manager...
2025-07-26 00:43:23,041 - INFO - 🔍 بدء جلب العدد الحالي للكروت في User Manager
2025-07-26 00:43:23,041 - INFO - ✅ مكتبة routeros_api متوفرة
2025-07-26 00:43:23,041 - INFO - 🔗 محاولة الاتصال بـ 6.6.6.100 باستخدام المستخدم: 11
2025-07-26 00:43:23,045 - INFO - استخدام الاتصال الحالي
2025-07-26 00:43:23,045 - INFO - ✅ تم الاتصال بـ MikroTik بنجاح
2025-07-26 00:43:23,045 - INFO - 📊 جلب قائمة المستخدمين من User Manager...
2025-07-26 00:43:32,303 - INFO - ✅ تم جلب 4659 مستخدم من User Manager بنجاح
2025-07-26 00:43:32,305 - INFO - 👤 مستخدم 1: غير محدد
2025-07-26 00:43:32,306 - INFO - 👤 مستخدم 2: غير محدد
2025-07-26 00:43:32,306 - INFO - 👤 مستخدم 3: غير محدد
2025-07-26 00:43:32,309 - INFO - 📈 العدد المجلوب من User Manager: 4659
2025-07-26 00:43:32,641 - INFO - ✅ تم إرسال إشعار اختيار القالب '10' - العدد الحالي: 4659
2025-07-26 00:43:46,194 - INFO - معالجة callback: independent_count_um_10_lightning_200
2025-07-26 00:43:46,809 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-26 00:43:46,810 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-26 00:43:46,825 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-26 00:43:46,826 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-26 00:43:46,863 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-26 00:43:46,865 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-26 00:43:46,867 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-26 00:43:46,873 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-26 00:43:47,443 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-26 00:43:47,444 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-26 00:43:47,470 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-26 00:43:47,471 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-26 00:43:47,526 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-26 00:43:47,527 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-26 00:43:47,530 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-26 00:43:47,535 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-26 00:43:47,551 - INFO - ⚡ البرق: تم الحفاظ على العدد المحدد: 200
2025-07-26 00:43:47,552 - INFO - تم تطبيق إعدادات القالب '10' بنجاح
2025-07-26 00:43:47,553 - INFO - ⚡ استخدام البرق للتلجرام (مع جدولة مؤقتة وتنظيف تلقائي)
2025-07-26 00:43:47,556 - INFO - ⚡ بدء البرق التلقائي للتلجرام (مع جدولة مؤقتة وتنظيف تلقائي)
2025-07-26 00:43:47,556 - INFO - ⚡ البرق: استخدام العدد من التلجرام بوت: 200
2025-07-26 00:43:47,556 - INFO - ⚡ البرق للتلجرام: العدد المطلوب 200 حساب
2025-07-26 00:43:47,557 - INFO - ⚡ البرق: تم تحديث عدد الحسابات في الواجهة إلى: 200
2025-07-26 00:43:47,560 - INFO - ⚡ البرق للتلجرام: تم توليد 200 حساب
2025-07-26 00:43:47,560 - INFO - ⚡ البرق للتلجرام: تنفيذ متسلسل مجدول لـ 200 كارت (حد التقسيم: 100)
2025-07-26 00:43:47,562 - INFO - ⚡ البرق للتلجرام: بدء معالجة الأعداد الكبيرة مع جدولة واحدة
2025-07-26 00:43:47,562 - INFO - ⚡ البرق للتلجرام: تم تقسيم 200 كارت إلى 2 مجموعة للتنفيذ المتسلسل
2025-07-26 00:43:47,562 - INFO - ⚡ البرق للتلجرام: إنشاء السكريبت للمجموعة 1/2 (100 كارت)
2025-07-26 00:43:47,563 - INFO - ⚡ البرق للتلجرام: تم إعداد السكريبت telegram_lightning_batch1_user_manager_20250726_004347
2025-07-26 00:43:47,564 - INFO - ⚡ البرق للتلجرام: إنشاء السكريبت للمجموعة 2/2 (100 كارت)
2025-07-26 00:43:47,565 - INFO - ⚡ البرق للتلجرام: تم إعداد السكريبت telegram_lightning_batch2_user_manager_20250726_004347
2025-07-26 00:43:47,573 - INFO - استخدام الاتصال الحالي
2025-07-26 00:43:47,701 - INFO - ⚡ تم إرسال السكريبت المترابط: telegram_lightning_batch1_user_manager_20250726_004347
2025-07-26 00:43:47,748 - INFO - ⚡ تم إرسال السكريبت المترابط: telegram_lightning_batch2_user_manager_20250726_004347
2025-07-26 00:43:47,751 - INFO - ⚡ وقت MikroTik الحالي: 21:43:47
2025-07-26 00:43:47,884 - INFO - ⚡ السكريبت الأول سينفذ في: 21:43:50 (بعد 3 ثواني من وقت MikroTik)
2025-07-26 00:43:47,941 - INFO - ⚡ تم إنشاء الجدولة الرئيسية: telegram_main_schedule_20250726_004347 لتشغيل telegram_lightning_batch1_user_manager_20250726_004347 في 21:43:50
2025-07-26 00:43:47,946 - INFO - ⚡ تم إعداد البرق المترابط بنجاح - جدولة واحدة: telegram_main_schedule_20250726_004347, 2 سكريبت مترابط
2025-07-26 00:43:47,946 - INFO - ⚡ البرق للتلجرام مكتمل: تم إنشاء جدولة واحدة لـ 2 مجموعة متسلسلة (200 كارت)
2025-07-26 00:43:50,858 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 200
2025-07-26 00:43:51,060 - INFO - تم إنشاء PDF بنجاح: exports/كروت_(10)_ب20-200 كارت-26-07-2025-00-43-50-um.pdf
2025-07-26 00:43:51,063 - INFO - تم حفظ ملف .rsc الاحتياطي: exports/كروت_(10)_ب20-200 كارت-26-07-2025-00-43-50-um.rsc
2025-07-26 00:43:52,695 - INFO - تم إرسال الملف بنجاح: كروت_(10)_ب20-200 كارت-26-07-2025-00-43-50-um.pdf
2025-07-26 00:43:52,937 - INFO - تم إرسال 200 كرت عبر التلجرام باستخدام قالب 10
2025-07-26 01:31:01,528 - INFO - تم بدء تشغيل التطبيق
2025-07-26 01:31:01,624 - INFO - تم إنشاء المجلدات الأساسية
2025-07-26 01:31:01,689 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-26 01:31:01,693 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-26 01:31:01,900 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-26 01:31:16,136 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-26 01:31:16,137 - INFO - تم إعداد التطبيق بنجاح
2025-07-26 01:31:18,191 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-26 01:31:18,845 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-26 01:31:18,846 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-26 01:31:21,858 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-26 01:31:21,947 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-26 01:31:22,223 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-26 01:31:22,224 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-26 01:31:26,876 - INFO - معالجة callback: select_system_um
2025-07-26 01:31:27,403 - INFO - تم إرسال 2 قالب مفلتر لنظام User Manager
2025-07-26 01:31:28,853 - INFO - معالجة callback: independent_template_um_10
2025-07-26 01:31:29,377 - INFO - 🔄 تبديل النظام تلقائياً من None إلى user_manager
2025-07-26 01:31:29,378 - INFO - 🔄 طلب تبديل النظام من التلجرام: None → user_manager
2025-07-26 01:31:29,655 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-26 01:31:29,806 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-26 01:31:29,907 - WARNING - تحذير: فشل في حفظ الإعدادات: 'MikroTikCardGenerator' object has no attribute 'version_combo'
2025-07-26 01:31:29,907 - INFO - تم تعيين النظام الجديد: user_manager
2025-07-26 01:31:29,908 - INFO - 🔄 إعادة بناء الواجهة للنظام: user_manager
2025-07-26 01:31:29,908 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-26 01:31:29,912 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-26 01:31:30,726 - INFO - تم إعداد تبويب عرض الكروت لـ User Manager بنجاح
2025-07-26 01:31:30,974 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-26 01:31:30,975 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-26 01:31:31,266 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام user_manager
2025-07-26 01:31:31,268 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-26 01:31:31,366 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager
2025-07-26 01:31:31,521 - INFO - تم تسجيل العملية: تبديل النظام من التلجرام - تم تبديل النظام من None إلى user_manager
2025-07-26 01:31:31,523 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-26 01:31:31,523 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-26 01:31:31,524 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-26 01:31:32,189 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-26 01:31:32,190 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-26 01:31:32,217 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-26 01:31:32,219 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-26 01:31:32,222 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-26 01:31:32,240 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-26 01:31:32,245 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-26 01:31:32,247 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager - القالب: 10
2025-07-26 01:31:32,529 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-26 01:31:36,273 - INFO - معالجة callback: independent_create_um_10_lightning
2025-07-26 01:31:36,514 - INFO - 🔍 بدء اختبار جلب العدد الحالي للكروت قبل إرسال الإشعار
2025-07-26 01:31:36,515 - INFO - 🧪 بدء اختبار الاتصال وجلب عدد User Manager
2025-07-26 01:31:36,515 - INFO - محاولة الاتصال بـ 6.6.6.100:8728 (عادي)
2025-07-26 01:31:36,581 - INFO - نجح الاتصال مع 6.6.6.100
2025-07-26 01:31:36,582 - INFO - ✅ نجح الاتصال - اختبار جلب العدد...
2025-07-26 01:31:36,621 - INFO - 🏷️ اسم النظام: MikroTik
2025-07-26 01:31:36,665 - INFO - 📋 إصدار RouterOS: 6.43.12 (stable)
2025-07-26 01:31:36,666 - INFO - 📊 اختبار جلب مستخدمي User Manager...
2025-07-26 01:31:46,434 - INFO - ✅ تم جلب 4859 مستخدم من User Manager
2025-07-26 01:31:46,435 - INFO - 👥 عرض أول 5 مستخدمين:
2025-07-26 01:31:46,436 - INFO -   1. غير محدد (البروفايل: غير محدد)
2025-07-26 01:31:46,437 - INFO -   2. غير محدد (البروفايل: غير محدد)
2025-07-26 01:31:46,437 - INFO -   3. غير محدد (البروفايل: غير محدد)
2025-07-26 01:31:46,437 - INFO -   4. غير محدد (البروفايل: غير محدد)
2025-07-26 01:31:46,438 - INFO -   5. غير محدد (البروفايل: غير محدد)
2025-07-26 01:31:46,438 - ERROR - ❌ خطأ في API: 'RouterOsApi' object has no attribute 'disconnect'
2025-07-26 01:31:46,442 - INFO - 📊 نتيجة الاختبار: خطأ في API: 'RouterOsApi' object has no attribute 'disconnect'
2025-07-26 01:31:46,442 - INFO - 🔔 بدء إرسال إشعار اختيار القالب: 10 (User Manager)
2025-07-26 01:31:46,443 - INFO - 📊 جلب العدد الحالي من User Manager...
2025-07-26 01:31:46,445 - INFO - 🔍 بدء جلب العدد الحالي للكروت في User Manager
2025-07-26 01:31:46,446 - INFO - ✅ مكتبة routeros_api متوفرة
2025-07-26 01:31:46,446 - INFO - 🔗 محاولة الاتصال بـ 6.6.6.100 باستخدام المستخدم: 11
2025-07-26 01:31:46,450 - INFO - استخدام الاتصال الحالي
2025-07-26 01:31:46,465 - INFO - ✅ تم الاتصال بـ MikroTik بنجاح
2025-07-26 01:31:46,466 - INFO - 📊 جلب قائمة المستخدمين من User Manager...
2025-07-26 01:31:56,208 - INFO - ✅ تم جلب 4859 مستخدم من User Manager بنجاح
2025-07-26 01:31:56,208 - INFO - 👤 مستخدم 1: غير محدد
2025-07-26 01:31:56,209 - INFO - 👤 مستخدم 2: غير محدد
2025-07-26 01:31:56,209 - INFO - 👤 مستخدم 3: غير محدد
2025-07-26 01:31:56,213 - INFO - 📈 العدد المجلوب من User Manager: 4859
2025-07-26 01:31:56,548 - INFO - ✅ تم إرسال إشعار اختيار القالب '10' - العدد الحالي: 4859
2025-07-26 01:32:28,211 - INFO - بدء إغلاق التطبيق
2025-07-26 01:32:28,212 - INFO - تم قطع الاتصال مع MikroTik
2025-07-26 01:32:28,223 - INFO - تم تسجيل العملية: إغلاق التطبيق - تم إغلاق التطبيق بنجاح
2025-07-26 01:32:28,226 - INFO - تم إغلاق التطبيق بنجاح
2025-07-26 01:34:15,108 - INFO - تم بدء تشغيل التطبيق
2025-07-26 01:34:15,210 - INFO - تم إنشاء المجلدات الأساسية
2025-07-26 01:34:15,213 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-26 01:34:15,248 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-26 01:34:15,351 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-26 01:34:15,956 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-26 01:34:15,958 - INFO - تم إعداد التطبيق بنجاح
2025-07-26 01:34:17,973 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-26 01:34:18,369 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-26 01:34:18,387 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-26 01:34:18,605 - INFO - معالجة callback: independent_count_um_10_lightning_50
2025-07-26 01:34:18,820 - ERROR - خطأ في الرد على callback query: HTTP Error 400: Bad Request
2025-07-26 01:34:18,822 - INFO - 🔄 تبديل النظام تلقائياً لإنشاء الكروت: None → user_manager
2025-07-26 01:34:18,822 - INFO - 🔄 طلب تبديل النظام من التلجرام: None → user_manager
2025-07-26 01:34:19,075 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-26 01:34:19,102 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-26 01:34:19,203 - WARNING - تحذير: فشل في حفظ الإعدادات: 'MikroTikCardGenerator' object has no attribute 'version_combo'
2025-07-26 01:34:19,203 - INFO - تم تعيين النظام الجديد: user_manager
2025-07-26 01:34:19,204 - INFO - 🔄 إعادة بناء الواجهة للنظام: user_manager
2025-07-26 01:34:19,205 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-26 01:34:19,208 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-26 01:34:19,696 - INFO - تم إعداد تبويب عرض الكروت لـ User Manager بنجاح
2025-07-26 01:34:19,877 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-26 01:34:19,877 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-26 01:34:20,002 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام user_manager
2025-07-26 01:34:20,004 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-26 01:34:20,087 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager
2025-07-26 01:34:20,095 - INFO - تم تسجيل العملية: تبديل النظام من التلجرام - تم تبديل النظام من None إلى user_manager
2025-07-26 01:34:20,376 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-26 01:34:20,377 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-26 01:34:20,432 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-26 01:34:20,433 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-26 01:34:20,461 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-26 01:34:20,468 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-26 01:34:20,471 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-26 01:34:20,481 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-26 01:34:20,739 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-26 01:34:20,740 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-26 01:34:20,754 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-26 01:34:20,756 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-26 01:34:20,795 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-26 01:34:20,804 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-26 01:34:20,807 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-26 01:34:20,811 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-26 01:34:20,812 - INFO - ⚡ البرق: تم الحفاظ على العدد المحدد: 200
2025-07-26 01:34:20,813 - INFO - تم تطبيق إعدادات القالب '10' بنجاح
2025-07-26 01:34:20,813 - INFO - ⚡ استخدام البرق للتلجرام (مع جدولة مؤقتة وتنظيف تلقائي)
2025-07-26 01:34:20,814 - INFO - ⚡ بدء البرق التلقائي للتلجرام (مع جدولة مؤقتة وتنظيف تلقائي)
2025-07-26 01:34:20,814 - INFO - ⚡ البرق: استخدام العدد من التلجرام بوت: 50
2025-07-26 01:34:20,814 - INFO - ⚡ البرق للتلجرام: العدد المطلوب 50 حساب
2025-07-26 01:34:20,815 - INFO - ⚡ البرق: تم تحديث عدد الحسابات في الواجهة إلى: 50
2025-07-26 01:34:20,816 - INFO - ⚡ البرق للتلجرام: تم توليد 50 حساب
2025-07-26 01:34:20,830 - INFO - ⚡ البرق للتلجرام: تنفيذ مجدول لـ 50 كارت (حد التقسيم: 100)
2025-07-26 01:34:20,863 - INFO - ⚡ البرق للتلجرام: تخطي حفظ الملفات المحلية - استخدام الجدولة المؤقتة
2025-07-26 01:34:20,865 - INFO - محاولة الاتصال بـ 6.6.6.100:8728 (عادي)
2025-07-26 01:34:20,934 - INFO - نجح الاتصال مع 6.6.6.100
2025-07-26 01:34:21,194 - INFO - ⚡ تم إضافة السكريبت: telegram_lightning_user_manager_20250726_013420
2025-07-26 01:34:21,234 - INFO - ⚡ وقت MikroTik الحالي: 22:34:20
2025-07-26 01:34:21,258 - INFO - ⚡ سيتم تنفيذ السكريبت في: 22:34:23 (بعد 3 ثواني من وقت MikroTik)
2025-07-26 01:34:21,316 - INFO - ⚡ تم إنشاء الجدولة المؤقتة: telegram_schedule_20250726_013420 للتنفيذ في 22:34:23
2025-07-26 01:34:21,332 - INFO - ⚡ تم إعداد البرق المجدول للتلجرام بنجاح - السكريبت: telegram_lightning_user_manager_20250726_013420, الجدولة: telegram_schedule_20250726_013420
2025-07-26 01:34:21,332 - INFO - ⚡ سيتم تنفيذ السكريبت في 22:34:23 مع تنظيف تلقائي بعد الانتهاء
2025-07-26 01:34:21,332 - INFO - ⚡ البرق للتلجرام مكتمل: تم جدولة إنشاء وإرسال 50 كارت بنجاح (مع تنظيف تلقائي)
2025-07-26 01:34:21,396 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-26 01:34:21,398 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-26 01:34:21,638 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-26 01:34:21,638 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-26 01:34:23,956 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 50
2025-07-26 01:34:24,047 - INFO - تم إنشاء PDF بنجاح: exports/كروت_(10)_ب20-50 كارت-26-07-2025-01-34-23-um.pdf
2025-07-26 01:34:24,062 - INFO - تم حفظ ملف .rsc الاحتياطي: exports/كروت_(10)_ب20-50 كارت-26-07-2025-01-34-23-um.rsc
2025-07-26 01:34:25,826 - INFO - تم إرسال الملف بنجاح: كروت_(10)_ب20-50 كارت-26-07-2025-01-34-23-um.pdf
2025-07-26 01:34:26,058 - INFO - تم إرسال 50 كرت عبر التلجرام باستخدام قالب 10
2025-07-26 01:35:08,704 - INFO - معالجة callback: select_system_um
2025-07-26 01:35:09,183 - INFO - تم إرسال 2 قالب مفلتر لنظام User Manager
2025-07-26 01:35:11,891 - INFO - معالجة callback: independent_template_um_10
2025-07-26 01:35:12,379 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-26 01:35:12,380 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-26 01:35:12,380 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-26 01:35:12,388 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-26 01:35:12,409 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-26 01:35:12,440 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-26 01:35:12,440 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-26 01:35:12,444 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-26 01:35:12,461 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-26 01:35:12,464 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-26 01:35:12,466 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager - القالب: 10
2025-07-26 01:35:12,724 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-26 01:35:14,555 - INFO - معالجة callback: independent_create_um_10_lightning
2025-07-26 01:35:14,786 - INFO - 🔍 بدء اختبار جلب العدد الحالي للكروت قبل إرسال الإشعار
2025-07-26 01:35:14,786 - INFO - 🧪 بدء اختبار الاتصال وجلب عدد User Manager
2025-07-26 01:35:14,789 - INFO - استخدام الاتصال الحالي
2025-07-26 01:35:14,790 - INFO - ✅ نجح الاتصال - اختبار جلب العدد...
2025-07-26 01:35:14,825 - INFO - 🏷️ اسم النظام: MikroTik
2025-07-26 01:35:14,868 - INFO - 📋 إصدار RouterOS: 6.43.12 (stable)
2025-07-26 01:35:14,869 - INFO - 📊 اختبار جلب مستخدمي User Manager...
2025-07-26 01:35:24,659 - INFO - ✅ تم جلب 4909 مستخدم من User Manager
2025-07-26 01:35:24,661 - INFO - 👥 عرض أول 5 مستخدمين:
2025-07-26 01:35:24,662 - INFO -   1. غير محدد (البروفايل: غير محدد)
2025-07-26 01:35:24,662 - INFO -   2. غير محدد (البروفايل: غير محدد)
2025-07-26 01:35:24,663 - INFO -   3. غير محدد (البروفايل: غير محدد)
2025-07-26 01:35:24,663 - INFO -   4. غير محدد (البروفايل: غير محدد)
2025-07-26 01:35:24,663 - INFO -   5. غير محدد (البروفايل: غير محدد)
2025-07-26 01:35:24,664 - ERROR - ❌ خطأ في API: 'RouterOsApi' object has no attribute 'disconnect'
2025-07-26 01:35:24,670 - INFO - 📊 نتيجة الاختبار: خطأ في API: 'RouterOsApi' object has no attribute 'disconnect'
2025-07-26 01:35:24,671 - INFO - 🔔 بدء إرسال إشعار اختيار القالب: 10 (User Manager)
2025-07-26 01:35:24,685 - INFO - 📊 جلب العدد الحالي من User Manager...
2025-07-26 01:35:24,686 - INFO - 🔍 بدء جلب العدد الحالي للكروت في User Manager
2025-07-26 01:35:24,686 - INFO - ✅ مكتبة routeros_api متوفرة
2025-07-26 01:35:24,687 - INFO - 🔗 محاولة الاتصال بـ 6.6.6.100 باستخدام المستخدم: 11
2025-07-26 01:35:24,690 - INFO - استخدام الاتصال الحالي
2025-07-26 01:35:24,690 - INFO - ✅ تم الاتصال بـ MikroTik بنجاح
2025-07-26 01:35:24,692 - INFO - 📊 جلب قائمة المستخدمين من User Manager...
2025-07-26 01:35:34,482 - INFO - ✅ تم جلب 4909 مستخدم من User Manager بنجاح
2025-07-26 01:35:34,482 - INFO - 👤 مستخدم 1: غير محدد
2025-07-26 01:35:34,483 - INFO - 👤 مستخدم 2: غير محدد
2025-07-26 01:35:34,483 - INFO - 👤 مستخدم 3: غير محدد
2025-07-26 01:35:34,487 - INFO - 📈 العدد المجلوب من User Manager: 4909
2025-07-26 01:35:34,791 - INFO - ✅ تم إرسال إشعار اختيار القالب '10' - العدد الحالي: 4909
2025-07-26 01:35:35,238 - INFO - معالجة callback: independent_create_um_10_lightning
2025-07-26 01:35:35,449 - INFO - 🔍 بدء اختبار جلب العدد الحالي للكروت قبل إرسال الإشعار
2025-07-26 01:35:35,449 - INFO - 🧪 بدء اختبار الاتصال وجلب عدد User Manager
2025-07-26 01:35:35,453 - INFO - استخدام الاتصال الحالي
2025-07-26 01:35:35,453 - INFO - ✅ نجح الاتصال - اختبار جلب العدد...
2025-07-26 01:35:35,496 - INFO - 🏷️ اسم النظام: MikroTik
2025-07-26 01:35:35,539 - INFO - 📋 إصدار RouterOS: 6.43.12 (stable)
2025-07-26 01:35:35,545 - INFO - 📊 اختبار جلب مستخدمي User Manager...
2025-07-26 01:35:45,377 - INFO - ✅ تم جلب 4909 مستخدم من User Manager
2025-07-26 01:35:45,377 - INFO - 👥 عرض أول 5 مستخدمين:
2025-07-26 01:35:45,377 - INFO -   1. غير محدد (البروفايل: غير محدد)
2025-07-26 01:35:45,378 - INFO -   2. غير محدد (البروفايل: غير محدد)
2025-07-26 01:35:45,378 - INFO -   3. غير محدد (البروفايل: غير محدد)
2025-07-26 01:35:45,378 - INFO -   4. غير محدد (البروفايل: غير محدد)
2025-07-26 01:35:45,379 - INFO -   5. غير محدد (البروفايل: غير محدد)
2025-07-26 01:35:45,379 - ERROR - ❌ خطأ في API: 'RouterOsApi' object has no attribute 'disconnect'
2025-07-26 01:35:45,383 - INFO - 📊 نتيجة الاختبار: خطأ في API: 'RouterOsApi' object has no attribute 'disconnect'
2025-07-26 01:35:45,384 - INFO - 🔔 بدء إرسال إشعار اختيار القالب: 10 (User Manager)
2025-07-26 01:35:45,385 - INFO - 📊 جلب العدد الحالي من User Manager...
2025-07-26 01:35:45,386 - INFO - 🔍 بدء جلب العدد الحالي للكروت في User Manager
2025-07-26 01:35:45,387 - INFO - ✅ مكتبة routeros_api متوفرة
2025-07-26 01:35:45,401 - INFO - 🔗 محاولة الاتصال بـ 6.6.6.100 باستخدام المستخدم: 11
2025-07-26 01:35:45,404 - INFO - استخدام الاتصال الحالي
2025-07-26 01:35:45,404 - INFO - ✅ تم الاتصال بـ MikroTik بنجاح
2025-07-26 01:35:45,405 - INFO - 📊 جلب قائمة المستخدمين من User Manager...
2025-07-26 01:35:55,214 - INFO - ✅ تم جلب 4909 مستخدم من User Manager بنجاح
2025-07-26 01:35:55,215 - INFO - 👤 مستخدم 1: غير محدد
2025-07-26 01:35:55,216 - INFO - 👤 مستخدم 2: غير محدد
2025-07-26 01:35:55,217 - INFO - 👤 مستخدم 3: غير محدد
2025-07-26 01:35:55,222 - INFO - 📈 العدد المجلوب من User Manager: 4909
2025-07-26 01:35:55,543 - INFO - ✅ تم إرسال إشعار اختيار القالب '10' - العدد الحالي: 4909
2025-07-26 01:36:05,443 - INFO - معالجة callback: independent_count_um_10_lightning_200
2025-07-26 01:36:05,909 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-26 01:36:05,912 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-26 01:36:05,915 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-26 01:36:05,926 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-26 01:36:05,994 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-26 01:36:05,994 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-26 01:36:05,997 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-26 01:36:06,001 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-26 01:36:06,254 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-26 01:36:06,254 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-26 01:36:06,281 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-26 01:36:06,282 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-26 01:36:06,309 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-26 01:36:06,309 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-26 01:36:06,313 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-26 01:36:06,327 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-26 01:36:06,330 - INFO - ⚡ البرق: تم الحفاظ على العدد المحدد: 200
2025-07-26 01:36:06,331 - INFO - تم تطبيق إعدادات القالب '10' بنجاح
2025-07-26 01:36:06,331 - INFO - ⚡ استخدام البرق للتلجرام (مع جدولة مؤقتة وتنظيف تلقائي)
2025-07-26 01:36:06,331 - INFO - ⚡ بدء البرق التلقائي للتلجرام (مع جدولة مؤقتة وتنظيف تلقائي)
2025-07-26 01:36:06,332 - INFO - ⚡ البرق: استخدام العدد من التلجرام بوت: 200
2025-07-26 01:36:06,332 - INFO - ⚡ البرق للتلجرام: العدد المطلوب 200 حساب
2025-07-26 01:36:06,333 - INFO - ⚡ البرق: تم تحديث عدد الحسابات في الواجهة إلى: 200
2025-07-26 01:36:06,337 - INFO - ⚡ البرق للتلجرام: تم توليد 200 حساب
2025-07-26 01:36:06,337 - INFO - ⚡ البرق للتلجرام: تنفيذ متسلسل مجدول لـ 200 كارت (حد التقسيم: 100)
2025-07-26 01:36:06,337 - INFO - ⚡ البرق للتلجرام: بدء معالجة الأعداد الكبيرة مع جدولة واحدة
2025-07-26 01:36:06,338 - INFO - ⚡ البرق للتلجرام: تم تقسيم 200 كارت إلى 2 مجموعة للتنفيذ المتسلسل
2025-07-26 01:36:06,339 - INFO - ⚡ البرق للتلجرام: إنشاء السكريبت للمجموعة 1/2 (100 كارت)
2025-07-26 01:36:06,339 - INFO - ⚡ البرق للتلجرام: تم إعداد السكريبت telegram_lightning_batch1_user_manager_20250726_013606
2025-07-26 01:36:06,340 - INFO - ⚡ البرق للتلجرام: إنشاء السكريبت للمجموعة 2/2 (100 كارت)
2025-07-26 01:36:06,341 - INFO - ⚡ البرق للتلجرام: تم إعداد السكريبت telegram_lightning_batch2_user_manager_20250726_013606
2025-07-26 01:36:06,344 - INFO - استخدام الاتصال الحالي
2025-07-26 01:36:06,411 - INFO - ⚡ تم إرسال السكريبت المترابط: telegram_lightning_batch1_user_manager_20250726_013606
2025-07-26 01:36:06,458 - INFO - ⚡ تم إرسال السكريبت المترابط: telegram_lightning_batch2_user_manager_20250726_013606
2025-07-26 01:36:06,496 - INFO - ⚡ وقت MikroTik الحالي: 22:36:06
2025-07-26 01:36:06,496 - INFO - ⚡ السكريبت الأول سينفذ في: 22:36:09 (بعد 3 ثواني من وقت MikroTik)
2025-07-26 01:36:06,539 - INFO - ⚡ تم إنشاء الجدولة الرئيسية: telegram_main_schedule_20250726_013606 لتشغيل telegram_lightning_batch1_user_manager_20250726_013606 في 22:36:09
2025-07-26 01:36:06,553 - INFO - ⚡ تم إعداد البرق المترابط بنجاح - جدولة واحدة: telegram_main_schedule_20250726_013606, 2 سكريبت مترابط
2025-07-26 01:36:06,556 - INFO - ⚡ البرق للتلجرام مكتمل: تم إنشاء جدولة واحدة لـ 2 مجموعة متسلسلة (200 كارت)
2025-07-26 01:36:09,418 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 200
2025-07-26 01:36:09,435 - INFO - تم إنشاء PDF بنجاح: exports/كروت_(10)_ب20-200 كارت-26-07-2025-01-36-09-um.pdf
2025-07-26 01:36:09,441 - INFO - تم حفظ ملف .rsc الاحتياطي: exports/كروت_(10)_ب20-200 كارت-26-07-2025-01-36-09-um.rsc
2025-07-26 01:36:10,416 - INFO - تم إرسال الملف بنجاح: كروت_(10)_ب20-200 كارت-26-07-2025-01-36-09-um.pdf
2025-07-26 01:36:10,804 - INFO - تم إرسال 200 كرت عبر التلجرام باستخدام قالب 10
2025-07-26 01:36:55,698 - INFO - بدء إغلاق التطبيق
2025-07-26 01:36:55,701 - INFO - تم قطع الاتصال مع MikroTik
2025-07-26 01:36:55,729 - INFO - تم تسجيل العملية: إغلاق التطبيق - تم إغلاق التطبيق بنجاح
2025-07-26 01:36:55,743 - INFO - تم إغلاق التطبيق بنجاح
2025-07-26 01:38:03,824 - INFO - تم بدء تشغيل التطبيق
2025-07-26 01:38:03,828 - INFO - تم إنشاء المجلدات الأساسية
2025-07-26 01:38:03,830 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-26 01:38:03,831 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-26 01:38:03,934 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-26 01:38:05,686 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-26 01:38:05,687 - INFO - تم إعداد التطبيق بنجاح
2025-07-26 01:38:07,924 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-26 01:38:11,067 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-26 01:38:11,118 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-26 01:38:11,569 - INFO - معالجة الأمر: /start
2025-07-26 01:38:12,211 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-26 01:38:14,127 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-26 01:38:14,128 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-26 01:38:14,800 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-26 01:38:14,800 - INFO - 🔄 تم إرسال قائمة اختيار النظام
