2025-07-26 00:12:11,926 - INFO - تم بدء تشغيل التطبيق
2025-07-26 00:12:11,970 - INFO - تم إنشاء المجلدات الأساسية
2025-07-26 00:12:12,036 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-26 00:12:12,069 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-26 00:12:13,328 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-26 00:12:30,652 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-26 00:12:30,652 - INFO - ت<PERSON> <PERSON>عداد التطبيق بنجاح
2025-07-26 00:12:33,136 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-26 00:12:35,387 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-26 00:12:35,387 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-26 00:12:35,712 - INFO - معالجة callback: select_system_um
2025-07-26 00:12:35,950 - ERROR - خطأ في الرد على callback query: HTTP Error 400: Bad Request
2025-07-26 00:12:36,253 - INFO - تم إرسال 2 قالب مفلتر لنظام User Manager
2025-07-26 00:12:38,012 - INFO - معالجة callback: independent_template_um_10
2025-07-26 00:12:38,395 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-26 00:12:38,458 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-26 00:12:38,732 - INFO - 🔄 تبديل النظام تلقائياً من None إلى user_manager
2025-07-26 00:12:38,732 - INFO - 🔄 طلب تبديل النظام من التلجرام: None → user_manager
2025-07-26 00:12:38,761 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-26 00:12:38,780 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-26 00:12:39,085 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-26 00:12:40,075 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-26 00:12:40,187 - WARNING - تحذير: فشل في حفظ الإعدادات: 'MikroTikCardGenerator' object has no attribute 'version_combo'
2025-07-26 00:12:40,190 - INFO - تم تعيين النظام الجديد: user_manager
2025-07-26 00:12:40,191 - INFO - 🔄 إعادة بناء الواجهة للنظام: user_manager
2025-07-26 00:12:40,197 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-26 00:12:40,199 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-26 00:12:44,080 - INFO - تم إعداد تبويب عرض الكروت لـ User Manager بنجاح
2025-07-26 00:12:44,646 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-26 00:12:44,647 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-26 00:12:45,078 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام user_manager
2025-07-26 00:12:45,078 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-26 00:12:46,029 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager
2025-07-26 00:12:46,104 - INFO - تم تسجيل العملية: تبديل النظام من التلجرام - تم تبديل النظام من None إلى user_manager
2025-07-26 00:12:46,105 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-26 00:12:46,106 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-26 00:12:46,106 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-26 00:12:47,444 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-26 00:12:47,445 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-26 00:12:47,501 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-26 00:12:47,508 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-26 00:12:47,513 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-26 00:12:47,518 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-26 00:12:47,521 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-26 00:12:47,522 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager - القالب: 10
2025-07-26 00:12:47,782 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-26 00:12:50,734 - INFO - معالجة callback: independent_create_um_10_lightning
2025-07-26 00:12:51,096 - INFO - 🔍 بدء اختبار جلب العدد الحالي للكروت قبل إرسال الإشعار
2025-07-26 00:12:51,096 - INFO - 🧪 بدء اختبار الاتصال وجلب عدد User Manager
2025-07-26 00:12:51,097 - INFO - محاولة الاتصال بـ 6.6.6.100:8728 (عادي)
2025-07-26 00:12:51,171 - INFO - نجح الاتصال مع 6.6.6.100
2025-07-26 00:12:51,176 - INFO - ✅ نجح الاتصال - اختبار جلب العدد...
2025-07-26 00:12:51,215 - INFO - 🏷️ اسم النظام: MikroTik
2025-07-26 00:12:51,259 - INFO - 📋 إصدار RouterOS: 6.43.12 (stable)
2025-07-26 00:12:51,259 - INFO - 📊 اختبار جلب مستخدمي User Manager...
2025-07-26 00:13:00,521 - INFO - ✅ تم جلب 4659 مستخدم من User Manager
2025-07-26 00:13:00,522 - INFO - 👥 عرض أول 5 مستخدمين:
2025-07-26 00:13:00,523 - INFO -   1. غير محدد (البروفايل: غير محدد)
2025-07-26 00:13:00,523 - INFO -   2. غير محدد (البروفايل: غير محدد)
2025-07-26 00:13:00,523 - INFO -   3. غير محدد (البروفايل: غير محدد)
2025-07-26 00:13:00,523 - INFO -   4. غير محدد (البروفايل: غير محدد)
2025-07-26 00:13:00,524 - INFO -   5. غير محدد (البروفايل: غير محدد)
2025-07-26 00:13:00,524 - ERROR - ❌ خطأ في API: 'RouterOsApi' object has no attribute 'disconnect'
2025-07-26 00:13:00,528 - INFO - 📊 نتيجة الاختبار: خطأ في API: 'RouterOsApi' object has no attribute 'disconnect'
2025-07-26 00:13:00,528 - INFO - 🔔 بدء إرسال إشعار اختيار القالب: 10 (User Manager)
2025-07-26 00:13:00,529 - INFO - 📊 جلب العدد الحالي من User Manager...
2025-07-26 00:13:00,530 - INFO - 🔍 بدء جلب العدد الحالي للكروت في User Manager
2025-07-26 00:13:00,530 - INFO - ✅ مكتبة routeros_api متوفرة
2025-07-26 00:13:00,531 - INFO - 🔗 محاولة الاتصال بـ 6.6.6.100 باستخدام المستخدم: 11
2025-07-26 00:13:00,534 - INFO - استخدام الاتصال الحالي
2025-07-26 00:13:00,535 - INFO - ✅ تم الاتصال بـ MikroTik بنجاح
2025-07-26 00:13:00,537 - INFO - 📊 جلب قائمة المستخدمين من User Manager...
2025-07-26 00:13:09,785 - INFO - ✅ تم جلب 4659 مستخدم من User Manager بنجاح
2025-07-26 00:13:09,785 - INFO - 👤 مستخدم 1: غير محدد
2025-07-26 00:13:09,785 - INFO - 👤 مستخدم 2: غير محدد
2025-07-26 00:13:09,786 - INFO - 👤 مستخدم 3: غير محدد
2025-07-26 00:13:09,790 - INFO - 📈 العدد المجلوب من User Manager: 4659
2025-07-26 00:13:10,132 - INFO - ✅ تم إرسال إشعار اختيار القالب '10' - العدد الحالي: 4659
2025-07-26 00:27:01,319 - ERROR - خطأ في معالجة تحديثات التلجرام: HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot5161769536:AAHFeXIB-kCvIfo_NwfR7dwiWOUgXJF-p-Y/getUpdates?offset=858841610&timeout=10 (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000194CFE08550>: Failed to establish a new connection: [WinError 10051] A socket operation was attempted to an unreachable network'))
2025-07-26 00:40:40,676 - INFO - تم بدء تشغيل التطبيق
2025-07-26 00:40:41,289 - INFO - تم إنشاء المجلدات الأساسية
2025-07-26 00:40:41,296 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-26 00:40:41,467 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-26 00:40:42,077 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-26 00:41:03,530 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-26 00:41:03,531 - INFO - تم إعداد التطبيق بنجاح
2025-07-26 00:41:05,720 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-26 00:41:06,668 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-26 00:41:06,669 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-26 00:41:09,682 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-26 00:41:09,752 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-26 00:41:10,087 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-26 00:41:10,087 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-26 00:42:55,566 - INFO - معالجة callback: select_system_um
2025-07-26 00:42:56,127 - INFO - تم إرسال 2 قالب مفلتر لنظام User Manager
2025-07-26 00:42:59,422 - INFO - معالجة callback: independent_template_um_10
2025-07-26 00:42:59,900 - INFO - 🔄 تبديل النظام تلقائياً من None إلى user_manager
2025-07-26 00:42:59,901 - INFO - 🔄 طلب تبديل النظام من التلجرام: None → user_manager
2025-07-26 00:43:00,155 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-26 00:43:00,299 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-26 00:43:00,400 - WARNING - تحذير: فشل في حفظ الإعدادات: 'MikroTikCardGenerator' object has no attribute 'version_combo'
2025-07-26 00:43:00,400 - INFO - تم تعيين النظام الجديد: user_manager
2025-07-26 00:43:00,401 - INFO - 🔄 إعادة بناء الواجهة للنظام: user_manager
2025-07-26 00:43:00,402 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-26 00:43:00,409 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-26 00:43:01,490 - INFO - تم إعداد تبويب عرض الكروت لـ User Manager بنجاح
2025-07-26 00:43:01,754 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-26 00:43:01,755 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-26 00:43:02,169 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام user_manager
2025-07-26 00:43:02,171 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-26 00:43:02,422 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager
2025-07-26 00:43:02,470 - INFO - تم تسجيل العملية: تبديل النظام من التلجرام - تم تبديل النظام من None إلى user_manager
2025-07-26 00:43:02,483 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-26 00:43:02,489 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-26 00:43:02,503 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-26 00:43:03,034 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-26 00:43:03,035 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-26 00:43:03,063 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-26 00:43:03,064 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-26 00:43:03,066 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-26 00:43:03,072 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-26 00:43:03,086 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-26 00:43:03,087 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager - القالب: 10
2025-07-26 00:43:03,406 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-26 00:43:13,316 - INFO - معالجة callback: independent_create_um_10_lightning
2025-07-26 00:43:13,633 - INFO - 🔍 بدء اختبار جلب العدد الحالي للكروت قبل إرسال الإشعار
2025-07-26 00:43:13,634 - INFO - 🧪 بدء اختبار الاتصال وجلب عدد User Manager
2025-07-26 00:43:13,634 - INFO - محاولة الاتصال بـ 6.6.6.100:8728 (عادي)
2025-07-26 00:43:13,705 - INFO - نجح الاتصال مع 6.6.6.100
2025-07-26 00:43:13,706 - INFO - ✅ نجح الاتصال - اختبار جلب العدد...
2025-07-26 00:43:13,740 - INFO - 🏷️ اسم النظام: MikroTik
2025-07-26 00:43:13,784 - INFO - 📋 إصدار RouterOS: 6.43.12 (stable)
2025-07-26 00:43:13,785 - INFO - 📊 اختبار جلب مستخدمي User Manager...
2025-07-26 00:43:23,025 - INFO - ✅ تم جلب 4659 مستخدم من User Manager
2025-07-26 00:43:23,026 - INFO - 👥 عرض أول 5 مستخدمين:
2025-07-26 00:43:23,027 - INFO -   1. غير محدد (البروفايل: غير محدد)
2025-07-26 00:43:23,027 - INFO -   2. غير محدد (البروفايل: غير محدد)
2025-07-26 00:43:23,027 - INFO -   3. غير محدد (البروفايل: غير محدد)
2025-07-26 00:43:23,027 - INFO -   4. غير محدد (البروفايل: غير محدد)
2025-07-26 00:43:23,028 - INFO -   5. غير محدد (البروفايل: غير محدد)
2025-07-26 00:43:23,028 - ERROR - ❌ خطأ في API: 'RouterOsApi' object has no attribute 'disconnect'
2025-07-26 00:43:23,032 - INFO - 📊 نتيجة الاختبار: خطأ في API: 'RouterOsApi' object has no attribute 'disconnect'
2025-07-26 00:43:23,040 - INFO - 🔔 بدء إرسال إشعار اختيار القالب: 10 (User Manager)
2025-07-26 00:43:23,040 - INFO - 📊 جلب العدد الحالي من User Manager...
2025-07-26 00:43:23,041 - INFO - 🔍 بدء جلب العدد الحالي للكروت في User Manager
2025-07-26 00:43:23,041 - INFO - ✅ مكتبة routeros_api متوفرة
2025-07-26 00:43:23,041 - INFO - 🔗 محاولة الاتصال بـ 6.6.6.100 باستخدام المستخدم: 11
2025-07-26 00:43:23,045 - INFO - استخدام الاتصال الحالي
2025-07-26 00:43:23,045 - INFO - ✅ تم الاتصال بـ MikroTik بنجاح
2025-07-26 00:43:23,045 - INFO - 📊 جلب قائمة المستخدمين من User Manager...
2025-07-26 00:43:32,303 - INFO - ✅ تم جلب 4659 مستخدم من User Manager بنجاح
2025-07-26 00:43:32,305 - INFO - 👤 مستخدم 1: غير محدد
2025-07-26 00:43:32,306 - INFO - 👤 مستخدم 2: غير محدد
2025-07-26 00:43:32,306 - INFO - 👤 مستخدم 3: غير محدد
2025-07-26 00:43:32,309 - INFO - 📈 العدد المجلوب من User Manager: 4659
2025-07-26 00:43:32,641 - INFO - ✅ تم إرسال إشعار اختيار القالب '10' - العدد الحالي: 4659
2025-07-26 00:43:46,194 - INFO - معالجة callback: independent_count_um_10_lightning_200
2025-07-26 00:43:46,809 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-26 00:43:46,810 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-26 00:43:46,825 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-26 00:43:46,826 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-26 00:43:46,863 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-26 00:43:46,865 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-26 00:43:46,867 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-26 00:43:46,873 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-26 00:43:47,443 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-26 00:43:47,444 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-26 00:43:47,470 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-26 00:43:47,471 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-26 00:43:47,526 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-26 00:43:47,527 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-26 00:43:47,530 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-26 00:43:47,535 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-26 00:43:47,551 - INFO - ⚡ البرق: تم الحفاظ على العدد المحدد: 200
2025-07-26 00:43:47,552 - INFO - تم تطبيق إعدادات القالب '10' بنجاح
2025-07-26 00:43:47,553 - INFO - ⚡ استخدام البرق للتلجرام (مع جدولة مؤقتة وتنظيف تلقائي)
2025-07-26 00:43:47,556 - INFO - ⚡ بدء البرق التلقائي للتلجرام (مع جدولة مؤقتة وتنظيف تلقائي)
2025-07-26 00:43:47,556 - INFO - ⚡ البرق: استخدام العدد من التلجرام بوت: 200
2025-07-26 00:43:47,556 - INFO - ⚡ البرق للتلجرام: العدد المطلوب 200 حساب
2025-07-26 00:43:47,557 - INFO - ⚡ البرق: تم تحديث عدد الحسابات في الواجهة إلى: 200
2025-07-26 00:43:47,560 - INFO - ⚡ البرق للتلجرام: تم توليد 200 حساب
2025-07-26 00:43:47,560 - INFO - ⚡ البرق للتلجرام: تنفيذ متسلسل مجدول لـ 200 كارت (حد التقسيم: 100)
2025-07-26 00:43:47,562 - INFO - ⚡ البرق للتلجرام: بدء معالجة الأعداد الكبيرة مع جدولة واحدة
2025-07-26 00:43:47,562 - INFO - ⚡ البرق للتلجرام: تم تقسيم 200 كارت إلى 2 مجموعة للتنفيذ المتسلسل
2025-07-26 00:43:47,562 - INFO - ⚡ البرق للتلجرام: إنشاء السكريبت للمجموعة 1/2 (100 كارت)
2025-07-26 00:43:47,563 - INFO - ⚡ البرق للتلجرام: تم إعداد السكريبت telegram_lightning_batch1_user_manager_20250726_004347
2025-07-26 00:43:47,564 - INFO - ⚡ البرق للتلجرام: إنشاء السكريبت للمجموعة 2/2 (100 كارت)
2025-07-26 00:43:47,565 - INFO - ⚡ البرق للتلجرام: تم إعداد السكريبت telegram_lightning_batch2_user_manager_20250726_004347
2025-07-26 00:43:47,573 - INFO - استخدام الاتصال الحالي
2025-07-26 00:43:47,701 - INFO - ⚡ تم إرسال السكريبت المترابط: telegram_lightning_batch1_user_manager_20250726_004347
2025-07-26 00:43:47,748 - INFO - ⚡ تم إرسال السكريبت المترابط: telegram_lightning_batch2_user_manager_20250726_004347
2025-07-26 00:43:47,751 - INFO - ⚡ وقت MikroTik الحالي: 21:43:47
2025-07-26 00:43:47,884 - INFO - ⚡ السكريبت الأول سينفذ في: 21:43:50 (بعد 3 ثواني من وقت MikroTik)
2025-07-26 00:43:47,941 - INFO - ⚡ تم إنشاء الجدولة الرئيسية: telegram_main_schedule_20250726_004347 لتشغيل telegram_lightning_batch1_user_manager_20250726_004347 في 21:43:50
2025-07-26 00:43:47,946 - INFO - ⚡ تم إعداد البرق المترابط بنجاح - جدولة واحدة: telegram_main_schedule_20250726_004347, 2 سكريبت مترابط
2025-07-26 00:43:47,946 - INFO - ⚡ البرق للتلجرام مكتمل: تم إنشاء جدولة واحدة لـ 2 مجموعة متسلسلة (200 كارت)
2025-07-26 00:43:50,858 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 200
2025-07-26 00:43:51,060 - INFO - تم إنشاء PDF بنجاح: exports/كروت_(10)_ب20-200 كارت-26-07-2025-00-43-50-um.pdf
2025-07-26 00:43:51,063 - INFO - تم حفظ ملف .rsc الاحتياطي: exports/كروت_(10)_ب20-200 كارت-26-07-2025-00-43-50-um.rsc
2025-07-26 00:43:52,695 - INFO - تم إرسال الملف بنجاح: كروت_(10)_ب20-200 كارت-26-07-2025-00-43-50-um.pdf
2025-07-26 00:43:52,937 - INFO - تم إرسال 200 كرت عبر التلجرام باستخدام قالب 10
