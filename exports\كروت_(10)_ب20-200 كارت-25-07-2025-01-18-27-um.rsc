# MikroTik Script - نسخة احتياطية للكروت
# تم الإنشاء: 2025-07-25 01:18:27
# القالب: 10
# النظام: user_manager
# عدد الكروت: 200
# تم إنشاؤه بواسطة: مولد كروت MikroTik

:put "🚀 بدء تنفيذ النسخة الاحتياطية للكروت";
:put "📋 القالب: 10";
:put "🎯 النظام: user_manager";
:put "📊 عدد الكروت: 200";

:local success 0;
:local errors 0;
:local total 200;

:put "⏳ بدء إضافة الكروت...";

# إضافة مستخدمي User Manager
:put "👤 إضافة 200 مستخدم User Manager...";

# المستخدم 1: 0171163259
:do {
    /tool user-manager user add customer="admin" username="0171163259" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0171163259";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0171163259";
};

# المستخدم 2: 0142111788
:do {
    /tool user-manager user add customer="admin" username="0142111788" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0142111788";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0142111788";
};

# المستخدم 3: 0198881482
:do {
    /tool user-manager user add customer="admin" username="0198881482" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0198881482";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0198881482";
};

# المستخدم 4: 0153988197
:do {
    /tool user-manager user add customer="admin" username="0153988197" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0153988197";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0153988197";
};

# المستخدم 5: 0184295485
:do {
    /tool user-manager user add customer="admin" username="0184295485" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0184295485";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0184295485";
};

# المستخدم 6: 0151274874
:do {
    /tool user-manager user add customer="admin" username="0151274874" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0151274874";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0151274874";
};

# المستخدم 7: 0146894072
:do {
    /tool user-manager user add customer="admin" username="0146894072" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0146894072";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0146894072";
};

# المستخدم 8: 0162829497
:do {
    /tool user-manager user add customer="admin" username="0162829497" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0162829497";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0162829497";
};

# المستخدم 9: 0171754492
:do {
    /tool user-manager user add customer="admin" username="0171754492" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0171754492";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0171754492";
};

# المستخدم 10: 0109026945
:do {
    /tool user-manager user add customer="admin" username="0109026945" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0109026945";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0109026945";
};

# المستخدم 11: 0197068369
:do {
    /tool user-manager user add customer="admin" username="0197068369" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0197068369";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0197068369";
};

# المستخدم 12: 0128260158
:do {
    /tool user-manager user add customer="admin" username="0128260158" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0128260158";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0128260158";
};

# المستخدم 13: 0187716747
:do {
    /tool user-manager user add customer="admin" username="0187716747" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0187716747";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0187716747";
};

# المستخدم 14: 0175378453
:do {
    /tool user-manager user add customer="admin" username="0175378453" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0175378453";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0175378453";
};

# المستخدم 15: 0128539797
:do {
    /tool user-manager user add customer="admin" username="0128539797" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0128539797";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0128539797";
};

# المستخدم 16: 0120359270
:do {
    /tool user-manager user add customer="admin" username="0120359270" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0120359270";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0120359270";
};

# المستخدم 17: 0157088593
:do {
    /tool user-manager user add customer="admin" username="0157088593" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0157088593";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0157088593";
};

# المستخدم 18: 0146190192
:do {
    /tool user-manager user add customer="admin" username="0146190192" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0146190192";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0146190192";
};

# المستخدم 19: 0106457650
:do {
    /tool user-manager user add customer="admin" username="0106457650" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0106457650";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0106457650";
};

# المستخدم 20: 0125270042
:do {
    /tool user-manager user add customer="admin" username="0125270042" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0125270042";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0125270042";
};

# المستخدم 21: 0166886074
:do {
    /tool user-manager user add customer="admin" username="0166886074" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0166886074";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0166886074";
};

# المستخدم 22: 0116016525
:do {
    /tool user-manager user add customer="admin" username="0116016525" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0116016525";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0116016525";
};

# المستخدم 23: 0118977494
:do {
    /tool user-manager user add customer="admin" username="0118977494" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0118977494";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0118977494";
};

# المستخدم 24: 0147662177
:do {
    /tool user-manager user add customer="admin" username="0147662177" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0147662177";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0147662177";
};

# المستخدم 25: 0103316395
:do {
    /tool user-manager user add customer="admin" username="0103316395" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0103316395";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0103316395";
};

# المستخدم 26: 0151793629
:do {
    /tool user-manager user add customer="admin" username="0151793629" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0151793629";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0151793629";
};

# المستخدم 27: 0198160485
:do {
    /tool user-manager user add customer="admin" username="0198160485" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0198160485";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0198160485";
};

# المستخدم 28: 0159761283
:do {
    /tool user-manager user add customer="admin" username="0159761283" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0159761283";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0159761283";
};

# المستخدم 29: 0199089353
:do {
    /tool user-manager user add customer="admin" username="0199089353" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0199089353";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0199089353";
};

# المستخدم 30: 0182410905
:do {
    /tool user-manager user add customer="admin" username="0182410905" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0182410905";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0182410905";
};

# المستخدم 31: 0193024207
:do {
    /tool user-manager user add customer="admin" username="0193024207" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0193024207";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0193024207";
};

# المستخدم 32: 0137950052
:do {
    /tool user-manager user add customer="admin" username="0137950052" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0137950052";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0137950052";
};

# المستخدم 33: 0184061030
:do {
    /tool user-manager user add customer="admin" username="0184061030" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0184061030";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0184061030";
};

# المستخدم 34: 0145595982
:do {
    /tool user-manager user add customer="admin" username="0145595982" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0145595982";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0145595982";
};

# المستخدم 35: 0143074763
:do {
    /tool user-manager user add customer="admin" username="0143074763" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0143074763";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0143074763";
};

# المستخدم 36: 0185694187
:do {
    /tool user-manager user add customer="admin" username="0185694187" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0185694187";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0185694187";
};

# المستخدم 37: 0109371384
:do {
    /tool user-manager user add customer="admin" username="0109371384" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0109371384";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0109371384";
};

# المستخدم 38: 0112673194
:do {
    /tool user-manager user add customer="admin" username="0112673194" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0112673194";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0112673194";
};

# المستخدم 39: 0174937674
:do {
    /tool user-manager user add customer="admin" username="0174937674" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0174937674";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0174937674";
};

# المستخدم 40: 0162125258
:do {
    /tool user-manager user add customer="admin" username="0162125258" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0162125258";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0162125258";
};

# المستخدم 41: 0154625045
:do {
    /tool user-manager user add customer="admin" username="0154625045" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0154625045";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0154625045";
};

# المستخدم 42: 0156009989
:do {
    /tool user-manager user add customer="admin" username="0156009989" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0156009989";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0156009989";
};

# المستخدم 43: 0141559470
:do {
    /tool user-manager user add customer="admin" username="0141559470" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0141559470";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0141559470";
};

# المستخدم 44: 0159335878
:do {
    /tool user-manager user add customer="admin" username="0159335878" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0159335878";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0159335878";
};

# المستخدم 45: 0153762830
:do {
    /tool user-manager user add customer="admin" username="0153762830" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0153762830";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0153762830";
};

# المستخدم 46: 0110460665
:do {
    /tool user-manager user add customer="admin" username="0110460665" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0110460665";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0110460665";
};

# المستخدم 47: 0193505130
:do {
    /tool user-manager user add customer="admin" username="0193505130" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0193505130";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0193505130";
};

# المستخدم 48: 0138848510
:do {
    /tool user-manager user add customer="admin" username="0138848510" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0138848510";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0138848510";
};

# المستخدم 49: 0163226747
:do {
    /tool user-manager user add customer="admin" username="0163226747" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0163226747";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0163226747";
};

# المستخدم 50: 0184354239
:do {
    /tool user-manager user add customer="admin" username="0184354239" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0184354239";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0184354239";
};

# المستخدم 51: 0145268760
:do {
    /tool user-manager user add customer="admin" username="0145268760" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0145268760";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0145268760";
};

# المستخدم 52: 0160935302
:do {
    /tool user-manager user add customer="admin" username="0160935302" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0160935302";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0160935302";
};

# المستخدم 53: 0170989384
:do {
    /tool user-manager user add customer="admin" username="0170989384" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0170989384";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0170989384";
};

# المستخدم 54: 0157261379
:do {
    /tool user-manager user add customer="admin" username="0157261379" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0157261379";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0157261379";
};

# المستخدم 55: 0132019125
:do {
    /tool user-manager user add customer="admin" username="0132019125" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0132019125";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0132019125";
};

# المستخدم 56: 0175840370
:do {
    /tool user-manager user add customer="admin" username="0175840370" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0175840370";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0175840370";
};

# المستخدم 57: 0129343665
:do {
    /tool user-manager user add customer="admin" username="0129343665" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0129343665";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0129343665";
};

# المستخدم 58: 0166983487
:do {
    /tool user-manager user add customer="admin" username="0166983487" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0166983487";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0166983487";
};

# المستخدم 59: 0107728154
:do {
    /tool user-manager user add customer="admin" username="0107728154" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0107728154";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0107728154";
};

# المستخدم 60: 0147986997
:do {
    /tool user-manager user add customer="admin" username="0147986997" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0147986997";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0147986997";
};

# المستخدم 61: 0130659622
:do {
    /tool user-manager user add customer="admin" username="0130659622" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0130659622";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0130659622";
};

# المستخدم 62: 0117473661
:do {
    /tool user-manager user add customer="admin" username="0117473661" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0117473661";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0117473661";
};

# المستخدم 63: 0131994430
:do {
    /tool user-manager user add customer="admin" username="0131994430" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0131994430";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0131994430";
};

# المستخدم 64: 0180457947
:do {
    /tool user-manager user add customer="admin" username="0180457947" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0180457947";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0180457947";
};

# المستخدم 65: 0116397108
:do {
    /tool user-manager user add customer="admin" username="0116397108" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0116397108";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0116397108";
};

# المستخدم 66: 0127489848
:do {
    /tool user-manager user add customer="admin" username="0127489848" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0127489848";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0127489848";
};

# المستخدم 67: 0158055979
:do {
    /tool user-manager user add customer="admin" username="0158055979" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0158055979";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0158055979";
};

# المستخدم 68: 0135081137
:do {
    /tool user-manager user add customer="admin" username="0135081137" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0135081137";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0135081137";
};

# المستخدم 69: 0123962793
:do {
    /tool user-manager user add customer="admin" username="0123962793" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0123962793";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0123962793";
};

# المستخدم 70: 0132692618
:do {
    /tool user-manager user add customer="admin" username="0132692618" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0132692618";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0132692618";
};

# المستخدم 71: 0101094517
:do {
    /tool user-manager user add customer="admin" username="0101094517" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0101094517";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0101094517";
};

# المستخدم 72: 0111998993
:do {
    /tool user-manager user add customer="admin" username="0111998993" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0111998993";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0111998993";
};

# المستخدم 73: 0198615646
:do {
    /tool user-manager user add customer="admin" username="0198615646" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0198615646";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0198615646";
};

# المستخدم 74: 0177619500
:do {
    /tool user-manager user add customer="admin" username="0177619500" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0177619500";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0177619500";
};

# المستخدم 75: 0123786851
:do {
    /tool user-manager user add customer="admin" username="0123786851" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0123786851";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0123786851";
};

# المستخدم 76: 0125818293
:do {
    /tool user-manager user add customer="admin" username="0125818293" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0125818293";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0125818293";
};

# المستخدم 77: 0131438780
:do {
    /tool user-manager user add customer="admin" username="0131438780" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0131438780";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0131438780";
};

# المستخدم 78: 0191755493
:do {
    /tool user-manager user add customer="admin" username="0191755493" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0191755493";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0191755493";
};

# المستخدم 79: 0182386387
:do {
    /tool user-manager user add customer="admin" username="0182386387" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0182386387";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0182386387";
};

# المستخدم 80: 0181119784
:do {
    /tool user-manager user add customer="admin" username="0181119784" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0181119784";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0181119784";
};

# المستخدم 81: 0151203707
:do {
    /tool user-manager user add customer="admin" username="0151203707" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0151203707";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0151203707";
};

# المستخدم 82: 0163206538
:do {
    /tool user-manager user add customer="admin" username="0163206538" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0163206538";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0163206538";
};

# المستخدم 83: 0110763578
:do {
    /tool user-manager user add customer="admin" username="0110763578" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0110763578";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0110763578";
};

# المستخدم 84: 0100518763
:do {
    /tool user-manager user add customer="admin" username="0100518763" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0100518763";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0100518763";
};

# المستخدم 85: 0191877238
:do {
    /tool user-manager user add customer="admin" username="0191877238" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0191877238";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0191877238";
};

# المستخدم 86: 0184929501
:do {
    /tool user-manager user add customer="admin" username="0184929501" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0184929501";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0184929501";
};

# المستخدم 87: 0191301866
:do {
    /tool user-manager user add customer="admin" username="0191301866" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0191301866";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0191301866";
};

# المستخدم 88: 0141152495
:do {
    /tool user-manager user add customer="admin" username="0141152495" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0141152495";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0141152495";
};

# المستخدم 89: 0126812975
:do {
    /tool user-manager user add customer="admin" username="0126812975" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0126812975";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0126812975";
};

# المستخدم 90: 0115878627
:do {
    /tool user-manager user add customer="admin" username="0115878627" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0115878627";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0115878627";
};

# المستخدم 91: 0103095898
:do {
    /tool user-manager user add customer="admin" username="0103095898" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0103095898";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0103095898";
};

# المستخدم 92: 0186412473
:do {
    /tool user-manager user add customer="admin" username="0186412473" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0186412473";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0186412473";
};

# المستخدم 93: 0116298162
:do {
    /tool user-manager user add customer="admin" username="0116298162" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0116298162";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0116298162";
};

# المستخدم 94: 0193470637
:do {
    /tool user-manager user add customer="admin" username="0193470637" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0193470637";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0193470637";
};

# المستخدم 95: 0165575064
:do {
    /tool user-manager user add customer="admin" username="0165575064" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0165575064";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0165575064";
};

# المستخدم 96: 0181276166
:do {
    /tool user-manager user add customer="admin" username="0181276166" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0181276166";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0181276166";
};

# المستخدم 97: 0112378278
:do {
    /tool user-manager user add customer="admin" username="0112378278" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0112378278";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0112378278";
};

# المستخدم 98: 0199717425
:do {
    /tool user-manager user add customer="admin" username="0199717425" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0199717425";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0199717425";
};

# المستخدم 99: 0126029854
:do {
    /tool user-manager user add customer="admin" username="0126029854" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0126029854";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0126029854";
};

# المستخدم 100: 0137056262
:do {
    /tool user-manager user add customer="admin" username="0137056262" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0137056262";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0137056262";
};

# المستخدم 101: 0163835143
:do {
    /tool user-manager user add customer="admin" username="0163835143" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0163835143";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0163835143";
};

# المستخدم 102: 0120383556
:do {
    /tool user-manager user add customer="admin" username="0120383556" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0120383556";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0120383556";
};

# المستخدم 103: 0154762993
:do {
    /tool user-manager user add customer="admin" username="0154762993" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0154762993";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0154762993";
};

# المستخدم 104: 0114877982
:do {
    /tool user-manager user add customer="admin" username="0114877982" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0114877982";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0114877982";
};

# المستخدم 105: 0177770146
:do {
    /tool user-manager user add customer="admin" username="0177770146" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0177770146";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0177770146";
};

# المستخدم 106: 0157805935
:do {
    /tool user-manager user add customer="admin" username="0157805935" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0157805935";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0157805935";
};

# المستخدم 107: 0151657051
:do {
    /tool user-manager user add customer="admin" username="0151657051" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0151657051";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0151657051";
};

# المستخدم 108: 0141174782
:do {
    /tool user-manager user add customer="admin" username="0141174782" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0141174782";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0141174782";
};

# المستخدم 109: 0128189987
:do {
    /tool user-manager user add customer="admin" username="0128189987" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0128189987";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0128189987";
};

# المستخدم 110: 0132255140
:do {
    /tool user-manager user add customer="admin" username="0132255140" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0132255140";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0132255140";
};

# المستخدم 111: 0154852491
:do {
    /tool user-manager user add customer="admin" username="0154852491" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0154852491";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0154852491";
};

# المستخدم 112: 0170753293
:do {
    /tool user-manager user add customer="admin" username="0170753293" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0170753293";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0170753293";
};

# المستخدم 113: 0148601253
:do {
    /tool user-manager user add customer="admin" username="0148601253" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0148601253";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0148601253";
};

# المستخدم 114: 0157659218
:do {
    /tool user-manager user add customer="admin" username="0157659218" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0157659218";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0157659218";
};

# المستخدم 115: 0128364979
:do {
    /tool user-manager user add customer="admin" username="0128364979" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0128364979";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0128364979";
};

# المستخدم 116: 0135037775
:do {
    /tool user-manager user add customer="admin" username="0135037775" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0135037775";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0135037775";
};

# المستخدم 117: 0107590603
:do {
    /tool user-manager user add customer="admin" username="0107590603" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0107590603";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0107590603";
};

# المستخدم 118: 0160267749
:do {
    /tool user-manager user add customer="admin" username="0160267749" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0160267749";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0160267749";
};

# المستخدم 119: 0167888591
:do {
    /tool user-manager user add customer="admin" username="0167888591" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0167888591";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0167888591";
};

# المستخدم 120: 0135775880
:do {
    /tool user-manager user add customer="admin" username="0135775880" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0135775880";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0135775880";
};

# المستخدم 121: 0193035377
:do {
    /tool user-manager user add customer="admin" username="0193035377" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0193035377";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0193035377";
};

# المستخدم 122: 0137559074
:do {
    /tool user-manager user add customer="admin" username="0137559074" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0137559074";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0137559074";
};

# المستخدم 123: 0126871046
:do {
    /tool user-manager user add customer="admin" username="0126871046" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0126871046";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0126871046";
};

# المستخدم 124: 0131539259
:do {
    /tool user-manager user add customer="admin" username="0131539259" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0131539259";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0131539259";
};

# المستخدم 125: 0100311016
:do {
    /tool user-manager user add customer="admin" username="0100311016" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0100311016";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0100311016";
};

# المستخدم 126: 0166714449
:do {
    /tool user-manager user add customer="admin" username="0166714449" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0166714449";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0166714449";
};

# المستخدم 127: 0183791651
:do {
    /tool user-manager user add customer="admin" username="0183791651" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0183791651";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0183791651";
};

# المستخدم 128: 0122383357
:do {
    /tool user-manager user add customer="admin" username="0122383357" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0122383357";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0122383357";
};

# المستخدم 129: 0195000218
:do {
    /tool user-manager user add customer="admin" username="0195000218" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0195000218";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0195000218";
};

# المستخدم 130: 0125637209
:do {
    /tool user-manager user add customer="admin" username="0125637209" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0125637209";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0125637209";
};

# المستخدم 131: 0122264996
:do {
    /tool user-manager user add customer="admin" username="0122264996" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0122264996";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0122264996";
};

# المستخدم 132: 0176849618
:do {
    /tool user-manager user add customer="admin" username="0176849618" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0176849618";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0176849618";
};

# المستخدم 133: 0139328650
:do {
    /tool user-manager user add customer="admin" username="0139328650" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0139328650";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0139328650";
};

# المستخدم 134: 0153780247
:do {
    /tool user-manager user add customer="admin" username="0153780247" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0153780247";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0153780247";
};

# المستخدم 135: 0110072368
:do {
    /tool user-manager user add customer="admin" username="0110072368" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0110072368";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0110072368";
};

# المستخدم 136: 0121695895
:do {
    /tool user-manager user add customer="admin" username="0121695895" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0121695895";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0121695895";
};

# المستخدم 137: 0111254817
:do {
    /tool user-manager user add customer="admin" username="0111254817" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0111254817";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0111254817";
};

# المستخدم 138: 0104636876
:do {
    /tool user-manager user add customer="admin" username="0104636876" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0104636876";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0104636876";
};

# المستخدم 139: 0154080211
:do {
    /tool user-manager user add customer="admin" username="0154080211" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0154080211";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0154080211";
};

# المستخدم 140: 0159305760
:do {
    /tool user-manager user add customer="admin" username="0159305760" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0159305760";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0159305760";
};

# المستخدم 141: 0116642686
:do {
    /tool user-manager user add customer="admin" username="0116642686" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0116642686";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0116642686";
};

# المستخدم 142: 0111139566
:do {
    /tool user-manager user add customer="admin" username="0111139566" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0111139566";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0111139566";
};

# المستخدم 143: 0183765107
:do {
    /tool user-manager user add customer="admin" username="0183765107" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0183765107";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0183765107";
};

# المستخدم 144: 0161269191
:do {
    /tool user-manager user add customer="admin" username="0161269191" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0161269191";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0161269191";
};

# المستخدم 145: 0136569642
:do {
    /tool user-manager user add customer="admin" username="0136569642" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0136569642";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0136569642";
};

# المستخدم 146: 0124909067
:do {
    /tool user-manager user add customer="admin" username="0124909067" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0124909067";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0124909067";
};

# المستخدم 147: 0159255405
:do {
    /tool user-manager user add customer="admin" username="0159255405" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0159255405";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0159255405";
};

# المستخدم 148: 0142128447
:do {
    /tool user-manager user add customer="admin" username="0142128447" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0142128447";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0142128447";
};

# المستخدم 149: 0162550640
:do {
    /tool user-manager user add customer="admin" username="0162550640" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0162550640";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0162550640";
};

# المستخدم 150: 0187378460
:do {
    /tool user-manager user add customer="admin" username="0187378460" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0187378460";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0187378460";
};

# المستخدم 151: 0123317085
:do {
    /tool user-manager user add customer="admin" username="0123317085" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0123317085";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0123317085";
};

# المستخدم 152: 0143941892
:do {
    /tool user-manager user add customer="admin" username="0143941892" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0143941892";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0143941892";
};

# المستخدم 153: 0122701262
:do {
    /tool user-manager user add customer="admin" username="0122701262" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0122701262";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0122701262";
};

# المستخدم 154: 0127498815
:do {
    /tool user-manager user add customer="admin" username="0127498815" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0127498815";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0127498815";
};

# المستخدم 155: 0127465400
:do {
    /tool user-manager user add customer="admin" username="0127465400" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0127465400";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0127465400";
};

# المستخدم 156: 0149546086
:do {
    /tool user-manager user add customer="admin" username="0149546086" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0149546086";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0149546086";
};

# المستخدم 157: 0163224691
:do {
    /tool user-manager user add customer="admin" username="0163224691" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0163224691";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0163224691";
};

# المستخدم 158: 0197265750
:do {
    /tool user-manager user add customer="admin" username="0197265750" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0197265750";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0197265750";
};

# المستخدم 159: 0199035732
:do {
    /tool user-manager user add customer="admin" username="0199035732" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0199035732";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0199035732";
};

# المستخدم 160: 0120556314
:do {
    /tool user-manager user add customer="admin" username="0120556314" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0120556314";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0120556314";
};

# المستخدم 161: 0139499453
:do {
    /tool user-manager user add customer="admin" username="0139499453" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0139499453";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0139499453";
};

# المستخدم 162: 0124560181
:do {
    /tool user-manager user add customer="admin" username="0124560181" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0124560181";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0124560181";
};

# المستخدم 163: 0186069928
:do {
    /tool user-manager user add customer="admin" username="0186069928" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0186069928";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0186069928";
};

# المستخدم 164: 0178654628
:do {
    /tool user-manager user add customer="admin" username="0178654628" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0178654628";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0178654628";
};

# المستخدم 165: 0107369068
:do {
    /tool user-manager user add customer="admin" username="0107369068" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0107369068";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0107369068";
};

# المستخدم 166: 0115723889
:do {
    /tool user-manager user add customer="admin" username="0115723889" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0115723889";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0115723889";
};

# المستخدم 167: 0114541534
:do {
    /tool user-manager user add customer="admin" username="0114541534" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0114541534";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0114541534";
};

# المستخدم 168: 0160222441
:do {
    /tool user-manager user add customer="admin" username="0160222441" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0160222441";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0160222441";
};

# المستخدم 169: 0125971257
:do {
    /tool user-manager user add customer="admin" username="0125971257" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0125971257";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0125971257";
};

# المستخدم 170: 0175076867
:do {
    /tool user-manager user add customer="admin" username="0175076867" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0175076867";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0175076867";
};

# المستخدم 171: 0162175612
:do {
    /tool user-manager user add customer="admin" username="0162175612" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0162175612";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0162175612";
};

# المستخدم 172: 0126891086
:do {
    /tool user-manager user add customer="admin" username="0126891086" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0126891086";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0126891086";
};

# المستخدم 173: 0141585230
:do {
    /tool user-manager user add customer="admin" username="0141585230" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0141585230";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0141585230";
};

# المستخدم 174: 0144326463
:do {
    /tool user-manager user add customer="admin" username="0144326463" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0144326463";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0144326463";
};

# المستخدم 175: 0176099634
:do {
    /tool user-manager user add customer="admin" username="0176099634" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0176099634";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0176099634";
};

# المستخدم 176: 0192342347
:do {
    /tool user-manager user add customer="admin" username="0192342347" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0192342347";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0192342347";
};

# المستخدم 177: 0196126550
:do {
    /tool user-manager user add customer="admin" username="0196126550" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0196126550";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0196126550";
};

# المستخدم 178: 0164799903
:do {
    /tool user-manager user add customer="admin" username="0164799903" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0164799903";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0164799903";
};

# المستخدم 179: 0124433753
:do {
    /tool user-manager user add customer="admin" username="0124433753" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0124433753";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0124433753";
};

# المستخدم 180: 0161209568
:do {
    /tool user-manager user add customer="admin" username="0161209568" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0161209568";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0161209568";
};

# المستخدم 181: 0143267921
:do {
    /tool user-manager user add customer="admin" username="0143267921" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0143267921";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0143267921";
};

# المستخدم 182: 0131717639
:do {
    /tool user-manager user add customer="admin" username="0131717639" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0131717639";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0131717639";
};

# المستخدم 183: 0164467510
:do {
    /tool user-manager user add customer="admin" username="0164467510" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0164467510";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0164467510";
};

# المستخدم 184: 0166375246
:do {
    /tool user-manager user add customer="admin" username="0166375246" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0166375246";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0166375246";
};

# المستخدم 185: 0190546750
:do {
    /tool user-manager user add customer="admin" username="0190546750" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0190546750";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0190546750";
};

# المستخدم 186: 0152522348
:do {
    /tool user-manager user add customer="admin" username="0152522348" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0152522348";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0152522348";
};

# المستخدم 187: 0102391340
:do {
    /tool user-manager user add customer="admin" username="0102391340" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0102391340";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0102391340";
};

# المستخدم 188: 0120700260
:do {
    /tool user-manager user add customer="admin" username="0120700260" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0120700260";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0120700260";
};

# المستخدم 189: 0199579856
:do {
    /tool user-manager user add customer="admin" username="0199579856" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0199579856";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0199579856";
};

# المستخدم 190: 0195101947
:do {
    /tool user-manager user add customer="admin" username="0195101947" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0195101947";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0195101947";
};

# المستخدم 191: 0192649659
:do {
    /tool user-manager user add customer="admin" username="0192649659" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0192649659";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0192649659";
};

# المستخدم 192: 0109393496
:do {
    /tool user-manager user add customer="admin" username="0109393496" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0109393496";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0109393496";
};

# المستخدم 193: 0178120627
:do {
    /tool user-manager user add customer="admin" username="0178120627" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0178120627";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0178120627";
};

# المستخدم 194: 0100544030
:do {
    /tool user-manager user add customer="admin" username="0100544030" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0100544030";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0100544030";
};

# المستخدم 195: 0100627947
:do {
    /tool user-manager user add customer="admin" username="0100627947" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0100627947";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0100627947";
};

# المستخدم 196: 0126690155
:do {
    /tool user-manager user add customer="admin" username="0126690155" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0126690155";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0126690155";
};

# المستخدم 197: 0123065894
:do {
    /tool user-manager user add customer="admin" username="0123065894" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0123065894";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0123065894";
};

# المستخدم 198: 0193786429
:do {
    /tool user-manager user add customer="admin" username="0193786429" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0193786429";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0193786429";
};

# المستخدم 199: 0162227234
:do {
    /tool user-manager user add customer="admin" username="0162227234" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0162227234";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0162227234";
};

# المستخدم 200: 0199760217
:do {
    /tool user-manager user add customer="admin" username="0199760217" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0199760217";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0199760217";
};


:put "📊 تم الانتهاء من النسخة الاحتياطية";
:put "✅ نجح: $success كرت";
:put "❌ فشل: $errors كرت";
:put "📈 الإجمالي: $total كرت";

:put "🎉 تم استعادة جميع كروت القالب: 10";
:put "💡 النسخة الاحتياطية مكتملة بنجاح!";
