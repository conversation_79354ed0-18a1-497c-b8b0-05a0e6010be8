2025-07-25 00:02:43,125 - INFO - تم بدء تشغيل التطبيق
2025-07-25 00:02:43,222 - INFO - تم إنشاء المجلدات الأساسية
2025-07-25 00:02:43,224 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-25 00:02:43,225 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-25 00:02:43,327 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-25 00:02:43,828 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-25 00:02:43,829 - INFO - تم <PERSON>عد<PERSON> التطبيق بنجاح
2025-07-25 00:02:45,831 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-25 00:02:46,122 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-25 00:02:46,123 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-25 00:02:49,132 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-25 00:02:49,133 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-25 00:02:49,373 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-25 00:02:49,374 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-25 00:02:50,727 - INFO - معالجة callback: select_system_um
2025-07-25 00:02:51,194 - INFO - تم إرسال 2 قالب مفلتر لنظام User Manager
2025-07-25 00:02:52,825 - INFO - معالجة callback: bot_stats
2025-07-25 00:02:54,996 - INFO - معالجة callback: refresh_templates
2025-07-25 00:02:55,451 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-25 00:02:59,460 - INFO - معالجة callback: select_system_um
2025-07-25 00:02:59,973 - INFO - تم إرسال 2 قالب مفلتر لنظام User Manager
2025-07-25 00:03:01,233 - INFO - معالجة callback: independent_template_um_10
2025-07-25 00:03:01,714 - INFO - 🔄 تبديل النظام تلقائياً من None إلى user_manager
2025-07-25 00:03:01,715 - INFO - 🔄 طلب تبديل النظام من التلجرام: None → user_manager
2025-07-25 00:03:01,986 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-25 00:03:02,023 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-25 00:03:02,123 - WARNING - تحذير: فشل في حفظ الإعدادات: 'MikroTikCardGenerator' object has no attribute 'version_combo'
2025-07-25 00:03:02,124 - INFO - تم تعيين النظام الجديد: user_manager
2025-07-25 00:03:02,125 - INFO - 🔄 إعادة بناء الواجهة للنظام: user_manager
2025-07-25 00:03:02,125 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-25 00:03:02,128 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-25 00:03:02,578 - INFO - تم إعداد تبويب عرض الكروت لـ User Manager بنجاح
2025-07-25 00:03:02,747 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-25 00:03:02,748 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-25 00:03:02,873 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام user_manager
2025-07-25 00:03:02,874 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-25 00:03:02,958 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager
2025-07-25 00:03:02,965 - INFO - تم تسجيل العملية: تبديل النظام من التلجرام - تم تبديل النظام من None إلى user_manager
2025-07-25 00:03:03,030 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-25 00:03:03,031 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-25 00:03:03,032 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-25 00:03:03,169 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-25 00:03:03,173 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-25 00:03:03,264 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-25 00:03:03,265 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-25 00:03:03,268 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-25 00:03:03,273 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-25 00:03:03,276 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-25 00:03:03,278 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager - القالب: 10
2025-07-25 00:03:03,534 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-25 00:03:11,526 - INFO - معالجة callback: independent_create_um_10_lightning
2025-07-25 00:03:16,962 - INFO - معالجة callback: independent_count_um_10_lightning_200
2025-07-25 00:03:17,614 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-25 00:03:17,614 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-25 00:03:17,628 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-25 00:03:17,629 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-25 00:03:17,655 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-25 00:03:17,656 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-25 00:03:17,659 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-25 00:03:17,662 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-25 00:03:17,922 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-25 00:03:17,922 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-25 00:03:17,937 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-25 00:03:17,966 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-25 00:03:17,996 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-25 00:03:18,014 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-25 00:03:18,017 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-25 00:03:18,021 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-25 00:03:18,022 - INFO - ⚡ البرق: تم الحفاظ على العدد المحدد: 200
2025-07-25 00:03:18,023 - INFO - تم تطبيق إعدادات القالب '10' بنجاح
2025-07-25 00:03:18,024 - INFO - تم تحديد معلومات المستخدم الحالي: مستخدم التلجرام بوت
2025-07-25 00:03:18,024 - INFO - ⚡ استخدام البرق للتلجرام (مع جدولة مؤقتة وتنظيف تلقائي)
2025-07-25 00:03:18,024 - INFO - ⚡ بدء البرق التلقائي للتلجرام (مع جدولة مؤقتة وتنظيف تلقائي)
2025-07-25 00:03:18,024 - INFO - ⚡ البرق: استخدام العدد من التلجرام بوت: 200
2025-07-25 00:03:18,025 - INFO - ⚡ البرق للتلجرام: العدد المطلوب 200 حساب
2025-07-25 00:03:18,025 - INFO - ⚡ البرق: تم تحديث عدد الحسابات في الواجهة إلى: 200
2025-07-25 00:03:18,028 - INFO - ⚡ البرق للتلجرام: تم توليد 200 حساب
2025-07-25 00:03:18,028 - INFO - ⚡ البرق للتلجرام: تنفيذ متسلسل مجدول لـ 200 كارت (حد التقسيم: 100)
2025-07-25 00:03:18,029 - INFO - ⚡ البرق للتلجرام: بدء معالجة الأعداد الكبيرة مع جدولة واحدة
2025-07-25 00:03:18,030 - INFO - ⚡ البرق للتلجرام: تم تقسيم 200 كارت إلى 2 مجموعة للتنفيذ المتسلسل
2025-07-25 00:03:18,030 - INFO - ⚡ البرق للتلجرام: إنشاء السكريبت للمجموعة 1/2 (100 كارت)
2025-07-25 00:03:18,030 - INFO - ⚡ البرق للتلجرام: تم إعداد السكريبت telegram_lightning_batch1_user_manager_20250725_000318
2025-07-25 00:03:18,031 - INFO - ⚡ البرق للتلجرام: إنشاء السكريبت للمجموعة 2/2 (100 كارت)
2025-07-25 00:03:18,031 - ERROR - خطأ في البرق للأعداد الكبيرة للتلجرام: name 'bot_token' is not defined
2025-07-25 00:03:28,903 - INFO - بدء إغلاق التطبيق
2025-07-25 00:03:28,909 - INFO - تم تسجيل العملية: إغلاق التطبيق - تم إغلاق التطبيق بنجاح
2025-07-25 00:03:28,910 - INFO - تم إغلاق التطبيق بنجاح
2025-07-25 00:12:44,145 - INFO - تم بدء تشغيل التطبيق
2025-07-25 00:12:44,238 - INFO - تم إنشاء المجلدات الأساسية
2025-07-25 00:12:44,283 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-25 00:12:44,283 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-25 00:12:44,622 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-25 00:12:49,933 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-25 00:12:49,933 - INFO - تم إعداد التطبيق بنجاح
2025-07-25 00:12:51,974 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-25 00:12:52,429 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-25 00:12:52,429 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-25 00:12:55,435 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-25 00:12:55,472 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-25 00:12:55,728 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-25 00:12:55,728 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-25 00:13:25,540 - INFO - معالجة callback: select_system_um
2025-07-25 00:13:26,182 - INFO - تم إرسال 2 قالب مفلتر لنظام User Manager
2025-07-25 00:13:29,555 - INFO - معالجة callback: independent_template_um_10
2025-07-25 00:13:30,050 - INFO - 🔄 تبديل النظام تلقائياً من None إلى user_manager
2025-07-25 00:13:30,051 - INFO - 🔄 طلب تبديل النظام من التلجرام: None → user_manager
2025-07-25 00:13:30,309 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-25 00:13:30,377 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-25 00:13:30,477 - WARNING - تحذير: فشل في حفظ الإعدادات: 'MikroTikCardGenerator' object has no attribute 'version_combo'
2025-07-25 00:13:30,478 - INFO - تم تعيين النظام الجديد: user_manager
2025-07-25 00:13:30,479 - INFO - 🔄 إعادة بناء الواجهة للنظام: user_manager
2025-07-25 00:13:30,479 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-25 00:13:30,482 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-25 00:13:31,149 - INFO - تم إعداد تبويب عرض الكروت لـ User Manager بنجاح
2025-07-25 00:13:31,329 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-25 00:13:31,329 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-25 00:13:31,473 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام user_manager
2025-07-25 00:13:31,474 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-25 00:13:31,544 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager
2025-07-25 00:13:31,581 - INFO - تم تسجيل العملية: تبديل النظام من التلجرام - تم تبديل النظام من None إلى user_manager
2025-07-25 00:13:31,643 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-25 00:13:31,644 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-25 00:13:31,649 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-25 00:13:31,871 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-25 00:13:31,872 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-25 00:13:31,897 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-25 00:13:31,923 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-25 00:13:31,927 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-25 00:13:31,931 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-25 00:13:31,934 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-25 00:13:31,935 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager - القالب: 10
2025-07-25 00:13:32,288 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-25 00:14:12,697 - INFO - معالجة callback: independent_create_um_10_lightning
2025-07-25 00:14:31,292 - INFO - معالجة callback: independent_count_um_10_lightning_200
2025-07-25 00:14:31,758 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-25 00:14:31,758 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-25 00:14:31,773 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-25 00:14:31,773 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-25 00:14:31,803 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-25 00:14:31,818 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-25 00:14:31,822 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-25 00:14:31,850 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-25 00:14:32,101 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-25 00:14:32,101 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-25 00:14:32,116 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-25 00:14:32,140 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-25 00:14:32,166 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-25 00:14:32,167 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-25 00:14:32,169 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-25 00:14:32,174 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-25 00:14:32,175 - INFO - ⚡ البرق: تم الحفاظ على العدد المحدد: 200
2025-07-25 00:14:32,184 - INFO - تم تطبيق إعدادات القالب '10' بنجاح
2025-07-25 00:14:32,201 - INFO - تم تحديد معلومات المستخدم الحالي: مستخدم التلجرام بوت
2025-07-25 00:14:32,201 - INFO - ⚡ استخدام البرق للتلجرام (مع جدولة مؤقتة وتنظيف تلقائي)
2025-07-25 00:14:32,201 - INFO - ⚡ بدء البرق التلقائي للتلجرام (مع جدولة مؤقتة وتنظيف تلقائي)
2025-07-25 00:14:32,202 - INFO - ⚡ البرق: استخدام العدد من التلجرام بوت: 200
2025-07-25 00:14:32,202 - INFO - ⚡ البرق للتلجرام: العدد المطلوب 200 حساب
2025-07-25 00:14:32,202 - INFO - ⚡ البرق: تم تحديث عدد الحسابات في الواجهة إلى: 200
2025-07-25 00:14:32,205 - INFO - ⚡ البرق للتلجرام: تم توليد 200 حساب
2025-07-25 00:14:32,207 - INFO - ⚡ البرق للتلجرام: تنفيذ متسلسل مجدول لـ 200 كارت (حد التقسيم: 100)
2025-07-25 00:14:32,208 - INFO - ⚡ البرق للتلجرام: بدء معالجة الأعداد الكبيرة مع جدولة واحدة
2025-07-25 00:14:32,208 - INFO - ⚡ البرق للتلجرام: تم تقسيم 200 كارت إلى 2 مجموعة للتنفيذ المتسلسل
2025-07-25 00:14:32,209 - INFO - ⚡ البرق للتلجرام: إنشاء السكريبت للمجموعة 1/2 (100 كارت)
2025-07-25 00:14:32,210 - INFO - ⚡ البرق للتلجرام: تم إعداد السكريبت telegram_lightning_batch1_user_manager_20250725_001432
2025-07-25 00:14:32,210 - INFO - ⚡ البرق للتلجرام: إنشاء السكريبت للمجموعة 2/2 (100 كارت)
2025-07-25 00:14:32,211 - INFO - ⚡ البرق للتلجرام: تم إعداد السكريبت telegram_lightning_batch2_user_manager_20250725_001432
2025-07-25 00:14:32,213 - INFO - محاولة الاتصال بـ 6.6.6.100:8728 (عادي)
2025-07-25 00:14:32,280 - INFO - نجح الاتصال مع 6.6.6.100
2025-07-25 00:14:32,357 - INFO - ⚡ تم إرسال السكريبت المترابط: telegram_lightning_batch1_user_manager_20250725_001432
2025-07-25 00:14:32,404 - INFO - ⚡ تم إرسال السكريبت المترابط: telegram_lightning_batch2_user_manager_20250725_001432
2025-07-25 00:14:32,428 - INFO - ⚡ وقت MikroTik الحالي: 21:14:32
2025-07-25 00:14:32,533 - INFO - ⚡ السكريبت الأول سينفذ في: 21:14:35 (بعد 3 ثواني من وقت MikroTik)
2025-07-25 00:14:32,591 - INFO - ⚡ تم إنشاء الجدولة الرئيسية: telegram_main_schedule_20250725_001432 لتشغيل telegram_lightning_batch1_user_manager_20250725_001432 في 21:14:35
2025-07-25 00:14:32,597 - INFO - ⚡ تم إعداد البرق المترابط بنجاح - جدولة واحدة: telegram_main_schedule_20250725_001432, 2 سكريبت مترابط
2025-07-25 00:14:32,598 - INFO - ⚡ البرق للتلجرام مكتمل: تم إنشاء جدولة واحدة لـ 2 مجموعة متسلسلة (200 كارت)
2025-07-25 00:14:35,359 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 200
2025-07-25 00:14:35,380 - INFO - تم إنشاء PDF بنجاح: exports/كروت_(10)_ب20-200 كارت-25-07-2025-00-14-35-um.pdf
2025-07-25 00:14:35,386 - INFO - تم حفظ ملف .rsc الاحتياطي: exports/كروت_(10)_ب20-200 كارت-25-07-2025-00-14-35-um.rsc
2025-07-25 00:14:36,220 - INFO - تم إرسال الملف بنجاح: كروت_(10)_ب20-200 كارت-25-07-2025-00-14-35-um.pdf
2025-07-25 00:14:36,715 - INFO - تم إرسال 200 كرت عبر التلجرام باستخدام قالب 10
2025-07-25 00:15:01,258 - INFO - معالجة callback: independent_count_um_10_lightning_200
2025-07-25 00:15:01,776 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-25 00:15:01,777 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-25 00:15:01,785 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-25 00:15:01,796 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-25 00:15:01,825 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-25 00:15:01,825 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-25 00:15:01,829 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-25 00:15:01,869 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-25 00:15:02,115 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-25 00:15:02,116 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-25 00:15:02,131 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-25 00:15:02,132 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-25 00:15:02,204 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-25 00:15:02,205 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-25 00:15:02,208 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-25 00:15:02,214 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-25 00:15:02,214 - INFO - ⚡ البرق: تم الحفاظ على العدد المحدد: 200
2025-07-25 00:15:02,215 - INFO - تم تطبيق إعدادات القالب '10' بنجاح
2025-07-25 00:15:02,215 - INFO - تم تحديد معلومات المستخدم الحالي: مستخدم التلجرام بوت
2025-07-25 00:15:02,216 - INFO - ⚡ استخدام البرق للتلجرام (مع جدولة مؤقتة وتنظيف تلقائي)
2025-07-25 00:15:02,216 - INFO - ⚡ بدء البرق التلقائي للتلجرام (مع جدولة مؤقتة وتنظيف تلقائي)
2025-07-25 00:15:02,216 - INFO - ⚡ البرق: استخدام العدد من التلجرام بوت: 200
2025-07-25 00:15:02,216 - INFO - ⚡ البرق للتلجرام: العدد المطلوب 200 حساب
2025-07-25 00:15:02,217 - INFO - ⚡ البرق: تم تحديث عدد الحسابات في الواجهة إلى: 200
2025-07-25 00:15:02,219 - INFO - ⚡ البرق للتلجرام: تم توليد 200 حساب
2025-07-25 00:15:02,219 - INFO - ⚡ البرق للتلجرام: تنفيذ متسلسل مجدول لـ 200 كارت (حد التقسيم: 100)
2025-07-25 00:15:02,222 - INFO - ⚡ البرق للتلجرام: بدء معالجة الأعداد الكبيرة مع جدولة واحدة
2025-07-25 00:15:02,222 - INFO - ⚡ البرق للتلجرام: تم تقسيم 200 كارت إلى 2 مجموعة للتنفيذ المتسلسل
2025-07-25 00:15:02,222 - INFO - ⚡ البرق للتلجرام: إنشاء السكريبت للمجموعة 1/2 (100 كارت)
2025-07-25 00:15:02,223 - INFO - ⚡ البرق للتلجرام: تم إعداد السكريبت telegram_lightning_batch1_user_manager_20250725_001502
2025-07-25 00:15:02,223 - INFO - ⚡ البرق للتلجرام: إنشاء السكريبت للمجموعة 2/2 (100 كارت)
2025-07-25 00:15:02,223 - INFO - ⚡ البرق للتلجرام: تم إعداد السكريبت telegram_lightning_batch2_user_manager_20250725_001502
2025-07-25 00:15:02,226 - INFO - استخدام الاتصال الحالي
2025-07-25 00:15:02,337 - INFO - ⚡ تم إرسال السكريبت المترابط: telegram_lightning_batch1_user_manager_20250725_001502
2025-07-25 00:15:02,385 - INFO - ⚡ تم إرسال السكريبت المترابط: telegram_lightning_batch2_user_manager_20250725_001502
2025-07-25 00:15:02,420 - INFO - ⚡ وقت MikroTik الحالي: 21:15:02
2025-07-25 00:15:02,425 - INFO - ⚡ السكريبت الأول سينفذ في: 21:15:05 (بعد 3 ثواني من وقت MikroTik)
2025-07-25 00:15:02,491 - INFO - ⚡ تم إنشاء الجدولة الرئيسية: telegram_main_schedule_20250725_001502 لتشغيل telegram_lightning_batch1_user_manager_20250725_001502 في 21:15:05
2025-07-25 00:15:02,505 - INFO - ⚡ تم إعداد البرق المترابط بنجاح - جدولة واحدة: telegram_main_schedule_20250725_001502, 2 سكريبت مترابط
2025-07-25 00:15:02,505 - INFO - ⚡ البرق للتلجرام مكتمل: تم إنشاء جدولة واحدة لـ 2 مجموعة متسلسلة (200 كارت)
2025-07-25 00:15:05,307 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 200
2025-07-25 00:15:05,364 - INFO - تم إنشاء PDF بنجاح: exports/كروت_(10)_ب20-200 كارت-25-07-2025-00-15-05-um.pdf
2025-07-25 00:15:05,386 - INFO - تم حفظ ملف .rsc الاحتياطي: exports/كروت_(10)_ب20-200 كارت-25-07-2025-00-15-05-um.rsc
2025-07-25 00:15:06,084 - INFO - تم إرسال الملف بنجاح: كروت_(10)_ب20-200 كارت-25-07-2025-00-15-05-um.pdf
2025-07-25 00:15:06,330 - INFO - تم إرسال 200 كرت عبر التلجرام باستخدام قالب 10
2025-07-25 00:27:46,362 - INFO - تم بدء تشغيل التطبيق
2025-07-25 00:27:46,415 - INFO - تم إنشاء المجلدات الأساسية
2025-07-25 00:27:46,567 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-25 00:27:46,567 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-25 00:27:46,896 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-25 00:27:48,778 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-25 00:27:48,778 - INFO - تم إعداد التطبيق بنجاح
2025-07-25 00:27:50,817 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-25 00:27:51,246 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-25 00:27:51,247 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-25 00:27:54,252 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-25 00:27:54,290 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-25 00:27:54,619 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-25 00:27:54,619 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-25 00:28:00,921 - INFO - معالجة callback: select_system_um
2025-07-25 00:28:01,394 - INFO - تم إرسال 2 قالب مفلتر لنظام User Manager
2025-07-25 00:28:02,473 - INFO - معالجة callback: independent_template_um_10
2025-07-25 00:28:03,212 - INFO - ✅ تم إرسال إشعار اختيار القالب '10' - العدد الحالي: 0
2025-07-25 00:28:03,212 - INFO - 🔄 تبديل النظام تلقائياً من None إلى user_manager
2025-07-25 00:28:03,212 - INFO - 🔄 طلب تبديل النظام من التلجرام: None → user_manager
2025-07-25 00:28:03,476 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-25 00:28:03,524 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-25 00:28:03,625 - WARNING - تحذير: فشل في حفظ الإعدادات: 'MikroTikCardGenerator' object has no attribute 'version_combo'
2025-07-25 00:28:03,625 - INFO - تم تعيين النظام الجديد: user_manager
2025-07-25 00:28:03,626 - INFO - 🔄 إعادة بناء الواجهة للنظام: user_manager
2025-07-25 00:28:03,626 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-25 00:28:03,629 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-25 00:28:04,247 - INFO - تم إعداد تبويب عرض الكروت لـ User Manager بنجاح
2025-07-25 00:28:04,438 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-25 00:28:04,451 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-25 00:28:04,607 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام user_manager
2025-07-25 00:28:04,607 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-25 00:28:04,676 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager
2025-07-25 00:28:04,717 - INFO - تم تسجيل العملية: تبديل النظام من التلجرام - تم تبديل النظام من None إلى user_manager
2025-07-25 00:28:04,768 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-25 00:28:04,770 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-25 00:28:04,770 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-25 00:28:05,012 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-25 00:28:05,013 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-25 00:28:05,038 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-25 00:28:05,040 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-25 00:28:05,043 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-25 00:28:05,048 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-25 00:28:05,051 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-25 00:28:05,053 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager - القالب: 10
2025-07-25 00:28:05,335 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-25 00:29:09,039 - INFO - معالجة callback: independent_create_um_10_lightning
2025-07-25 00:38:00,746 - INFO - تم بدء تشغيل التطبيق
2025-07-25 00:38:00,783 - INFO - تم إنشاء المجلدات الأساسية
2025-07-25 00:38:00,817 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-25 00:38:00,818 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-25 00:38:01,139 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-25 00:38:09,470 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-25 00:38:09,471 - INFO - تم إعداد التطبيق بنجاح
2025-07-25 00:38:11,518 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-25 00:38:11,880 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-25 00:38:11,881 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-25 00:38:14,895 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-25 00:38:14,957 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-25 00:38:15,275 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-25 00:38:15,275 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-25 00:38:16,826 - INFO - معالجة callback: select_system_um
2025-07-25 00:38:17,311 - INFO - تم إرسال 2 قالب مفلتر لنظام User Manager
2025-07-25 00:38:18,350 - INFO - معالجة callback: independent_template_um_10
2025-07-25 00:38:18,829 - INFO - 🔄 تبديل النظام تلقائياً من None إلى user_manager
2025-07-25 00:38:18,829 - INFO - 🔄 طلب تبديل النظام من التلجرام: None → user_manager
2025-07-25 00:38:19,104 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-25 00:38:19,162 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-25 00:38:19,263 - WARNING - تحذير: فشل في حفظ الإعدادات: 'MikroTikCardGenerator' object has no attribute 'version_combo'
2025-07-25 00:38:19,263 - INFO - تم تعيين النظام الجديد: user_manager
2025-07-25 00:38:19,264 - INFO - 🔄 إعادة بناء الواجهة للنظام: user_manager
2025-07-25 00:38:19,265 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-25 00:38:19,267 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-25 00:38:19,761 - INFO - تم إعداد تبويب عرض الكروت لـ User Manager بنجاح
2025-07-25 00:38:19,954 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-25 00:38:19,954 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-25 00:38:20,126 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام user_manager
2025-07-25 00:38:20,126 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-25 00:38:20,213 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager
2025-07-25 00:38:20,217 - INFO - تم تسجيل العملية: تبديل النظام من التلجرام - تم تبديل النظام من None إلى user_manager
2025-07-25 00:38:20,282 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-25 00:38:20,283 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-25 00:38:20,283 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-25 00:38:20,588 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-25 00:38:20,589 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-25 00:38:20,617 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-25 00:38:20,639 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-25 00:38:20,642 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-25 00:38:20,646 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-25 00:38:20,649 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-25 00:38:20,650 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager - القالب: 10
2025-07-25 00:38:20,913 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-25 00:38:22,685 - INFO - معالجة callback: independent_create_um_10_lightning
2025-07-25 00:38:22,983 - INFO - محاولة الاتصال بـ 6.6.6.100:8728 (عادي)
2025-07-25 00:38:23,042 - INFO - نجح الاتصال مع 6.6.6.100
2025-07-25 00:38:25,435 - INFO - ✅ تم إرسال إشعار اختيار القالب '10' - العدد الحالي: 0
2025-07-25 00:52:14,917 - INFO - تم بدء تشغيل التطبيق
2025-07-25 00:52:14,985 - INFO - تم إنشاء المجلدات الأساسية
2025-07-25 00:52:15,394 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-25 00:52:15,395 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-25 00:52:18,948 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-25 00:52:42,496 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-25 00:52:42,497 - INFO - تم إعداد التطبيق بنجاح
2025-07-25 00:52:44,577 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-25 00:52:45,998 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-25 00:52:46,006 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-25 00:52:49,010 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-25 00:52:49,099 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-25 00:52:49,458 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-25 00:52:49,458 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-25 00:53:20,185 - INFO - معالجة callback: select_system_um
2025-07-25 00:53:20,640 - INFO - تم إرسال 2 قالب مفلتر لنظام User Manager
2025-07-25 00:53:23,233 - INFO - معالجة callback: independent_template_um_10
2025-07-25 00:53:23,852 - INFO - 🔄 تبديل النظام تلقائياً من None إلى user_manager
2025-07-25 00:53:23,852 - INFO - 🔄 طلب تبديل النظام من التلجرام: None → user_manager
2025-07-25 00:53:24,162 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-25 00:53:24,257 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-25 00:53:24,357 - WARNING - تحذير: فشل في حفظ الإعدادات: 'MikroTikCardGenerator' object has no attribute 'version_combo'
2025-07-25 00:53:24,358 - INFO - تم تعيين النظام الجديد: user_manager
2025-07-25 00:53:24,359 - INFO - 🔄 إعادة بناء الواجهة للنظام: user_manager
2025-07-25 00:53:24,359 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-25 00:53:24,363 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-25 00:53:25,031 - INFO - تم إعداد تبويب عرض الكروت لـ User Manager بنجاح
2025-07-25 00:53:25,220 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-25 00:53:25,220 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-25 00:53:25,376 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام user_manager
2025-07-25 00:53:25,377 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-25 00:53:25,708 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager
2025-07-25 00:53:25,713 - INFO - تم تسجيل العملية: تبديل النظام من التلجرام - تم تبديل النظام من None إلى user_manager
2025-07-25 00:53:25,718 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-25 00:53:25,719 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-25 00:53:25,720 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-25 00:53:25,788 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-25 00:53:25,803 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-25 00:53:25,854 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-25 00:53:25,866 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-25 00:53:25,870 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-25 00:53:25,878 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-25 00:53:25,883 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-25 00:53:25,895 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager - القالب: 10
2025-07-25 00:53:26,166 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-25 00:53:30,879 - INFO - معالجة callback: independent_create_um_10_lightning
2025-07-25 00:53:31,092 - INFO - 🔍 بدء اختبار جلب العدد الحالي للكروت قبل إرسال الإشعار
2025-07-25 00:53:31,092 - INFO - 🧪 بدء اختبار الاتصال وجلب عدد User Manager
2025-07-25 00:53:31,093 - INFO - محاولة الاتصال بـ 6.6.6.100:8728 (عادي)
2025-07-25 00:53:31,164 - INFO - نجح الاتصال مع 6.6.6.100
2025-07-25 00:53:31,166 - INFO - ✅ نجح الاتصال - اختبار جلب العدد...
2025-07-25 00:53:31,204 - INFO - 🏷️ اسم النظام: MikroTik
2025-07-25 00:53:31,247 - INFO - 📋 إصدار RouterOS: 6.43.12 (stable)
2025-07-25 00:53:31,247 - INFO - 📊 اختبار جلب مستخدمي User Manager...
2025-07-25 00:53:33,329 - INFO - ✅ تم جلب 1159 مستخدم من User Manager
2025-07-25 00:53:33,329 - INFO - 👥 عرض أول 5 مستخدمين:
2025-07-25 00:53:33,330 - INFO -   1. غير محدد (البروفايل: غير محدد)
2025-07-25 00:53:33,330 - INFO -   2. غير محدد (البروفايل: غير محدد)
2025-07-25 00:53:33,330 - INFO -   3. غير محدد (البروفايل: غير محدد)
2025-07-25 00:53:33,330 - INFO -   4. غير محدد (البروفايل: غير محدد)
2025-07-25 00:53:33,331 - INFO -   5. غير محدد (البروفايل: غير محدد)
2025-07-25 00:53:33,331 - ERROR - ❌ خطأ في API: 'RouterOsApi' object has no attribute 'disconnect'
2025-07-25 00:53:33,332 - INFO - 📊 نتيجة الاختبار: خطأ في API: 'RouterOsApi' object has no attribute 'disconnect'
2025-07-25 00:53:33,332 - INFO - 🔔 بدء إرسال إشعار اختيار القالب: 10 (User Manager)
2025-07-25 00:53:33,333 - INFO - 📊 جلب العدد الحالي من User Manager...
2025-07-25 00:53:33,333 - INFO - 🔍 بدء جلب العدد الحالي للكروت في User Manager
2025-07-25 00:53:33,334 - INFO - ✅ مكتبة routeros_api متوفرة
2025-07-25 00:53:33,334 - INFO - 🔗 محاولة الاتصال بـ 6.6.6.100 باستخدام المستخدم: 11
2025-07-25 00:53:33,338 - INFO - استخدام الاتصال الحالي
2025-07-25 00:53:33,340 - INFO - ✅ تم الاتصال بـ MikroTik بنجاح
2025-07-25 00:53:33,341 - INFO - 📊 جلب قائمة المستخدمين من User Manager...
2025-07-25 00:53:35,422 - INFO - ✅ تم جلب 1159 مستخدم من User Manager بنجاح
2025-07-25 00:53:35,422 - INFO - 👤 مستخدم 1: غير محدد
2025-07-25 00:53:35,422 - INFO - 👤 مستخدم 2: غير محدد
2025-07-25 00:53:35,423 - INFO - 👤 مستخدم 3: غير محدد
2025-07-25 00:53:35,423 - INFO - 📈 العدد المجلوب من User Manager: 1159
2025-07-25 00:53:35,690 - INFO - ✅ تم إرسال إشعار اختيار القالب '10' - العدد الحالي: 1159
2025-07-25 01:18:02,119 - INFO - معالجة الأمر: /start
2025-07-25 01:18:02,456 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-25 01:18:09,131 - INFO - معالجة callback: select_system_um
2025-07-25 01:18:10,231 - INFO - تم إرسال 2 قالب مفلتر لنظام User Manager
2025-07-25 01:18:12,339 - INFO - معالجة callback: independent_template_um_10
2025-07-25 01:18:12,875 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-25 01:18:12,876 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-25 01:18:12,876 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-25 01:18:12,893 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-25 01:18:12,894 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-25 01:18:12,934 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-25 01:18:12,935 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-25 01:18:12,938 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-25 01:18:12,943 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-25 01:18:12,948 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-25 01:18:12,959 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager - القالب: 10
2025-07-25 01:18:13,284 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-25 01:18:15,363 - INFO - معالجة callback: independent_create_um_10_lightning
2025-07-25 01:18:15,689 - INFO - 🔍 بدء اختبار جلب العدد الحالي للكروت قبل إرسال الإشعار
2025-07-25 01:18:15,689 - INFO - 🧪 بدء اختبار الاتصال وجلب عدد User Manager
2025-07-25 01:18:15,693 - INFO - استخدام الاتصال الحالي
2025-07-25 01:18:15,693 - INFO - ✅ نجح الاتصال - اختبار جلب العدد...
2025-07-25 01:18:15,734 - INFO - 🏷️ اسم النظام: MikroTik
2025-07-25 01:18:15,777 - INFO - 📋 إصدار RouterOS: 6.43.12 (stable)
2025-07-25 01:18:15,778 - INFO - 📊 اختبار جلب مستخدمي User Manager...
2025-07-25 01:18:17,847 - INFO - ✅ تم جلب 1159 مستخدم من User Manager
2025-07-25 01:18:17,848 - INFO - 👥 عرض أول 5 مستخدمين:
2025-07-25 01:18:17,848 - INFO -   1. غير محدد (البروفايل: غير محدد)
2025-07-25 01:18:17,848 - INFO -   2. غير محدد (البروفايل: غير محدد)
2025-07-25 01:18:17,849 - INFO -   3. غير محدد (البروفايل: غير محدد)
2025-07-25 01:18:17,850 - INFO -   4. غير محدد (البروفايل: غير محدد)
2025-07-25 01:18:17,850 - INFO -   5. غير محدد (البروفايل: غير محدد)
2025-07-25 01:18:17,851 - ERROR - ❌ خطأ في API: 'RouterOsApi' object has no attribute 'disconnect'
2025-07-25 01:18:17,851 - INFO - 📊 نتيجة الاختبار: خطأ في API: 'RouterOsApi' object has no attribute 'disconnect'
2025-07-25 01:18:17,852 - INFO - 🔔 بدء إرسال إشعار اختيار القالب: 10 (User Manager)
2025-07-25 01:18:17,853 - INFO - 📊 جلب العدد الحالي من User Manager...
2025-07-25 01:18:17,854 - INFO - 🔍 بدء جلب العدد الحالي للكروت في User Manager
2025-07-25 01:18:17,855 - INFO - ✅ مكتبة routeros_api متوفرة
2025-07-25 01:18:17,856 - INFO - 🔗 محاولة الاتصال بـ 6.6.6.100 باستخدام المستخدم: 11
2025-07-25 01:18:17,860 - INFO - استخدام الاتصال الحالي
2025-07-25 01:18:17,862 - INFO - ✅ تم الاتصال بـ MikroTik بنجاح
2025-07-25 01:18:17,863 - INFO - 📊 جلب قائمة المستخدمين من User Manager...
2025-07-25 01:18:19,939 - INFO - ✅ تم جلب 1159 مستخدم من User Manager بنجاح
2025-07-25 01:18:19,940 - INFO - 👤 مستخدم 1: غير محدد
2025-07-25 01:18:19,940 - INFO - 👤 مستخدم 2: غير محدد
2025-07-25 01:18:19,941 - INFO - 👤 مستخدم 3: غير محدد
2025-07-25 01:18:19,942 - INFO - 📈 العدد المجلوب من User Manager: 1159
2025-07-25 01:18:20,424 - INFO - ✅ تم إرسال إشعار اختيار القالب '10' - العدد الحالي: 1159
2025-07-25 01:18:23,625 - INFO - معالجة callback: independent_count_um_10_lightning_200
2025-07-25 01:18:24,161 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-25 01:18:24,162 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-25 01:18:24,177 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-25 01:18:24,179 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-25 01:18:24,250 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-25 01:18:24,251 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-25 01:18:24,255 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-25 01:18:24,261 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-25 01:18:24,519 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-25 01:18:24,519 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-25 01:18:24,544 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-25 01:18:24,545 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-25 01:18:24,586 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-25 01:18:24,587 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-25 01:18:24,590 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-25 01:18:24,595 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-25 01:18:24,595 - INFO - ⚡ البرق: تم الحفاظ على العدد المحدد: 200
2025-07-25 01:18:24,597 - INFO - تم تطبيق إعدادات القالب '10' بنجاح
2025-07-25 01:18:24,598 - INFO - ⚡ استخدام البرق للتلجرام (مع جدولة مؤقتة وتنظيف تلقائي)
2025-07-25 01:18:24,598 - INFO - ⚡ بدء البرق التلقائي للتلجرام (مع جدولة مؤقتة وتنظيف تلقائي)
2025-07-25 01:18:24,598 - INFO - ⚡ البرق: استخدام العدد من التلجرام بوت: 200
2025-07-25 01:18:24,599 - INFO - ⚡ البرق للتلجرام: العدد المطلوب 200 حساب
2025-07-25 01:18:24,599 - INFO - ⚡ البرق: تم تحديث عدد الحسابات في الواجهة إلى: 200
2025-07-25 01:18:24,602 - INFO - ⚡ البرق للتلجرام: تم توليد 200 حساب
2025-07-25 01:18:24,614 - INFO - ⚡ البرق للتلجرام: تنفيذ متسلسل مجدول لـ 200 كارت (حد التقسيم: 100)
2025-07-25 01:18:24,615 - INFO - ⚡ البرق للتلجرام: بدء معالجة الأعداد الكبيرة مع جدولة واحدة
2025-07-25 01:18:24,615 - INFO - ⚡ البرق للتلجرام: تم تقسيم 200 كارت إلى 2 مجموعة للتنفيذ المتسلسل
2025-07-25 01:18:24,615 - INFO - ⚡ البرق للتلجرام: إنشاء السكريبت للمجموعة 1/2 (100 كارت)
2025-07-25 01:18:24,616 - INFO - ⚡ البرق للتلجرام: تم إعداد السكريبت telegram_lightning_batch1_user_manager_20250725_011824
2025-07-25 01:18:24,616 - INFO - ⚡ البرق للتلجرام: إنشاء السكريبت للمجموعة 2/2 (100 كارت)
2025-07-25 01:18:24,617 - INFO - ⚡ البرق للتلجرام: تم إعداد السكريبت telegram_lightning_batch2_user_manager_20250725_011824
2025-07-25 01:18:24,620 - INFO - استخدام الاتصال الحالي
2025-07-25 01:18:24,675 - INFO - ⚡ تم إرسال السكريبت المترابط: telegram_lightning_batch1_user_manager_20250725_011824
2025-07-25 01:18:24,716 - INFO - ⚡ تم إرسال السكريبت المترابط: telegram_lightning_batch2_user_manager_20250725_011824
2025-07-25 01:18:24,755 - INFO - ⚡ وقت MikroTik الحالي: 22:18:24
2025-07-25 01:18:24,778 - INFO - ⚡ السكريبت الأول سينفذ في: 22:18:27 (بعد 3 ثواني من وقت MikroTik)
2025-07-25 01:18:24,826 - INFO - ⚡ تم إنشاء الجدولة الرئيسية: telegram_main_schedule_20250725_011824 لتشغيل telegram_lightning_batch1_user_manager_20250725_011824 في 22:18:27
2025-07-25 01:18:24,834 - INFO - ⚡ تم إعداد البرق المترابط بنجاح - جدولة واحدة: telegram_main_schedule_20250725_011824, 2 سكريبت مترابط
2025-07-25 01:18:24,835 - INFO - ⚡ البرق للتلجرام مكتمل: تم إنشاء جدولة واحدة لـ 2 مجموعة متسلسلة (200 كارت)
2025-07-25 01:18:27,779 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 200
2025-07-25 01:18:27,833 - INFO - تم إنشاء PDF بنجاح: exports/كروت_(10)_ب20-200 كارت-25-07-2025-01-18-27-um.pdf
2025-07-25 01:18:27,863 - INFO - تم حفظ ملف .rsc الاحتياطي: exports/كروت_(10)_ب20-200 كارت-25-07-2025-01-18-27-um.rsc
2025-07-25 01:18:28,547 - INFO - تم إرسال الملف بنجاح: كروت_(10)_ب20-200 كارت-25-07-2025-01-18-27-um.pdf
2025-07-25 01:18:28,809 - INFO - تم إرسال 200 كرت عبر التلجرام باستخدام قالب 10
2025-07-25 01:18:36,473 - INFO - معالجة الأمر: /start
2025-07-25 01:18:36,869 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-25 01:18:48,325 - INFO - معالجة callback: select_system_um
2025-07-25 01:18:48,925 - INFO - تم إرسال 2 قالب مفلتر لنظام User Manager
2025-07-25 01:18:50,155 - INFO - معالجة callback: independent_template_um_10
2025-07-25 01:18:50,677 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-25 01:18:50,677 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-25 01:18:50,677 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-25 01:18:50,703 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-25 01:18:50,704 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-25 01:18:50,746 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-25 01:18:50,747 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-25 01:18:50,749 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-25 01:18:50,754 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-25 01:18:50,755 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-25 01:18:50,757 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager - القالب: 10
2025-07-25 01:18:51,138 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-25 01:18:54,084 - INFO - معالجة callback: independent_create_um_10_lightning
2025-07-25 01:18:54,338 - INFO - 🔍 بدء اختبار جلب العدد الحالي للكروت قبل إرسال الإشعار
2025-07-25 01:18:54,339 - INFO - 🧪 بدء اختبار الاتصال وجلب عدد User Manager
2025-07-25 01:18:54,343 - INFO - استخدام الاتصال الحالي
2025-07-25 01:18:54,347 - INFO - ✅ نجح الاتصال - اختبار جلب العدد...
2025-07-25 01:18:54,384 - INFO - 🏷️ اسم النظام: MikroTik
2025-07-25 01:18:54,428 - INFO - 📋 إصدار RouterOS: 6.43.12 (stable)
2025-07-25 01:18:54,428 - INFO - 📊 اختبار جلب مستخدمي User Manager...
2025-07-25 01:18:56,858 - INFO - ✅ تم جلب 1359 مستخدم من User Manager
2025-07-25 01:18:56,859 - INFO - 👥 عرض أول 5 مستخدمين:
2025-07-25 01:18:56,859 - INFO -   1. غير محدد (البروفايل: غير محدد)
2025-07-25 01:18:56,859 - INFO -   2. غير محدد (البروفايل: غير محدد)
2025-07-25 01:18:56,860 - INFO -   3. غير محدد (البروفايل: غير محدد)
2025-07-25 01:18:56,860 - INFO -   4. غير محدد (البروفايل: غير محدد)
2025-07-25 01:18:56,860 - INFO -   5. غير محدد (البروفايل: غير محدد)
2025-07-25 01:18:56,861 - ERROR - ❌ خطأ في API: 'RouterOsApi' object has no attribute 'disconnect'
2025-07-25 01:18:56,862 - INFO - 📊 نتيجة الاختبار: خطأ في API: 'RouterOsApi' object has no attribute 'disconnect'
2025-07-25 01:18:56,862 - INFO - 🔔 بدء إرسال إشعار اختيار القالب: 10 (User Manager)
2025-07-25 01:18:56,862 - INFO - 📊 جلب العدد الحالي من User Manager...
2025-07-25 01:18:56,862 - INFO - 🔍 بدء جلب العدد الحالي للكروت في User Manager
2025-07-25 01:18:56,863 - INFO - ✅ مكتبة routeros_api متوفرة
2025-07-25 01:18:56,863 - INFO - 🔗 محاولة الاتصال بـ 6.6.6.100 باستخدام المستخدم: 11
2025-07-25 01:18:56,866 - INFO - استخدام الاتصال الحالي
2025-07-25 01:18:56,866 - INFO - ✅ تم الاتصال بـ MikroTik بنجاح
2025-07-25 01:18:56,867 - INFO - 📊 جلب قائمة المستخدمين من User Manager...
2025-07-25 01:18:59,288 - INFO - ✅ تم جلب 1359 مستخدم من User Manager بنجاح
2025-07-25 01:18:59,289 - INFO - 👤 مستخدم 1: غير محدد
2025-07-25 01:18:59,289 - INFO - 👤 مستخدم 2: غير محدد
2025-07-25 01:18:59,289 - INFO - 👤 مستخدم 3: غير محدد
2025-07-25 01:18:59,291 - INFO - 📈 العدد المجلوب من User Manager: 1359
2025-07-25 01:18:59,624 - INFO - ✅ تم إرسال إشعار اختيار القالب '10' - العدد الحالي: 1359
2025-07-25 01:19:17,754 - INFO - بدء إغلاق التطبيق
2025-07-25 01:19:17,755 - INFO - تم قطع الاتصال مع MikroTik
2025-07-25 01:19:17,761 - INFO - تم تسجيل العملية: إغلاق التطبيق - تم إغلاق التطبيق بنجاح
2025-07-25 01:19:17,761 - INFO - تم إغلاق التطبيق بنجاح
