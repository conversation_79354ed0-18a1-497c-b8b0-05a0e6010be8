2025-07-25 00:02:43,125 - INFO - تم بدء تشغيل التطبيق
2025-07-25 00:02:43,222 - INFO - تم إنشاء المجلدات الأساسية
2025-07-25 00:02:43,224 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-25 00:02:43,225 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-25 00:02:43,327 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-25 00:02:43,828 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-25 00:02:43,829 - INFO - تم <PERSON>عد<PERSON> التطبيق بنجاح
2025-07-25 00:02:45,831 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-25 00:02:46,122 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-25 00:02:46,123 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-25 00:02:49,132 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-25 00:02:49,133 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-25 00:02:49,373 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-25 00:02:49,374 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-25 00:02:50,727 - INFO - معالجة callback: select_system_um
2025-07-25 00:02:51,194 - INFO - تم إرسال 2 قالب مفلتر لنظام User Manager
2025-07-25 00:02:52,825 - INFO - معالجة callback: bot_stats
2025-07-25 00:02:54,996 - INFO - معالجة callback: refresh_templates
2025-07-25 00:02:55,451 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-25 00:02:59,460 - INFO - معالجة callback: select_system_um
2025-07-25 00:02:59,973 - INFO - تم إرسال 2 قالب مفلتر لنظام User Manager
2025-07-25 00:03:01,233 - INFO - معالجة callback: independent_template_um_10
2025-07-25 00:03:01,714 - INFO - 🔄 تبديل النظام تلقائياً من None إلى user_manager
2025-07-25 00:03:01,715 - INFO - 🔄 طلب تبديل النظام من التلجرام: None → user_manager
2025-07-25 00:03:01,986 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-25 00:03:02,023 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-25 00:03:02,123 - WARNING - تحذير: فشل في حفظ الإعدادات: 'MikroTikCardGenerator' object has no attribute 'version_combo'
2025-07-25 00:03:02,124 - INFO - تم تعيين النظام الجديد: user_manager
2025-07-25 00:03:02,125 - INFO - 🔄 إعادة بناء الواجهة للنظام: user_manager
2025-07-25 00:03:02,125 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-25 00:03:02,128 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-25 00:03:02,578 - INFO - تم إعداد تبويب عرض الكروت لـ User Manager بنجاح
2025-07-25 00:03:02,747 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-25 00:03:02,748 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-25 00:03:02,873 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام user_manager
2025-07-25 00:03:02,874 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-25 00:03:02,958 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager
2025-07-25 00:03:02,965 - INFO - تم تسجيل العملية: تبديل النظام من التلجرام - تم تبديل النظام من None إلى user_manager
2025-07-25 00:03:03,030 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-25 00:03:03,031 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-25 00:03:03,032 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-25 00:03:03,169 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-25 00:03:03,173 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-25 00:03:03,264 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-25 00:03:03,265 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-25 00:03:03,268 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-25 00:03:03,273 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-25 00:03:03,276 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-25 00:03:03,278 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager - القالب: 10
2025-07-25 00:03:03,534 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-25 00:03:11,526 - INFO - معالجة callback: independent_create_um_10_lightning
2025-07-25 00:03:16,962 - INFO - معالجة callback: independent_count_um_10_lightning_200
2025-07-25 00:03:17,614 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-25 00:03:17,614 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-25 00:03:17,628 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-25 00:03:17,629 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-25 00:03:17,655 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-25 00:03:17,656 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-25 00:03:17,659 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-25 00:03:17,662 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-25 00:03:17,922 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-25 00:03:17,922 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-25 00:03:17,937 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-25 00:03:17,966 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-25 00:03:17,996 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-25 00:03:18,014 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-25 00:03:18,017 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-25 00:03:18,021 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-25 00:03:18,022 - INFO - ⚡ البرق: تم الحفاظ على العدد المحدد: 200
2025-07-25 00:03:18,023 - INFO - تم تطبيق إعدادات القالب '10' بنجاح
2025-07-25 00:03:18,024 - INFO - تم تحديد معلومات المستخدم الحالي: مستخدم التلجرام بوت
2025-07-25 00:03:18,024 - INFO - ⚡ استخدام البرق للتلجرام (مع جدولة مؤقتة وتنظيف تلقائي)
2025-07-25 00:03:18,024 - INFO - ⚡ بدء البرق التلقائي للتلجرام (مع جدولة مؤقتة وتنظيف تلقائي)
2025-07-25 00:03:18,024 - INFO - ⚡ البرق: استخدام العدد من التلجرام بوت: 200
2025-07-25 00:03:18,025 - INFO - ⚡ البرق للتلجرام: العدد المطلوب 200 حساب
2025-07-25 00:03:18,025 - INFO - ⚡ البرق: تم تحديث عدد الحسابات في الواجهة إلى: 200
2025-07-25 00:03:18,028 - INFO - ⚡ البرق للتلجرام: تم توليد 200 حساب
2025-07-25 00:03:18,028 - INFO - ⚡ البرق للتلجرام: تنفيذ متسلسل مجدول لـ 200 كارت (حد التقسيم: 100)
2025-07-25 00:03:18,029 - INFO - ⚡ البرق للتلجرام: بدء معالجة الأعداد الكبيرة مع جدولة واحدة
2025-07-25 00:03:18,030 - INFO - ⚡ البرق للتلجرام: تم تقسيم 200 كارت إلى 2 مجموعة للتنفيذ المتسلسل
2025-07-25 00:03:18,030 - INFO - ⚡ البرق للتلجرام: إنشاء السكريبت للمجموعة 1/2 (100 كارت)
2025-07-25 00:03:18,030 - INFO - ⚡ البرق للتلجرام: تم إعداد السكريبت telegram_lightning_batch1_user_manager_20250725_000318
2025-07-25 00:03:18,031 - INFO - ⚡ البرق للتلجرام: إنشاء السكريبت للمجموعة 2/2 (100 كارت)
2025-07-25 00:03:18,031 - ERROR - خطأ في البرق للأعداد الكبيرة للتلجرام: name 'bot_token' is not defined
2025-07-25 00:03:28,903 - INFO - بدء إغلاق التطبيق
2025-07-25 00:03:28,909 - INFO - تم تسجيل العملية: إغلاق التطبيق - تم إغلاق التطبيق بنجاح
2025-07-25 00:03:28,910 - INFO - تم إغلاق التطبيق بنجاح
2025-07-25 00:12:44,145 - INFO - تم بدء تشغيل التطبيق
2025-07-25 00:12:44,238 - INFO - تم إنشاء المجلدات الأساسية
2025-07-25 00:12:44,283 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-25 00:12:44,283 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-25 00:12:44,622 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-25 00:12:49,933 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-25 00:12:49,933 - INFO - تم إعداد التطبيق بنجاح
2025-07-25 00:12:51,974 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-25 00:12:52,429 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-25 00:12:52,429 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-25 00:12:55,435 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-25 00:12:55,472 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-25 00:12:55,728 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-25 00:12:55,728 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-25 00:13:25,540 - INFO - معالجة callback: select_system_um
2025-07-25 00:13:26,182 - INFO - تم إرسال 2 قالب مفلتر لنظام User Manager
2025-07-25 00:13:29,555 - INFO - معالجة callback: independent_template_um_10
2025-07-25 00:13:30,050 - INFO - 🔄 تبديل النظام تلقائياً من None إلى user_manager
2025-07-25 00:13:30,051 - INFO - 🔄 طلب تبديل النظام من التلجرام: None → user_manager
2025-07-25 00:13:30,309 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-25 00:13:30,377 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-25 00:13:30,477 - WARNING - تحذير: فشل في حفظ الإعدادات: 'MikroTikCardGenerator' object has no attribute 'version_combo'
2025-07-25 00:13:30,478 - INFO - تم تعيين النظام الجديد: user_manager
2025-07-25 00:13:30,479 - INFO - 🔄 إعادة بناء الواجهة للنظام: user_manager
2025-07-25 00:13:30,479 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-25 00:13:30,482 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-25 00:13:31,149 - INFO - تم إعداد تبويب عرض الكروت لـ User Manager بنجاح
2025-07-25 00:13:31,329 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-25 00:13:31,329 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-25 00:13:31,473 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام user_manager
2025-07-25 00:13:31,474 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-25 00:13:31,544 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager
2025-07-25 00:13:31,581 - INFO - تم تسجيل العملية: تبديل النظام من التلجرام - تم تبديل النظام من None إلى user_manager
2025-07-25 00:13:31,643 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-25 00:13:31,644 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-25 00:13:31,649 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-25 00:13:31,871 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-25 00:13:31,872 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-25 00:13:31,897 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-25 00:13:31,923 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-25 00:13:31,927 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-25 00:13:31,931 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-25 00:13:31,934 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-25 00:13:31,935 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager - القالب: 10
2025-07-25 00:13:32,288 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-25 00:14:12,697 - INFO - معالجة callback: independent_create_um_10_lightning
2025-07-25 00:14:31,292 - INFO - معالجة callback: independent_count_um_10_lightning_200
2025-07-25 00:14:31,758 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-25 00:14:31,758 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-25 00:14:31,773 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-25 00:14:31,773 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-25 00:14:31,803 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-25 00:14:31,818 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-25 00:14:31,822 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-25 00:14:31,850 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-25 00:14:32,101 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-25 00:14:32,101 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-25 00:14:32,116 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-25 00:14:32,140 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-25 00:14:32,166 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-25 00:14:32,167 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-25 00:14:32,169 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-25 00:14:32,174 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-25 00:14:32,175 - INFO - ⚡ البرق: تم الحفاظ على العدد المحدد: 200
2025-07-25 00:14:32,184 - INFO - تم تطبيق إعدادات القالب '10' بنجاح
2025-07-25 00:14:32,201 - INFO - تم تحديد معلومات المستخدم الحالي: مستخدم التلجرام بوت
2025-07-25 00:14:32,201 - INFO - ⚡ استخدام البرق للتلجرام (مع جدولة مؤقتة وتنظيف تلقائي)
2025-07-25 00:14:32,201 - INFO - ⚡ بدء البرق التلقائي للتلجرام (مع جدولة مؤقتة وتنظيف تلقائي)
2025-07-25 00:14:32,202 - INFO - ⚡ البرق: استخدام العدد من التلجرام بوت: 200
2025-07-25 00:14:32,202 - INFO - ⚡ البرق للتلجرام: العدد المطلوب 200 حساب
2025-07-25 00:14:32,202 - INFO - ⚡ البرق: تم تحديث عدد الحسابات في الواجهة إلى: 200
2025-07-25 00:14:32,205 - INFO - ⚡ البرق للتلجرام: تم توليد 200 حساب
2025-07-25 00:14:32,207 - INFO - ⚡ البرق للتلجرام: تنفيذ متسلسل مجدول لـ 200 كارت (حد التقسيم: 100)
2025-07-25 00:14:32,208 - INFO - ⚡ البرق للتلجرام: بدء معالجة الأعداد الكبيرة مع جدولة واحدة
2025-07-25 00:14:32,208 - INFO - ⚡ البرق للتلجرام: تم تقسيم 200 كارت إلى 2 مجموعة للتنفيذ المتسلسل
2025-07-25 00:14:32,209 - INFO - ⚡ البرق للتلجرام: إنشاء السكريبت للمجموعة 1/2 (100 كارت)
2025-07-25 00:14:32,210 - INFO - ⚡ البرق للتلجرام: تم إعداد السكريبت telegram_lightning_batch1_user_manager_20250725_001432
2025-07-25 00:14:32,210 - INFO - ⚡ البرق للتلجرام: إنشاء السكريبت للمجموعة 2/2 (100 كارت)
2025-07-25 00:14:32,211 - INFO - ⚡ البرق للتلجرام: تم إعداد السكريبت telegram_lightning_batch2_user_manager_20250725_001432
2025-07-25 00:14:32,213 - INFO - محاولة الاتصال بـ 6.6.6.100:8728 (عادي)
2025-07-25 00:14:32,280 - INFO - نجح الاتصال مع 6.6.6.100
2025-07-25 00:14:32,357 - INFO - ⚡ تم إرسال السكريبت المترابط: telegram_lightning_batch1_user_manager_20250725_001432
2025-07-25 00:14:32,404 - INFO - ⚡ تم إرسال السكريبت المترابط: telegram_lightning_batch2_user_manager_20250725_001432
2025-07-25 00:14:32,428 - INFO - ⚡ وقت MikroTik الحالي: 21:14:32
2025-07-25 00:14:32,533 - INFO - ⚡ السكريبت الأول سينفذ في: 21:14:35 (بعد 3 ثواني من وقت MikroTik)
2025-07-25 00:14:32,591 - INFO - ⚡ تم إنشاء الجدولة الرئيسية: telegram_main_schedule_20250725_001432 لتشغيل telegram_lightning_batch1_user_manager_20250725_001432 في 21:14:35
2025-07-25 00:14:32,597 - INFO - ⚡ تم إعداد البرق المترابط بنجاح - جدولة واحدة: telegram_main_schedule_20250725_001432, 2 سكريبت مترابط
2025-07-25 00:14:32,598 - INFO - ⚡ البرق للتلجرام مكتمل: تم إنشاء جدولة واحدة لـ 2 مجموعة متسلسلة (200 كارت)
2025-07-25 00:14:35,359 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 200
2025-07-25 00:14:35,380 - INFO - تم إنشاء PDF بنجاح: exports/كروت_(10)_ب20-200 كارت-25-07-2025-00-14-35-um.pdf
2025-07-25 00:14:35,386 - INFO - تم حفظ ملف .rsc الاحتياطي: exports/كروت_(10)_ب20-200 كارت-25-07-2025-00-14-35-um.rsc
2025-07-25 00:14:36,220 - INFO - تم إرسال الملف بنجاح: كروت_(10)_ب20-200 كارت-25-07-2025-00-14-35-um.pdf
2025-07-25 00:14:36,715 - INFO - تم إرسال 200 كرت عبر التلجرام باستخدام قالب 10
2025-07-25 00:15:01,258 - INFO - معالجة callback: independent_count_um_10_lightning_200
2025-07-25 00:15:01,776 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-25 00:15:01,777 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-25 00:15:01,785 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-25 00:15:01,796 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-25 00:15:01,825 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-25 00:15:01,825 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-25 00:15:01,829 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-25 00:15:01,869 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-25 00:15:02,115 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-25 00:15:02,116 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-25 00:15:02,131 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-25 00:15:02,132 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-25 00:15:02,204 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-25 00:15:02,205 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-25 00:15:02,208 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-25 00:15:02,214 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-25 00:15:02,214 - INFO - ⚡ البرق: تم الحفاظ على العدد المحدد: 200
2025-07-25 00:15:02,215 - INFO - تم تطبيق إعدادات القالب '10' بنجاح
2025-07-25 00:15:02,215 - INFO - تم تحديد معلومات المستخدم الحالي: مستخدم التلجرام بوت
2025-07-25 00:15:02,216 - INFO - ⚡ استخدام البرق للتلجرام (مع جدولة مؤقتة وتنظيف تلقائي)
2025-07-25 00:15:02,216 - INFO - ⚡ بدء البرق التلقائي للتلجرام (مع جدولة مؤقتة وتنظيف تلقائي)
2025-07-25 00:15:02,216 - INFO - ⚡ البرق: استخدام العدد من التلجرام بوت: 200
2025-07-25 00:15:02,216 - INFO - ⚡ البرق للتلجرام: العدد المطلوب 200 حساب
2025-07-25 00:15:02,217 - INFO - ⚡ البرق: تم تحديث عدد الحسابات في الواجهة إلى: 200
2025-07-25 00:15:02,219 - INFO - ⚡ البرق للتلجرام: تم توليد 200 حساب
2025-07-25 00:15:02,219 - INFO - ⚡ البرق للتلجرام: تنفيذ متسلسل مجدول لـ 200 كارت (حد التقسيم: 100)
2025-07-25 00:15:02,222 - INFO - ⚡ البرق للتلجرام: بدء معالجة الأعداد الكبيرة مع جدولة واحدة
2025-07-25 00:15:02,222 - INFO - ⚡ البرق للتلجرام: تم تقسيم 200 كارت إلى 2 مجموعة للتنفيذ المتسلسل
2025-07-25 00:15:02,222 - INFO - ⚡ البرق للتلجرام: إنشاء السكريبت للمجموعة 1/2 (100 كارت)
2025-07-25 00:15:02,223 - INFO - ⚡ البرق للتلجرام: تم إعداد السكريبت telegram_lightning_batch1_user_manager_20250725_001502
2025-07-25 00:15:02,223 - INFO - ⚡ البرق للتلجرام: إنشاء السكريبت للمجموعة 2/2 (100 كارت)
2025-07-25 00:15:02,223 - INFO - ⚡ البرق للتلجرام: تم إعداد السكريبت telegram_lightning_batch2_user_manager_20250725_001502
2025-07-25 00:15:02,226 - INFO - استخدام الاتصال الحالي
2025-07-25 00:15:02,337 - INFO - ⚡ تم إرسال السكريبت المترابط: telegram_lightning_batch1_user_manager_20250725_001502
2025-07-25 00:15:02,385 - INFO - ⚡ تم إرسال السكريبت المترابط: telegram_lightning_batch2_user_manager_20250725_001502
2025-07-25 00:15:02,420 - INFO - ⚡ وقت MikroTik الحالي: 21:15:02
2025-07-25 00:15:02,425 - INFO - ⚡ السكريبت الأول سينفذ في: 21:15:05 (بعد 3 ثواني من وقت MikroTik)
2025-07-25 00:15:02,491 - INFO - ⚡ تم إنشاء الجدولة الرئيسية: telegram_main_schedule_20250725_001502 لتشغيل telegram_lightning_batch1_user_manager_20250725_001502 في 21:15:05
2025-07-25 00:15:02,505 - INFO - ⚡ تم إعداد البرق المترابط بنجاح - جدولة واحدة: telegram_main_schedule_20250725_001502, 2 سكريبت مترابط
2025-07-25 00:15:02,505 - INFO - ⚡ البرق للتلجرام مكتمل: تم إنشاء جدولة واحدة لـ 2 مجموعة متسلسلة (200 كارت)
2025-07-25 00:15:05,307 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 200
2025-07-25 00:15:05,364 - INFO - تم إنشاء PDF بنجاح: exports/كروت_(10)_ب20-200 كارت-25-07-2025-00-15-05-um.pdf
2025-07-25 00:15:05,386 - INFO - تم حفظ ملف .rsc الاحتياطي: exports/كروت_(10)_ب20-200 كارت-25-07-2025-00-15-05-um.rsc
2025-07-25 00:15:06,084 - INFO - تم إرسال الملف بنجاح: كروت_(10)_ب20-200 كارت-25-07-2025-00-15-05-um.pdf
2025-07-25 00:15:06,330 - INFO - تم إرسال 200 كرت عبر التلجرام باستخدام قالب 10
2025-07-25 00:27:46,362 - INFO - تم بدء تشغيل التطبيق
2025-07-25 00:27:46,415 - INFO - تم إنشاء المجلدات الأساسية
2025-07-25 00:27:46,567 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-25 00:27:46,567 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-25 00:27:46,896 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-25 00:27:48,778 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-25 00:27:48,778 - INFO - تم إعداد التطبيق بنجاح
2025-07-25 00:27:50,817 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-25 00:27:51,246 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-25 00:27:51,247 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-25 00:27:54,252 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-25 00:27:54,290 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-25 00:27:54,619 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-25 00:27:54,619 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-25 00:28:00,921 - INFO - معالجة callback: select_system_um
2025-07-25 00:28:01,394 - INFO - تم إرسال 2 قالب مفلتر لنظام User Manager
2025-07-25 00:28:02,473 - INFO - معالجة callback: independent_template_um_10
2025-07-25 00:28:03,212 - INFO - ✅ تم إرسال إشعار اختيار القالب '10' - العدد الحالي: 0
2025-07-25 00:28:03,212 - INFO - 🔄 تبديل النظام تلقائياً من None إلى user_manager
2025-07-25 00:28:03,212 - INFO - 🔄 طلب تبديل النظام من التلجرام: None → user_manager
2025-07-25 00:28:03,476 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-25 00:28:03,524 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-25 00:28:03,625 - WARNING - تحذير: فشل في حفظ الإعدادات: 'MikroTikCardGenerator' object has no attribute 'version_combo'
2025-07-25 00:28:03,625 - INFO - تم تعيين النظام الجديد: user_manager
2025-07-25 00:28:03,626 - INFO - 🔄 إعادة بناء الواجهة للنظام: user_manager
2025-07-25 00:28:03,626 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-25 00:28:03,629 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-25 00:28:04,247 - INFO - تم إعداد تبويب عرض الكروت لـ User Manager بنجاح
2025-07-25 00:28:04,438 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-25 00:28:04,451 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-25 00:28:04,607 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام user_manager
2025-07-25 00:28:04,607 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-25 00:28:04,676 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager
2025-07-25 00:28:04,717 - INFO - تم تسجيل العملية: تبديل النظام من التلجرام - تم تبديل النظام من None إلى user_manager
2025-07-25 00:28:04,768 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-25 00:28:04,770 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-25 00:28:04,770 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-25 00:28:05,012 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-25 00:28:05,013 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-25 00:28:05,038 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-25 00:28:05,040 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-25 00:28:05,043 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-25 00:28:05,048 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-25 00:28:05,051 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-25 00:28:05,053 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager - القالب: 10
2025-07-25 00:28:05,335 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-25 00:29:09,039 - INFO - معالجة callback: independent_create_um_10_lightning
2025-07-25 00:38:00,746 - INFO - تم بدء تشغيل التطبيق
2025-07-25 00:38:00,783 - INFO - تم إنشاء المجلدات الأساسية
2025-07-25 00:38:00,817 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-25 00:38:00,818 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-25 00:38:01,139 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-25 00:38:09,470 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-25 00:38:09,471 - INFO - تم إعداد التطبيق بنجاح
2025-07-25 00:38:11,518 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-25 00:38:11,880 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-25 00:38:11,881 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-25 00:38:14,895 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-25 00:38:14,957 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-25 00:38:15,275 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-25 00:38:15,275 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-25 00:38:16,826 - INFO - معالجة callback: select_system_um
2025-07-25 00:38:17,311 - INFO - تم إرسال 2 قالب مفلتر لنظام User Manager
2025-07-25 00:38:18,350 - INFO - معالجة callback: independent_template_um_10
2025-07-25 00:38:18,829 - INFO - 🔄 تبديل النظام تلقائياً من None إلى user_manager
2025-07-25 00:38:18,829 - INFO - 🔄 طلب تبديل النظام من التلجرام: None → user_manager
2025-07-25 00:38:19,104 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-25 00:38:19,162 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-25 00:38:19,263 - WARNING - تحذير: فشل في حفظ الإعدادات: 'MikroTikCardGenerator' object has no attribute 'version_combo'
2025-07-25 00:38:19,263 - INFO - تم تعيين النظام الجديد: user_manager
2025-07-25 00:38:19,264 - INFO - 🔄 إعادة بناء الواجهة للنظام: user_manager
2025-07-25 00:38:19,265 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-25 00:38:19,267 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-25 00:38:19,761 - INFO - تم إعداد تبويب عرض الكروت لـ User Manager بنجاح
2025-07-25 00:38:19,954 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-25 00:38:19,954 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-25 00:38:20,126 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام user_manager
2025-07-25 00:38:20,126 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-25 00:38:20,213 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager
2025-07-25 00:38:20,217 - INFO - تم تسجيل العملية: تبديل النظام من التلجرام - تم تبديل النظام من None إلى user_manager
2025-07-25 00:38:20,282 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-25 00:38:20,283 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-25 00:38:20,283 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-25 00:38:20,588 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-25 00:38:20,589 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-25 00:38:20,617 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-25 00:38:20,639 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-25 00:38:20,642 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-25 00:38:20,646 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-25 00:38:20,649 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-25 00:38:20,650 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager - القالب: 10
2025-07-25 00:38:20,913 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-25 00:38:22,685 - INFO - معالجة callback: independent_create_um_10_lightning
2025-07-25 00:38:22,983 - INFO - محاولة الاتصال بـ 6.6.6.100:8728 (عادي)
2025-07-25 00:38:23,042 - INFO - نجح الاتصال مع 6.6.6.100
2025-07-25 00:38:25,435 - INFO - ✅ تم إرسال إشعار اختيار القالب '10' - العدد الحالي: 0
2025-07-25 00:52:14,917 - INFO - تم بدء تشغيل التطبيق
2025-07-25 00:52:14,985 - INFO - تم إنشاء المجلدات الأساسية
2025-07-25 00:52:15,394 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-25 00:52:15,395 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-25 00:52:18,948 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-25 00:52:42,496 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-25 00:52:42,497 - INFO - تم إعداد التطبيق بنجاح
2025-07-25 00:52:44,577 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-25 00:52:45,998 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-25 00:52:46,006 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-25 00:52:49,010 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-25 00:52:49,099 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-25 00:52:49,458 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-25 00:52:49,458 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-25 00:53:20,185 - INFO - معالجة callback: select_system_um
2025-07-25 00:53:20,640 - INFO - تم إرسال 2 قالب مفلتر لنظام User Manager
2025-07-25 00:53:23,233 - INFO - معالجة callback: independent_template_um_10
2025-07-25 00:53:23,852 - INFO - 🔄 تبديل النظام تلقائياً من None إلى user_manager
2025-07-25 00:53:23,852 - INFO - 🔄 طلب تبديل النظام من التلجرام: None → user_manager
2025-07-25 00:53:24,162 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-25 00:53:24,257 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-25 00:53:24,357 - WARNING - تحذير: فشل في حفظ الإعدادات: 'MikroTikCardGenerator' object has no attribute 'version_combo'
2025-07-25 00:53:24,358 - INFO - تم تعيين النظام الجديد: user_manager
2025-07-25 00:53:24,359 - INFO - 🔄 إعادة بناء الواجهة للنظام: user_manager
2025-07-25 00:53:24,359 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-25 00:53:24,363 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-25 00:53:25,031 - INFO - تم إعداد تبويب عرض الكروت لـ User Manager بنجاح
2025-07-25 00:53:25,220 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-25 00:53:25,220 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-25 00:53:25,376 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام user_manager
2025-07-25 00:53:25,377 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-25 00:53:25,708 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager
2025-07-25 00:53:25,713 - INFO - تم تسجيل العملية: تبديل النظام من التلجرام - تم تبديل النظام من None إلى user_manager
2025-07-25 00:53:25,718 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-25 00:53:25,719 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-25 00:53:25,720 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-25 00:53:25,788 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-25 00:53:25,803 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-25 00:53:25,854 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-25 00:53:25,866 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-25 00:53:25,870 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-25 00:53:25,878 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-25 00:53:25,883 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-25 00:53:25,895 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager - القالب: 10
2025-07-25 00:53:26,166 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-25 00:53:30,879 - INFO - معالجة callback: independent_create_um_10_lightning
2025-07-25 00:53:31,092 - INFO - 🔍 بدء اختبار جلب العدد الحالي للكروت قبل إرسال الإشعار
2025-07-25 00:53:31,092 - INFO - 🧪 بدء اختبار الاتصال وجلب عدد User Manager
2025-07-25 00:53:31,093 - INFO - محاولة الاتصال بـ 6.6.6.100:8728 (عادي)
2025-07-25 00:53:31,164 - INFO - نجح الاتصال مع 6.6.6.100
2025-07-25 00:53:31,166 - INFO - ✅ نجح الاتصال - اختبار جلب العدد...
2025-07-25 00:53:31,204 - INFO - 🏷️ اسم النظام: MikroTik
2025-07-25 00:53:31,247 - INFO - 📋 إصدار RouterOS: 6.43.12 (stable)
2025-07-25 00:53:31,247 - INFO - 📊 اختبار جلب مستخدمي User Manager...
2025-07-25 00:53:33,329 - INFO - ✅ تم جلب 1159 مستخدم من User Manager
2025-07-25 00:53:33,329 - INFO - 👥 عرض أول 5 مستخدمين:
2025-07-25 00:53:33,330 - INFO -   1. غير محدد (البروفايل: غير محدد)
2025-07-25 00:53:33,330 - INFO -   2. غير محدد (البروفايل: غير محدد)
2025-07-25 00:53:33,330 - INFO -   3. غير محدد (البروفايل: غير محدد)
2025-07-25 00:53:33,330 - INFO -   4. غير محدد (البروفايل: غير محدد)
2025-07-25 00:53:33,331 - INFO -   5. غير محدد (البروفايل: غير محدد)
2025-07-25 00:53:33,331 - ERROR - ❌ خطأ في API: 'RouterOsApi' object has no attribute 'disconnect'
2025-07-25 00:53:33,332 - INFO - 📊 نتيجة الاختبار: خطأ في API: 'RouterOsApi' object has no attribute 'disconnect'
2025-07-25 00:53:33,332 - INFO - 🔔 بدء إرسال إشعار اختيار القالب: 10 (User Manager)
2025-07-25 00:53:33,333 - INFO - 📊 جلب العدد الحالي من User Manager...
2025-07-25 00:53:33,333 - INFO - 🔍 بدء جلب العدد الحالي للكروت في User Manager
2025-07-25 00:53:33,334 - INFO - ✅ مكتبة routeros_api متوفرة
2025-07-25 00:53:33,334 - INFO - 🔗 محاولة الاتصال بـ 6.6.6.100 باستخدام المستخدم: 11
2025-07-25 00:53:33,338 - INFO - استخدام الاتصال الحالي
2025-07-25 00:53:33,340 - INFO - ✅ تم الاتصال بـ MikroTik بنجاح
2025-07-25 00:53:33,341 - INFO - 📊 جلب قائمة المستخدمين من User Manager...
2025-07-25 00:53:35,422 - INFO - ✅ تم جلب 1159 مستخدم من User Manager بنجاح
2025-07-25 00:53:35,422 - INFO - 👤 مستخدم 1: غير محدد
2025-07-25 00:53:35,422 - INFO - 👤 مستخدم 2: غير محدد
2025-07-25 00:53:35,423 - INFO - 👤 مستخدم 3: غير محدد
2025-07-25 00:53:35,423 - INFO - 📈 العدد المجلوب من User Manager: 1159
2025-07-25 00:53:35,690 - INFO - ✅ تم إرسال إشعار اختيار القالب '10' - العدد الحالي: 1159
2025-07-25 01:18:02,119 - INFO - معالجة الأمر: /start
2025-07-25 01:18:02,456 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-25 01:18:09,131 - INFO - معالجة callback: select_system_um
2025-07-25 01:18:10,231 - INFO - تم إرسال 2 قالب مفلتر لنظام User Manager
2025-07-25 01:18:12,339 - INFO - معالجة callback: independent_template_um_10
2025-07-25 01:18:12,875 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-25 01:18:12,876 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-25 01:18:12,876 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-25 01:18:12,893 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-25 01:18:12,894 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-25 01:18:12,934 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-25 01:18:12,935 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-25 01:18:12,938 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-25 01:18:12,943 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-25 01:18:12,948 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-25 01:18:12,959 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager - القالب: 10
2025-07-25 01:18:13,284 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-25 01:18:15,363 - INFO - معالجة callback: independent_create_um_10_lightning
2025-07-25 01:18:15,689 - INFO - 🔍 بدء اختبار جلب العدد الحالي للكروت قبل إرسال الإشعار
2025-07-25 01:18:15,689 - INFO - 🧪 بدء اختبار الاتصال وجلب عدد User Manager
2025-07-25 01:18:15,693 - INFO - استخدام الاتصال الحالي
2025-07-25 01:18:15,693 - INFO - ✅ نجح الاتصال - اختبار جلب العدد...
2025-07-25 01:18:15,734 - INFO - 🏷️ اسم النظام: MikroTik
2025-07-25 01:18:15,777 - INFO - 📋 إصدار RouterOS: 6.43.12 (stable)
2025-07-25 01:18:15,778 - INFO - 📊 اختبار جلب مستخدمي User Manager...
2025-07-25 01:18:17,847 - INFO - ✅ تم جلب 1159 مستخدم من User Manager
2025-07-25 01:18:17,848 - INFO - 👥 عرض أول 5 مستخدمين:
2025-07-25 01:18:17,848 - INFO -   1. غير محدد (البروفايل: غير محدد)
2025-07-25 01:18:17,848 - INFO -   2. غير محدد (البروفايل: غير محدد)
2025-07-25 01:18:17,849 - INFO -   3. غير محدد (البروفايل: غير محدد)
2025-07-25 01:18:17,850 - INFO -   4. غير محدد (البروفايل: غير محدد)
2025-07-25 01:18:17,850 - INFO -   5. غير محدد (البروفايل: غير محدد)
2025-07-25 01:18:17,851 - ERROR - ❌ خطأ في API: 'RouterOsApi' object has no attribute 'disconnect'
2025-07-25 01:18:17,851 - INFO - 📊 نتيجة الاختبار: خطأ في API: 'RouterOsApi' object has no attribute 'disconnect'
2025-07-25 01:18:17,852 - INFO - 🔔 بدء إرسال إشعار اختيار القالب: 10 (User Manager)
2025-07-25 01:18:17,853 - INFO - 📊 جلب العدد الحالي من User Manager...
2025-07-25 01:18:17,854 - INFO - 🔍 بدء جلب العدد الحالي للكروت في User Manager
2025-07-25 01:18:17,855 - INFO - ✅ مكتبة routeros_api متوفرة
2025-07-25 01:18:17,856 - INFO - 🔗 محاولة الاتصال بـ 6.6.6.100 باستخدام المستخدم: 11
2025-07-25 01:18:17,860 - INFO - استخدام الاتصال الحالي
2025-07-25 01:18:17,862 - INFO - ✅ تم الاتصال بـ MikroTik بنجاح
2025-07-25 01:18:17,863 - INFO - 📊 جلب قائمة المستخدمين من User Manager...
2025-07-25 01:18:19,939 - INFO - ✅ تم جلب 1159 مستخدم من User Manager بنجاح
2025-07-25 01:18:19,940 - INFO - 👤 مستخدم 1: غير محدد
2025-07-25 01:18:19,940 - INFO - 👤 مستخدم 2: غير محدد
2025-07-25 01:18:19,941 - INFO - 👤 مستخدم 3: غير محدد
2025-07-25 01:18:19,942 - INFO - 📈 العدد المجلوب من User Manager: 1159
2025-07-25 01:18:20,424 - INFO - ✅ تم إرسال إشعار اختيار القالب '10' - العدد الحالي: 1159
2025-07-25 01:18:23,625 - INFO - معالجة callback: independent_count_um_10_lightning_200
2025-07-25 01:18:24,161 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-25 01:18:24,162 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-25 01:18:24,177 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-25 01:18:24,179 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-25 01:18:24,250 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-25 01:18:24,251 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-25 01:18:24,255 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-25 01:18:24,261 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-25 01:18:24,519 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-25 01:18:24,519 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-25 01:18:24,544 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-25 01:18:24,545 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-25 01:18:24,586 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-25 01:18:24,587 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-25 01:18:24,590 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-25 01:18:24,595 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-25 01:18:24,595 - INFO - ⚡ البرق: تم الحفاظ على العدد المحدد: 200
2025-07-25 01:18:24,597 - INFO - تم تطبيق إعدادات القالب '10' بنجاح
2025-07-25 01:18:24,598 - INFO - ⚡ استخدام البرق للتلجرام (مع جدولة مؤقتة وتنظيف تلقائي)
2025-07-25 01:18:24,598 - INFO - ⚡ بدء البرق التلقائي للتلجرام (مع جدولة مؤقتة وتنظيف تلقائي)
2025-07-25 01:18:24,598 - INFO - ⚡ البرق: استخدام العدد من التلجرام بوت: 200
2025-07-25 01:18:24,599 - INFO - ⚡ البرق للتلجرام: العدد المطلوب 200 حساب
2025-07-25 01:18:24,599 - INFO - ⚡ البرق: تم تحديث عدد الحسابات في الواجهة إلى: 200
2025-07-25 01:18:24,602 - INFO - ⚡ البرق للتلجرام: تم توليد 200 حساب
2025-07-25 01:18:24,614 - INFO - ⚡ البرق للتلجرام: تنفيذ متسلسل مجدول لـ 200 كارت (حد التقسيم: 100)
2025-07-25 01:18:24,615 - INFO - ⚡ البرق للتلجرام: بدء معالجة الأعداد الكبيرة مع جدولة واحدة
2025-07-25 01:18:24,615 - INFO - ⚡ البرق للتلجرام: تم تقسيم 200 كارت إلى 2 مجموعة للتنفيذ المتسلسل
2025-07-25 01:18:24,615 - INFO - ⚡ البرق للتلجرام: إنشاء السكريبت للمجموعة 1/2 (100 كارت)
2025-07-25 01:18:24,616 - INFO - ⚡ البرق للتلجرام: تم إعداد السكريبت telegram_lightning_batch1_user_manager_20250725_011824
2025-07-25 01:18:24,616 - INFO - ⚡ البرق للتلجرام: إنشاء السكريبت للمجموعة 2/2 (100 كارت)
2025-07-25 01:18:24,617 - INFO - ⚡ البرق للتلجرام: تم إعداد السكريبت telegram_lightning_batch2_user_manager_20250725_011824
2025-07-25 01:18:24,620 - INFO - استخدام الاتصال الحالي
2025-07-25 01:18:24,675 - INFO - ⚡ تم إرسال السكريبت المترابط: telegram_lightning_batch1_user_manager_20250725_011824
2025-07-25 01:18:24,716 - INFO - ⚡ تم إرسال السكريبت المترابط: telegram_lightning_batch2_user_manager_20250725_011824
2025-07-25 01:18:24,755 - INFO - ⚡ وقت MikroTik الحالي: 22:18:24
2025-07-25 01:18:24,778 - INFO - ⚡ السكريبت الأول سينفذ في: 22:18:27 (بعد 3 ثواني من وقت MikroTik)
2025-07-25 01:18:24,826 - INFO - ⚡ تم إنشاء الجدولة الرئيسية: telegram_main_schedule_20250725_011824 لتشغيل telegram_lightning_batch1_user_manager_20250725_011824 في 22:18:27
2025-07-25 01:18:24,834 - INFO - ⚡ تم إعداد البرق المترابط بنجاح - جدولة واحدة: telegram_main_schedule_20250725_011824, 2 سكريبت مترابط
2025-07-25 01:18:24,835 - INFO - ⚡ البرق للتلجرام مكتمل: تم إنشاء جدولة واحدة لـ 2 مجموعة متسلسلة (200 كارت)
2025-07-25 01:18:27,779 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 200
2025-07-25 01:18:27,833 - INFO - تم إنشاء PDF بنجاح: exports/كروت_(10)_ب20-200 كارت-25-07-2025-01-18-27-um.pdf
2025-07-25 01:18:27,863 - INFO - تم حفظ ملف .rsc الاحتياطي: exports/كروت_(10)_ب20-200 كارت-25-07-2025-01-18-27-um.rsc
2025-07-25 01:18:28,547 - INFO - تم إرسال الملف بنجاح: كروت_(10)_ب20-200 كارت-25-07-2025-01-18-27-um.pdf
2025-07-25 01:18:28,809 - INFO - تم إرسال 200 كرت عبر التلجرام باستخدام قالب 10
2025-07-25 01:18:36,473 - INFO - معالجة الأمر: /start
2025-07-25 01:18:36,869 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-25 01:18:48,325 - INFO - معالجة callback: select_system_um
2025-07-25 01:18:48,925 - INFO - تم إرسال 2 قالب مفلتر لنظام User Manager
2025-07-25 01:18:50,155 - INFO - معالجة callback: independent_template_um_10
2025-07-25 01:18:50,677 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-25 01:18:50,677 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-25 01:18:50,677 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-25 01:18:50,703 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-25 01:18:50,704 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-25 01:18:50,746 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-25 01:18:50,747 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-25 01:18:50,749 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-25 01:18:50,754 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-25 01:18:50,755 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-25 01:18:50,757 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager - القالب: 10
2025-07-25 01:18:51,138 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-25 01:18:54,084 - INFO - معالجة callback: independent_create_um_10_lightning
2025-07-25 01:18:54,338 - INFO - 🔍 بدء اختبار جلب العدد الحالي للكروت قبل إرسال الإشعار
2025-07-25 01:18:54,339 - INFO - 🧪 بدء اختبار الاتصال وجلب عدد User Manager
2025-07-25 01:18:54,343 - INFO - استخدام الاتصال الحالي
2025-07-25 01:18:54,347 - INFO - ✅ نجح الاتصال - اختبار جلب العدد...
2025-07-25 01:18:54,384 - INFO - 🏷️ اسم النظام: MikroTik
2025-07-25 01:18:54,428 - INFO - 📋 إصدار RouterOS: 6.43.12 (stable)
2025-07-25 01:18:54,428 - INFO - 📊 اختبار جلب مستخدمي User Manager...
2025-07-25 01:18:56,858 - INFO - ✅ تم جلب 1359 مستخدم من User Manager
2025-07-25 01:18:56,859 - INFO - 👥 عرض أول 5 مستخدمين:
2025-07-25 01:18:56,859 - INFO -   1. غير محدد (البروفايل: غير محدد)
2025-07-25 01:18:56,859 - INFO -   2. غير محدد (البروفايل: غير محدد)
2025-07-25 01:18:56,860 - INFO -   3. غير محدد (البروفايل: غير محدد)
2025-07-25 01:18:56,860 - INFO -   4. غير محدد (البروفايل: غير محدد)
2025-07-25 01:18:56,860 - INFO -   5. غير محدد (البروفايل: غير محدد)
2025-07-25 01:18:56,861 - ERROR - ❌ خطأ في API: 'RouterOsApi' object has no attribute 'disconnect'
2025-07-25 01:18:56,862 - INFO - 📊 نتيجة الاختبار: خطأ في API: 'RouterOsApi' object has no attribute 'disconnect'
2025-07-25 01:18:56,862 - INFO - 🔔 بدء إرسال إشعار اختيار القالب: 10 (User Manager)
2025-07-25 01:18:56,862 - INFO - 📊 جلب العدد الحالي من User Manager...
2025-07-25 01:18:56,862 - INFO - 🔍 بدء جلب العدد الحالي للكروت في User Manager
2025-07-25 01:18:56,863 - INFO - ✅ مكتبة routeros_api متوفرة
2025-07-25 01:18:56,863 - INFO - 🔗 محاولة الاتصال بـ 6.6.6.100 باستخدام المستخدم: 11
2025-07-25 01:18:56,866 - INFO - استخدام الاتصال الحالي
2025-07-25 01:18:56,866 - INFO - ✅ تم الاتصال بـ MikroTik بنجاح
2025-07-25 01:18:56,867 - INFO - 📊 جلب قائمة المستخدمين من User Manager...
2025-07-25 01:18:59,288 - INFO - ✅ تم جلب 1359 مستخدم من User Manager بنجاح
2025-07-25 01:18:59,289 - INFO - 👤 مستخدم 1: غير محدد
2025-07-25 01:18:59,289 - INFO - 👤 مستخدم 2: غير محدد
2025-07-25 01:18:59,289 - INFO - 👤 مستخدم 3: غير محدد
2025-07-25 01:18:59,291 - INFO - 📈 العدد المجلوب من User Manager: 1359
2025-07-25 01:18:59,624 - INFO - ✅ تم إرسال إشعار اختيار القالب '10' - العدد الحالي: 1359
2025-07-25 01:19:17,754 - INFO - بدء إغلاق التطبيق
2025-07-25 01:19:17,755 - INFO - تم قطع الاتصال مع MikroTik
2025-07-25 01:19:17,761 - INFO - تم تسجيل العملية: إغلاق التطبيق - تم إغلاق التطبيق بنجاح
2025-07-25 01:19:17,761 - INFO - تم إغلاق التطبيق بنجاح
2025-07-25 01:37:53,391 - INFO - تم بدء تشغيل التطبيق
2025-07-25 01:37:53,438 - INFO - تم إنشاء المجلدات الأساسية
2025-07-25 01:37:53,579 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-25 01:37:53,579 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-25 01:37:54,001 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-25 01:37:56,261 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-25 01:37:56,262 - INFO - تم إعداد التطبيق بنجاح
2025-07-25 01:37:58,579 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-25 01:38:01,166 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-25 01:38:01,167 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-25 01:38:04,181 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-25 01:38:04,227 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-25 01:38:04,514 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-25 01:38:04,515 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-25 01:38:09,264 - INFO - معالجة callback: select_system_um
2025-07-25 01:38:09,849 - INFO - تم إرسال 2 قالب مفلتر لنظام User Manager
2025-07-25 01:38:13,918 - INFO - معالجة callback: independent_template_um_10
2025-07-25 01:38:14,563 - INFO - 🔄 تبديل النظام تلقائياً من None إلى user_manager
2025-07-25 01:38:14,563 - INFO - 🔄 طلب تبديل النظام من التلجرام: None → user_manager
2025-07-25 01:38:14,841 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-25 01:38:21,560 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-25 01:38:21,661 - WARNING - تحذير: فشل في حفظ الإعدادات: 'MikroTikCardGenerator' object has no attribute 'version_combo'
2025-07-25 01:38:21,663 - INFO - تم تعيين النظام الجديد: user_manager
2025-07-25 01:38:21,663 - INFO - 🔄 إعادة بناء الواجهة للنظام: user_manager
2025-07-25 01:38:21,666 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-25 01:38:21,673 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-25 01:38:22,689 - INFO - تم إعداد تبويب عرض الكروت لـ User Manager بنجاح
2025-07-25 01:38:22,899 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-25 01:38:22,902 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-25 01:38:23,231 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام user_manager
2025-07-25 01:38:23,232 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-25 01:38:23,276 - INFO - تم تسجيل العملية: تبديل النظام من التلجرام - تم تبديل النظام من None إلى user_manager
2025-07-25 01:38:23,278 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-25 01:38:23,279 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-25 01:38:23,280 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-25 01:38:23,368 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager
2025-07-25 01:38:23,738 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-25 01:38:23,739 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-25 01:38:23,776 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-25 01:38:23,776 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-25 01:38:23,780 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-25 01:38:23,785 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-25 01:38:23,788 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-25 01:38:23,792 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager - القالب: 10
2025-07-25 01:38:24,058 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-25 01:38:27,285 - INFO - معالجة callback: independent_create_um_10_lightning
2025-07-25 01:38:27,519 - INFO - 🔍 بدء اختبار جلب العدد الحالي للكروت قبل إرسال الإشعار
2025-07-25 01:38:27,520 - INFO - 🧪 بدء اختبار الاتصال وجلب عدد User Manager
2025-07-25 01:38:27,520 - INFO - محاولة الاتصال بـ 6.6.6.100:8728 (عادي)
2025-07-25 01:38:27,584 - INFO - نجح الاتصال مع 6.6.6.100
2025-07-25 01:38:27,588 - INFO - ✅ نجح الاتصال - اختبار جلب العدد...
2025-07-25 01:38:27,622 - INFO - 🏷️ اسم النظام: MikroTik
2025-07-25 01:38:27,666 - INFO - 📋 إصدار RouterOS: 6.43.12 (stable)
2025-07-25 01:38:27,675 - INFO - 📊 اختبار جلب مستخدمي User Manager...
2025-07-25 01:38:30,110 - INFO - ✅ تم جلب 1359 مستخدم من User Manager
2025-07-25 01:38:30,110 - INFO - 👥 عرض أول 5 مستخدمين:
2025-07-25 01:38:30,110 - INFO -   1. غير محدد (البروفايل: غير محدد)
2025-07-25 01:38:30,111 - INFO -   2. غير محدد (البروفايل: غير محدد)
2025-07-25 01:38:30,111 - INFO -   3. غير محدد (البروفايل: غير محدد)
2025-07-25 01:38:30,111 - INFO -   4. غير محدد (البروفايل: غير محدد)
2025-07-25 01:38:30,112 - INFO -   5. غير محدد (البروفايل: غير محدد)
2025-07-25 01:38:30,112 - ERROR - ❌ خطأ في API: 'RouterOsApi' object has no attribute 'disconnect'
2025-07-25 01:38:30,113 - INFO - 📊 نتيجة الاختبار: خطأ في API: 'RouterOsApi' object has no attribute 'disconnect'
2025-07-25 01:38:30,390 - INFO - 🔔 بدء إرسال إشعار اختيار القالب: 10 (User Manager)
2025-07-25 01:38:30,390 - INFO - 📊 جلب العدد الحالي من User Manager...
2025-07-25 01:38:30,391 - INFO - 🔍 بدء جلب العدد الحالي للكروت في User Manager
2025-07-25 01:38:30,391 - INFO - ✅ مكتبة routeros_api متوفرة
2025-07-25 01:38:30,391 - INFO - 🔗 محاولة الاتصال بـ 6.6.6.100 باستخدام المستخدم: 11
2025-07-25 01:38:30,394 - INFO - استخدام الاتصال الحالي
2025-07-25 01:38:30,394 - INFO - ✅ تم الاتصال بـ MikroTik بنجاح
2025-07-25 01:38:30,395 - INFO - 📊 جلب قائمة المستخدمين من User Manager...
2025-07-25 01:38:32,823 - INFO - ✅ تم جلب 1359 مستخدم من User Manager بنجاح
2025-07-25 01:38:32,823 - INFO - 👤 مستخدم 1: غير محدد
2025-07-25 01:38:32,823 - INFO - 👤 مستخدم 2: غير محدد
2025-07-25 01:38:32,824 - INFO - 👤 مستخدم 3: غير محدد
2025-07-25 01:38:32,825 - INFO - 📈 العدد المجلوب من User Manager: 1359
2025-07-25 01:38:33,082 - INFO - ✅ تم إرسال إشعار اختيار القالب '10' - العدد الحالي: 1359
2025-07-25 01:38:42,335 - INFO - معالجة callback: independent_count_um_10_lightning_200
2025-07-25 01:38:42,922 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-25 01:38:42,923 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-25 01:38:42,938 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-25 01:38:42,939 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-25 01:38:42,975 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-25 01:38:42,986 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-25 01:38:42,989 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-25 01:38:42,993 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-25 01:38:43,502 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-25 01:38:43,503 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-25 01:38:43,517 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-25 01:38:43,529 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-25 01:38:43,553 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-25 01:38:43,554 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-25 01:38:43,557 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-25 01:38:43,561 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-25 01:38:43,561 - INFO - ⚡ البرق: تم الحفاظ على العدد المحدد: 200
2025-07-25 01:38:43,562 - INFO - تم تطبيق إعدادات القالب '10' بنجاح
2025-07-25 01:38:43,563 - INFO - ⚡ استخدام البرق للتلجرام (مع جدولة مؤقتة وتنظيف تلقائي)
2025-07-25 01:38:43,563 - INFO - ⚡ بدء البرق التلقائي للتلجرام (مع جدولة مؤقتة وتنظيف تلقائي)
2025-07-25 01:38:43,563 - INFO - ⚡ البرق: استخدام العدد من التلجرام بوت: 200
2025-07-25 01:38:43,564 - INFO - ⚡ البرق للتلجرام: العدد المطلوب 200 حساب
2025-07-25 01:38:43,565 - INFO - ⚡ البرق: تم تحديث عدد الحسابات في الواجهة إلى: 200
2025-07-25 01:38:43,567 - INFO - ⚡ البرق للتلجرام: تم توليد 200 حساب
2025-07-25 01:38:43,568 - INFO - ⚡ البرق للتلجرام: تنفيذ متسلسل مجدول لـ 200 كارت (حد التقسيم: 100)
2025-07-25 01:38:43,570 - INFO - ⚡ البرق للتلجرام: بدء معالجة الأعداد الكبيرة مع جدولة واحدة
2025-07-25 01:38:43,580 - INFO - ⚡ البرق للتلجرام: تم تقسيم 200 كارت إلى 2 مجموعة للتنفيذ المتسلسل
2025-07-25 01:38:43,581 - INFO - ⚡ البرق للتلجرام: إنشاء السكريبت للمجموعة 1/2 (100 كارت)
2025-07-25 01:38:43,581 - INFO - ⚡ البرق للتلجرام: تم إعداد السكريبت telegram_lightning_batch1_user_manager_20250725_013843
2025-07-25 01:38:43,584 - INFO - ⚡ البرق للتلجرام: إنشاء السكريبت للمجموعة 2/2 (100 كارت)
2025-07-25 01:38:43,585 - INFO - ⚡ البرق للتلجرام: تم إعداد السكريبت telegram_lightning_batch2_user_manager_20250725_013843
2025-07-25 01:38:43,589 - INFO - استخدام الاتصال الحالي
2025-07-25 01:38:43,669 - INFO - ⚡ تم إرسال السكريبت المترابط: telegram_lightning_batch1_user_manager_20250725_013843
2025-07-25 01:38:43,719 - INFO - ⚡ تم إرسال السكريبت المترابط: telegram_lightning_batch2_user_manager_20250725_013843
2025-07-25 01:38:43,752 - INFO - ⚡ وقت MikroTik الحالي: 22:38:43
2025-07-25 01:38:43,787 - INFO - ⚡ السكريبت الأول سينفذ في: 22:38:46 (بعد 3 ثواني من وقت MikroTik)
2025-07-25 01:38:43,845 - INFO - ⚡ تم إنشاء الجدولة الرئيسية: telegram_main_schedule_20250725_013843 لتشغيل telegram_lightning_batch1_user_manager_20250725_013843 في 22:38:46
2025-07-25 01:38:43,848 - INFO - ⚡ تم إعداد البرق المترابط بنجاح - جدولة واحدة: telegram_main_schedule_20250725_013843, 2 سكريبت مترابط
2025-07-25 01:38:43,849 - INFO - ⚡ البرق للتلجرام مكتمل: تم إنشاء جدولة واحدة لـ 2 مجموعة متسلسلة (200 كارت)
2025-07-25 01:38:46,806 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 200
2025-07-25 01:38:46,918 - INFO - تم إنشاء PDF بنجاح: exports/كروت_(10)_ب20-200 كارت-25-07-2025-01-38-46-um.pdf
2025-07-25 01:38:46,967 - INFO - تم حفظ ملف .rsc الاحتياطي: exports/كروت_(10)_ب20-200 كارت-25-07-2025-01-38-46-um.rsc
2025-07-25 01:38:48,121 - INFO - تم إرسال الملف بنجاح: كروت_(10)_ب20-200 كارت-25-07-2025-01-38-46-um.pdf
2025-07-25 01:38:48,441 - INFO - تم إرسال 200 كرت عبر التلجرام باستخدام قالب 10
2025-07-25 01:40:29,100 - INFO - معالجة الأمر: /start
2025-07-25 01:40:29,418 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-25 01:40:30,632 - INFO - معالجة callback: select_system_um
2025-07-25 01:40:31,136 - INFO - تم إرسال 2 قالب مفلتر لنظام User Manager
2025-07-25 01:40:32,424 - INFO - معالجة callback: independent_template_um_10
2025-07-25 01:40:33,056 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-25 01:40:33,057 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-25 01:40:33,057 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-25 01:40:33,061 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-25 01:40:33,062 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-25 01:40:33,104 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-25 01:40:33,105 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-25 01:40:33,116 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-25 01:40:33,122 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-25 01:40:33,124 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-25 01:40:33,125 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager - القالب: 10
2025-07-25 01:40:33,474 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-25 01:40:35,186 - INFO - معالجة callback: independent_create_um_10_lightning
2025-07-25 01:40:35,423 - INFO - 🔍 بدء اختبار جلب العدد الحالي للكروت قبل إرسال الإشعار
2025-07-25 01:40:35,424 - INFO - 🧪 بدء اختبار الاتصال وجلب عدد User Manager
2025-07-25 01:40:35,427 - INFO - استخدام الاتصال الحالي
2025-07-25 01:40:35,428 - INFO - ✅ نجح الاتصال - اختبار جلب العدد...
2025-07-25 01:40:35,464 - INFO - 🏷️ اسم النظام: MikroTik
2025-07-25 01:40:35,508 - INFO - 📋 إصدار RouterOS: 6.43.12 (stable)
2025-07-25 01:40:35,518 - INFO - 📊 اختبار جلب مستخدمي User Manager...
2025-07-25 01:40:38,330 - INFO - ✅ تم جلب 1559 مستخدم من User Manager
2025-07-25 01:40:38,331 - INFO - 👥 عرض أول 5 مستخدمين:
2025-07-25 01:40:38,331 - INFO -   1. غير محدد (البروفايل: غير محدد)
2025-07-25 01:40:38,332 - INFO -   2. غير محدد (البروفايل: غير محدد)
2025-07-25 01:40:38,332 - INFO -   3. غير محدد (البروفايل: غير محدد)
2025-07-25 01:40:38,332 - INFO -   4. غير محدد (البروفايل: غير محدد)
2025-07-25 01:40:38,333 - INFO -   5. غير محدد (البروفايل: غير محدد)
2025-07-25 01:40:38,333 - ERROR - ❌ خطأ في API: 'RouterOsApi' object has no attribute 'disconnect'
2025-07-25 01:40:38,334 - INFO - 📊 نتيجة الاختبار: خطأ في API: 'RouterOsApi' object has no attribute 'disconnect'
2025-07-25 01:40:38,638 - INFO - 🔔 بدء إرسال إشعار اختيار القالب: 10 (User Manager)
2025-07-25 01:40:38,638 - INFO - 📊 جلب العدد الحالي من User Manager...
2025-07-25 01:40:38,639 - INFO - 🔍 بدء جلب العدد الحالي للكروت في User Manager
2025-07-25 01:40:38,639 - INFO - ✅ مكتبة routeros_api متوفرة
2025-07-25 01:40:38,639 - INFO - 🔗 محاولة الاتصال بـ 6.6.6.100 باستخدام المستخدم: 11
2025-07-25 01:40:38,643 - INFO - استخدام الاتصال الحالي
2025-07-25 01:40:38,643 - INFO - ✅ تم الاتصال بـ MikroTik بنجاح
2025-07-25 01:40:38,644 - INFO - 📊 جلب قائمة المستخدمين من User Manager...
2025-07-25 01:40:41,448 - INFO - ✅ تم جلب 1559 مستخدم من User Manager بنجاح
2025-07-25 01:40:41,449 - INFO - 👤 مستخدم 1: غير محدد
2025-07-25 01:40:41,449 - INFO - 👤 مستخدم 2: غير محدد
2025-07-25 01:40:41,449 - INFO - 👤 مستخدم 3: غير محدد
2025-07-25 01:40:41,451 - INFO - 📈 العدد المجلوب من User Manager: 1559
2025-07-25 01:40:41,711 - INFO - ✅ تم إرسال إشعار اختيار القالب '10' - العدد الحالي: 1559
2025-07-25 01:57:32,850 - INFO - تم بدء تشغيل التطبيق
2025-07-25 01:57:32,850 - INFO - تم إنشاء المجلدات الأساسية
2025-07-25 01:57:32,863 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-25 01:57:32,864 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-25 01:57:33,361 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-25 01:57:40,297 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-25 01:57:40,298 - INFO - تم إعداد التطبيق بنجاح
2025-07-25 01:57:42,321 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-25 01:57:42,691 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-25 01:57:42,692 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-25 01:57:45,697 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-25 01:57:45,736 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-25 01:57:46,100 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-25 01:57:46,100 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-25 02:01:10,835 - INFO - معالجة callback: select_system_um
2025-07-25 02:01:11,408 - INFO - تم إرسال 2 قالب مفلتر لنظام User Manager
2025-07-25 02:01:15,245 - INFO - معالجة callback: independent_template_um_10
2025-07-25 02:01:15,717 - INFO - 🔄 تبديل النظام تلقائياً من None إلى user_manager
2025-07-25 02:01:15,717 - INFO - 🔄 طلب تبديل النظام من التلجرام: None → user_manager
2025-07-25 02:01:16,003 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-25 02:01:16,058 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-25 02:01:16,158 - WARNING - تحذير: فشل في حفظ الإعدادات: 'MikroTikCardGenerator' object has no attribute 'version_combo'
2025-07-25 02:01:16,159 - INFO - تم تعيين النظام الجديد: user_manager
2025-07-25 02:01:16,160 - INFO - 🔄 إعادة بناء الواجهة للنظام: user_manager
2025-07-25 02:01:16,161 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-25 02:01:16,164 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-25 02:01:16,659 - INFO - تم إعداد تبويب عرض الكروت لـ User Manager بنجاح
2025-07-25 02:01:16,837 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-25 02:01:16,838 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-25 02:01:16,982 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام user_manager
2025-07-25 02:01:16,982 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-25 02:01:17,058 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager
2025-07-25 02:01:17,122 - INFO - تم تسجيل العملية: تبديل النظام من التلجرام - تم تبديل النظام من None إلى user_manager
2025-07-25 02:01:17,125 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-25 02:01:17,126 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-25 02:01:17,129 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-25 02:01:17,378 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-25 02:01:17,378 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-25 02:01:17,414 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-25 02:01:17,424 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-25 02:01:17,428 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-25 02:01:17,432 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-25 02:01:17,434 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-25 02:01:17,435 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager - القالب: 10
2025-07-25 02:01:17,802 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-25 02:01:19,738 - INFO - معالجة callback: independent_create_um_10_lightning
2025-07-25 02:01:19,983 - INFO - 🔍 بدء اختبار جلب العدد الحالي للكروت قبل إرسال الإشعار
2025-07-25 02:01:19,983 - INFO - 🧪 بدء اختبار الاتصال وجلب عدد User Manager
2025-07-25 02:01:19,984 - INFO - محاولة الاتصال بـ 6.6.6.100:8728 (عادي)
2025-07-25 02:01:20,051 - INFO - نجح الاتصال مع 6.6.6.100
2025-07-25 02:01:20,053 - INFO - ✅ نجح الاتصال - اختبار جلب العدد...
2025-07-25 02:01:20,091 - INFO - 🏷️ اسم النظام: MikroTik
2025-07-25 02:01:20,135 - INFO - 📋 إصدار RouterOS: 6.43.12 (stable)
2025-07-25 02:01:20,136 - INFO - 📊 اختبار جلب مستخدمي User Manager...
2025-07-25 02:01:22,923 - INFO - ✅ تم جلب 1559 مستخدم من User Manager
2025-07-25 02:01:22,924 - INFO - 👥 عرض أول 5 مستخدمين:
2025-07-25 02:01:22,924 - INFO -   1. غير محدد (البروفايل: غير محدد)
2025-07-25 02:01:22,925 - INFO -   2. غير محدد (البروفايل: غير محدد)
2025-07-25 02:01:22,925 - INFO -   3. غير محدد (البروفايل: غير محدد)
2025-07-25 02:01:22,926 - INFO -   4. غير محدد (البروفايل: غير محدد)
2025-07-25 02:01:22,927 - INFO -   5. غير محدد (البروفايل: غير محدد)
2025-07-25 02:01:22,927 - ERROR - ❌ خطأ في API: 'RouterOsApi' object has no attribute 'disconnect'
2025-07-25 02:01:22,928 - INFO - 📊 نتيجة الاختبار: خطأ في API: 'RouterOsApi' object has no attribute 'disconnect'
2025-07-25 02:01:23,277 - INFO - 🔔 بدء إرسال إشعار اختيار القالب: 10 (User Manager)
2025-07-25 02:01:23,277 - INFO - 📊 جلب العدد الحالي من User Manager...
2025-07-25 02:01:23,278 - INFO - 🔍 بدء جلب العدد الحالي للكروت في User Manager
2025-07-25 02:01:23,278 - INFO - ✅ مكتبة routeros_api متوفرة
2025-07-25 02:01:23,278 - INFO - 🔗 محاولة الاتصال بـ 6.6.6.100 باستخدام المستخدم: 11
2025-07-25 02:01:23,281 - INFO - استخدام الاتصال الحالي
2025-07-25 02:01:23,282 - INFO - ✅ تم الاتصال بـ MikroTik بنجاح
2025-07-25 02:01:23,282 - INFO - 📊 جلب قائمة المستخدمين من User Manager...
2025-07-25 02:01:26,074 - INFO - ✅ تم جلب 1559 مستخدم من User Manager بنجاح
2025-07-25 02:01:26,075 - INFO - 👤 مستخدم 1: غير محدد
2025-07-25 02:01:26,075 - INFO - 👤 مستخدم 2: غير محدد
2025-07-25 02:01:26,075 - INFO - 👤 مستخدم 3: غير محدد
2025-07-25 02:01:26,076 - INFO - 📈 العدد المجلوب من User Manager: 1559
2025-07-25 02:01:26,382 - INFO - ✅ تم إرسال إشعار اختيار القالب '10' - العدد الحالي: 1559
2025-07-25 02:01:43,040 - INFO - معالجة callback: independent_count_um_10_lightning_200
2025-07-25 02:01:43,500 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-25 02:01:43,501 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-25 02:01:43,515 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-25 02:01:43,525 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-25 02:01:43,562 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-25 02:01:43,562 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-25 02:01:43,566 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-25 02:01:43,571 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-25 02:01:43,819 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-25 02:01:43,819 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-25 02:01:43,832 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-25 02:01:43,833 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-25 02:01:43,879 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-25 02:01:43,879 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-25 02:01:43,882 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-25 02:01:43,896 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-25 02:01:43,896 - INFO - ⚡ البرق: تم الحفاظ على العدد المحدد: 200
2025-07-25 02:01:43,897 - INFO - تم تطبيق إعدادات القالب '10' بنجاح
2025-07-25 02:01:43,897 - INFO - ⚡ استخدام البرق للتلجرام (مع جدولة مؤقتة وتنظيف تلقائي)
2025-07-25 02:01:43,898 - INFO - ⚡ بدء البرق التلقائي للتلجرام (مع جدولة مؤقتة وتنظيف تلقائي)
2025-07-25 02:01:43,898 - INFO - ⚡ البرق: استخدام العدد من التلجرام بوت: 200
2025-07-25 02:01:43,899 - INFO - ⚡ البرق للتلجرام: العدد المطلوب 200 حساب
2025-07-25 02:01:43,899 - INFO - ⚡ البرق: تم تحديث عدد الحسابات في الواجهة إلى: 200
2025-07-25 02:01:43,902 - INFO - ⚡ البرق للتلجرام: تم توليد 200 حساب
2025-07-25 02:01:43,902 - INFO - ⚡ البرق للتلجرام: تنفيذ متسلسل مجدول لـ 200 كارت (حد التقسيم: 100)
2025-07-25 02:01:43,903 - INFO - ⚡ البرق للتلجرام: بدء معالجة الأعداد الكبيرة مع جدولة واحدة
2025-07-25 02:01:43,904 - INFO - ⚡ البرق للتلجرام: تم تقسيم 200 كارت إلى 2 مجموعة للتنفيذ المتسلسل
2025-07-25 02:01:43,904 - INFO - ⚡ البرق للتلجرام: إنشاء السكريبت للمجموعة 1/2 (100 كارت)
2025-07-25 02:01:43,905 - INFO - ⚡ البرق للتلجرام: تم إعداد السكريبت telegram_lightning_batch1_user_manager_20250725_020143
2025-07-25 02:01:43,905 - INFO - ⚡ البرق للتلجرام: إنشاء السكريبت للمجموعة 2/2 (100 كارت)
2025-07-25 02:01:43,905 - INFO - ⚡ البرق للتلجرام: تم إعداد السكريبت telegram_lightning_batch2_user_manager_20250725_020143
2025-07-25 02:01:43,909 - INFO - استخدام الاتصال الحالي
2025-07-25 02:01:43,975 - INFO - ⚡ تم إرسال السكريبت المترابط: telegram_lightning_batch1_user_manager_20250725_020143
2025-07-25 02:01:44,024 - INFO - ⚡ تم إرسال السكريبت المترابط: telegram_lightning_batch2_user_manager_20250725_020143
2025-07-25 02:01:44,061 - INFO - ⚡ وقت MikroTik الحالي: 23:01:43
2025-07-25 02:01:44,088 - INFO - ⚡ السكريبت الأول سينفذ في: 23:01:46 (بعد 3 ثواني من وقت MikroTik)
2025-07-25 02:01:44,135 - INFO - ⚡ تم إنشاء الجدولة الرئيسية: telegram_main_schedule_20250725_020143 لتشغيل telegram_lightning_batch1_user_manager_20250725_020143 في 23:01:46
2025-07-25 02:01:44,147 - INFO - ⚡ تم إعداد البرق المترابط بنجاح - جدولة واحدة: telegram_main_schedule_20250725_020143, 2 سكريبت مترابط
2025-07-25 02:01:44,147 - INFO - ⚡ البرق للتلجرام مكتمل: تم إنشاء جدولة واحدة لـ 2 مجموعة متسلسلة (200 كارت)
2025-07-25 02:01:47,091 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 200
2025-07-25 02:01:47,112 - INFO - تم إنشاء PDF بنجاح: exports/كروت_(10)_ب20-200 كارت-25-07-2025-02-01-46-um.pdf
2025-07-25 02:01:47,136 - INFO - تم حفظ ملف .rsc الاحتياطي: exports/كروت_(10)_ب20-200 كارت-25-07-2025-02-01-46-um.rsc
2025-07-25 02:01:47,842 - INFO - تم إرسال الملف بنجاح: كروت_(10)_ب20-200 كارت-25-07-2025-02-01-46-um.pdf
2025-07-25 02:01:48,338 - INFO - تم إرسال 200 كرت عبر التلجرام باستخدام قالب 10
2025-07-25 02:31:43,252 - INFO - تم بدء تشغيل التطبيق
2025-07-25 02:31:43,378 - INFO - تم إنشاء المجلدات الأساسية
2025-07-25 02:31:43,381 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-25 02:31:43,383 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-25 02:31:43,486 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-25 02:31:44,131 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-25 02:31:44,148 - INFO - تم إعداد التطبيق بنجاح
2025-07-25 02:31:46,150 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-25 02:31:46,437 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-25 02:31:46,439 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-25 02:31:49,447 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-25 02:31:49,448 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-25 02:31:49,799 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-25 02:31:49,800 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-25 02:31:53,433 - INFO - معالجة callback: select_system_um
2025-07-25 02:31:53,948 - INFO - تم إرسال 2 قالب مفلتر لنظام User Manager
2025-07-25 02:31:55,795 - INFO - معالجة callback: independent_template_um_10
2025-07-25 02:31:56,275 - INFO - 🔄 تبديل النظام تلقائياً من None إلى user_manager
2025-07-25 02:31:56,276 - INFO - 🔄 طلب تبديل النظام من التلجرام: None → user_manager
2025-07-25 02:31:56,517 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-25 02:31:56,547 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-25 02:31:56,647 - WARNING - تحذير: فشل في حفظ الإعدادات: 'MikroTikCardGenerator' object has no attribute 'version_combo'
2025-07-25 02:31:56,648 - INFO - تم تعيين النظام الجديد: user_manager
2025-07-25 02:31:56,649 - INFO - 🔄 إعادة بناء الواجهة للنظام: user_manager
2025-07-25 02:31:56,650 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-25 02:31:56,653 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-25 02:31:57,133 - INFO - تم إعداد تبويب عرض الكروت لـ User Manager بنجاح
2025-07-25 02:31:57,331 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-25 02:31:57,332 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-25 02:31:57,489 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام user_manager
2025-07-25 02:31:57,490 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-25 02:31:57,599 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager
2025-07-25 02:31:57,601 - INFO - تم تسجيل العملية: تبديل النظام من التلجرام - تم تبديل النظام من None إلى user_manager
2025-07-25 02:31:57,605 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-25 02:31:57,629 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-25 02:31:57,631 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-25 02:31:57,915 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-25 02:31:57,915 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-25 02:31:57,952 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-25 02:31:57,953 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-25 02:31:57,956 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-25 02:31:57,960 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-25 02:31:57,965 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-25 02:31:57,966 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager - القالب: 10
2025-07-25 02:31:58,220 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-25 02:32:01,146 - INFO - معالجة callback: independent_create_um_10_lightning
2025-07-25 02:32:01,359 - INFO - 🔍 بدء اختبار جلب العدد الحالي للكروت قبل إرسال الإشعار
2025-07-25 02:32:01,359 - INFO - 🧪 بدء اختبار الاتصال وجلب عدد User Manager
2025-07-25 02:32:01,360 - INFO - محاولة الاتصال بـ 6.6.6.100:8728 (عادي)
2025-07-25 02:32:01,428 - INFO - نجح الاتصال مع 6.6.6.100
2025-07-25 02:32:01,429 - INFO - ✅ نجح الاتصال - اختبار جلب العدد...
2025-07-25 02:32:01,468 - INFO - 🏷️ اسم النظام: MikroTik
2025-07-25 02:32:01,511 - INFO - 📋 إصدار RouterOS: 6.43.12 (stable)
2025-07-25 02:32:01,512 - INFO - 📊 اختبار جلب مستخدمي User Manager...
2025-07-25 02:32:04,671 - INFO - ✅ تم جلب 1759 مستخدم من User Manager
2025-07-25 02:32:04,672 - INFO - 👥 عرض أول 5 مستخدمين:
2025-07-25 02:32:04,672 - INFO -   1. غير محدد (البروفايل: غير محدد)
2025-07-25 02:32:04,672 - INFO -   2. غير محدد (البروفايل: غير محدد)
2025-07-25 02:32:04,673 - INFO -   3. غير محدد (البروفايل: غير محدد)
2025-07-25 02:32:04,673 - INFO -   4. غير محدد (البروفايل: غير محدد)
2025-07-25 02:32:04,674 - INFO -   5. غير محدد (البروفايل: غير محدد)
2025-07-25 02:32:04,674 - ERROR - ❌ خطأ في API: 'RouterOsApi' object has no attribute 'disconnect'
2025-07-25 02:32:04,675 - INFO - 📊 نتيجة الاختبار: خطأ في API: 'RouterOsApi' object has no attribute 'disconnect'
2025-07-25 02:32:04,949 - INFO - 🔔 بدء إرسال إشعار اختيار القالب: 10 (User Manager)
2025-07-25 02:32:04,950 - INFO - 📊 جلب العدد الحالي من User Manager...
2025-07-25 02:32:04,950 - INFO - 🔍 بدء جلب العدد الحالي للكروت في User Manager
2025-07-25 02:32:04,951 - INFO - ✅ مكتبة routeros_api متوفرة
2025-07-25 02:32:04,951 - INFO - 🔗 محاولة الاتصال بـ 6.6.6.100 باستخدام المستخدم: 11
2025-07-25 02:32:04,954 - INFO - استخدام الاتصال الحالي
2025-07-25 02:32:04,954 - INFO - ✅ تم الاتصال بـ MikroTik بنجاح
2025-07-25 02:32:04,955 - INFO - 📊 جلب قائمة المستخدمين من User Manager...
2025-07-25 02:32:08,114 - INFO - ✅ تم جلب 1759 مستخدم من User Manager بنجاح
2025-07-25 02:32:08,114 - INFO - 👤 مستخدم 1: غير محدد
2025-07-25 02:32:08,114 - INFO - 👤 مستخدم 2: غير محدد
2025-07-25 02:32:08,115 - INFO - 👤 مستخدم 3: غير محدد
2025-07-25 02:32:08,116 - INFO - 📈 العدد المجلوب من User Manager: 1759
2025-07-25 02:32:08,396 - INFO - ✅ تم إرسال إشعار اختيار القالب '10' - العدد الحالي: 1759
2025-07-25 02:32:14,381 - INFO - معالجة callback: independent_count_um_10_lightning_200
2025-07-25 02:32:14,886 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-25 02:32:14,887 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-25 02:32:14,901 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-25 02:32:14,901 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-25 02:32:14,944 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-25 02:32:14,944 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-25 02:32:14,959 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-25 02:32:14,964 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-25 02:32:15,205 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-25 02:32:15,206 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-25 02:32:15,230 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-25 02:32:15,230 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-25 02:32:15,257 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-25 02:32:15,270 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-25 02:32:15,274 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-25 02:32:15,279 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-25 02:32:15,279 - INFO - ⚡ البرق: تم الحفاظ على العدد المحدد: 200
2025-07-25 02:32:15,280 - INFO - تم تطبيق إعدادات القالب '10' بنجاح
2025-07-25 02:32:15,280 - INFO - ⚡ استخدام البرق للتلجرام (مع جدولة مؤقتة وتنظيف تلقائي)
2025-07-25 02:32:15,281 - INFO - ⚡ بدء البرق التلقائي للتلجرام (مع جدولة مؤقتة وتنظيف تلقائي)
2025-07-25 02:32:15,282 - INFO - ⚡ البرق: استخدام العدد من التلجرام بوت: 200
2025-07-25 02:32:15,282 - INFO - ⚡ البرق للتلجرام: العدد المطلوب 200 حساب
2025-07-25 02:32:15,283 - INFO - ⚡ البرق: تم تحديث عدد الحسابات في الواجهة إلى: 200
2025-07-25 02:32:15,285 - INFO - ⚡ البرق للتلجرام: تم توليد 200 حساب
2025-07-25 02:32:15,286 - INFO - ⚡ البرق للتلجرام: تنفيذ متسلسل مجدول لـ 200 كارت (حد التقسيم: 100)
2025-07-25 02:32:15,287 - INFO - ⚡ البرق للتلجرام: بدء معالجة الأعداد الكبيرة مع جدولة واحدة
2025-07-25 02:32:15,287 - INFO - ⚡ البرق للتلجرام: تم تقسيم 200 كارت إلى 2 مجموعة للتنفيذ المتسلسل
2025-07-25 02:32:15,288 - INFO - ⚡ البرق للتلجرام: إنشاء السكريبت للمجموعة 1/2 (100 كارت)
2025-07-25 02:32:15,288 - INFO - ⚡ البرق للتلجرام: تم إعداد السكريبت telegram_lightning_batch1_user_manager_20250725_023215
2025-07-25 02:32:15,290 - INFO - ⚡ البرق للتلجرام: إنشاء السكريبت للمجموعة 2/2 (100 كارت)
2025-07-25 02:32:15,294 - INFO - ⚡ البرق للتلجرام: تم إعداد السكريبت telegram_lightning_batch2_user_manager_20250725_023215
2025-07-25 02:32:15,309 - INFO - استخدام الاتصال الحالي
2025-07-25 02:32:15,372 - INFO - ⚡ تم إرسال السكريبت المترابط: telegram_lightning_batch1_user_manager_20250725_023215
2025-07-25 02:32:15,492 - INFO - ⚡ تم إرسال السكريبت المترابط: telegram_lightning_batch2_user_manager_20250725_023215
2025-07-25 02:32:15,528 - INFO - ⚡ وقت MikroTik الحالي: 23:32:15
2025-07-25 02:32:15,532 - INFO - ⚡ السكريبت الأول سينفذ في: 23:32:18 (بعد 3 ثواني من وقت MikroTik)
2025-07-25 02:32:15,581 - INFO - ⚡ تم إنشاء الجدولة الرئيسية: telegram_main_schedule_20250725_023215 لتشغيل telegram_lightning_batch1_user_manager_20250725_023215 في 23:32:18
2025-07-25 02:32:15,586 - INFO - ⚡ تم إعداد البرق المترابط بنجاح - جدولة واحدة: telegram_main_schedule_20250725_023215, 2 سكريبت مترابط
2025-07-25 02:32:15,590 - INFO - ⚡ البرق للتلجرام مكتمل: تم إنشاء جدولة واحدة لـ 2 مجموعة متسلسلة (200 كارت)
2025-07-25 02:32:18,393 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 200
2025-07-25 02:32:18,438 - INFO - تم إنشاء PDF بنجاح: exports/كروت_(10)_ب20-200 كارت-25-07-2025-02-32-18-um.pdf
2025-07-25 02:32:18,441 - INFO - تم حفظ ملف .rsc الاحتياطي: exports/كروت_(10)_ب20-200 كارت-25-07-2025-02-32-18-um.rsc
2025-07-25 02:32:19,066 - INFO - تم إرسال الملف بنجاح: كروت_(10)_ب20-200 كارت-25-07-2025-02-32-18-um.pdf
2025-07-25 02:32:19,310 - INFO - تم إرسال 200 كرت عبر التلجرام باستخدام قالب 10
2025-07-25 02:33:00,300 - INFO - معالجة callback: independent_count_um_10_lightning_200
2025-07-25 02:33:00,762 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-25 02:33:00,762 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-25 02:33:00,778 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-25 02:33:00,779 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-25 02:33:00,818 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-25 02:33:00,818 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-25 02:33:00,821 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-25 02:33:00,836 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-25 02:33:01,075 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-25 02:33:01,075 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-25 02:33:01,100 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-25 02:33:01,100 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-25 02:33:01,145 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-25 02:33:01,146 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-25 02:33:01,149 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-25 02:33:01,153 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-25 02:33:01,153 - INFO - ⚡ البرق: تم الحفاظ على العدد المحدد: 200
2025-07-25 02:33:01,155 - INFO - تم تطبيق إعدادات القالب '10' بنجاح
2025-07-25 02:33:01,155 - INFO - ⚡ استخدام البرق للتلجرام (مع جدولة مؤقتة وتنظيف تلقائي)
2025-07-25 02:33:01,155 - INFO - ⚡ بدء البرق التلقائي للتلجرام (مع جدولة مؤقتة وتنظيف تلقائي)
2025-07-25 02:33:01,156 - INFO - ⚡ البرق: استخدام العدد من التلجرام بوت: 200
2025-07-25 02:33:01,156 - INFO - ⚡ البرق للتلجرام: العدد المطلوب 200 حساب
2025-07-25 02:33:01,157 - INFO - ⚡ البرق: تم تحديث عدد الحسابات في الواجهة إلى: 200
2025-07-25 02:33:01,159 - INFO - ⚡ البرق للتلجرام: تم توليد 200 حساب
2025-07-25 02:33:01,159 - INFO - ⚡ البرق للتلجرام: تنفيذ متسلسل مجدول لـ 200 كارت (حد التقسيم: 100)
2025-07-25 02:33:01,161 - INFO - ⚡ البرق للتلجرام: بدء معالجة الأعداد الكبيرة مع جدولة واحدة
2025-07-25 02:33:01,162 - INFO - ⚡ البرق للتلجرام: تم تقسيم 200 كارت إلى 2 مجموعة للتنفيذ المتسلسل
2025-07-25 02:33:01,163 - INFO - ⚡ البرق للتلجرام: إنشاء السكريبت للمجموعة 1/2 (100 كارت)
2025-07-25 02:33:01,163 - INFO - ⚡ البرق للتلجرام: تم إعداد السكريبت telegram_lightning_batch1_user_manager_20250725_023301
2025-07-25 02:33:01,163 - INFO - ⚡ البرق للتلجرام: إنشاء السكريبت للمجموعة 2/2 (100 كارت)
2025-07-25 02:33:01,164 - INFO - ⚡ البرق للتلجرام: تم إعداد السكريبت telegram_lightning_batch2_user_manager_20250725_023301
2025-07-25 02:33:01,179 - INFO - استخدام الاتصال الحالي
2025-07-25 02:33:01,313 - INFO - ⚡ تم إرسال السكريبت المترابط: telegram_lightning_batch1_user_manager_20250725_023301
2025-07-25 02:33:01,362 - INFO - ⚡ تم إرسال السكريبت المترابط: telegram_lightning_batch2_user_manager_20250725_023301
2025-07-25 02:33:01,365 - INFO - ⚡ وقت MikroTik الحالي: 23:33:01
2025-07-25 02:33:01,365 - INFO - ⚡ السكريبت الأول سينفذ في: 23:33:04 (بعد 3 ثواني من وقت MikroTik)
2025-07-25 02:33:01,411 - INFO - ⚡ تم إنشاء الجدولة الرئيسية: telegram_main_schedule_20250725_023301 لتشغيل telegram_lightning_batch1_user_manager_20250725_023301 في 23:33:04
2025-07-25 02:33:01,414 - INFO - ⚡ تم إعداد البرق المترابط بنجاح - جدولة واحدة: telegram_main_schedule_20250725_023301, 2 سكريبت مترابط
2025-07-25 02:33:01,426 - INFO - ⚡ البرق للتلجرام مكتمل: تم إنشاء جدولة واحدة لـ 2 مجموعة متسلسلة (200 كارت)
2025-07-25 02:33:04,245 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 200
2025-07-25 02:33:04,262 - INFO - تم إنشاء PDF بنجاح: exports/كروت_(10)_ب20-200 كارت-25-07-2025-02-33-04-um.pdf
2025-07-25 02:33:04,278 - INFO - تم حفظ ملف .rsc الاحتياطي: exports/كروت_(10)_ب20-200 كارت-25-07-2025-02-33-04-um.rsc
2025-07-25 02:33:04,906 - INFO - تم إرسال الملف بنجاح: كروت_(10)_ب20-200 كارت-25-07-2025-02-33-04-um.pdf
2025-07-25 02:33:05,152 - INFO - تم إرسال 200 كرت عبر التلجرام باستخدام قالب 10
2025-07-25 02:33:42,098 - INFO - معالجة callback: independent_count_um_10_lightning_200
2025-07-25 02:33:42,560 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-25 02:33:42,561 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-25 02:33:42,575 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-25 02:33:42,577 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-25 02:33:42,625 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-25 02:33:42,626 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-25 02:33:42,627 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-25 02:33:42,632 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-25 02:33:42,872 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-25 02:33:42,872 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-25 02:33:42,887 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-25 02:33:42,898 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-25 02:33:42,935 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-25 02:33:42,935 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-25 02:33:42,938 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-25 02:33:42,943 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-25 02:33:42,944 - INFO - ⚡ البرق: تم الحفاظ على العدد المحدد: 200
2025-07-25 02:33:42,945 - INFO - تم تطبيق إعدادات القالب '10' بنجاح
2025-07-25 02:33:42,946 - INFO - ⚡ استخدام البرق للتلجرام (مع جدولة مؤقتة وتنظيف تلقائي)
2025-07-25 02:33:42,947 - INFO - ⚡ بدء البرق التلقائي للتلجرام (مع جدولة مؤقتة وتنظيف تلقائي)
2025-07-25 02:33:42,948 - INFO - ⚡ البرق: استخدام العدد من التلجرام بوت: 200
2025-07-25 02:33:42,961 - INFO - ⚡ البرق للتلجرام: العدد المطلوب 200 حساب
2025-07-25 02:33:42,961 - INFO - ⚡ البرق: تم تحديث عدد الحسابات في الواجهة إلى: 200
2025-07-25 02:33:42,965 - INFO - ⚡ البرق للتلجرام: تم توليد 200 حساب
2025-07-25 02:33:42,965 - INFO - ⚡ البرق للتلجرام: تنفيذ متسلسل مجدول لـ 200 كارت (حد التقسيم: 100)
2025-07-25 02:33:42,966 - INFO - ⚡ البرق للتلجرام: بدء معالجة الأعداد الكبيرة مع جدولة واحدة
2025-07-25 02:33:42,967 - INFO - ⚡ البرق للتلجرام: تم تقسيم 200 كارت إلى 2 مجموعة للتنفيذ المتسلسل
2025-07-25 02:33:42,967 - INFO - ⚡ البرق للتلجرام: إنشاء السكريبت للمجموعة 1/2 (100 كارت)
2025-07-25 02:33:42,967 - INFO - ⚡ البرق للتلجرام: تم إعداد السكريبت telegram_lightning_batch1_user_manager_20250725_023342
2025-07-25 02:33:42,968 - INFO - ⚡ البرق للتلجرام: إنشاء السكريبت للمجموعة 2/2 (100 كارت)
2025-07-25 02:33:42,968 - INFO - ⚡ البرق للتلجرام: تم إعداد السكريبت telegram_lightning_batch2_user_manager_20250725_023342
2025-07-25 02:33:42,971 - INFO - استخدام الاتصال الحالي
2025-07-25 02:33:43,028 - INFO - ⚡ تم إرسال السكريبت المترابط: telegram_lightning_batch1_user_manager_20250725_023342
2025-07-25 02:33:43,081 - INFO - ⚡ تم إرسال السكريبت المترابط: telegram_lightning_batch2_user_manager_20250725_023342
2025-07-25 02:33:43,084 - INFO - ⚡ وقت MikroTik الحالي: 23:33:42
2025-07-25 02:33:43,084 - INFO - ⚡ السكريبت الأول سينفذ في: 23:33:45 (بعد 3 ثواني من وقت MikroTik)
2025-07-25 02:33:43,132 - INFO - ⚡ تم إنشاء الجدولة الرئيسية: telegram_main_schedule_20250725_023342 لتشغيل telegram_lightning_batch1_user_manager_20250725_023342 في 23:33:45
2025-07-25 02:33:43,137 - INFO - ⚡ تم إعداد البرق المترابط بنجاح - جدولة واحدة: telegram_main_schedule_20250725_023342, 2 سكريبت مترابط
2025-07-25 02:33:43,138 - INFO - ⚡ البرق للتلجرام مكتمل: تم إنشاء جدولة واحدة لـ 2 مجموعة متسلسلة (200 كارت)
2025-07-25 02:33:46,036 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 200
2025-07-25 02:33:46,088 - INFO - تم إنشاء PDF بنجاح: exports/كروت_(10)_ب20-200 كارت-25-07-2025-02-33-45-um.pdf
2025-07-25 02:33:46,090 - INFO - تم حفظ ملف .rsc الاحتياطي: exports/كروت_(10)_ب20-200 كارت-25-07-2025-02-33-45-um.rsc
2025-07-25 02:33:46,822 - INFO - تم إرسال الملف بنجاح: كروت_(10)_ب20-200 كارت-25-07-2025-02-33-45-um.pdf
2025-07-25 02:33:47,059 - INFO - تم إرسال 200 كرت عبر التلجرام باستخدام قالب 10
2025-07-25 02:36:49,722 - INFO - بدء إغلاق التطبيق
2025-07-25 02:36:49,869 - INFO - تم قطع الاتصال مع MikroTik
2025-07-25 02:36:50,031 - INFO - تم تسجيل العملية: إغلاق التطبيق - تم إغلاق التطبيق بنجاح
2025-07-25 02:36:50,173 - INFO - تم إغلاق التطبيق بنجاح
2025-07-25 02:53:42,806 - INFO - تم بدء تشغيل التطبيق
2025-07-25 02:53:42,861 - INFO - تم إنشاء المجلدات الأساسية
2025-07-25 02:53:43,125 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-25 02:53:43,125 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-25 02:53:43,962 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-25 02:53:47,592 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-25 02:53:47,592 - INFO - تم إعداد التطبيق بنجاح
2025-07-25 02:53:49,683 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-25 02:53:50,246 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-25 02:53:50,247 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-25 02:53:53,259 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-25 02:53:53,448 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-25 02:53:53,714 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-25 02:53:53,714 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-25 02:53:55,438 - INFO - معالجة callback: select_system_um
2025-07-25 02:53:55,898 - INFO - تم إرسال 2 قالب مفلتر لنظام User Manager
2025-07-25 02:53:58,645 - INFO - معالجة callback: independent_template_um_10
2025-07-25 02:53:59,267 - INFO - 🔄 تبديل النظام تلقائياً من None إلى user_manager
2025-07-25 02:53:59,267 - INFO - 🔄 طلب تبديل النظام من التلجرام: None → user_manager
2025-07-25 02:53:59,513 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-25 02:53:59,823 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-25 02:53:59,924 - WARNING - تحذير: فشل في حفظ الإعدادات: 'MikroTikCardGenerator' object has no attribute 'version_combo'
2025-07-25 02:53:59,924 - INFO - تم تعيين النظام الجديد: user_manager
2025-07-25 02:53:59,925 - INFO - 🔄 إعادة بناء الواجهة للنظام: user_manager
2025-07-25 02:53:59,926 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-25 02:53:59,930 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-25 02:54:00,922 - INFO - تم إعداد تبويب عرض الكروت لـ User Manager بنجاح
2025-07-25 02:54:01,133 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-25 02:54:01,134 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-25 02:54:01,441 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام user_manager
2025-07-25 02:54:01,441 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-25 02:54:01,682 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager
2025-07-25 02:54:01,687 - INFO - تم تسجيل العملية: تبديل النظام من التلجرام - تم تبديل النظام من None إلى user_manager
2025-07-25 02:54:01,688 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-25 02:54:01,690 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-25 02:54:01,691 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-25 02:54:02,064 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-25 02:54:02,064 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-25 02:54:02,105 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-25 02:54:02,105 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-25 02:54:02,109 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-25 02:54:02,113 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-25 02:54:02,116 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-25 02:54:02,118 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager - القالب: 10
2025-07-25 02:54:02,379 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-25 02:54:05,673 - INFO - معالجة callback: independent_create_um_10_lightning
2025-07-25 02:54:05,894 - INFO - 🔍 بدء اختبار جلب العدد الحالي للكروت قبل إرسال الإشعار
2025-07-25 02:54:05,894 - INFO - 🧪 بدء اختبار الاتصال وجلب عدد User Manager
2025-07-25 02:54:05,895 - INFO - محاولة الاتصال بـ 6.6.6.100:8728 (عادي)
2025-07-25 02:54:05,958 - INFO - نجح الاتصال مع 6.6.6.100
2025-07-25 02:54:05,959 - INFO - ✅ نجح الاتصال - اختبار جلب العدد...
2025-07-25 02:54:05,998 - INFO - 🏷️ اسم النظام: MikroTik
2025-07-25 02:54:06,051 - INFO - 📋 إصدار RouterOS: 6.43.12 (stable)
2025-07-25 02:54:06,052 - INFO - 📊 اختبار جلب مستخدمي User Manager...
2025-07-25 02:54:10,318 - INFO - ✅ تم جلب 2359 مستخدم من User Manager
2025-07-25 02:54:10,318 - INFO - 👥 عرض أول 5 مستخدمين:
2025-07-25 02:54:10,319 - INFO -   1. غير محدد (البروفايل: غير محدد)
2025-07-25 02:54:10,319 - INFO -   2. غير محدد (البروفايل: غير محدد)
2025-07-25 02:54:10,319 - INFO -   3. غير محدد (البروفايل: غير محدد)
2025-07-25 02:54:10,320 - INFO -   4. غير محدد (البروفايل: غير محدد)
2025-07-25 02:54:10,320 - INFO -   5. غير محدد (البروفايل: غير محدد)
2025-07-25 02:54:10,320 - ERROR - ❌ خطأ في API: 'RouterOsApi' object has no attribute 'disconnect'
2025-07-25 02:54:10,322 - INFO - 📊 نتيجة الاختبار: خطأ في API: 'RouterOsApi' object has no attribute 'disconnect'
2025-07-25 02:54:10,322 - INFO - 🔔 بدء إرسال إشعار اختيار القالب: 10 (User Manager)
2025-07-25 02:54:10,323 - INFO - 📊 جلب العدد الحالي من User Manager...
2025-07-25 02:54:10,323 - INFO - 🔍 بدء جلب العدد الحالي للكروت في User Manager
2025-07-25 02:54:10,325 - INFO - ✅ مكتبة routeros_api متوفرة
2025-07-25 02:54:10,326 - INFO - 🔗 محاولة الاتصال بـ 6.6.6.100 باستخدام المستخدم: 11
2025-07-25 02:54:10,329 - INFO - استخدام الاتصال الحالي
2025-07-25 02:54:10,342 - INFO - ✅ تم الاتصال بـ MikroTik بنجاح
2025-07-25 02:54:10,344 - INFO - 📊 جلب قائمة المستخدمين من User Manager...
2025-07-25 02:54:14,563 - INFO - ✅ تم جلب 2359 مستخدم من User Manager بنجاح
2025-07-25 02:54:14,564 - INFO - 👤 مستخدم 1: غير محدد
2025-07-25 02:54:14,564 - INFO - 👤 مستخدم 2: غير محدد
2025-07-25 02:54:14,565 - INFO - 👤 مستخدم 3: غير محدد
2025-07-25 02:54:14,566 - INFO - 📈 العدد المجلوب من User Manager: 2359
2025-07-25 02:54:14,864 - INFO - ✅ تم إرسال إشعار اختيار القالب '10' - العدد الحالي: 2359
2025-07-25 02:54:21,002 - INFO - معالجة callback: independent_count_um_10_lightning_200
2025-07-25 02:54:21,495 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-25 02:54:21,495 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-25 02:54:21,509 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-25 02:54:21,509 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-25 02:54:21,558 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-25 02:54:21,559 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-25 02:54:21,562 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-25 02:54:21,567 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-25 02:54:21,846 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-25 02:54:21,847 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-25 02:54:21,874 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-25 02:54:21,874 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-25 02:54:21,911 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-25 02:54:21,912 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-25 02:54:21,914 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-25 02:54:21,919 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-25 02:54:21,920 - INFO - ⚡ البرق: تم الحفاظ على العدد المحدد: 200
2025-07-25 02:54:21,922 - INFO - تم تطبيق إعدادات القالب '10' بنجاح
2025-07-25 02:54:21,922 - INFO - ⚡ استخدام البرق للتلجرام (مع جدولة مؤقتة وتنظيف تلقائي)
2025-07-25 02:54:21,922 - INFO - ⚡ بدء البرق التلقائي للتلجرام (مع جدولة مؤقتة وتنظيف تلقائي)
2025-07-25 02:54:21,922 - INFO - ⚡ البرق: استخدام العدد من التلجرام بوت: 200
2025-07-25 02:54:21,922 - INFO - ⚡ البرق للتلجرام: العدد المطلوب 200 حساب
2025-07-25 02:54:21,923 - INFO - ⚡ البرق: تم تحديث عدد الحسابات في الواجهة إلى: 200
2025-07-25 02:54:21,925 - INFO - ⚡ البرق للتلجرام: تم توليد 200 حساب
2025-07-25 02:54:21,925 - INFO - ⚡ البرق للتلجرام: تنفيذ متسلسل مجدول لـ 200 كارت (حد التقسيم: 100)
2025-07-25 02:54:21,926 - INFO - ⚡ البرق للتلجرام: بدء معالجة الأعداد الكبيرة مع جدولة واحدة
2025-07-25 02:54:21,926 - INFO - ⚡ البرق للتلجرام: تم تقسيم 200 كارت إلى 2 مجموعة للتنفيذ المتسلسل
2025-07-25 02:54:21,926 - INFO - ⚡ البرق للتلجرام: إنشاء السكريبت للمجموعة 1/2 (100 كارت)
2025-07-25 02:54:21,926 - INFO - ⚡ البرق للتلجرام: تم إعداد السكريبت telegram_lightning_batch1_user_manager_20250725_025421
2025-07-25 02:54:21,927 - INFO - ⚡ البرق للتلجرام: إنشاء السكريبت للمجموعة 2/2 (100 كارت)
2025-07-25 02:54:21,927 - INFO - ⚡ البرق للتلجرام: تم إعداد السكريبت telegram_lightning_batch2_user_manager_20250725_025421
2025-07-25 02:54:21,930 - INFO - استخدام الاتصال الحالي
2025-07-25 02:54:21,998 - INFO - ⚡ تم إرسال السكريبت المترابط: telegram_lightning_batch1_user_manager_20250725_025421
2025-07-25 02:54:22,040 - INFO - ⚡ تم إرسال السكريبت المترابط: telegram_lightning_batch2_user_manager_20250725_025421
2025-07-25 02:54:22,053 - INFO - ⚡ وقت MikroTik الحالي: 23:54:21
2025-07-25 02:54:22,085 - INFO - ⚡ السكريبت الأول سينفذ في: 23:54:24 (بعد 3 ثواني من وقت MikroTik)
2025-07-25 02:54:22,130 - INFO - ⚡ تم إنشاء الجدولة الرئيسية: telegram_main_schedule_20250725_025421 لتشغيل telegram_lightning_batch1_user_manager_20250725_025421 في 23:54:24
2025-07-25 02:54:22,134 - INFO - ⚡ تم إعداد البرق المترابط بنجاح - جدولة واحدة: telegram_main_schedule_20250725_025421, 2 سكريبت مترابط
2025-07-25 02:54:22,147 - INFO - ⚡ البرق للتلجرام مكتمل: تم إنشاء جدولة واحدة لـ 2 مجموعة متسلسلة (200 كارت)
2025-07-25 02:54:25,115 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 200
2025-07-25 02:54:25,154 - INFO - تم إنشاء PDF بنجاح: exports/كروت_(10)_ب20-200 كارت-25-07-2025-02-54-24-um.pdf
2025-07-25 02:54:25,158 - INFO - تم حفظ ملف .rsc الاحتياطي: exports/كروت_(10)_ب20-200 كارت-25-07-2025-02-54-24-um.rsc
2025-07-25 02:54:26,794 - INFO - تم إرسال الملف بنجاح: كروت_(10)_ب20-200 كارت-25-07-2025-02-54-24-um.pdf
2025-07-25 02:54:27,050 - INFO - تم إرسال 200 كرت عبر التلجرام باستخدام قالب 10
2025-07-25 02:55:02,298 - INFO - معالجة callback: independent_count_um_10_lightning_200
2025-07-25 02:55:02,794 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-25 02:55:02,795 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-25 02:55:02,809 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-25 02:55:02,810 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-25 02:55:02,847 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-25 02:55:02,847 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-25 02:55:02,850 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-25 02:55:02,865 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-25 02:55:03,138 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-25 02:55:03,139 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-25 02:55:03,153 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-25 02:55:03,164 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-25 02:55:03,192 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-25 02:55:03,193 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-25 02:55:03,196 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-25 02:55:03,212 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-25 02:55:03,212 - INFO - ⚡ البرق: تم الحفاظ على العدد المحدد: 200
2025-07-25 02:55:03,214 - INFO - تم تطبيق إعدادات القالب '10' بنجاح
2025-07-25 02:55:03,215 - INFO - ⚡ استخدام البرق للتلجرام (مع جدولة مؤقتة وتنظيف تلقائي)
2025-07-25 02:55:03,215 - INFO - ⚡ بدء البرق التلقائي للتلجرام (مع جدولة مؤقتة وتنظيف تلقائي)
2025-07-25 02:55:03,215 - INFO - ⚡ البرق: استخدام العدد من التلجرام بوت: 200
2025-07-25 02:55:03,216 - INFO - ⚡ البرق للتلجرام: العدد المطلوب 200 حساب
2025-07-25 02:55:03,216 - INFO - ⚡ البرق: تم تحديث عدد الحسابات في الواجهة إلى: 200
2025-07-25 02:55:03,219 - INFO - ⚡ البرق للتلجرام: تم توليد 200 حساب
2025-07-25 02:55:03,219 - INFO - ⚡ البرق للتلجرام: تنفيذ متسلسل مجدول لـ 200 كارت (حد التقسيم: 100)
2025-07-25 02:55:03,220 - INFO - ⚡ البرق للتلجرام: بدء معالجة الأعداد الكبيرة مع جدولة واحدة
2025-07-25 02:55:03,220 - INFO - ⚡ البرق للتلجرام: تم تقسيم 200 كارت إلى 2 مجموعة للتنفيذ المتسلسل
2025-07-25 02:55:03,222 - INFO - ⚡ البرق للتلجرام: إنشاء السكريبت للمجموعة 1/2 (100 كارت)
2025-07-25 02:55:03,223 - INFO - ⚡ البرق للتلجرام: تم إعداد السكريبت telegram_lightning_batch1_user_manager_20250725_025503
2025-07-25 02:55:03,225 - INFO - ⚡ البرق للتلجرام: إنشاء السكريبت للمجموعة 2/2 (100 كارت)
2025-07-25 02:55:03,227 - INFO - ⚡ البرق للتلجرام: تم إعداد السكريبت telegram_lightning_batch2_user_manager_20250725_025503
2025-07-25 02:55:03,243 - INFO - استخدام الاتصال الحالي
2025-07-25 02:55:03,297 - INFO - ⚡ تم إرسال السكريبت المترابط: telegram_lightning_batch1_user_manager_20250725_025503
2025-07-25 02:55:03,339 - INFO - ⚡ تم إرسال السكريبت المترابط: telegram_lightning_batch2_user_manager_20250725_025503
2025-07-25 02:55:03,343 - INFO - ⚡ وقت MikroTik الحالي: 23:55:02
2025-07-25 02:55:03,343 - INFO - ⚡ السكريبت الأول سينفذ في: 23:55:05 (بعد 3 ثواني من وقت MikroTik)
2025-07-25 02:55:03,391 - INFO - ⚡ تم إنشاء الجدولة الرئيسية: telegram_main_schedule_20250725_025503 لتشغيل telegram_lightning_batch1_user_manager_20250725_025503 في 23:55:05
2025-07-25 02:55:03,396 - INFO - ⚡ تم إعداد البرق المترابط بنجاح - جدولة واحدة: telegram_main_schedule_20250725_025503, 2 سكريبت مترابط
2025-07-25 02:55:03,397 - INFO - ⚡ البرق للتلجرام مكتمل: تم إنشاء جدولة واحدة لـ 2 مجموعة متسلسلة (200 كارت)
2025-07-25 02:55:06,302 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 200
2025-07-25 02:55:06,319 - INFO - تم إنشاء PDF بنجاح: exports/كروت_(10)_ب20-200 كارت-25-07-2025-02-55-06-um.pdf
2025-07-25 02:55:06,325 - INFO - تم حفظ ملف .rsc الاحتياطي: exports/كروت_(10)_ب20-200 كارت-25-07-2025-02-55-06-um.rsc
2025-07-25 02:55:06,962 - INFO - تم إرسال الملف بنجاح: كروت_(10)_ب20-200 كارت-25-07-2025-02-55-06-um.pdf
2025-07-25 02:55:07,303 - INFO - تم إرسال 200 كرت عبر التلجرام باستخدام قالب 10
2025-07-25 02:55:46,610 - INFO - معالجة callback: independent_count_um_10_lightning_200
2025-07-25 02:55:47,122 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-25 02:55:47,125 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-25 02:55:47,139 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-25 02:55:47,144 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-25 02:55:47,194 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-25 02:55:47,195 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-25 02:55:47,196 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-25 02:55:47,212 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-25 02:55:47,453 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-25 02:55:47,453 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-25 02:55:47,482 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-25 02:55:47,483 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-25 02:55:47,520 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-25 02:55:47,521 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-25 02:55:47,524 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-25 02:55:47,529 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-25 02:55:47,529 - INFO - ⚡ البرق: تم الحفاظ على العدد المحدد: 200
2025-07-25 02:55:47,531 - INFO - تم تطبيق إعدادات القالب '10' بنجاح
2025-07-25 02:55:47,531 - INFO - ⚡ استخدام البرق للتلجرام (مع جدولة مؤقتة وتنظيف تلقائي)
2025-07-25 02:55:47,531 - INFO - ⚡ بدء البرق التلقائي للتلجرام (مع جدولة مؤقتة وتنظيف تلقائي)
2025-07-25 02:55:47,532 - INFO - ⚡ البرق: استخدام العدد من التلجرام بوت: 200
2025-07-25 02:55:47,532 - INFO - ⚡ البرق للتلجرام: العدد المطلوب 200 حساب
2025-07-25 02:55:47,532 - INFO - ⚡ البرق: تم تحديث عدد الحسابات في الواجهة إلى: 200
2025-07-25 02:55:47,535 - INFO - ⚡ البرق للتلجرام: تم توليد 200 حساب
2025-07-25 02:55:47,535 - INFO - ⚡ البرق للتلجرام: تنفيذ متسلسل مجدول لـ 200 كارت (حد التقسيم: 100)
2025-07-25 02:55:47,537 - INFO - ⚡ البرق للتلجرام: بدء معالجة الأعداد الكبيرة مع جدولة واحدة
2025-07-25 02:55:47,537 - INFO - ⚡ البرق للتلجرام: تم تقسيم 200 كارت إلى 2 مجموعة للتنفيذ المتسلسل
2025-07-25 02:55:47,539 - INFO - ⚡ البرق للتلجرام: إنشاء السكريبت للمجموعة 1/2 (100 كارت)
2025-07-25 02:55:47,540 - INFO - ⚡ البرق للتلجرام: تم إعداد السكريبت telegram_lightning_batch1_user_manager_20250725_025547
2025-07-25 02:55:47,542 - INFO - ⚡ البرق للتلجرام: إنشاء السكريبت للمجموعة 2/2 (100 كارت)
2025-07-25 02:55:47,543 - INFO - ⚡ البرق للتلجرام: تم إعداد السكريبت telegram_lightning_batch2_user_manager_20250725_025547
2025-07-25 02:55:47,554 - INFO - استخدام الاتصال الحالي
2025-07-25 02:55:47,621 - INFO - ⚡ تم إرسال السكريبت المترابط: telegram_lightning_batch1_user_manager_20250725_025547
2025-07-25 02:55:47,663 - INFO - ⚡ تم إرسال السكريبت المترابط: telegram_lightning_batch2_user_manager_20250725_025547
2025-07-25 02:55:47,667 - INFO - ⚡ وقت MikroTik الحالي: 23:55:47
2025-07-25 02:55:47,667 - INFO - ⚡ السكريبت الأول سينفذ في: 23:55:50 (بعد 3 ثواني من وقت MikroTik)
2025-07-25 02:55:47,712 - INFO - ⚡ تم إنشاء الجدولة الرئيسية: telegram_main_schedule_20250725_025547 لتشغيل telegram_lightning_batch1_user_manager_20250725_025547 في 23:55:50
2025-07-25 02:55:47,715 - INFO - ⚡ تم إعداد البرق المترابط بنجاح - جدولة واحدة: telegram_main_schedule_20250725_025547, 2 سكريبت مترابط
2025-07-25 02:55:47,715 - INFO - ⚡ البرق للتلجرام مكتمل: تم إنشاء جدولة واحدة لـ 2 مجموعة متسلسلة (200 كارت)
2025-07-25 02:55:50,626 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 200
2025-07-25 02:55:50,647 - INFO - تم إنشاء PDF بنجاح: exports/كروت_(10)_ب20-200 كارت-25-07-2025-02-55-50-um.pdf
2025-07-25 02:55:50,651 - INFO - تم حفظ ملف .rsc الاحتياطي: exports/كروت_(10)_ب20-200 كارت-25-07-2025-02-55-50-um.rsc
2025-07-25 02:55:51,399 - INFO - تم إرسال الملف بنجاح: كروت_(10)_ب20-200 كارت-25-07-2025-02-55-50-um.pdf
2025-07-25 02:55:51,638 - INFO - تم إرسال 200 كرت عبر التلجرام باستخدام قالب 10
2025-07-25 03:10:04,499 - INFO - تم بدء تشغيل التطبيق
2025-07-25 03:10:04,595 - INFO - تم إنشاء المجلدات الأساسية
2025-07-25 03:10:04,668 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-25 03:10:04,668 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-25 03:10:04,980 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-25 03:10:06,816 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-25 03:10:06,817 - INFO - تم إعداد التطبيق بنجاح
2025-07-25 03:10:08,851 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-25 03:10:09,188 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-25 03:10:09,189 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-25 03:10:12,193 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-25 03:10:12,228 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-25 03:10:12,575 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-25 03:10:12,576 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-25 03:10:16,242 - INFO - معالجة callback: select_system_um
2025-07-25 03:10:16,734 - INFO - تم إرسال 2 قالب مفلتر لنظام User Manager
2025-07-25 03:10:18,007 - INFO - معالجة callback: independent_template_um_10
2025-07-25 03:10:18,487 - INFO - 🔄 تبديل النظام تلقائياً من None إلى user_manager
2025-07-25 03:10:18,487 - INFO - 🔄 طلب تبديل النظام من التلجرام: None → user_manager
2025-07-25 03:10:18,772 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-25 03:10:18,819 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-25 03:10:18,921 - WARNING - تحذير: فشل في حفظ الإعدادات: 'MikroTikCardGenerator' object has no attribute 'version_combo'
2025-07-25 03:10:18,922 - INFO - تم تعيين النظام الجديد: user_manager
2025-07-25 03:10:18,922 - INFO - 🔄 إعادة بناء الواجهة للنظام: user_manager
2025-07-25 03:10:18,923 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-25 03:10:18,930 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-25 03:10:19,519 - INFO - تم إعداد تبويب عرض الكروت لـ User Manager بنجاح
2025-07-25 03:10:19,714 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-25 03:10:19,715 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-25 03:10:19,882 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام user_manager
2025-07-25 03:10:19,883 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-25 03:10:19,984 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager
2025-07-25 03:10:19,989 - INFO - تم تسجيل العملية: تبديل النظام من التلجرام - تم تبديل النظام من None إلى user_manager
2025-07-25 03:10:19,995 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-25 03:10:19,998 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-25 03:10:19,998 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-25 03:10:20,303 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-25 03:10:20,304 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-25 03:10:20,342 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-25 03:10:20,342 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-25 03:10:20,345 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-25 03:10:20,350 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-25 03:10:20,364 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-25 03:10:20,366 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager - القالب: 10
2025-07-25 03:10:20,665 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-25 03:10:23,659 - INFO - معالجة callback: independent_create_um_10_lightning
2025-07-25 03:10:23,869 - INFO - 🔍 بدء اختبار جلب العدد الحالي للكروت قبل إرسال الإشعار
2025-07-25 03:10:23,870 - INFO - 🧪 بدء اختبار الاتصال وجلب عدد User Manager
2025-07-25 03:10:23,870 - INFO - محاولة الاتصال بـ 6.6.6.100:8728 (عادي)
2025-07-25 03:10:23,932 - INFO - نجح الاتصال مع 6.6.6.100
2025-07-25 03:10:23,933 - INFO - ✅ نجح الاتصال - اختبار جلب العدد...
2025-07-25 03:10:23,972 - INFO - 🏷️ اسم النظام: MikroTik
2025-07-25 03:10:24,015 - INFO - 📋 إصدار RouterOS: 6.43.12 (stable)
2025-07-25 03:10:24,024 - INFO - 📊 اختبار جلب مستخدمي User Manager...
2025-07-25 03:10:29,210 - INFO - ✅ تم جلب 2859 مستخدم من User Manager
2025-07-25 03:10:29,211 - INFO - 👥 عرض أول 5 مستخدمين:
2025-07-25 03:10:29,212 - INFO -   1. غير محدد (البروفايل: غير محدد)
2025-07-25 03:10:29,213 - INFO -   2. غير محدد (البروفايل: غير محدد)
2025-07-25 03:10:29,213 - INFO -   3. غير محدد (البروفايل: غير محدد)
2025-07-25 03:10:29,214 - INFO -   4. غير محدد (البروفايل: غير محدد)
2025-07-25 03:10:29,214 - INFO -   5. غير محدد (البروفايل: غير محدد)
2025-07-25 03:10:29,215 - ERROR - ❌ خطأ في API: 'RouterOsApi' object has no attribute 'disconnect'
2025-07-25 03:10:29,217 - INFO - 📊 نتيجة الاختبار: خطأ في API: 'RouterOsApi' object has no attribute 'disconnect'
2025-07-25 03:10:29,217 - INFO - 🔔 بدء إرسال إشعار اختيار القالب: 10 (User Manager)
2025-07-25 03:10:29,218 - INFO - 📊 جلب العدد الحالي من User Manager...
2025-07-25 03:10:29,218 - INFO - 🔍 بدء جلب العدد الحالي للكروت في User Manager
2025-07-25 03:10:29,218 - INFO - ✅ مكتبة routeros_api متوفرة
2025-07-25 03:10:29,220 - INFO - 🔗 محاولة الاتصال بـ 6.6.6.100 باستخدام المستخدم: 11
2025-07-25 03:10:29,224 - INFO - استخدام الاتصال الحالي
2025-07-25 03:10:29,226 - INFO - ✅ تم الاتصال بـ MikroTik بنجاح
2025-07-25 03:10:29,238 - INFO - 📊 جلب قائمة المستخدمين من User Manager...
2025-07-25 03:10:34,414 - INFO - ✅ تم جلب 2859 مستخدم من User Manager بنجاح
2025-07-25 03:10:34,414 - INFO - 👤 مستخدم 1: غير محدد
2025-07-25 03:10:34,415 - INFO - 👤 مستخدم 2: غير محدد
2025-07-25 03:10:34,415 - INFO - 👤 مستخدم 3: غير محدد
2025-07-25 03:10:34,417 - INFO - 📈 العدد المجلوب من User Manager: 2859
2025-07-25 03:10:34,664 - INFO - ✅ تم إرسال إشعار اختيار القالب '10' - العدد الحالي: 2859
2025-07-25 03:10:37,240 - INFO - معالجة callback: independent_count_um_10_lightning_200
2025-07-25 03:10:37,729 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-25 03:10:37,729 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-25 03:10:37,744 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-25 03:10:37,744 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-25 03:10:37,783 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-25 03:10:37,784 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-25 03:10:37,787 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-25 03:10:37,800 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-25 03:10:38,039 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-25 03:10:38,039 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-25 03:10:38,052 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-25 03:10:38,053 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-25 03:10:38,100 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-25 03:10:38,102 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-25 03:10:38,106 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-25 03:10:38,110 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-25 03:10:38,110 - INFO - ⚡ البرق: تم الحفاظ على العدد المحدد: 200
2025-07-25 03:10:38,111 - INFO - تم تطبيق إعدادات القالب '10' بنجاح
2025-07-25 03:10:38,111 - INFO - ⚡ استخدام البرق للتلجرام (مع جدولة مؤقتة وتنظيف تلقائي)
2025-07-25 03:10:38,112 - INFO - ⚡ بدء البرق التلقائي للتلجرام (مع جدولة مؤقتة وتنظيف تلقائي)
2025-07-25 03:10:38,113 - INFO - ⚡ البرق: استخدام العدد من التلجرام بوت: 200
2025-07-25 03:10:38,114 - INFO - ⚡ البرق للتلجرام: العدد المطلوب 200 حساب
2025-07-25 03:10:38,114 - INFO - ⚡ البرق: تم تحديث عدد الحسابات في الواجهة إلى: 200
2025-07-25 03:10:38,117 - INFO - ⚡ البرق للتلجرام: تم توليد 200 حساب
2025-07-25 03:10:38,118 - INFO - ⚡ البرق للتلجرام: تنفيذ متسلسل مجدول لـ 200 كارت (حد التقسيم: 100)
2025-07-25 03:10:38,119 - INFO - ⚡ البرق للتلجرام: بدء معالجة الأعداد الكبيرة مع جدولة واحدة
2025-07-25 03:10:38,119 - INFO - ⚡ البرق للتلجرام: تم تقسيم 200 كارت إلى 2 مجموعة للتنفيذ المتسلسل
2025-07-25 03:10:38,119 - INFO - ⚡ البرق للتلجرام: إنشاء السكريبت للمجموعة 1/2 (100 كارت)
2025-07-25 03:10:38,120 - INFO - ⚡ البرق للتلجرام: تم إعداد السكريبت telegram_lightning_batch1_user_manager_20250725_031038
2025-07-25 03:10:38,121 - INFO - ⚡ البرق للتلجرام: إنشاء السكريبت للمجموعة 2/2 (100 كارت)
2025-07-25 03:10:38,121 - INFO - ⚡ البرق للتلجرام: تم إعداد السكريبت telegram_lightning_batch2_user_manager_20250725_031038
2025-07-25 03:10:38,125 - INFO - استخدام الاتصال الحالي
2025-07-25 03:10:38,209 - INFO - ⚡ تم إرسال السكريبت المترابط: telegram_lightning_batch1_user_manager_20250725_031038
2025-07-25 03:10:38,251 - INFO - ⚡ تم إرسال السكريبت المترابط: telegram_lightning_batch2_user_manager_20250725_031038
2025-07-25 03:10:38,292 - INFO - ⚡ وقت MikroTik الحالي: 00:10:37
2025-07-25 03:10:38,309 - INFO - ⚡ تم تعديل وقت التنفيذ لليوم التالي: 00:10:40
2025-07-25 03:10:38,309 - INFO - ⚡ السكريبت الأول سينفذ في: 00:10:40 (بعد 3 ثواني من وقت MikroTik)
2025-07-25 03:10:38,354 - INFO - ⚡ تم إنشاء الجدولة الرئيسية: telegram_main_schedule_20250725_031038 لتشغيل telegram_lightning_batch1_user_manager_20250725_031038 في 00:10:40
2025-07-25 03:10:38,365 - INFO - ⚡ تم إعداد البرق المترابط بنجاح - جدولة واحدة: telegram_main_schedule_20250725_031038, 2 سكريبت مترابط
2025-07-25 03:10:38,366 - INFO - ⚡ البرق للتلجرام مكتمل: تم إنشاء جدولة واحدة لـ 2 مجموعة متسلسلة (200 كارت)
2025-07-25 03:10:41,297 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 200
2025-07-25 03:10:41,327 - INFO - تم إنشاء PDF بنجاح: exports/كروت_(10)_ب20-200 كارت-25-07-2025-03-10-41-um.pdf
2025-07-25 03:10:41,351 - INFO - تم حفظ ملف .rsc الاحتياطي: exports/كروت_(10)_ب20-200 كارت-25-07-2025-03-10-41-um.rsc
2025-07-25 03:10:41,946 - INFO - تم إرسال الملف بنجاح: كروت_(10)_ب20-200 كارت-25-07-2025-03-10-41-um.pdf
2025-07-25 03:10:42,184 - INFO - تم إرسال 200 كرت عبر التلجرام باستخدام قالب 10
2025-07-25 03:23:19,500 - INFO - تم بدء تشغيل التطبيق
2025-07-25 03:23:19,585 - INFO - تم إنشاء المجلدات الأساسية
2025-07-25 03:23:19,668 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-25 03:23:19,669 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-25 03:23:20,056 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-25 03:23:22,321 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-25 03:23:22,321 - INFO - تم إعداد التطبيق بنجاح
2025-07-25 03:23:24,351 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-25 03:23:24,714 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-25 03:23:24,723 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-25 03:23:27,734 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-25 03:23:27,769 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-25 03:23:28,044 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-25 03:23:28,045 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-25 03:23:29,589 - INFO - معالجة callback: select_system_um
2025-07-25 03:23:30,039 - INFO - تم إرسال 2 قالب مفلتر لنظام User Manager
2025-07-25 03:23:32,479 - INFO - معالجة callback: independent_template_um_10
2025-07-25 03:23:32,939 - INFO - 🔄 تبديل النظام تلقائياً من None إلى user_manager
2025-07-25 03:23:32,940 - INFO - 🔄 طلب تبديل النظام من التلجرام: None → user_manager
2025-07-25 03:23:33,188 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-25 03:23:33,271 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-25 03:23:33,372 - WARNING - تحذير: فشل في حفظ الإعدادات: 'MikroTikCardGenerator' object has no attribute 'version_combo'
2025-07-25 03:23:33,372 - INFO - تم تعيين النظام الجديد: user_manager
2025-07-25 03:23:33,373 - INFO - 🔄 إعادة بناء الواجهة للنظام: user_manager
2025-07-25 03:23:33,374 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-25 03:23:33,379 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-25 03:23:33,996 - INFO - تم إعداد تبويب عرض الكروت لـ User Manager بنجاح
2025-07-25 03:23:34,174 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-25 03:23:34,175 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-25 03:23:34,326 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام user_manager
2025-07-25 03:23:34,326 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-25 03:23:34,423 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager
2025-07-25 03:23:34,431 - INFO - تم تسجيل العملية: تبديل النظام من التلجرام - تم تبديل النظام من None إلى user_manager
2025-07-25 03:23:34,438 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-25 03:23:34,440 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-25 03:23:34,440 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-25 03:23:34,784 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-25 03:23:34,785 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-25 03:23:34,823 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-25 03:23:34,823 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-25 03:23:34,827 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-25 03:23:34,831 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-25 03:23:34,835 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-25 03:23:34,836 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager - القالب: 10
2025-07-25 03:23:35,090 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-25 03:23:37,945 - INFO - معالجة callback: independent_create_um_10_lightning
2025-07-25 03:23:38,158 - INFO - 🔍 بدء اختبار جلب العدد الحالي للكروت قبل إرسال الإشعار
2025-07-25 03:23:38,159 - INFO - 🧪 بدء اختبار الاتصال وجلب عدد User Manager
2025-07-25 03:23:38,159 - INFO - محاولة الاتصال بـ 6.6.6.100:8728 (عادي)
2025-07-25 03:23:38,224 - INFO - نجح الاتصال مع 6.6.6.100
2025-07-25 03:23:38,225 - INFO - ✅ نجح الاتصال - اختبار جلب العدد...
2025-07-25 03:23:38,263 - INFO - 🏷️ اسم النظام: MikroTik
2025-07-25 03:23:38,307 - INFO - 📋 إصدار RouterOS: 6.43.12 (stable)
2025-07-25 03:23:38,307 - INFO - 📊 اختبار جلب مستخدمي User Manager...
2025-07-25 03:23:43,901 - INFO - ✅ تم جلب 3059 مستخدم من User Manager
2025-07-25 03:23:43,902 - INFO - 👥 عرض أول 5 مستخدمين:
2025-07-25 03:23:43,902 - INFO -   1. غير محدد (البروفايل: غير محدد)
2025-07-25 03:23:43,903 - INFO -   2. غير محدد (البروفايل: غير محدد)
2025-07-25 03:23:43,903 - INFO -   3. غير محدد (البروفايل: غير محدد)
2025-07-25 03:23:43,903 - INFO -   4. غير محدد (البروفايل: غير محدد)
2025-07-25 03:23:43,904 - INFO -   5. غير محدد (البروفايل: غير محدد)
2025-07-25 03:23:43,904 - ERROR - ❌ خطأ في API: 'RouterOsApi' object has no attribute 'disconnect'
2025-07-25 03:23:43,907 - INFO - 📊 نتيجة الاختبار: خطأ في API: 'RouterOsApi' object has no attribute 'disconnect'
2025-07-25 03:23:43,907 - INFO - 🔔 بدء إرسال إشعار اختيار القالب: 10 (User Manager)
2025-07-25 03:23:43,930 - INFO - 📊 جلب العدد الحالي من User Manager...
2025-07-25 03:23:43,933 - INFO - 🔍 بدء جلب العدد الحالي للكروت في User Manager
2025-07-25 03:23:43,934 - INFO - ✅ مكتبة routeros_api متوفرة
2025-07-25 03:23:43,934 - INFO - 🔗 محاولة الاتصال بـ 6.6.6.100 باستخدام المستخدم: 11
2025-07-25 03:23:43,938 - INFO - استخدام الاتصال الحالي
2025-07-25 03:23:43,938 - INFO - ✅ تم الاتصال بـ MikroTik بنجاح
2025-07-25 03:23:43,939 - INFO - 📊 جلب قائمة المستخدمين من User Manager...
2025-07-25 03:23:49,509 - INFO - ✅ تم جلب 3059 مستخدم من User Manager بنجاح
2025-07-25 03:23:49,510 - INFO - 👤 مستخدم 1: غير محدد
2025-07-25 03:23:49,510 - INFO - 👤 مستخدم 2: غير محدد
2025-07-25 03:23:49,511 - INFO - 👤 مستخدم 3: غير محدد
2025-07-25 03:23:49,513 - INFO - 📈 العدد المجلوب من User Manager: 3059
2025-07-25 03:23:49,822 - INFO - ✅ تم إرسال إشعار اختيار القالب '10' - العدد الحالي: 3059
2025-07-25 03:23:53,639 - INFO - معالجة callback: independent_count_um_10_lightning_200
2025-07-25 03:23:54,090 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-25 03:23:54,091 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-25 03:23:54,106 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-25 03:23:54,117 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-25 03:23:54,145 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-25 03:23:54,145 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-25 03:23:54,149 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-25 03:23:54,153 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-25 03:23:54,387 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-25 03:23:54,387 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-25 03:23:54,424 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-25 03:23:54,424 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-25 03:23:54,461 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-25 03:23:54,461 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-25 03:23:54,464 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-25 03:23:54,468 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-25 03:23:54,469 - INFO - ⚡ البرق: تم الحفاظ على العدد المحدد: 200
2025-07-25 03:23:54,471 - INFO - تم تطبيق إعدادات القالب '10' بنجاح
2025-07-25 03:23:54,471 - INFO - ⚡ استخدام البرق للتلجرام (مع جدولة مؤقتة وتنظيف تلقائي)
2025-07-25 03:23:54,471 - INFO - ⚡ بدء البرق التلقائي للتلجرام (مع جدولة مؤقتة وتنظيف تلقائي)
2025-07-25 03:23:54,472 - INFO - ⚡ البرق: استخدام العدد من التلجرام بوت: 200
2025-07-25 03:23:54,472 - INFO - ⚡ البرق للتلجرام: العدد المطلوب 200 حساب
2025-07-25 03:23:54,472 - INFO - ⚡ البرق: تم تحديث عدد الحسابات في الواجهة إلى: 200
2025-07-25 03:23:54,475 - INFO - ⚡ البرق للتلجرام: تم توليد 200 حساب
2025-07-25 03:23:54,477 - INFO - ⚡ البرق للتلجرام: تنفيذ متسلسل مجدول لـ 200 كارت (حد التقسيم: 100)
2025-07-25 03:23:54,477 - INFO - ⚡ البرق للتلجرام: بدء معالجة الأعداد الكبيرة مع جدولة واحدة
2025-07-25 03:23:54,478 - INFO - ⚡ البرق للتلجرام: تم تقسيم 200 كارت إلى 2 مجموعة للتنفيذ المتسلسل
2025-07-25 03:23:54,480 - INFO - ⚡ البرق للتلجرام: إنشاء السكريبت للمجموعة 1/2 (100 كارت)
2025-07-25 03:23:54,482 - INFO - ⚡ البرق للتلجرام: تم إعداد السكريبت telegram_lightning_batch1_user_manager_20250725_032354
2025-07-25 03:23:54,484 - INFO - ⚡ البرق للتلجرام: إنشاء السكريبت للمجموعة 2/2 (100 كارت)
2025-07-25 03:23:54,497 - INFO - ⚡ البرق للتلجرام: تم إعداد السكريبت telegram_lightning_batch2_user_manager_20250725_032354
2025-07-25 03:23:54,500 - INFO - استخدام الاتصال الحالي
2025-07-25 03:23:54,575 - INFO - ⚡ تم إرسال السكريبت المترابط: telegram_lightning_batch1_user_manager_20250725_032354
2025-07-25 03:23:54,616 - INFO - ⚡ تم إرسال السكريبت المترابط: telegram_lightning_batch2_user_manager_20250725_032354
2025-07-25 03:23:54,619 - INFO - ⚡ وقت MikroTik الحالي: 00:23:54
2025-07-25 03:23:54,634 - INFO - ⚡ تم تعديل وقت التنفيذ لليوم التالي: 00:23:57
2025-07-25 03:23:54,646 - INFO - ⚡ السكريبت الأول سينفذ في: 00:23:57 (بعد 3 ثواني من وقت MikroTik)
2025-07-25 03:23:54,686 - INFO - ⚡ تم إنشاء الجدولة الرئيسية: telegram_main_schedule_20250725_032354 لتشغيل telegram_lightning_batch1_user_manager_20250725_032354 في 00:23:57
2025-07-25 03:23:54,689 - INFO - ⚡ تم إعداد البرق المترابط بنجاح - جدولة واحدة: telegram_main_schedule_20250725_032354, 2 سكريبت مترابط
2025-07-25 03:23:54,690 - INFO - ⚡ البرق للتلجرام مكتمل: تم إنشاء جدولة واحدة لـ 2 مجموعة متسلسلة (200 كارت)
2025-07-25 03:23:57,663 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 200
2025-07-25 03:23:57,725 - INFO - تم إنشاء PDF بنجاح: exports/كروت_(10)_ب20-200 كارت-25-07-2025-03-23-57-um.pdf
2025-07-25 03:23:57,728 - INFO - تم حفظ ملف .rsc الاحتياطي: exports/كروت_(10)_ب20-200 كارت-25-07-2025-03-23-57-um.rsc
2025-07-25 03:23:59,137 - INFO - تم إرسال الملف بنجاح: كروت_(10)_ب20-200 كارت-25-07-2025-03-23-57-um.pdf
2025-07-25 03:23:59,369 - INFO - تم إرسال 200 كرت عبر التلجرام باستخدام قالب 10
2025-07-25 03:35:05,948 - INFO - تم بدء تشغيل التطبيق
2025-07-25 03:35:06,022 - INFO - تم إنشاء المجلدات الأساسية
2025-07-25 03:35:06,394 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-25 03:35:06,394 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-25 03:35:07,206 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-25 03:35:09,956 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-25 03:35:09,957 - INFO - تم إعداد التطبيق بنجاح
2025-07-25 03:35:12,027 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-25 03:35:12,598 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-25 03:35:12,599 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-25 03:35:15,599 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-25 03:35:15,635 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-25 03:35:15,899 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-25 03:35:15,899 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-25 03:36:05,782 - INFO - معالجة callback: select_system_um
2025-07-25 03:36:06,802 - INFO - تم إرسال 2 قالب مفلتر لنظام User Manager
2025-07-25 03:36:09,101 - INFO - معالجة callback: independent_template_um_10
2025-07-25 03:36:09,593 - INFO - 🔄 تبديل النظام تلقائياً من None إلى user_manager
2025-07-25 03:36:09,593 - INFO - 🔄 طلب تبديل النظام من التلجرام: None → user_manager
2025-07-25 03:36:09,979 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-25 03:36:10,053 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-25 03:36:10,154 - WARNING - تحذير: فشل في حفظ الإعدادات: 'MikroTikCardGenerator' object has no attribute 'version_combo'
2025-07-25 03:36:10,155 - INFO - تم تعيين النظام الجديد: user_manager
2025-07-25 03:36:10,155 - INFO - 🔄 إعادة بناء الواجهة للنظام: user_manager
2025-07-25 03:36:10,156 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-25 03:36:10,159 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-25 03:36:10,833 - INFO - تم إعداد تبويب عرض الكروت لـ User Manager بنجاح
2025-07-25 03:36:11,033 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-25 03:36:11,034 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-25 03:36:11,194 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام user_manager
2025-07-25 03:36:11,195 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-25 03:36:11,280 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager
2025-07-25 03:36:11,338 - INFO - تم تسجيل العملية: تبديل النظام من التلجرام - تم تبديل النظام من None إلى user_manager
2025-07-25 03:36:11,340 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-25 03:36:11,341 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-25 03:36:11,341 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-25 03:36:11,705 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-25 03:36:11,707 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-25 03:36:11,745 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-25 03:36:11,746 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-25 03:36:11,749 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-25 03:36:11,753 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-25 03:36:11,757 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-25 03:36:11,762 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager - القالب: 10
2025-07-25 03:36:12,072 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-25 03:36:16,229 - INFO - معالجة callback: independent_create_um_10_lightning
2025-07-25 03:36:16,445 - INFO - 🔍 بدء اختبار جلب العدد الحالي للكروت قبل إرسال الإشعار
2025-07-25 03:36:16,445 - INFO - 🧪 بدء اختبار الاتصال وجلب عدد User Manager
2025-07-25 03:36:16,446 - INFO - محاولة الاتصال بـ 6.6.6.100:8728 (عادي)
2025-07-25 03:36:16,514 - INFO - نجح الاتصال مع 6.6.6.100
2025-07-25 03:36:16,514 - INFO - ✅ نجح الاتصال - اختبار جلب العدد...
2025-07-25 03:36:16,554 - INFO - 🏷️ اسم النظام: MikroTik
2025-07-25 03:36:16,597 - INFO - 📋 إصدار RouterOS: 6.43.12 (stable)
2025-07-25 03:36:16,598 - INFO - 📊 اختبار جلب مستخدمي User Manager...
2025-07-25 03:36:22,514 - INFO - ✅ تم جلب 3259 مستخدم من User Manager
2025-07-25 03:36:22,514 - INFO - 👥 عرض أول 5 مستخدمين:
2025-07-25 03:36:22,515 - INFO -   1. غير محدد (البروفايل: غير محدد)
2025-07-25 03:36:22,515 - INFO -   2. غير محدد (البروفايل: غير محدد)
2025-07-25 03:36:22,515 - INFO -   3. غير محدد (البروفايل: غير محدد)
2025-07-25 03:36:22,516 - INFO -   4. غير محدد (البروفايل: غير محدد)
2025-07-25 03:36:22,516 - INFO -   5. غير محدد (البروفايل: غير محدد)
2025-07-25 03:36:22,516 - ERROR - ❌ خطأ في API: 'RouterOsApi' object has no attribute 'disconnect'
2025-07-25 03:36:22,519 - INFO - 📊 نتيجة الاختبار: خطأ في API: 'RouterOsApi' object has no attribute 'disconnect'
2025-07-25 03:36:22,519 - INFO - 🔔 بدء إرسال إشعار اختيار القالب: 10 (User Manager)
2025-07-25 03:36:22,520 - INFO - 📊 جلب العدد الحالي من User Manager...
2025-07-25 03:36:22,521 - INFO - 🔍 بدء جلب العدد الحالي للكروت في User Manager
2025-07-25 03:36:22,522 - INFO - ✅ مكتبة routeros_api متوفرة
2025-07-25 03:36:22,522 - INFO - 🔗 محاولة الاتصال بـ 6.6.6.100 باستخدام المستخدم: 11
2025-07-25 03:36:22,526 - INFO - استخدام الاتصال الحالي
2025-07-25 03:36:22,526 - INFO - ✅ تم الاتصال بـ MikroTik بنجاح
2025-07-25 03:36:22,528 - INFO - 📊 جلب قائمة المستخدمين من User Manager...
2025-07-25 03:36:28,454 - INFO - ✅ تم جلب 3259 مستخدم من User Manager بنجاح
2025-07-25 03:36:28,454 - INFO - 👤 مستخدم 1: غير محدد
2025-07-25 03:36:28,454 - INFO - 👤 مستخدم 2: غير محدد
2025-07-25 03:36:28,455 - INFO - 👤 مستخدم 3: غير محدد
2025-07-25 03:36:28,457 - INFO - 📈 العدد المجلوب من User Manager: 3259
2025-07-25 03:36:28,804 - INFO - ✅ تم إرسال إشعار اختيار القالب '10' - العدد الحالي: 3259
2025-07-25 03:36:34,106 - INFO - معالجة callback: independent_count_um_10_lightning_200
2025-07-25 03:36:35,296 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-25 03:36:35,297 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-25 03:36:35,314 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-25 03:36:35,314 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-25 03:36:35,353 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-25 03:36:35,354 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-25 03:36:35,357 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-25 03:36:35,361 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-25 03:36:36,004 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-25 03:36:36,004 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-25 03:36:36,021 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-25 03:36:36,021 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-25 03:36:36,054 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-25 03:36:36,055 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-25 03:36:36,058 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-25 03:36:36,062 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-25 03:36:36,065 - INFO - ⚡ البرق: تم الحفاظ على العدد المحدد: 200
2025-07-25 03:36:36,066 - INFO - تم تطبيق إعدادات القالب '10' بنجاح
2025-07-25 03:36:36,066 - INFO - ⚡ استخدام البرق للتلجرام (مع جدولة مؤقتة وتنظيف تلقائي)
2025-07-25 03:36:36,066 - INFO - ⚡ بدء البرق التلقائي للتلجرام (مع جدولة مؤقتة وتنظيف تلقائي)
2025-07-25 03:36:36,067 - INFO - ⚡ البرق: استخدام العدد من التلجرام بوت: 200
2025-07-25 03:36:36,067 - INFO - ⚡ البرق للتلجرام: العدد المطلوب 200 حساب
2025-07-25 03:36:36,067 - INFO - ⚡ البرق: تم تحديث عدد الحسابات في الواجهة إلى: 200
2025-07-25 03:36:36,070 - INFO - ⚡ البرق للتلجرام: تم توليد 200 حساب
2025-07-25 03:36:36,072 - INFO - ⚡ البرق للتلجرام: تنفيذ متسلسل مجدول لـ 200 كارت (حد التقسيم: 100)
2025-07-25 03:36:36,073 - INFO - ⚡ البرق للتلجرام: بدء معالجة الأعداد الكبيرة مع جدولة واحدة
2025-07-25 03:36:36,075 - INFO - ⚡ البرق للتلجرام: تم تقسيم 200 كارت إلى 2 مجموعة للتنفيذ المتسلسل
2025-07-25 03:36:36,085 - INFO - ⚡ البرق للتلجرام: إنشاء السكريبت للمجموعة 1/2 (100 كارت)
2025-07-25 03:36:36,085 - INFO - ⚡ البرق للتلجرام: تم إعداد السكريبت telegram_lightning_batch1_user_manager_20250725_033636
2025-07-25 03:36:36,086 - INFO - ⚡ البرق للتلجرام: إنشاء السكريبت للمجموعة 2/2 (100 كارت)
2025-07-25 03:36:36,086 - INFO - ⚡ البرق للتلجرام: تم إعداد السكريبت telegram_lightning_batch2_user_manager_20250725_033636
2025-07-25 03:36:36,089 - INFO - استخدام الاتصال الحالي
2025-07-25 03:36:36,168 - INFO - ⚡ تم إرسال السكريبت المترابط: telegram_lightning_batch1_user_manager_20250725_033636
2025-07-25 03:36:36,212 - INFO - ⚡ تم إرسال السكريبت المترابط: telegram_lightning_batch2_user_manager_20250725_033636
2025-07-25 03:36:36,256 - INFO - ⚡ وقت MikroTik الحالي: 00:36:35
2025-07-25 03:36:36,290 - INFO - ⚡ تم تعديل وقت التنفيذ لليوم التالي: 00:36:38
2025-07-25 03:36:36,291 - INFO - ⚡ السكريبت الأول سينفذ في: 00:36:38 (بعد 3 ثواني من وقت MikroTik)
2025-07-25 03:36:36,346 - INFO - ⚡ تم إنشاء الجدولة الرئيسية: telegram_main_schedule_20250725_033636 لتشغيل telegram_lightning_batch1_user_manager_20250725_033636 في 00:36:38
2025-07-25 03:36:36,350 - INFO - ⚡ تم إعداد البرق المترابط بنجاح - جدولة واحدة: telegram_main_schedule_20250725_033636, 2 سكريبت مترابط
2025-07-25 03:36:36,355 - INFO - ⚡ البرق للتلجرام مكتمل: تم إنشاء جدولة واحدة لـ 2 مجموعة متسلسلة (200 كارت)
2025-07-25 03:36:39,246 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 200
2025-07-25 03:36:39,301 - INFO - تم إنشاء PDF بنجاح: exports/كروت_(10)_ب20-200 كارت-25-07-2025-03-36-39-um.pdf
2025-07-25 03:36:39,303 - INFO - تم حفظ ملف .rsc الاحتياطي: exports/كروت_(10)_ب20-200 كارت-25-07-2025-03-36-39-um.rsc
2025-07-25 03:36:40,689 - INFO - تم إرسال الملف بنجاح: كروت_(10)_ب20-200 كارت-25-07-2025-03-36-39-um.pdf
2025-07-25 03:36:40,989 - INFO - تم إرسال 200 كرت عبر التلجرام باستخدام قالب 10
2025-07-25 03:58:47,652 - INFO - تم بدء تشغيل التطبيق
2025-07-25 03:58:47,733 - INFO - تم إنشاء المجلدات الأساسية
2025-07-25 03:58:48,370 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-25 03:58:48,370 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-25 03:58:48,875 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-25 03:58:50,522 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-25 03:58:50,523 - INFO - تم إعداد التطبيق بنجاح
2025-07-25 03:58:52,545 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-25 03:58:52,916 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-25 03:58:52,917 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-25 03:58:55,924 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-25 03:58:55,990 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-25 03:58:56,260 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-25 03:58:56,261 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-25 03:59:00,542 - INFO - معالجة callback: select_system_um
2025-07-25 03:59:01,142 - INFO - تم إرسال 2 قالب مفلتر لنظام User Manager
2025-07-25 03:59:02,405 - INFO - معالجة callback: independent_template_um_10
2025-07-25 03:59:02,867 - INFO - 🔄 تبديل النظام تلقائياً من None إلى user_manager
2025-07-25 03:59:02,868 - INFO - 🔄 طلب تبديل النظام من التلجرام: None → user_manager
2025-07-25 03:59:03,112 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-25 03:59:03,177 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-25 03:59:03,278 - WARNING - تحذير: فشل في حفظ الإعدادات: 'MikroTikCardGenerator' object has no attribute 'version_combo'
2025-07-25 03:59:03,279 - INFO - تم تعيين النظام الجديد: user_manager
2025-07-25 03:59:03,280 - INFO - 🔄 إعادة بناء الواجهة للنظام: user_manager
2025-07-25 03:59:03,281 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-25 03:59:03,283 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-25 03:59:03,881 - INFO - تم إعداد تبويب عرض الكروت لـ User Manager بنجاح
2025-07-25 03:59:04,072 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-25 03:59:04,072 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-25 03:59:04,228 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام user_manager
2025-07-25 03:59:04,229 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-25 03:59:04,322 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager
2025-07-25 03:59:04,338 - INFO - تم تسجيل العملية: تبديل النظام من التلجرام - تم تبديل النظام من None إلى user_manager
2025-07-25 03:59:04,341 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-25 03:59:04,342 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-25 03:59:04,343 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-25 03:59:04,710 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-25 03:59:04,710 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-25 03:59:04,747 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-25 03:59:04,747 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-25 03:59:04,750 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-25 03:59:04,754 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-25 03:59:04,757 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-25 03:59:04,769 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager - القالب: 10
2025-07-25 03:59:05,022 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-25 03:59:09,107 - INFO - معالجة callback: independent_create_um_10_lightning
2025-07-25 03:59:09,335 - INFO - 🔍 بدء اختبار جلب العدد الحالي للكروت قبل إرسال الإشعار
2025-07-25 03:59:09,335 - INFO - 🧪 بدء اختبار الاتصال وجلب عدد User Manager
2025-07-25 03:59:09,336 - INFO - محاولة الاتصال بـ 6.6.6.100:8728 (عادي)
2025-07-25 03:59:09,403 - INFO - نجح الاتصال مع 6.6.6.100
2025-07-25 03:59:09,404 - INFO - ✅ نجح الاتصال - اختبار جلب العدد...
2025-07-25 03:59:09,442 - INFO - 🏷️ اسم النظام: MikroTik
2025-07-25 03:59:09,487 - INFO - 📋 إصدار RouterOS: 6.43.12 (stable)
2025-07-25 03:59:09,496 - INFO - 📊 اختبار جلب مستخدمي User Manager...
2025-07-25 03:59:15,780 - INFO - ✅ تم جلب 3459 مستخدم من User Manager
2025-07-25 03:59:15,782 - INFO - 👥 عرض أول 5 مستخدمين:
2025-07-25 03:59:15,782 - INFO -   1. غير محدد (البروفايل: غير محدد)
2025-07-25 03:59:15,783 - INFO -   2. غير محدد (البروفايل: غير محدد)
2025-07-25 03:59:15,783 - INFO -   3. غير محدد (البروفايل: غير محدد)
2025-07-25 03:59:15,783 - INFO -   4. غير محدد (البروفايل: غير محدد)
2025-07-25 03:59:15,784 - INFO -   5. غير محدد (البروفايل: غير محدد)
2025-07-25 03:59:15,784 - ERROR - ❌ خطأ في API: 'RouterOsApi' object has no attribute 'disconnect'
2025-07-25 03:59:15,786 - INFO - 📊 نتيجة الاختبار: خطأ في API: 'RouterOsApi' object has no attribute 'disconnect'
2025-07-25 03:59:15,787 - INFO - 🔔 بدء إرسال إشعار اختيار القالب: 10 (User Manager)
2025-07-25 03:59:15,788 - INFO - 📊 جلب العدد الحالي من User Manager...
2025-07-25 03:59:15,789 - INFO - 🔍 بدء جلب العدد الحالي للكروت في User Manager
2025-07-25 03:59:15,790 - INFO - ✅ مكتبة routeros_api متوفرة
2025-07-25 03:59:15,791 - INFO - 🔗 محاولة الاتصال بـ 6.6.6.100 باستخدام المستخدم: 11
2025-07-25 03:59:15,796 - INFO - استخدام الاتصال الحالي
2025-07-25 03:59:15,808 - INFO - ✅ تم الاتصال بـ MikroTik بنجاح
2025-07-25 03:59:15,809 - INFO - 📊 جلب قائمة المستخدمين من User Manager...
2025-07-25 03:59:22,082 - INFO - ✅ تم جلب 3459 مستخدم من User Manager بنجاح
2025-07-25 03:59:22,083 - INFO - 👤 مستخدم 1: غير محدد
2025-07-25 03:59:22,083 - INFO - 👤 مستخدم 2: غير محدد
2025-07-25 03:59:22,084 - INFO - 👤 مستخدم 3: غير محدد
2025-07-25 03:59:22,086 - INFO - 📈 العدد المجلوب من User Manager: 3459
2025-07-25 03:59:22,325 - INFO - ✅ تم إرسال إشعار اختيار القالب '10' - العدد الحالي: 3459
2025-07-25 03:59:26,059 - INFO - معالجة callback: independent_count_um_10_lightning_200
2025-07-25 03:59:26,536 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-25 03:59:26,536 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-25 03:59:26,551 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-25 03:59:26,551 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-25 03:59:26,599 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-25 03:59:26,600 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-25 03:59:26,605 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-25 03:59:26,609 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-25 03:59:26,861 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-25 03:59:26,862 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-25 03:59:26,887 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-25 03:59:26,887 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-25 03:59:26,915 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-25 03:59:26,925 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-25 03:59:26,929 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-25 03:59:26,933 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-25 03:59:26,933 - INFO - ⚡ البرق: تم الحفاظ على العدد المحدد: 200
2025-07-25 03:59:26,934 - INFO - تم تطبيق إعدادات القالب '10' بنجاح
2025-07-25 03:59:26,935 - INFO - ⚡ استخدام البرق للتلجرام (مع جدولة مؤقتة وتنظيف تلقائي)
2025-07-25 03:59:26,935 - INFO - ⚡ بدء البرق التلقائي للتلجرام (مع جدولة مؤقتة وتنظيف تلقائي)
2025-07-25 03:59:26,936 - INFO - ⚡ البرق: استخدام العدد من التلجرام بوت: 200
2025-07-25 03:59:26,936 - INFO - ⚡ البرق للتلجرام: العدد المطلوب 200 حساب
2025-07-25 03:59:26,936 - INFO - ⚡ البرق: تم تحديث عدد الحسابات في الواجهة إلى: 200
2025-07-25 03:59:26,939 - INFO - ⚡ البرق للتلجرام: تم توليد 200 حساب
2025-07-25 03:59:26,939 - INFO - ⚡ البرق للتلجرام: تنفيذ متسلسل مجدول لـ 200 كارت (حد التقسيم: 100)
2025-07-25 03:59:26,941 - INFO - ⚡ البرق للتلجرام: بدء معالجة الأعداد الكبيرة مع جدولة واحدة
2025-07-25 03:59:26,942 - INFO - ⚡ البرق للتلجرام: تم تقسيم 200 كارت إلى 2 مجموعة للتنفيذ المتسلسل
2025-07-25 03:59:26,945 - INFO - ⚡ البرق للتلجرام: إنشاء السكريبت للمجموعة 1/2 (100 كارت)
2025-07-25 03:59:26,947 - INFO - ⚡ البرق للتلجرام: تم إعداد السكريبت telegram_lightning_batch1_user_manager_20250725_035926
2025-07-25 03:59:26,949 - INFO - ⚡ البرق للتلجرام: إنشاء السكريبت للمجموعة 2/2 (100 كارت)
2025-07-25 03:59:26,950 - INFO - ⚡ البرق للتلجرام: تم إعداد السكريبت telegram_lightning_batch2_user_manager_20250725_035926
2025-07-25 03:59:26,964 - INFO - استخدام الاتصال الحالي
2025-07-25 03:59:27,036 - INFO - ⚡ تم إرسال السكريبت المترابط: telegram_lightning_batch1_user_manager_20250725_035926
2025-07-25 03:59:27,077 - INFO - ⚡ تم إرسال السكريبت المترابط: telegram_lightning_batch2_user_manager_20250725_035926
2025-07-25 03:59:27,123 - INFO - ⚡ وقت MikroTik الحالي: 00:59:26
2025-07-25 03:59:27,153 - INFO - ⚡ تم تعديل وقت التنفيذ لليوم التالي: 00:59:29
2025-07-25 03:59:27,153 - INFO - ⚡ السكريبت الأول سينفذ في: 00:59:29 (بعد 3 ثواني من وقت MikroTik)
2025-07-25 03:59:27,195 - INFO - ⚡ تم إنشاء الجدولة الرئيسية: telegram_main_schedule_20250725_035926 لتشغيل telegram_lightning_batch1_user_manager_20250725_035926 في 00:59:29
2025-07-25 03:59:27,200 - INFO - ⚡ تم إعداد البرق المترابط بنجاح - جدولة واحدة: telegram_main_schedule_20250725_035926, 2 سكريبت مترابط
2025-07-25 03:59:27,200 - INFO - ⚡ البرق للتلجرام مكتمل: تم إنشاء جدولة واحدة لـ 2 مجموعة متسلسلة (200 كارت)
2025-07-25 03:59:30,245 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 200
2025-07-25 03:59:30,263 - INFO - تم إنشاء PDF بنجاح: exports/كروت_(10)_ب20-200 كارت-25-07-2025-03-59-29-um.pdf
2025-07-25 03:59:30,266 - INFO - تم حفظ ملف .rsc الاحتياطي: exports/كروت_(10)_ب20-200 كارت-25-07-2025-03-59-29-um.rsc
2025-07-25 03:59:31,026 - INFO - تم إرسال الملف بنجاح: كروت_(10)_ب20-200 كارت-25-07-2025-03-59-29-um.pdf
2025-07-25 03:59:31,266 - INFO - تم إرسال 200 كرت عبر التلجرام باستخدام قالب 10
2025-07-25 04:00:13,406 - INFO - معالجة callback: independent_count_um_10_lightning_200
2025-07-25 04:00:13,958 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-25 04:00:13,958 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-25 04:00:13,973 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-25 04:00:13,974 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-25 04:00:14,010 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-25 04:00:14,022 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-25 04:00:14,026 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-25 04:00:14,030 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-25 04:00:14,316 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-25 04:00:14,316 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-25 04:00:14,344 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-25 04:00:14,344 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-25 04:00:14,380 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-25 04:00:14,380 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-25 04:00:14,382 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-25 04:00:14,396 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-25 04:00:14,397 - INFO - ⚡ البرق: تم الحفاظ على العدد المحدد: 200
2025-07-25 04:00:14,398 - INFO - تم تطبيق إعدادات القالب '10' بنجاح
2025-07-25 04:00:14,398 - INFO - ⚡ استخدام البرق للتلجرام (مع جدولة مؤقتة وتنظيف تلقائي)
2025-07-25 04:00:14,399 - INFO - ⚡ بدء البرق التلقائي للتلجرام (مع جدولة مؤقتة وتنظيف تلقائي)
2025-07-25 04:00:14,399 - INFO - ⚡ البرق: استخدام العدد من التلجرام بوت: 200
2025-07-25 04:00:14,399 - INFO - ⚡ البرق للتلجرام: العدد المطلوب 200 حساب
2025-07-25 04:00:14,400 - INFO - ⚡ البرق: تم تحديث عدد الحسابات في الواجهة إلى: 200
2025-07-25 04:00:14,403 - INFO - ⚡ البرق للتلجرام: تم توليد 200 حساب
2025-07-25 04:00:14,403 - INFO - ⚡ البرق للتلجرام: تنفيذ متسلسل مجدول لـ 200 كارت (حد التقسيم: 100)
2025-07-25 04:00:14,403 - INFO - ⚡ البرق للتلجرام: بدء معالجة الأعداد الكبيرة مع جدولة واحدة
2025-07-25 04:00:14,405 - INFO - ⚡ البرق للتلجرام: تم تقسيم 200 كارت إلى 2 مجموعة للتنفيذ المتسلسل
2025-07-25 04:00:14,405 - INFO - ⚡ البرق للتلجرام: إنشاء السكريبت للمجموعة 1/2 (100 كارت)
2025-07-25 04:00:14,405 - INFO - ⚡ البرق للتلجرام: تم إعداد السكريبت telegram_lightning_batch1_user_manager_20250725_040014
2025-07-25 04:00:14,406 - INFO - ⚡ البرق للتلجرام: إنشاء السكريبت للمجموعة 2/2 (100 كارت)
2025-07-25 04:00:14,406 - INFO - ⚡ البرق للتلجرام: تم إعداد السكريبت telegram_lightning_batch2_user_manager_20250725_040014
2025-07-25 04:00:14,409 - INFO - استخدام الاتصال الحالي
2025-07-25 04:00:14,493 - INFO - ⚡ تم إرسال السكريبت المترابط: telegram_lightning_batch1_user_manager_20250725_040014
2025-07-25 04:00:14,535 - INFO - ⚡ تم إرسال السكريبت المترابط: telegram_lightning_batch2_user_manager_20250725_040014
2025-07-25 04:00:14,539 - INFO - ⚡ وقت MikroTik الحالي: 01:00:14
2025-07-25 04:00:14,540 - INFO - ⚡ تم تعديل وقت التنفيذ لليوم التالي: 01:00:17
2025-07-25 04:00:14,541 - INFO - ⚡ السكريبت الأول سينفذ في: 01:00:17 (بعد 3 ثواني من وقت MikroTik)
2025-07-25 04:00:14,586 - INFO - ⚡ تم إنشاء الجدولة الرئيسية: telegram_main_schedule_20250725_040014 لتشغيل telegram_lightning_batch1_user_manager_20250725_040014 في 01:00:17
2025-07-25 04:00:14,589 - INFO - ⚡ تم إعداد البرق المترابط بنجاح - جدولة واحدة: telegram_main_schedule_20250725_040014, 2 سكريبت مترابط
2025-07-25 04:00:14,590 - INFO - ⚡ البرق للتلجرام مكتمل: تم إنشاء جدولة واحدة لـ 2 مجموعة متسلسلة (200 كارت)
2025-07-25 04:00:17,489 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 200
2025-07-25 04:00:17,509 - INFO - تم إنشاء PDF بنجاح: exports/كروت_(10)_ب20-200 كارت-25-07-2025-04-00-17-um.pdf
2025-07-25 04:00:17,548 - INFO - تم حفظ ملف .rsc الاحتياطي: exports/كروت_(10)_ب20-200 كارت-25-07-2025-04-00-17-um.rsc
2025-07-25 04:00:18,293 - INFO - تم إرسال الملف بنجاح: كروت_(10)_ب20-200 كارت-25-07-2025-04-00-17-um.pdf
2025-07-25 04:00:18,527 - INFO - تم إرسال 200 كرت عبر التلجرام باستخدام قالب 10
2025-07-25 04:14:18,136 - INFO - تم بدء تشغيل التطبيق
2025-07-25 04:14:25,439 - INFO - تم بدء تشغيل التطبيق
2025-07-25 04:14:25,440 - INFO - تم إنشاء المجلدات الأساسية
2025-07-25 04:14:25,904 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-25 04:14:25,905 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-25 04:14:27,092 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-25 04:14:43,133 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-25 04:14:43,134 - INFO - تم إعداد التطبيق بنجاح
2025-07-25 04:14:45,162 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-25 04:14:45,534 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-25 04:14:45,534 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-25 04:14:48,542 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-25 04:14:48,577 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-25 04:14:48,862 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-25 04:14:48,862 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-25 04:14:51,537 - INFO - معالجة callback: select_system_um
2025-07-25 04:14:51,992 - INFO - تم إرسال 2 قالب مفلتر لنظام User Manager
2025-07-25 04:14:53,184 - INFO - معالجة callback: independent_template_um_10
2025-07-25 04:14:53,766 - INFO - 🔄 تبديل النظام تلقائياً من None إلى user_manager
2025-07-25 04:14:53,766 - INFO - 🔄 طلب تبديل النظام من التلجرام: None → user_manager
2025-07-25 04:14:54,017 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-25 04:14:54,108 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-25 04:14:54,209 - WARNING - تحذير: فشل في حفظ الإعدادات: 'MikroTikCardGenerator' object has no attribute 'version_combo'
2025-07-25 04:14:54,209 - INFO - تم تعيين النظام الجديد: user_manager
2025-07-25 04:14:54,210 - INFO - 🔄 إعادة بناء الواجهة للنظام: user_manager
2025-07-25 04:14:54,211 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-25 04:14:54,216 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-25 04:14:55,051 - INFO - تم إعداد تبويب عرض الكروت لـ User Manager بنجاح
2025-07-25 04:14:55,265 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-25 04:14:55,266 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-25 04:14:55,438 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام user_manager
2025-07-25 04:14:55,439 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-25 04:14:55,593 - INFO - تم تسجيل العملية: تبديل النظام من التلجرام - تم تبديل النظام من None إلى user_manager
2025-07-25 04:14:55,602 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager
2025-07-25 04:14:55,608 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-25 04:14:55,609 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-25 04:14:55,611 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-25 04:14:55,973 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-25 04:14:56,020 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-25 04:14:56,113 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-25 04:14:56,113 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-25 04:14:56,117 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-25 04:14:56,121 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-25 04:14:56,124 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-25 04:14:56,126 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager - القالب: 10
2025-07-25 04:14:56,465 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-25 04:15:10,607 - INFO - معالجة callback: independent_create_um_10_lightning
2025-07-25 04:15:10,843 - INFO - 🔍 بدء اختبار جلب العدد الحالي للكروت قبل إرسال الإشعار
2025-07-25 04:15:10,843 - INFO - 🧪 بدء اختبار الاتصال وجلب عدد User Manager
2025-07-25 04:15:10,844 - INFO - محاولة الاتصال بـ 6.6.6.100:8728 (عادي)
2025-07-25 04:15:10,907 - INFO - نجح الاتصال مع 6.6.6.100
2025-07-25 04:15:10,908 - INFO - ✅ نجح الاتصال - اختبار جلب العدد...
2025-07-25 04:15:10,947 - INFO - 🏷️ اسم النظام: MikroTik
2025-07-25 04:15:11,000 - INFO - 📋 إصدار RouterOS: 6.43.12 (stable)
2025-07-25 04:15:11,000 - INFO - 📊 اختبار جلب مستخدمي User Manager...
2025-07-25 04:15:18,045 - INFO - ✅ تم جلب 3859 مستخدم من User Manager
2025-07-25 04:15:18,045 - INFO - 👥 عرض أول 5 مستخدمين:
2025-07-25 04:15:18,045 - INFO -   1. غير محدد (البروفايل: غير محدد)
2025-07-25 04:15:18,046 - INFO -   2. غير محدد (البروفايل: غير محدد)
2025-07-25 04:15:18,046 - INFO -   3. غير محدد (البروفايل: غير محدد)
2025-07-25 04:15:18,046 - INFO -   4. غير محدد (البروفايل: غير محدد)
2025-07-25 04:15:18,047 - INFO -   5. غير محدد (البروفايل: غير محدد)
2025-07-25 04:15:18,047 - ERROR - ❌ خطأ في API: 'RouterOsApi' object has no attribute 'disconnect'
2025-07-25 04:15:18,050 - INFO - 📊 نتيجة الاختبار: خطأ في API: 'RouterOsApi' object has no attribute 'disconnect'
2025-07-25 04:15:18,050 - INFO - 🔔 بدء إرسال إشعار اختيار القالب: 10 (User Manager)
2025-07-25 04:15:18,052 - INFO - 📊 جلب العدد الحالي من User Manager...
2025-07-25 04:15:18,052 - INFO - 🔍 بدء جلب العدد الحالي للكروت في User Manager
2025-07-25 04:15:18,053 - INFO - ✅ مكتبة routeros_api متوفرة
2025-07-25 04:15:18,054 - INFO - 🔗 محاولة الاتصال بـ 6.6.6.100 باستخدام المستخدم: 11
2025-07-25 04:15:18,059 - INFO - استخدام الاتصال الحالي
2025-07-25 04:15:18,073 - INFO - ✅ تم الاتصال بـ MikroTik بنجاح
2025-07-25 04:15:18,073 - INFO - 📊 جلب قائمة المستخدمين من User Manager...
2025-07-25 04:15:25,098 - INFO - ✅ تم جلب 3859 مستخدم من User Manager بنجاح
2025-07-25 04:15:25,098 - INFO - 👤 مستخدم 1: غير محدد
2025-07-25 04:15:25,099 - INFO - 👤 مستخدم 2: غير محدد
2025-07-25 04:15:25,099 - INFO - 👤 مستخدم 3: غير محدد
2025-07-25 04:15:25,103 - INFO - 📈 العدد المجلوب من User Manager: 3859
2025-07-25 04:15:25,403 - INFO - ✅ تم إرسال إشعار اختيار القالب '10' - العدد الحالي: 3859
2025-07-25 04:15:28,658 - INFO - معالجة callback: independent_count_um_10_lightning_200
2025-07-25 04:15:29,115 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-25 04:15:29,115 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-25 04:15:29,130 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-25 04:15:29,130 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-25 04:15:29,184 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-25 04:15:29,184 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-25 04:15:29,187 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-25 04:15:29,191 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-25 04:15:29,416 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-25 04:15:29,416 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-25 04:15:29,430 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-25 04:15:29,431 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-25 04:15:29,483 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-25 04:15:29,489 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-25 04:15:29,492 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-25 04:15:29,497 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-25 04:15:29,497 - INFO - ⚡ البرق: تم الحفاظ على العدد المحدد: 200
2025-07-25 04:15:29,498 - INFO - تم تطبيق إعدادات القالب '10' بنجاح
2025-07-25 04:15:29,498 - INFO - ⚡ استخدام البرق للتلجرام (مع جدولة مؤقتة وتنظيف تلقائي)
2025-07-25 04:15:29,499 - INFO - ⚡ بدء البرق التلقائي للتلجرام (مع جدولة مؤقتة وتنظيف تلقائي)
2025-07-25 04:15:29,500 - INFO - ⚡ البرق: استخدام العدد من التلجرام بوت: 200
2025-07-25 04:15:29,500 - INFO - ⚡ البرق للتلجرام: العدد المطلوب 200 حساب
2025-07-25 04:15:29,501 - INFO - ⚡ البرق: تم تحديث عدد الحسابات في الواجهة إلى: 200
2025-07-25 04:15:29,503 - INFO - ⚡ البرق للتلجرام: تم توليد 200 حساب
2025-07-25 04:15:29,503 - INFO - ⚡ البرق للتلجرام: تنفيذ متسلسل مجدول لـ 200 كارت (حد التقسيم: 100)
2025-07-25 04:15:29,503 - INFO - ⚡ البرق للتلجرام: بدء معالجة الأعداد الكبيرة مع جدولة واحدة
2025-07-25 04:15:29,504 - INFO - ⚡ البرق للتلجرام: تم تقسيم 200 كارت إلى 2 مجموعة للتنفيذ المتسلسل
2025-07-25 04:15:29,504 - INFO - ⚡ البرق للتلجرام: إنشاء السكريبت للمجموعة 1/2 (100 كارت)
2025-07-25 04:15:29,505 - INFO - ⚡ البرق للتلجرام: تم إعداد السكريبت telegram_lightning_batch1_user_manager_20250725_041529
2025-07-25 04:15:29,507 - INFO - ⚡ البرق للتلجرام: إنشاء السكريبت للمجموعة 2/2 (100 كارت)
2025-07-25 04:15:29,510 - INFO - ⚡ البرق للتلجرام: تم إعداد السكريبت telegram_lightning_batch2_user_manager_20250725_041529
2025-07-25 04:15:29,515 - INFO - استخدام الاتصال الحالي
2025-07-25 04:15:29,593 - INFO - ⚡ تم إرسال السكريبت المترابط: telegram_lightning_batch1_user_manager_20250725_041529
2025-07-25 04:15:29,635 - INFO - ⚡ تم إرسال السكريبت المترابط: telegram_lightning_batch2_user_manager_20250725_041529
2025-07-25 04:15:29,677 - INFO - ⚡ وقت MikroTik الحالي: 01:15:29
2025-07-25 04:15:29,713 - INFO - ⚡ تم تعديل وقت التنفيذ لليوم التالي: 01:15:32
2025-07-25 04:15:29,713 - INFO - ⚡ السكريبت الأول سينفذ في: 01:15:32 (بعد 3 ثواني من وقت MikroTik)
2025-07-25 04:15:29,759 - INFO - ⚡ تم إنشاء الجدولة الرئيسية: telegram_main_schedule_20250725_041529 لتشغيل telegram_lightning_batch1_user_manager_20250725_041529 في 01:15:32
2025-07-25 04:15:29,772 - INFO - ⚡ تم إعداد البرق المترابط بنجاح - جدولة واحدة: telegram_main_schedule_20250725_041529, 2 سكريبت مترابط
2025-07-25 04:15:29,773 - INFO - ⚡ البرق للتلجرام مكتمل: تم إنشاء جدولة واحدة لـ 2 مجموعة متسلسلة (200 كارت)
2025-07-25 04:15:32,776 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 200
2025-07-25 04:15:32,862 - INFO - تم إنشاء PDF بنجاح: exports/كروت_(10)_ب20-200 كارت-25-07-2025-04-15-32-um.pdf
2025-07-25 04:15:32,896 - INFO - تم حفظ ملف .rsc الاحتياطي: exports/كروت_(10)_ب20-200 كارت-25-07-2025-04-15-32-um.rsc
2025-07-25 04:15:34,120 - INFO - تم إرسال الملف بنجاح: كروت_(10)_ب20-200 كارت-25-07-2025-04-15-32-um.pdf
2025-07-25 04:15:34,349 - INFO - تم إرسال 200 كرت عبر التلجرام باستخدام قالب 10
2025-07-25 04:16:16,597 - INFO - بدء إغلاق التطبيق
2025-07-25 04:16:16,598 - INFO - تم قطع الاتصال مع MikroTik
2025-07-25 04:16:16,603 - INFO - تم تسجيل العملية: إغلاق التطبيق - تم إغلاق التطبيق بنجاح
2025-07-25 04:16:16,618 - INFO - تم إغلاق التطبيق بنجاح
2025-07-25 04:32:28,404 - INFO - تم بدء تشغيل التطبيق
2025-07-25 04:32:28,443 - INFO - تم إنشاء المجلدات الأساسية
2025-07-25 04:32:28,689 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-25 04:32:28,690 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-25 04:32:30,035 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-25 04:32:43,982 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-25 04:32:43,983 - INFO - تم إعداد التطبيق بنجاح
2025-07-25 04:32:46,005 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-25 04:32:46,376 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-25 04:32:46,377 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-25 04:32:49,380 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-25 04:32:49,420 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-25 04:32:49,681 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-25 04:32:49,682 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-25 04:35:09,459 - INFO - معالجة callback: select_system_um
2025-07-25 04:35:09,920 - INFO - تم إرسال 2 قالب مفلتر لنظام User Manager
2025-07-25 04:35:11,699 - INFO - معالجة callback: independent_template_um_10
2025-07-25 04:35:12,287 - INFO - 🔄 تبديل النظام تلقائياً من None إلى user_manager
2025-07-25 04:35:12,287 - INFO - 🔄 طلب تبديل النظام من التلجرام: None → user_manager
2025-07-25 04:35:12,566 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-25 04:35:12,660 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-25 04:35:12,760 - WARNING - تحذير: فشل في حفظ الإعدادات: 'MikroTikCardGenerator' object has no attribute 'version_combo'
2025-07-25 04:35:12,761 - INFO - تم تعيين النظام الجديد: user_manager
2025-07-25 04:35:12,761 - INFO - 🔄 إعادة بناء الواجهة للنظام: user_manager
2025-07-25 04:35:12,762 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-25 04:35:12,765 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-25 04:35:13,368 - INFO - تم إعداد تبويب عرض الكروت لـ User Manager بنجاح
2025-07-25 04:35:13,563 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-25 04:35:13,563 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-25 04:35:13,817 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام user_manager
2025-07-25 04:35:13,818 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-25 04:35:14,443 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager
2025-07-25 04:35:14,446 - INFO - تم تسجيل العملية: تبديل النظام من التلجرام - تم تبديل النظام من None إلى user_manager
2025-07-25 04:35:14,448 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-25 04:35:14,450 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-25 04:35:14,451 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-25 04:35:14,764 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-25 04:35:14,770 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-25 04:35:14,816 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-25 04:35:14,816 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-25 04:35:14,822 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-25 04:35:14,835 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-25 04:35:14,839 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-25 04:35:14,841 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager - القالب: 10
2025-07-25 04:35:15,181 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-25 04:35:23,189 - INFO - معالجة callback: independent_create_um_10_lightning
2025-07-25 04:35:23,408 - INFO - 🔍 بدء اختبار جلب العدد الحالي للكروت قبل إرسال الإشعار
2025-07-25 04:35:23,408 - INFO - 🧪 بدء اختبار الاتصال وجلب عدد User Manager
2025-07-25 04:35:23,409 - INFO - محاولة الاتصال بـ 6.6.6.100:8728 (عادي)
2025-07-25 04:35:23,475 - INFO - نجح الاتصال مع 6.6.6.100
2025-07-25 04:35:23,479 - INFO - ✅ نجح الاتصال - اختبار جلب العدد...
2025-07-25 04:35:23,515 - INFO - 🏷️ اسم النظام: MikroTik
2025-07-25 04:35:23,558 - INFO - 📋 إصدار RouterOS: 6.43.12 (stable)
2025-07-25 04:35:23,560 - INFO - 📊 اختبار جلب مستخدمي User Manager...
2025-07-25 04:35:30,933 - INFO - ✅ تم جلب 4059 مستخدم من User Manager
2025-07-25 04:35:30,933 - INFO - 👥 عرض أول 5 مستخدمين:
2025-07-25 04:35:30,934 - INFO -   1. غير محدد (البروفايل: غير محدد)
2025-07-25 04:35:30,934 - INFO -   2. غير محدد (البروفايل: غير محدد)
2025-07-25 04:35:30,934 - INFO -   3. غير محدد (البروفايل: غير محدد)
2025-07-25 04:35:30,934 - INFO -   4. غير محدد (البروفايل: غير محدد)
2025-07-25 04:35:30,935 - INFO -   5. غير محدد (البروفايل: غير محدد)
2025-07-25 04:35:30,935 - ERROR - ❌ خطأ في API: 'RouterOsApi' object has no attribute 'disconnect'
2025-07-25 04:35:30,938 - INFO - 📊 نتيجة الاختبار: خطأ في API: 'RouterOsApi' object has no attribute 'disconnect'
2025-07-25 04:35:30,938 - INFO - 🔔 بدء إرسال إشعار اختيار القالب: 10 (User Manager)
2025-07-25 04:35:30,940 - INFO - 📊 جلب العدد الحالي من User Manager...
2025-07-25 04:35:30,940 - INFO - 🔍 بدء جلب العدد الحالي للكروت في User Manager
2025-07-25 04:35:30,941 - INFO - ✅ مكتبة routeros_api متوفرة
2025-07-25 04:35:30,942 - INFO - 🔗 محاولة الاتصال بـ 6.6.6.100 باستخدام المستخدم: 11
2025-07-25 04:35:30,946 - INFO - استخدام الاتصال الحالي
2025-07-25 04:35:30,966 - INFO - ✅ تم الاتصال بـ MikroTik بنجاح
2025-07-25 04:35:30,966 - INFO - 📊 جلب قائمة المستخدمين من User Manager...
2025-07-25 04:35:38,371 - INFO - ✅ تم جلب 4059 مستخدم من User Manager بنجاح
2025-07-25 04:35:38,372 - INFO - 👤 مستخدم 1: غير محدد
2025-07-25 04:35:38,372 - INFO - 👤 مستخدم 2: غير محدد
2025-07-25 04:35:38,373 - INFO - 👤 مستخدم 3: غير محدد
2025-07-25 04:35:38,376 - INFO - 📈 العدد المجلوب من User Manager: 4059
2025-07-25 04:35:38,674 - INFO - ✅ تم إرسال إشعار اختيار القالب '10' - العدد الحالي: 4059
2025-07-25 04:35:45,035 - INFO - معالجة callback: independent_count_um_10_lightning_200
2025-07-25 04:35:45,674 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-25 04:35:45,675 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-25 04:35:45,690 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-25 04:35:45,691 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-25 04:35:45,727 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-25 04:35:45,729 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-25 04:35:45,738 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-25 04:35:45,745 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-25 04:35:46,057 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-25 04:35:46,059 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-25 04:35:46,073 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-25 04:35:46,074 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-25 04:35:46,120 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-25 04:35:46,120 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-25 04:35:46,124 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-25 04:35:46,128 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-25 04:35:46,128 - INFO - ⚡ البرق: تم الحفاظ على العدد المحدد: 200
2025-07-25 04:35:46,129 - INFO - تم تطبيق إعدادات القالب '10' بنجاح
2025-07-25 04:35:46,129 - INFO - ⚡ استخدام البرق للتلجرام (مع جدولة مؤقتة وتنظيف تلقائي)
2025-07-25 04:35:46,130 - INFO - ⚡ بدء البرق التلقائي للتلجرام (مع جدولة مؤقتة وتنظيف تلقائي)
2025-07-25 04:35:46,130 - INFO - ⚡ البرق: استخدام العدد من التلجرام بوت: 200
2025-07-25 04:35:46,130 - INFO - ⚡ البرق للتلجرام: العدد المطلوب 200 حساب
2025-07-25 04:35:46,130 - INFO - ⚡ البرق: تم تحديث عدد الحسابات في الواجهة إلى: 200
2025-07-25 04:35:46,142 - INFO - ⚡ البرق للتلجرام: تم توليد 200 حساب
2025-07-25 04:35:46,145 - INFO - ⚡ البرق للتلجرام: تنفيذ متسلسل مجدول لـ 200 كارت (حد التقسيم: 100)
2025-07-25 04:35:46,145 - INFO - ⚡ البرق للتلجرام: بدء معالجة الأعداد الكبيرة مع جدولة واحدة
2025-07-25 04:35:46,146 - INFO - ⚡ البرق للتلجرام: تم تقسيم 200 كارت إلى 2 مجموعة للتنفيذ المتسلسل
2025-07-25 04:35:46,146 - INFO - ⚡ البرق للتلجرام: إنشاء السكريبت للمجموعة 1/2 (100 كارت)
2025-07-25 04:35:46,146 - INFO - ⚡ البرق للتلجرام: تم إعداد السكريبت telegram_lightning_batch1_user_manager_20250725_043546
2025-07-25 04:35:46,146 - INFO - ⚡ البرق للتلجرام: إنشاء السكريبت للمجموعة 2/2 (100 كارت)
2025-07-25 04:35:46,147 - INFO - ⚡ البرق للتلجرام: تم إعداد السكريبت telegram_lightning_batch2_user_manager_20250725_043546
2025-07-25 04:35:46,150 - INFO - استخدام الاتصال الحالي
2025-07-25 04:35:46,228 - INFO - ⚡ تم إرسال السكريبت المترابط: telegram_lightning_batch1_user_manager_20250725_043546
2025-07-25 04:35:46,321 - INFO - ⚡ تم إرسال السكريبت المترابط: telegram_lightning_batch2_user_manager_20250725_043546
2025-07-25 04:35:46,365 - INFO - ⚡ وقت MikroTik الحالي: 01:35:45
2025-07-25 04:35:46,393 - INFO - ⚡ تم تعديل وقت التنفيذ لليوم التالي: 01:35:48
2025-07-25 04:35:46,394 - INFO - ⚡ السكريبت الأول سينفذ في: 01:35:48 (بعد 3 ثواني من وقت MikroTik)
2025-07-25 04:35:46,437 - INFO - ⚡ تم إنشاء الجدولة الرئيسية: telegram_main_schedule_20250725_043546 لتشغيل telegram_lightning_batch1_user_manager_20250725_043546 في 01:35:48
2025-07-25 04:35:46,441 - INFO - ⚡ تم إعداد البرق المترابط بنجاح - جدولة واحدة: telegram_main_schedule_20250725_043546, 2 سكريبت مترابط
2025-07-25 04:35:46,442 - INFO - ⚡ البرق للتلجرام مكتمل: تم إنشاء جدولة واحدة لـ 2 مجموعة متسلسلة (200 كارت)
2025-07-25 04:35:49,321 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 200
2025-07-25 04:35:49,363 - INFO - تم إنشاء PDF بنجاح: exports/كروت_(10)_ب20-200 كارت-25-07-2025-04-35-49-um.pdf
2025-07-25 04:35:49,387 - INFO - تم حفظ ملف .rsc الاحتياطي: exports/كروت_(10)_ب20-200 كارت-25-07-2025-04-35-49-um.rsc
2025-07-25 04:35:50,646 - INFO - تم إرسال الملف بنجاح: كروت_(10)_ب20-200 كارت-25-07-2025-04-35-49-um.pdf
2025-07-25 04:35:51,082 - INFO - تم إرسال 200 كرت عبر التلجرام باستخدام قالب 10
2025-07-25 04:36:26,052 - INFO - بدء إغلاق التطبيق
2025-07-25 04:36:26,053 - INFO - تم قطع الاتصال مع MikroTik
2025-07-25 04:36:26,080 - INFO - تم تسجيل العملية: إغلاق التطبيق - تم إغلاق التطبيق بنجاح
2025-07-25 04:36:26,081 - INFO - تم إغلاق التطبيق بنجاح
