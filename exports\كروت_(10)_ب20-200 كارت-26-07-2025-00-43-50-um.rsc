# MikroTik Script - نسخة احتياطية للكروت
# تم الإنشاء: 2025-07-26 00:43:51
# القالب: 10
# النظام: user_manager
# عدد الكروت: 200
# تم إنشاؤه بواسطة: مولد كروت MikroTik

:put "🚀 بدء تنفيذ النسخة الاحتياطية للكروت";
:put "📋 القالب: 10";
:put "🎯 النظام: user_manager";
:put "📊 عدد الكروت: 200";

:local success 0;
:local errors 0;
:local total 200;

:put "⏳ بدء إضافة الكروت...";

# إضافة مستخدمي User Manager
:put "👤 إضافة 200 مستخدم User Manager...";

# المستخدم 1: 0193510327
:do {
    /tool user-manager user add customer="admin" username="0193510327" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0193510327";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0193510327";
};

# المستخدم 2: 0107749174
:do {
    /tool user-manager user add customer="admin" username="0107749174" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0107749174";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0107749174";
};

# المستخدم 3: 0180942217
:do {
    /tool user-manager user add customer="admin" username="0180942217" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0180942217";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0180942217";
};

# المستخدم 4: 0198331290
:do {
    /tool user-manager user add customer="admin" username="0198331290" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0198331290";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0198331290";
};

# المستخدم 5: 0126000065
:do {
    /tool user-manager user add customer="admin" username="0126000065" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0126000065";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0126000065";
};

# المستخدم 6: 0105073006
:do {
    /tool user-manager user add customer="admin" username="0105073006" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0105073006";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0105073006";
};

# المستخدم 7: 0108664172
:do {
    /tool user-manager user add customer="admin" username="0108664172" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0108664172";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0108664172";
};

# المستخدم 8: 0136949373
:do {
    /tool user-manager user add customer="admin" username="0136949373" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0136949373";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0136949373";
};

# المستخدم 9: 0198723534
:do {
    /tool user-manager user add customer="admin" username="0198723534" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0198723534";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0198723534";
};

# المستخدم 10: 0169963065
:do {
    /tool user-manager user add customer="admin" username="0169963065" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0169963065";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0169963065";
};

# المستخدم 11: 0109387835
:do {
    /tool user-manager user add customer="admin" username="0109387835" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0109387835";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0109387835";
};

# المستخدم 12: 0179779996
:do {
    /tool user-manager user add customer="admin" username="0179779996" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0179779996";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0179779996";
};

# المستخدم 13: 0158920023
:do {
    /tool user-manager user add customer="admin" username="0158920023" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0158920023";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0158920023";
};

# المستخدم 14: 0153897141
:do {
    /tool user-manager user add customer="admin" username="0153897141" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0153897141";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0153897141";
};

# المستخدم 15: 0152144634
:do {
    /tool user-manager user add customer="admin" username="0152144634" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0152144634";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0152144634";
};

# المستخدم 16: 0150949636
:do {
    /tool user-manager user add customer="admin" username="0150949636" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0150949636";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0150949636";
};

# المستخدم 17: 0193095931
:do {
    /tool user-manager user add customer="admin" username="0193095931" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0193095931";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0193095931";
};

# المستخدم 18: 0161124975
:do {
    /tool user-manager user add customer="admin" username="0161124975" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0161124975";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0161124975";
};

# المستخدم 19: 0184658289
:do {
    /tool user-manager user add customer="admin" username="0184658289" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0184658289";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0184658289";
};

# المستخدم 20: 0168142833
:do {
    /tool user-manager user add customer="admin" username="0168142833" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0168142833";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0168142833";
};

# المستخدم 21: 0119535312
:do {
    /tool user-manager user add customer="admin" username="0119535312" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0119535312";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0119535312";
};

# المستخدم 22: 0189358810
:do {
    /tool user-manager user add customer="admin" username="0189358810" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0189358810";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0189358810";
};

# المستخدم 23: 0182743866
:do {
    /tool user-manager user add customer="admin" username="0182743866" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0182743866";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0182743866";
};

# المستخدم 24: 0111582543
:do {
    /tool user-manager user add customer="admin" username="0111582543" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0111582543";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0111582543";
};

# المستخدم 25: 0161123605
:do {
    /tool user-manager user add customer="admin" username="0161123605" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0161123605";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0161123605";
};

# المستخدم 26: 0174809792
:do {
    /tool user-manager user add customer="admin" username="0174809792" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0174809792";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0174809792";
};

# المستخدم 27: 0144512962
:do {
    /tool user-manager user add customer="admin" username="0144512962" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0144512962";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0144512962";
};

# المستخدم 28: 0121657104
:do {
    /tool user-manager user add customer="admin" username="0121657104" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0121657104";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0121657104";
};

# المستخدم 29: 0182349264
:do {
    /tool user-manager user add customer="admin" username="0182349264" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0182349264";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0182349264";
};

# المستخدم 30: 0140925130
:do {
    /tool user-manager user add customer="admin" username="0140925130" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0140925130";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0140925130";
};

# المستخدم 31: 0174863009
:do {
    /tool user-manager user add customer="admin" username="0174863009" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0174863009";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0174863009";
};

# المستخدم 32: 0165680537
:do {
    /tool user-manager user add customer="admin" username="0165680537" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0165680537";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0165680537";
};

# المستخدم 33: 0186289167
:do {
    /tool user-manager user add customer="admin" username="0186289167" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0186289167";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0186289167";
};

# المستخدم 34: 0128396219
:do {
    /tool user-manager user add customer="admin" username="0128396219" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0128396219";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0128396219";
};

# المستخدم 35: 0157976145
:do {
    /tool user-manager user add customer="admin" username="0157976145" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0157976145";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0157976145";
};

# المستخدم 36: 0126601198
:do {
    /tool user-manager user add customer="admin" username="0126601198" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0126601198";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0126601198";
};

# المستخدم 37: 0170318820
:do {
    /tool user-manager user add customer="admin" username="0170318820" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0170318820";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0170318820";
};

# المستخدم 38: 0153793029
:do {
    /tool user-manager user add customer="admin" username="0153793029" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0153793029";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0153793029";
};

# المستخدم 39: 0177659856
:do {
    /tool user-manager user add customer="admin" username="0177659856" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0177659856";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0177659856";
};

# المستخدم 40: 0167069016
:do {
    /tool user-manager user add customer="admin" username="0167069016" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0167069016";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0167069016";
};

# المستخدم 41: 0168189708
:do {
    /tool user-manager user add customer="admin" username="0168189708" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0168189708";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0168189708";
};

# المستخدم 42: 0143230047
:do {
    /tool user-manager user add customer="admin" username="0143230047" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0143230047";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0143230047";
};

# المستخدم 43: 0127495202
:do {
    /tool user-manager user add customer="admin" username="0127495202" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0127495202";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0127495202";
};

# المستخدم 44: 0104373505
:do {
    /tool user-manager user add customer="admin" username="0104373505" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0104373505";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0104373505";
};

# المستخدم 45: 0187585245
:do {
    /tool user-manager user add customer="admin" username="0187585245" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0187585245";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0187585245";
};

# المستخدم 46: 0175587605
:do {
    /tool user-manager user add customer="admin" username="0175587605" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0175587605";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0175587605";
};

# المستخدم 47: 0189777204
:do {
    /tool user-manager user add customer="admin" username="0189777204" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0189777204";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0189777204";
};

# المستخدم 48: 0192659111
:do {
    /tool user-manager user add customer="admin" username="0192659111" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0192659111";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0192659111";
};

# المستخدم 49: 0132256739
:do {
    /tool user-manager user add customer="admin" username="0132256739" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0132256739";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0132256739";
};

# المستخدم 50: 0176070292
:do {
    /tool user-manager user add customer="admin" username="0176070292" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0176070292";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0176070292";
};

# المستخدم 51: 0122946478
:do {
    /tool user-manager user add customer="admin" username="0122946478" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0122946478";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0122946478";
};

# المستخدم 52: 0178367802
:do {
    /tool user-manager user add customer="admin" username="0178367802" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0178367802";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0178367802";
};

# المستخدم 53: 0175888517
:do {
    /tool user-manager user add customer="admin" username="0175888517" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0175888517";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0175888517";
};

# المستخدم 54: 0162171255
:do {
    /tool user-manager user add customer="admin" username="0162171255" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0162171255";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0162171255";
};

# المستخدم 55: 0108635040
:do {
    /tool user-manager user add customer="admin" username="0108635040" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0108635040";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0108635040";
};

# المستخدم 56: 0111586398
:do {
    /tool user-manager user add customer="admin" username="0111586398" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0111586398";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0111586398";
};

# المستخدم 57: 0176535532
:do {
    /tool user-manager user add customer="admin" username="0176535532" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0176535532";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0176535532";
};

# المستخدم 58: 0142885122
:do {
    /tool user-manager user add customer="admin" username="0142885122" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0142885122";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0142885122";
};

# المستخدم 59: 0108026516
:do {
    /tool user-manager user add customer="admin" username="0108026516" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0108026516";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0108026516";
};

# المستخدم 60: 0146790282
:do {
    /tool user-manager user add customer="admin" username="0146790282" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0146790282";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0146790282";
};

# المستخدم 61: 0125599680
:do {
    /tool user-manager user add customer="admin" username="0125599680" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0125599680";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0125599680";
};

# المستخدم 62: 0192526895
:do {
    /tool user-manager user add customer="admin" username="0192526895" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0192526895";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0192526895";
};

# المستخدم 63: 0158639105
:do {
    /tool user-manager user add customer="admin" username="0158639105" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0158639105";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0158639105";
};

# المستخدم 64: 0163027059
:do {
    /tool user-manager user add customer="admin" username="0163027059" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0163027059";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0163027059";
};

# المستخدم 65: 0187945852
:do {
    /tool user-manager user add customer="admin" username="0187945852" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0187945852";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0187945852";
};

# المستخدم 66: 0186759869
:do {
    /tool user-manager user add customer="admin" username="0186759869" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0186759869";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0186759869";
};

# المستخدم 67: 0166383742
:do {
    /tool user-manager user add customer="admin" username="0166383742" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0166383742";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0166383742";
};

# المستخدم 68: 0124097370
:do {
    /tool user-manager user add customer="admin" username="0124097370" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0124097370";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0124097370";
};

# المستخدم 69: 0107961008
:do {
    /tool user-manager user add customer="admin" username="0107961008" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0107961008";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0107961008";
};

# المستخدم 70: 0109043121
:do {
    /tool user-manager user add customer="admin" username="0109043121" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0109043121";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0109043121";
};

# المستخدم 71: 0191715260
:do {
    /tool user-manager user add customer="admin" username="0191715260" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0191715260";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0191715260";
};

# المستخدم 72: 0170212468
:do {
    /tool user-manager user add customer="admin" username="0170212468" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0170212468";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0170212468";
};

# المستخدم 73: 0104398805
:do {
    /tool user-manager user add customer="admin" username="0104398805" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0104398805";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0104398805";
};

# المستخدم 74: 0100625322
:do {
    /tool user-manager user add customer="admin" username="0100625322" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0100625322";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0100625322";
};

# المستخدم 75: 0173419592
:do {
    /tool user-manager user add customer="admin" username="0173419592" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0173419592";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0173419592";
};

# المستخدم 76: 0133062624
:do {
    /tool user-manager user add customer="admin" username="0133062624" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0133062624";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0133062624";
};

# المستخدم 77: 0148233051
:do {
    /tool user-manager user add customer="admin" username="0148233051" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0148233051";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0148233051";
};

# المستخدم 78: 0196801377
:do {
    /tool user-manager user add customer="admin" username="0196801377" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0196801377";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0196801377";
};

# المستخدم 79: 0147127414
:do {
    /tool user-manager user add customer="admin" username="0147127414" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0147127414";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0147127414";
};

# المستخدم 80: 0150712313
:do {
    /tool user-manager user add customer="admin" username="0150712313" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0150712313";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0150712313";
};

# المستخدم 81: 0184644448
:do {
    /tool user-manager user add customer="admin" username="0184644448" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0184644448";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0184644448";
};

# المستخدم 82: 0145273062
:do {
    /tool user-manager user add customer="admin" username="0145273062" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0145273062";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0145273062";
};

# المستخدم 83: 0139880255
:do {
    /tool user-manager user add customer="admin" username="0139880255" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0139880255";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0139880255";
};

# المستخدم 84: 0179629849
:do {
    /tool user-manager user add customer="admin" username="0179629849" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0179629849";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0179629849";
};

# المستخدم 85: 0116481853
:do {
    /tool user-manager user add customer="admin" username="0116481853" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0116481853";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0116481853";
};

# المستخدم 86: 0189018685
:do {
    /tool user-manager user add customer="admin" username="0189018685" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0189018685";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0189018685";
};

# المستخدم 87: 0191870321
:do {
    /tool user-manager user add customer="admin" username="0191870321" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0191870321";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0191870321";
};

# المستخدم 88: 0135486388
:do {
    /tool user-manager user add customer="admin" username="0135486388" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0135486388";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0135486388";
};

# المستخدم 89: 0112173681
:do {
    /tool user-manager user add customer="admin" username="0112173681" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0112173681";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0112173681";
};

# المستخدم 90: 0196084556
:do {
    /tool user-manager user add customer="admin" username="0196084556" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0196084556";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0196084556";
};

# المستخدم 91: 0114987358
:do {
    /tool user-manager user add customer="admin" username="0114987358" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0114987358";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0114987358";
};

# المستخدم 92: 0147983949
:do {
    /tool user-manager user add customer="admin" username="0147983949" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0147983949";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0147983949";
};

# المستخدم 93: 0186938339
:do {
    /tool user-manager user add customer="admin" username="0186938339" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0186938339";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0186938339";
};

# المستخدم 94: 0183260941
:do {
    /tool user-manager user add customer="admin" username="0183260941" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0183260941";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0183260941";
};

# المستخدم 95: 0154760840
:do {
    /tool user-manager user add customer="admin" username="0154760840" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0154760840";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0154760840";
};

# المستخدم 96: 0185915094
:do {
    /tool user-manager user add customer="admin" username="0185915094" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0185915094";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0185915094";
};

# المستخدم 97: 0108550852
:do {
    /tool user-manager user add customer="admin" username="0108550852" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0108550852";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0108550852";
};

# المستخدم 98: 0167183860
:do {
    /tool user-manager user add customer="admin" username="0167183860" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0167183860";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0167183860";
};

# المستخدم 99: 0107748300
:do {
    /tool user-manager user add customer="admin" username="0107748300" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0107748300";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0107748300";
};

# المستخدم 100: 0113173893
:do {
    /tool user-manager user add customer="admin" username="0113173893" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0113173893";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0113173893";
};

# المستخدم 101: 0177456154
:do {
    /tool user-manager user add customer="admin" username="0177456154" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0177456154";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0177456154";
};

# المستخدم 102: 0168588226
:do {
    /tool user-manager user add customer="admin" username="0168588226" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0168588226";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0168588226";
};

# المستخدم 103: 0103401532
:do {
    /tool user-manager user add customer="admin" username="0103401532" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0103401532";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0103401532";
};

# المستخدم 104: 0120438433
:do {
    /tool user-manager user add customer="admin" username="0120438433" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0120438433";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0120438433";
};

# المستخدم 105: 0173515081
:do {
    /tool user-manager user add customer="admin" username="0173515081" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0173515081";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0173515081";
};

# المستخدم 106: 0195296347
:do {
    /tool user-manager user add customer="admin" username="0195296347" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0195296347";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0195296347";
};

# المستخدم 107: 0151176575
:do {
    /tool user-manager user add customer="admin" username="0151176575" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0151176575";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0151176575";
};

# المستخدم 108: 0109972004
:do {
    /tool user-manager user add customer="admin" username="0109972004" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0109972004";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0109972004";
};

# المستخدم 109: 0199565000
:do {
    /tool user-manager user add customer="admin" username="0199565000" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0199565000";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0199565000";
};

# المستخدم 110: 0131108308
:do {
    /tool user-manager user add customer="admin" username="0131108308" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0131108308";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0131108308";
};

# المستخدم 111: 0190322449
:do {
    /tool user-manager user add customer="admin" username="0190322449" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0190322449";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0190322449";
};

# المستخدم 112: 0182107726
:do {
    /tool user-manager user add customer="admin" username="0182107726" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0182107726";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0182107726";
};

# المستخدم 113: 0145700879
:do {
    /tool user-manager user add customer="admin" username="0145700879" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0145700879";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0145700879";
};

# المستخدم 114: 0113232593
:do {
    /tool user-manager user add customer="admin" username="0113232593" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0113232593";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0113232593";
};

# المستخدم 115: 0154321180
:do {
    /tool user-manager user add customer="admin" username="0154321180" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0154321180";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0154321180";
};

# المستخدم 116: 0148224015
:do {
    /tool user-manager user add customer="admin" username="0148224015" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0148224015";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0148224015";
};

# المستخدم 117: 0127516330
:do {
    /tool user-manager user add customer="admin" username="0127516330" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0127516330";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0127516330";
};

# المستخدم 118: 0146069629
:do {
    /tool user-manager user add customer="admin" username="0146069629" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0146069629";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0146069629";
};

# المستخدم 119: 0196722452
:do {
    /tool user-manager user add customer="admin" username="0196722452" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0196722452";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0196722452";
};

# المستخدم 120: 0172384406
:do {
    /tool user-manager user add customer="admin" username="0172384406" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0172384406";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0172384406";
};

# المستخدم 121: 0148300312
:do {
    /tool user-manager user add customer="admin" username="0148300312" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0148300312";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0148300312";
};

# المستخدم 122: 0139377967
:do {
    /tool user-manager user add customer="admin" username="0139377967" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0139377967";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0139377967";
};

# المستخدم 123: 0115567625
:do {
    /tool user-manager user add customer="admin" username="0115567625" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0115567625";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0115567625";
};

# المستخدم 124: 0143848956
:do {
    /tool user-manager user add customer="admin" username="0143848956" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0143848956";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0143848956";
};

# المستخدم 125: 0187604639
:do {
    /tool user-manager user add customer="admin" username="0187604639" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0187604639";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0187604639";
};

# المستخدم 126: 0172881872
:do {
    /tool user-manager user add customer="admin" username="0172881872" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0172881872";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0172881872";
};

# المستخدم 127: 0177965974
:do {
    /tool user-manager user add customer="admin" username="0177965974" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0177965974";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0177965974";
};

# المستخدم 128: 0131637600
:do {
    /tool user-manager user add customer="admin" username="0131637600" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0131637600";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0131637600";
};

# المستخدم 129: 0179457523
:do {
    /tool user-manager user add customer="admin" username="0179457523" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0179457523";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0179457523";
};

# المستخدم 130: 0192688723
:do {
    /tool user-manager user add customer="admin" username="0192688723" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0192688723";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0192688723";
};

# المستخدم 131: 0139434867
:do {
    /tool user-manager user add customer="admin" username="0139434867" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0139434867";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0139434867";
};

# المستخدم 132: 0198024461
:do {
    /tool user-manager user add customer="admin" username="0198024461" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0198024461";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0198024461";
};

# المستخدم 133: 0183712684
:do {
    /tool user-manager user add customer="admin" username="0183712684" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0183712684";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0183712684";
};

# المستخدم 134: 0108422587
:do {
    /tool user-manager user add customer="admin" username="0108422587" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0108422587";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0108422587";
};

# المستخدم 135: 0171861733
:do {
    /tool user-manager user add customer="admin" username="0171861733" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0171861733";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0171861733";
};

# المستخدم 136: 0134024498
:do {
    /tool user-manager user add customer="admin" username="0134024498" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0134024498";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0134024498";
};

# المستخدم 137: 0175231984
:do {
    /tool user-manager user add customer="admin" username="0175231984" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0175231984";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0175231984";
};

# المستخدم 138: 0195708778
:do {
    /tool user-manager user add customer="admin" username="0195708778" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0195708778";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0195708778";
};

# المستخدم 139: 0107887780
:do {
    /tool user-manager user add customer="admin" username="0107887780" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0107887780";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0107887780";
};

# المستخدم 140: 0112989257
:do {
    /tool user-manager user add customer="admin" username="0112989257" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0112989257";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0112989257";
};

# المستخدم 141: 0123227162
:do {
    /tool user-manager user add customer="admin" username="0123227162" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0123227162";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0123227162";
};

# المستخدم 142: 0134480200
:do {
    /tool user-manager user add customer="admin" username="0134480200" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0134480200";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0134480200";
};

# المستخدم 143: 0143556186
:do {
    /tool user-manager user add customer="admin" username="0143556186" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0143556186";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0143556186";
};

# المستخدم 144: 0157187599
:do {
    /tool user-manager user add customer="admin" username="0157187599" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0157187599";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0157187599";
};

# المستخدم 145: 0195296088
:do {
    /tool user-manager user add customer="admin" username="0195296088" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0195296088";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0195296088";
};

# المستخدم 146: 0181115797
:do {
    /tool user-manager user add customer="admin" username="0181115797" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0181115797";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0181115797";
};

# المستخدم 147: 0104288037
:do {
    /tool user-manager user add customer="admin" username="0104288037" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0104288037";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0104288037";
};

# المستخدم 148: 0136452314
:do {
    /tool user-manager user add customer="admin" username="0136452314" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0136452314";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0136452314";
};

# المستخدم 149: 0170561789
:do {
    /tool user-manager user add customer="admin" username="0170561789" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0170561789";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0170561789";
};

# المستخدم 150: 0106201360
:do {
    /tool user-manager user add customer="admin" username="0106201360" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0106201360";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0106201360";
};

# المستخدم 151: 0118160495
:do {
    /tool user-manager user add customer="admin" username="0118160495" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0118160495";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0118160495";
};

# المستخدم 152: 0101660426
:do {
    /tool user-manager user add customer="admin" username="0101660426" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0101660426";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0101660426";
};

# المستخدم 153: 0143675429
:do {
    /tool user-manager user add customer="admin" username="0143675429" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0143675429";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0143675429";
};

# المستخدم 154: 0170834902
:do {
    /tool user-manager user add customer="admin" username="0170834902" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0170834902";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0170834902";
};

# المستخدم 155: 0115266452
:do {
    /tool user-manager user add customer="admin" username="0115266452" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0115266452";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0115266452";
};

# المستخدم 156: 0108786839
:do {
    /tool user-manager user add customer="admin" username="0108786839" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0108786839";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0108786839";
};

# المستخدم 157: 0166234405
:do {
    /tool user-manager user add customer="admin" username="0166234405" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0166234405";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0166234405";
};

# المستخدم 158: 0145843663
:do {
    /tool user-manager user add customer="admin" username="0145843663" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0145843663";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0145843663";
};

# المستخدم 159: 0182009438
:do {
    /tool user-manager user add customer="admin" username="0182009438" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0182009438";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0182009438";
};

# المستخدم 160: 0188916589
:do {
    /tool user-manager user add customer="admin" username="0188916589" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0188916589";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0188916589";
};

# المستخدم 161: 0152481695
:do {
    /tool user-manager user add customer="admin" username="0152481695" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0152481695";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0152481695";
};

# المستخدم 162: 0176887102
:do {
    /tool user-manager user add customer="admin" username="0176887102" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0176887102";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0176887102";
};

# المستخدم 163: 0124935790
:do {
    /tool user-manager user add customer="admin" username="0124935790" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0124935790";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0124935790";
};

# المستخدم 164: 0123073105
:do {
    /tool user-manager user add customer="admin" username="0123073105" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0123073105";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0123073105";
};

# المستخدم 165: 0166302119
:do {
    /tool user-manager user add customer="admin" username="0166302119" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0166302119";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0166302119";
};

# المستخدم 166: 0132902098
:do {
    /tool user-manager user add customer="admin" username="0132902098" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0132902098";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0132902098";
};

# المستخدم 167: 0141116401
:do {
    /tool user-manager user add customer="admin" username="0141116401" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0141116401";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0141116401";
};

# المستخدم 168: 0198095959
:do {
    /tool user-manager user add customer="admin" username="0198095959" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0198095959";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0198095959";
};

# المستخدم 169: 0124565369
:do {
    /tool user-manager user add customer="admin" username="0124565369" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0124565369";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0124565369";
};

# المستخدم 170: 0111135934
:do {
    /tool user-manager user add customer="admin" username="0111135934" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0111135934";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0111135934";
};

# المستخدم 171: 0182540698
:do {
    /tool user-manager user add customer="admin" username="0182540698" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0182540698";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0182540698";
};

# المستخدم 172: 0140129564
:do {
    /tool user-manager user add customer="admin" username="0140129564" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0140129564";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0140129564";
};

# المستخدم 173: 0124734072
:do {
    /tool user-manager user add customer="admin" username="0124734072" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0124734072";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0124734072";
};

# المستخدم 174: 0174830862
:do {
    /tool user-manager user add customer="admin" username="0174830862" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0174830862";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0174830862";
};

# المستخدم 175: 0185989090
:do {
    /tool user-manager user add customer="admin" username="0185989090" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0185989090";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0185989090";
};

# المستخدم 176: 0172824199
:do {
    /tool user-manager user add customer="admin" username="0172824199" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0172824199";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0172824199";
};

# المستخدم 177: 0185315559
:do {
    /tool user-manager user add customer="admin" username="0185315559" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0185315559";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0185315559";
};

# المستخدم 178: 0121950427
:do {
    /tool user-manager user add customer="admin" username="0121950427" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0121950427";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0121950427";
};

# المستخدم 179: 0137831314
:do {
    /tool user-manager user add customer="admin" username="0137831314" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0137831314";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0137831314";
};

# المستخدم 180: 0151022479
:do {
    /tool user-manager user add customer="admin" username="0151022479" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0151022479";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0151022479";
};

# المستخدم 181: 0124861151
:do {
    /tool user-manager user add customer="admin" username="0124861151" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0124861151";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0124861151";
};

# المستخدم 182: 0199696001
:do {
    /tool user-manager user add customer="admin" username="0199696001" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0199696001";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0199696001";
};

# المستخدم 183: 0195143692
:do {
    /tool user-manager user add customer="admin" username="0195143692" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0195143692";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0195143692";
};

# المستخدم 184: 0164379974
:do {
    /tool user-manager user add customer="admin" username="0164379974" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0164379974";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0164379974";
};

# المستخدم 185: 0181098216
:do {
    /tool user-manager user add customer="admin" username="0181098216" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0181098216";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0181098216";
};

# المستخدم 186: 0164487590
:do {
    /tool user-manager user add customer="admin" username="0164487590" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0164487590";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0164487590";
};

# المستخدم 187: 0126829477
:do {
    /tool user-manager user add customer="admin" username="0126829477" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0126829477";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0126829477";
};

# المستخدم 188: 0158424029
:do {
    /tool user-manager user add customer="admin" username="0158424029" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0158424029";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0158424029";
};

# المستخدم 189: 0128788516
:do {
    /tool user-manager user add customer="admin" username="0128788516" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0128788516";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0128788516";
};

# المستخدم 190: 0125613595
:do {
    /tool user-manager user add customer="admin" username="0125613595" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0125613595";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0125613595";
};

# المستخدم 191: 0101827386
:do {
    /tool user-manager user add customer="admin" username="0101827386" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0101827386";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0101827386";
};

# المستخدم 192: 0132738628
:do {
    /tool user-manager user add customer="admin" username="0132738628" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0132738628";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0132738628";
};

# المستخدم 193: 0182123520
:do {
    /tool user-manager user add customer="admin" username="0182123520" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0182123520";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0182123520";
};

# المستخدم 194: 0179954352
:do {
    /tool user-manager user add customer="admin" username="0179954352" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0179954352";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0179954352";
};

# المستخدم 195: 0187222474
:do {
    /tool user-manager user add customer="admin" username="0187222474" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0187222474";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0187222474";
};

# المستخدم 196: 0196335028
:do {
    /tool user-manager user add customer="admin" username="0196335028" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0196335028";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0196335028";
};

# المستخدم 197: 0125954631
:do {
    /tool user-manager user add customer="admin" username="0125954631" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0125954631";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0125954631";
};

# المستخدم 198: 0139211725
:do {
    /tool user-manager user add customer="admin" username="0139211725" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0139211725";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0139211725";
};

# المستخدم 199: 0170345174
:do {
    /tool user-manager user add customer="admin" username="0170345174" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0170345174";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0170345174";
};

# المستخدم 200: 0119850889
:do {
    /tool user-manager user add customer="admin" username="0119850889" password="" profile="CARD";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0119850889";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0119850889";
};


:put "📊 تم الانتهاء من النسخة الاحتياطية";
:put "✅ نجح: $success كرت";
:put "❌ فشل: $errors كرت";
:put "📈 الإجمالي: $total كرت";

:put "🎉 تم استعادة جميع كروت القالب: 10";
:put "💡 النسخة الاحتياطية مكتملة بنجاح!";
